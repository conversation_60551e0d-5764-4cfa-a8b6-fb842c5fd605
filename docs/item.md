
# 商品二次开发手册

修改历史：

1. 初稿，2016-07
	张成栋 <[<EMAIL>](mailto:<EMAIL>)>，186-6800-3913
2. 新增搜索结果扩展文档,2017-03-20
	郭超鹏 <[<EMAIL>](mailto:<EMAIL>)>

----------


[toc]


## 1. 商品模块引入和初始化

### 1.1 系统环境要求

商品模块、示例工程依赖的基础环境与中间件如下表所示：

Component Name  | Version                  | Required
--------------- | ------------------------ | :------:
Java (jdk)      | ~= 1.7                   |    Y
Maven           | ~= 3.3.x                 |    Y
Mysql (Mariadb) | ~= 5.x (Mariadb ~= 10.x) |    Y
Elasticsearch   | ~= 2.2.x                 |    Y
Zookeeper       | ~= 3.4.x                 |    N
Redis           | ~= 3.x                   |    N
Nginx           | ~= 1.10.x                |    N


### 1.2 项目与初始化

&emsp;&emsp;首先需要新建一个 Maven 项目，例如现有一个基于 Springboot 框架的项目，名为 `showcase`，启动文件为 `ShowcaseApplication.java`，配置文件为 `ShowcaseConfiguration.java`。

1. 在项目的 `pom.xml` 文件中引入依赖：

	```xml
	<dependency>
		<groupId>io.terminus.parana</groupId>
		<artifactId>parana-item</artifactId>
		<version>4.3.BUILD-SNAPSHOT</version>
	</dependency>
	<dependency>
		<groupId>io.terminus.parana</groupId>
		<artifactId>parana-item-api</artifactId>
		<version>4.3.BUILD-SNAPSHOT</version>
	</dependency>
	```

2. 初始化商品数据库结构，在你的 Mysql 数据库中执行以下2个 SQL：

	* `parana-core/parana-item/src/test/resources/schema.sql`
	* `parana-core/parana-category/src/test/resources/schema.sql`

3. 初始化商品上下文 Bean，在项目的自动配置文件入口引入商品配置：

	例如项目的配置文件为 `ShowcaseConfiguration.java`：

	```java
	@Configuration
	@Import({ItemAutoConfig.class,
	        ItemApiConfiguration.class})
	public class ShowcaseConfiguration{}
	```

4. 在 `resources` 目录下加入 `application.yml` 文件，并运行 `ShowcaseApplication.java`：

	**注意：**其中数据库连接等配置需要按照*你的机器环境*配置：

	```yaml
	spring:
	  datasource:
	    driver-class-name: com.mysql.jdbc.Driver
	    url: ***************************************************************************
	    username: root
	    password: anywhere

	mybatis:
	  mapperLocations:  classpath*:mapper/*Mapper.xml
	  typeAliasesPackage: >
	    io.terminus.parana.item.model,
	    io.terminus.parana.category.model,
	    io.terminus.parana.brand.model,
	    io.terminus.parana.spu.model,
	    io.terminus.parana.shop.model,
	    io.terminus.parana.delivery.model
	    
	search:
	  host: localhost
	  port: 9200
	
	item.search:
	  index-name: items
	  index-type: item
	  mapping-path: item_mapping.json
	  full-dump-range: 3
	  batch-size: 100
	```

----------

## 2. 通用扩展设计

&emsp;&emsp; 通用扩展包括实体类的扩展字段，和事件机制，可以让实施项目在尽量不侵入基础项目代码的情况下扩展新业务功能。

### 2.1 可序列化、非查询条件字段非查询的扩展

&emsp;&emsp; **！！！注意：**因为数据库支持问题、性能问题，通过这种方式扩展的字段不推荐作为查询条件。

1. 字段定义

	现有多数实体类在设计时考虑了对非查询字段的扩展，一般为加入一个叫做 `extraJson` 的统一入口，例如商品模块中，数据库定义为：

	```sql
	CREATE TABLE `parana_items` (
	-- 前略
	`extra_json` VARCHAR(1024) NULL COMMENT '商品额外信息,建议json字符串',
	-- 后略
	) COMMENT='商品表';
	```

	在极端情况下定义为 `extra_json text null`。

2. 使用方式

	`String extraJson;` 与 `Map<String, String> extra;` 互相对应，使用时，往`extra` 中加入所需的消息即可，在储存时将其序列化为 JSON 字符串（可以选用其他序列化方式）储存；读取时则相反，获取 JSON 字符串反序列化即可。 



### 2.2 基于已有事件的扩展

&emsp;&emsp;通过 Guava 提供的事件发布、订阅机制，可以处理异步或者高延迟的任务，同时也可以通过订阅事件扩展新的业务功能，各个事件**一般**在 WEB 层 core 模块中定义，在 Controller 层抛出、在 WEB 层订阅。

&emsp;&emsp;例如在商品创建后，会由：`Items#create` 抛出 `ItemCreatedEvent` 事件，此时可以在项目中加入一个事件订阅类 `ShowcaseItemEventListener.java` 订阅：

```java
@Component
public class ShowcaseItemEventListener {	
    @Autowired
    private EventBus eventBus;

    @Autowired
    private Promotion promotion;


    @PostConstruct
    public void init(){
        this.eventBus.register(this);
    }

	/**
	 * 每次创建商品，所有带 @Subscribe 且入参为
	 * ItemCreatedEvent 的方法都会被调用。
	 */
    @Subscribe
    public void onItemUpdate(ItemCreatedEvent event){
        // 例如为新加入的商品增加优惠券
        promotion.createByItemId(event.getItemId());
    }
   }
```

&emsp;&emsp;目前商品模块预定义的事件有：

<table>
  <thead>
    <tr>
      <th>Event name</th>
      <th>Event tigger</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>ItemCreatedEvent</td>
      <td>io.terminus.parana.web.front.item.ItemController#create</td>
    </tr>
    <tr>
      <td rowspan="6">ItemUpdateEvent</td>
      <td>io.terminus.parana.web.admin.item.Items#updateStatus</td>
    </tr>
    <tr>
      <td>io.terminus.parana.web.admin.item.Items#batchUpdateStatus</td>
    </tr>
    <tr>
      <td>io.terminus.parana.web.admin.item.Items#updateTags</td>
    </tr>
    <tr>
      <td>io.terminus.parana.web.front.item.ItemController#batchUpdateStatus</td>
    </tr>
    <tr>
      <td>io.terminus.parana.web.front.item.ItemController#update</td>
    </tr>
    <tr>
      <td>io.terminus.parana.web.front.item.ItemController#updateStatus</td>
    </tr>
    <tr>
      <td>ItemDeleteEvent</td>
      <td><strong>Unused</strong></td>
    </tr>
  </tbody>
</table>

----------


## 3. 类目 & SPU

&emsp;&emsp;暂无

----------


## 4. 商品

&emsp;&emsp;发布商品是一个核心关键的功能，商品类目数据规则相当复杂，且每个实施项目运营对商品属性规则的要求又各不相同。

&emsp;&emsp;**商品规则**作为一个可扩展的重要功能，Parana 默认提供了一系列规则模板，用来检验发布商品、SPU时参数的正确性，例如：

* `AttributeLiteralRule`  检查了商品的属性键或者属性值中是否包含非法字符
* `OtherAttributeRuleBySpuExecutor` 检查了SPU发商品时商品是否继承了SPU的属性

&emsp;&emsp;等等。

&emsp;&emsp;用户也可以选择不使用这些规则，或者只使用部分默认规则，并插入当前项目运营需要的新规则，下文中我们举例说明如何加入一个新的简单规则：属性值长度不能超过15个字符，并且继续使用 Parana 提供的默认规则。

1.  在项目中新增一个新的 `RuleExecutor` 子类

	我们新增一个新的规则类，并按照业务含义命名，这个例子中我们把它命名为 `ShowcaseAttrvalLengthRuleExecutor`

	```java
	@Slf4j
	public class ShowcaseAttrvalLengthRuleExecutor extends RuleExecutor {
	    /**
	     * 规则引擎在处理数据输入时会调用这个方法 (例如用户发布或者更新商品/spu)
	     * <p/>
	     * 检查用户输入的属性长度是否超过15字符, 如果超长就报错
	     *
	     * @param input  用户提交的数据, 这个例子中只处理这个参数
	     * @param output 可能转换成为不同类型的数据, 注意, 也可能直接修改input的数据作为处理结果
	     * @throws InvalidException 如果策略是校验失败, 抛出异常说明原因
	     */
	    @Override
	    public void doHandleInboundData(BaseInput input, BaseOutput output) 
		    throws InvalidException {
	        List<GroupedOtherAttribute> groupedOtherAttrList =
		        input.getGroupedOtherAttributes();
	        for (GroupedOtherAttribute groupedOtherAtt : groupedOtherAttrList) {
	            for (OtherAttribute otherAttr : groupedOtherAtt.getOtherAttributes()) {
	                final String attrVal = otherAttr.getAttrVal();
	                if (!(StringUtils.hasText(attrVal) && attrVal.length() <= 15)) {
	                    log.error("attrVal({}).over.size", attrVal);
	                    throw new InvalidException("attrVal({0}).over.size", attrVal);
	                }
	            }
	        }
	
	        List<GroupedSkuAttribute> groupedSkuAttrList =
		        input.getGroupedSkuAttributes();
	        for (GroupedSkuAttribute groupedSkuAttribute : groupedSkuAttrList) {
	            for (SkuAttribute skuAttribute : groupedSkuAttribute.getSkuAttributes()) {
	                final String attrVal = skuAttribute.getAttrVal();
	                if (!(StringUtils.hasText(attrVal) && attrVal.length() <= 15)) {
	                    log.error("attrVal({}).over.size", attrVal);
	                    throw new InvalidException("attrVal({0}).over.size", attrVal);
	                }
	            }
	        }
	    }
	
	    /**
	     * 规则引擎在处理数据输出时会调用这个方法 (例如用户查询商品/spu, 或者进入编辑商品/spu的界面)
	     * <p/>
	     * 检查展示的属性长度是否超过15字符, 如果超长就不展示
	     *
	     * @param input  用户提交的数据, 在处理过程中可能会逐步的修正, 这个例子中只处理这个参数
	     * @param output 如果要求输出数据类型和输入类型不一致,则需要使用这个参数, 用来收集处理的输出,可能需要分步处理
	     */
	    @Override
	    public void doHandleOutboundData(BaseInput input, BaseOutput output) {
	        for (GroupedOtherAttribute groupedOtherAtt : input.getGroupedOtherAttributes()) {
	            List<OtherAttribute> filterdOtherAttr = Lists.newArrayList();
	            for (OtherAttribute otherAttr : groupedOtherAtt.getOtherAttributes()) {
	                // 只返回长度合法的属性
	                final String attrVal = otherAttr.getAttrVal();
	                if (!(StringUtils.hasText(attrVal) && attrVal.length() <= 15)) {
	                    filterdOtherAttr.add(otherAttr);
	                }
	            }
	            groupedOtherAtt.setOtherAttributes(filterdOtherAttr);
	        }
	
	        for (GroupedSkuAttribute groupedSkuAttribute : input.getGroupedSkuAttributes()) {
	            List<SkuAttribute> filterdSkuAttr = Lists.newArrayList();
	            for (SkuAttribute skuAttribute : groupedSkuAttribute.getSkuAttributes()) {
	                // 只返回长度合法的属性
	                final String attrVal = skuAttribute.getAttrVal();
	                if (StringUtils.hasText(attrVal) && attrVal.length() <= 15) {
	                    filterdSkuAttr.add(skuAttribute);
	                }
	            }
	            groupedSkuAttribute.setSkuAttributes(filterdSkuAttr);
	        }
	    }
	
	    /**
	     * 由于只校验属性长度, 所有数据类型都适合本规则
	     *
	     * @param input 数据
	     * @return 是否适用本规则
	     */
	    @Override
	    public boolean support(BaseInput input) {
	        return Boolean.TRUE;
	    }
	
	    /**
	     * 获取本执行器要执行的属性规则列表, 例如检查必填属性和属性数据类型
	     * <p/>
	     * 本例只检查属性值长度, 返回空列表即可
	     *
	     * @param data 待处理的数据
	     * @return 要校验的规则列表
	     */
	    @Override
	    protected List<?> getRules(BaseInput data) {
	        return Collections.emptyList();
	    }
	}
	```

2. 添加一个规则配置类 `PipelineConfigurer` 的子类

	通过在 `PipelineConfigurer`中调用 `RuleExecutorRegistry#register` 方法，可以将我们的自定义规则注册到规则中心，在每次检查商品数据的时候都会按照调用 `register` 注册规则的顺序**依次调用**。由于我们需要复用预置的默认规则列表，我们加入 `ShowcasePipelineConfigurer` 并让它继承 `DefaultPipelineConfigurer`：

	```java
	@Component
	public class ShowcasePipelineConfigurer 
		extends DefaultPipelineConfigurer {
	
	    @Autowired
	    public ShowcasePipelineConfigurer(SpuCacher spuCacher, 
	                                      CategoryAttributeCacher categoryAttributeCacher, 
	                                      CategoryAttributeNoCacher categoryAttributeNoCacher) {
	        super(spuCacher, categoryAttributeCacher, categoryAttributeNoCacher);
	    }
	
	    @Override
	    public void configureRuleExecutors(RuleExecutorRegistry ruleExecutorRegistry) {
	        // 将自定义规则插入规则队列头部
	        ruleExecutorRegistry.register(new ShowcaseAttrvalLengthRuleExecutor());
	        // 调用父方法注册预置的规则
	        super.configureRuleExecutors(ruleExecutorRegistry);
	    }
	}

	```

	这样我们就复用了默认规则列表，而且在规则列表开始处插入了一个我们自定义规则：属性值长度不能超过15字符。


----------


## 5. 库存

&emsp;&emsp;现允许在实施项目中简单自定义交易扣减库存逻辑，也一定程度上支持复杂的库存逻辑，如分仓业务下变更库存，。

&emsp;&emsp;在实施项目中添加一个 `StorageService` 子类，并复写其接口，通过这种方式可以隐藏底层的库存逻辑，对外提供统一的库存操作入口，包括：查询库存、增减库存、设置库存。

----------


## 6. 搜索

### 6.1 搜索数据结构扩展

&emsp;&emsp;搜索的数据结构定义位于每个项目商品模块的  `resources/item_mapping.json`，例如现有一商品打标需求，需要把 `Item.tags` 字段作为搜索条件，则按以下步骤改变 ES 中商品的数据结构：

1. 更改 `item_mapping.json` 数据结构

	`tags` 为一个字典结构，Elasticsearch 虽然支持对象搜索，但处于性能考虑，存入搜索数据库中前应当把 `tags` 转换为一个列表，比如把源数据 `{"a":1, "b":2}` 转换为 `["a:1", "b:2"]` 后存入搜索。
	
	确定数据结构后直接在 `item_mapping.json` 插入对应的字段定义即可：

	```json
      "promotionTypes":{
        "type": "integer"
      },
      // 可以在任意位置插入这个字段
      "tags": {
        "type": "string",
        "index": "not_analyzed"
      },
      "updatedAt": {
        "type": "date"
      }
	```

2. 更改 `IndexedItem` 数据结构

	在对应的实施项目中创建一个新的 `IndexedItem` 子类，并加入这个字段：

	```java
	public class ShowcaseIndexedItem extends IndexedItem {
	    private static final long serialVersionUID =
	     4864416251631048505L;	
	    
	    /**
	     * 用':'分割的商品标签键值对列表
	     */
	    @Getter
	    @Setter
	    private List<String> tags; 
	}
	```

3. 更改 `IndexedItemFactory<T>` 工厂类，让它支持新的字段

	在对应的实施项目中创建一个新的 `IndexedItemFactory` 子类，把 `tags` 字段塞入 `ShowcaseIndexedItem`：

	```java
	@Component
	public class ShowcaseItemIndexFactory extends
		BaseIndexedItemFactory<ShowcaseIndexedItem> {
		// ... 省略
		
	    /**
	     * 创建dump到搜索引擎的商品对象, 包含属性, 类目等信息
	     *
	     * @param item          原有的商品对象
	     * @param itemAttribute 商品属性信息
	     * @param others        可能还需要其他信息来组装
	     * @return dump到搜索引擎的商品对象
	     */
	    @Override
	    public ShowcaseIndexedItem create(
		    Item item, 
		    ItemAttribute itemAttribute, 
		    Object... others) {
		    
		    ShowcaseIndexedItem indexedItem = 
			    super.create(item, itemAttribute, others);
            // 商品打标
	        if (item.getTags() != null) {
	            List<String> tags = Lists.newArrayList();
	            for (Map.Entry<String, String> entry : item.getTags().entrySet()) {
	                tags.add(entry.getKey() + ":" + entry.getValue());
	            }
	            indexedItem.setTags(tags);
	        }
	
	        return indexedItem;
	    }
	}
	```

4. 更新 Elasticsearch 中的文档结构

	此时停止商品服务，先删除 Elasticsearch 中的原有商品文档：

	`curl -XDELETE 0:9200/items`

	再重启商品服务即可，通过 admin full dump 后搜索验证：

	```json
	curl -XPOST 0:9200/items/_search?pretty -d '{
	  "size": 1,
	  "query": {
	    "constant_score": {
	      "filter": {
	        "exists": {
	          "field": "tags"
	        }
	      }
	    }
	  }
	}'
	``` 

	即可以在结果中看到新加的 `tags` 字段：

	```json
	// ... 省略
	"updatedAt": 1468569469000,
	// 新添加的字段
	"tags": [
		"a:1",
		"b:2"
	]
	// ... 省略
	```

### 6.2 搜索查询条件扩展

1. 让查询条件支持新字段

	在实施项目中加入一个 `BaseItemQueryBuilder` 或者 `DefaultItemQueryBuilder` 的子类，并确认查询条件类型，例如 `tags` 字段是过滤查询条件，`price` 字段是范围查询条件。目前支持的查询条件类型有：

	* 关键字查询，对应 `buildKeyword`
	* 单值、多值过滤查询，对应 `buildTerm`、`buildTerms`
	* 范围查询，对应 `buildRanges`
	* 聚合查询，对应 `buildAggs`
	* 自定义排序，对应 `buildSort`
	* 自定义高亮，对应 `buildHighlight`

	确定新的查询条件后加入即可：

	```java
	@Component
	public class ShowcaseItemQueryBuilder
		extends DefaultItemQueryBuilder {
	    @Override
	    public List<Terms> buildTerms(Map<String, String> params) {
	        List<Terms> termsList =  super.buildTerms(params);
	        // 商品打标、搜索
	        if (StringUtils.hasText(params.get("tags"))) {
		        var tags = Splitters.UNDERSCORE.splitToList(params.get("tags"));
	            termsList.add(new Terms("tags", tags));
	        }
	        return termsList;
	    }
    }
	```

2. 在查询前预处理字段

	预处理包括但不限于以下几个场景：
	* 查询条件使用了别名，例如用 `t` 代替了 `tags`。
	* 查询条件需要默认值，例如针对新用户加入特定的商品标签。
	* 纠错或者过滤非法的查询条件。

	正对上面这些场景，只需在实施项目中加入自己的主搜服务，在真正调用 `io.terminus.parana.search.item.ItemSearchReadService#searchWithAggs` 前预处理查询参数：

	```java
	@Component
	@Slf4j
	public class ShowcaseMainSearch {
		@Autowire
		private ShowcaseUserService showcaseUserService;

		@RpcConsumer
		private ItemSearchReadService itemSearchReadService;

		/**
	     * 搜索商品, 并且包括属性导航, 面包屑等
	     *
	     * @param user  可能的登陆用户
	     * @param pageNo       起始页码
	     * @param pageSize     每页记录条数
	     * @param params       搜索上下文
	     * @return 搜索结果, 包括属性导航, 面包屑等
	     */
	    public Response<SearchWithAggs> searchWithAggs(Optional<ParanaUser> user,
	                                                   InnerCookie cookie,
	                                                   Integer pageNo,
	                                                   Integer pageSize,
	                                                   Map<String, String> params){
	        String templateName = "search.mustache";
	        
	        // 1. 处理别名,
	        // 2. 加入默认值
	        String tags = params.remove("t");
	        if (StringUtils.hasText(tags)) {
	            if (user.isPresent() && showcaseUserService.isNewUser(user.get())) {
	                Joiner.on("_").join(tags, "target:newbee");
	            }
	            params.put("tags", tags);
	        }
	        return itemSearchReadService.searchWithAggs(pageNo,pageSize, templateName, params);
	    }
	}
	```

	如此一来搜索服务就支持了按照商品标签搜索，支持了一个别名为 `t` 的查询参数：

	```shell
	curl -XGET "parana.dithub.com/search?q=电脑&t=target:newbee"
	```

	上面这个搜索结果中就只会返回包含了 `target:newbee` 这个标签的商品。



### 6.3 搜索结果扩展

1.搜索结果返回自定义的ItemIndexFactory新增的字段

例如我们通过自定义的ItemIndexFactory将商品的生产日期productionDate dump到搜索引擎，
现在我们需要在搜索时返回这个字段，那么可以在item-api(或者web)提供一个实现以下接口的bean:
```java
public class ShowcaseSearchedItemType implements SearchedItemType {
    @Override
    public Class<? extends SearchedItem> getType() {
        return ShowcaseSearchedItem.class;
    }
}

```

其中ShowcaseSearchedItem是一个继承SearchedItem的普通dto,其包含要返回的productionDate字段:
```java
public class ShowcaseSearchedItem extends SearchedItem {

    @Getter
    @Setter
    private String productionDate;
}
```

2.如果希望对搜索结果进行更多的扩展，比如聚合等，可以在item模块提供一个实现以下接口的bean:
```java
/**
 * 商品主搜结果组装器
 */
public interface ItemSearchResultComposer<T extends SearchedItemWithAggs> {

    /**
     * 组装商品主搜结果
     *
     * @param withAggs 搜索原生结果
     * @param params   参数
     * @param context  上下文
     * @return 组装好的商品信息
     */
    T compose(WithAggregations withAggs, Map<String, String> params, Map<String, Object> context);

}
```

其中withAggs参数是原生的搜索结果，可以按具体需求重新组装,默认实现DefaultItemSearchResultComposer。
类似的，店铺内搜索可以通过实现ItemSearchInShopResultComposer来自己组装搜索结果.

----------


**Copyright (c) 2016 [杭州端点网络科技有限公司](http://terminus.io)**

