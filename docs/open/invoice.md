## 获取当前登录用户所有发票信息

* 请求url
```
GET invoice.list
```

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": [
        {
          "id": 3,
          "userId": 2,
          "title": "个人",
          "detail": {
            "type": "1",
            "titleType": "1"
          },
          "status": 1,
          "isDefault": true,
          "createdAt": 1466857262000,
          "updatedAt": 1466857262000
        },
        {
          "id": 4,
          "userId": 2,
          "title": "杭州端点网络科技有限公司",
          "detail": {
            "type": "1",
            "titleType": "2"
          },
          "status": 1,
          "isDefault": false,
          "createdAt": *************,
          "updatedAt": *************
        },
        {
          "id": 5,
          "userId": 2,
          "title": "杭州端点网络科技有限公司",
          "detail": {
            "type": "2",
            "companyName": "杭州端点网络科技有限公司",
            "taxRegisterNo": "***************",
            "registerAddress": "浙江杭州",
            "registerPhone": "***********",
            "registerBank": "招商",
            "bankAccount": "************"
          },
          "status": 1,
          "isDefault": false,
          "createdAt": *************,
          "updatedAt": *************
        }
      ]
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| id | 整形 | 发票id |  |
| userId | 整形 | 用户id |  |
| title | 字符串 | 发票抬头 |  |
| detail.type | 整形 | 发票类型 | 1表示普通发票,2表示增值税发票 |
| detail.titleType | 整形 | 发票抬头类型,普通发票时有值 | 1表示个人,2表示公司 |
| detail.companyName | 字符串 | 公司名称 | 当是增值税发票时有值 |
| detail.taxRegisterNo | 字符串 | 税务登记号 | 当是增值税发票时有值 |
| detail.registerAddress | 字符串 | 注册地址 | 当是增值税发票时有值 |
| detail.registerPhone | 字符串 | 注册电话 | 当是增值税发票时有值 |
| detail.registerBank | 字符串 | 开户银行 | 当是增值税发票时有值 |
| detail.bankAccount | 字符串 | 银行账号 |  当是增值税发票时有值 |
| status | 整形 | 状态 | 1表示可用 |
| isDefault | 布尔 | 是否为默认发票 | true表示是默认发票,false表示不是 |
| createdAt | 日期 | 以毫秒数表示的发票创建日期 |  |
| updatedAt | 日期 | 以毫秒数表示的发票更新日期 |  |

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询发票信息失败"
     }
    ```

## 创建发票

* 请求url
```
POST invoice.create
```

* 请求参数示例
  - 创建普通发票个人抬头
    ```
    invoice=
    {
      "title": "个人",
      "detail": {
        "type": "1",
        "titleType": "1"
      }
    }
    ```

    - 创建普通发票公司抬头
        ```
        invoice=
        {
          "title": "杭州端点网络科技有限公司",
          "detail": {
            "type": "1",
            "titleType": "2"
          }
        }
        ```

    - 创建增值税发票
        ```
        invoice=
        {
          "title": "杭州端点网络科技有限公司",
          "detail": {
            "type": "2",
            "companyName": "杭州端点网络科技有限公司",
            "taxRegisterNo": "***************",
            "registerAddress": "浙江杭州",
            "registerPhone": "***********",
            "registerBank": "招商",
            "bankAccount": "************"
          }
        }
        ```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| title | 字符串 | 发票抬头| 是 | 普通发票个人抬头时,建议该值为"个人" |
| detail.type | 整形 | 发票类型 | 是 | 1表示普通发票,2表示增值税发票 |
| detail.titleType | 整形 | 发票抬头类型,普通发票时有值 | 否 | 1表示个人,2表示公司 |
| detail.companyName | 字符串 | 公司名称 | 否 | 当是增值税发票时必填 |
| detail.taxRegisterNo | 字符串 | 税务登记号 | 否 | 当是增值税发票时必填|
| detail.registerAddress | 字符串 | 注册地址 | 否 | 当是增值税发票时必填 |
| detail.registerPhone | 字符串 | 注册电话 | 否 |  当是增值税发票时必填 |
| detail.registerBank | 字符串 | 开户银行 | 否 | 当是增值税发票时必填 |
| detail.bankAccount | 字符串 | 银行账号 | 否 | 当是增值税发票时必填 |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": 22
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| result | 整形 | 发票id |  |


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "创建发票失败"
     }
    ```


## 删除发票

* 请求url
```
POST invoice.delete
```

* 请求参数示例
```
id=22
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| id | 整形 | 要删除的发票id | 是 |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": true
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| result | 布尔 | 是否删除成功 |  |


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "删除发票失败"
     }
    ```


## 设置默认发票

* 请求url
```
PUT invoice.default
```

* 请求参数示例
```
id=22
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| id | 整形 | 发票id | 是 |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": true
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| result | 布尔 | 是否设置成功 |  |


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "设置默认发票失败"
     }
    ```