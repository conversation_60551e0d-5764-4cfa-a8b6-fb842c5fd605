## 查询商品的营销活动(给商品详情页用)

* 请求url
```
GET item.promotion.list
```

* 请求参数示例
```
itemId=5144
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| itemId | 整形 | 商品id | 是 |  |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": [
        {
          "promotion": {
            "id": 149,
            "shopId": 68,
            "name": "商品9折",
            "promotionDefId": 20,
            "type": 3,
            "status": 1,
            "startAt": 1483977600000,
            "endAt": 1515513600000,
            "extra": null,
            "createdAt": 1475908430000,
            "updatedAt": 1481630100000
          },
          "skuWithPromotions": [
            {
              "sku": {
                "id": 6189,
                "skuCode": null,
                "itemId": 5144,
                "shopId": 68,
                "status": 1,
                "specification": null,
                "outerSkuId": "",
                "outerShopId": null,
                "image": null,
                "thumbnail": null,
                "name": null,
                "extraPriceJson": "{\"platformPrice\":10000,\"originPrice\":0}",
                "extraPrice": {
                  "platformPrice": 10000,
                  "originPrice": 0
                },
                "price": 10000,
                "attrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "军绿色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "stockType": 0,
                "stockQuantity": 96,
                "extra": {
                  "unitQuantity": "1"
                },
                "createdAt": 1475133726000,
                "updatedAt": 1478741908000
              },
              "promotionInfo": {
                "discount": 90
              },
              "promotionPrice": 9000
            },
            {
              "sku": {
                "id": 6190,
                "skuCode": null,
                "itemId": 5144,
                "shopId": 68,
                "status": 1,
                "specification": null,
                "outerSkuId": "",
                "outerShopId": null,
                "image": null,
                "thumbnail": null,
                "name": null,
                "extraPriceJson": "{\"platformPrice\":0,\"originPrice\":0}",
                "extraPrice": {
                  "platformPrice": 0,
                  "originPrice": 0
                },
                "price": 10000,
                "attrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "天蓝色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "stockType": 0,
                "stockQuantity": 91,
                "extra": {
                  "unitQuantity": "1"
                },
                "createdAt": 1475133726000,
                "updatedAt": 1478741908000
              },
              "promotionInfo": {
                "discount": 90
              },
              "promotionPrice": 9000
            }
          ]
        },
        {
          "promotion": {
            "id": 150,
            "shopId": 68,
            "name": "商品减10元",
            "promotionDefId": 20,
            "type": 3,
            "status": 1,
            "startAt": 1483977600000,
            "endAt": 1515513600000,
            "extra": null,
            "createdAt": 1475908495000,
            "updatedAt": 1483670701000
          },
          "skuWithPromotions": [
            {
              "sku": {
                "id": 6189,
                "skuCode": null,
                "itemId": 5144,
                "shopId": 68,
                "status": 1,
                "specification": null,
                "outerSkuId": "",
                "outerShopId": null,
                "image": null,
                "thumbnail": null,
                "name": null,
                "extraPriceJson": "{\"platformPrice\":10000,\"originPrice\":0}",
                "extraPrice": {
                  "platformPrice": 10000,
                  "originPrice": 0
                },
                "price": 10000,
                "attrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "军绿色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "stockType": 0,
                "stockQuantity": 96,
                "extra": {
                  "unitQuantity": "1"
                },
                "createdAt": 1475133726000,
                "updatedAt": 1478741908000
              },
              "promotionInfo": {
                "reduceFee": 1000
              },
              "promotionPrice": 9000
            },
            {
              "sku": {
                "id": 6190,
                "skuCode": null,
                "itemId": 5144,
                "shopId": 68,
                "status": 1,
                "specification": null,
                "outerSkuId": "",
                "outerShopId": null,
                "image": null,
                "thumbnail": null,
                "name": null,
                "extraPriceJson": "{\"platformPrice\":0,\"originPrice\":0}",
                "extraPrice": {
                  "platformPrice": 0,
                  "originPrice": 0
                },
                "price": 10000,
                "attrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "天蓝色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "stockType": 0,
                "stockQuantity": 91,
                "extra": {
                  "unitQuantity": "1"
                },
                "createdAt": 1475133726000,
                "updatedAt": 1478741908000
              },
              "promotionInfo": {
                "reduceFee": 1000
              },
              "promotionPrice": 9000
            }
          ]
        }
      ]
   }

    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询营销活动失败"
     }
    ```


## 查询指定商品下每个sku采用默认营销时的价格(给商品详情用)

* 请求url
```
GET skus.price.with.default.promotion
```

* 请求参数示例
```
itemId=5144
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| itemId | 整形 | 商品id | 是 |  |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": [
        {
          "skuId": 6189,
          "promotionPrice": 9000
        },
        {
          "skuId": 6190,
          "promotionPrice": 9000
        }
      ]
    }

    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询营销活动失败"
     }
    ```