## msg模块使用文档

### 引入依赖
首先选定一个应用层的消息版本: 现在只有terminus-msg-light

* terminus-msg-light: 不依赖于数据库的轻量级版本, 包含功能:
    * 自动根据发送者格式,识别消息类型(短信,邮件,app消息推送)
    * 记录消息发送请求和发送结果日志
    * 可以创建hbs文件消息模板, 从而可发送模板消息
    
* terminus-msg-pro: 依赖于数据库的功能更强大的版本(还未实现), 包含的功能:
    * 自动根据发送者格式,识别消息类型(短信,邮件,站内信,app消息推送)
    * 在数据中记录下消息发送请求和发送结果, 方便查找和后期操作
    * 可以创建hbs文件消息模板, 从而可发送模板消息。 
    * 可以在数据库中配置消息模板, 从而可以不用重启服务器就能替换消息模板
    * 消息订阅功能, 可以由用户选择是否接收消息
    * 失败消息重发
    * 消息定时发送
    * 消息定时关闭
    * 消息发送者转换(由用户ID推知手机,电话, appToken等)
    * 群组消息
    * 站内信
    
然后选定具体的消息网关:
* 短信
    * terminus-msg-sms-alidayu  利用阿里云账号发短信(付费)
    * terminus-msg-sms-aliyun   利用阿里大鱼账号发短信(付费)
    * terminus-msg-sms-emay 亿美短信(付费)
* 邮件
    * terminus-msg-email-javax SMTP协议邮件(只要你有邮箱,可以找到对应的邮箱服务器, 可以免费)
    * terminus-msg-email-sendcloud sendcloud邮件(付费)
* app消息
    *  temrinus-msg-app-umeng 友盟(免费)
    
将选好的组合的依赖加入到pom中, 就能增加相应的配置, 不需要显式Import Config。示例如下:
```xml
        <dependency>
            <groupId>io.terminus.msg</groupId>
            <artifactId>terminus-msg-light</artifactId>
            <version>1.4.BUILD-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.msg</groupId>
            <artifactId>terminus-msg-sms-alidayu</artifactId>
            <version>1.4.BUILD-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.terminus.msg</groupId>
            <artifactId>terminus-msg-email-sendcloud</artifactId>
            <version>1.4.BUILD-SNAPSHOT</version>
        </dependency>
```
    
### 增加yaml配置,主要为配置网关

```yaml
msg:
    current:
        smsService: aliSmsService | aliYunSmsService | emaySmsService
        emailService: javaxEmailService | sendCloudEmailService
        appPushService: umengAppPushService
    alidayu: #参见AliSmsToken
         appKey: xxx
         appSecret: xxx
    aliyun: #参见AliYunSmsToken
        appKey: xxx
        appSecret: xxx
    emay: #参见EmayToken
        account: xxx
        password: xxx
    javaxemail: #参见JavaxEmailToken
        mailServerHost: smtp.163.com"
        fromAddress: <EMAIL>
        userName: user
        password: pwd
    sendcloud: #参见SendCloudToken
        user: ~~
        triggerUser: ~~
        batchUser: ~~
        key: ~~
    umeng: #参见UmengAndroidToken和UmengIosToken
        android:
            appKey: xxx
            appMasterSecret: xxx
        ios:
            appKey: xxx
            appMasterSecret: xxx   
```     
            
### 增加消息模板

1. 在web项目的resources下新增template.yaml, 或者直接更新使用application.yaml

2. 添加消息模板配置如下:

```yaml
msg.template.list:
  - key: sms.user.register.code
    title: 用户注册
    content: >
      {
        "smsName":"注册验证",
        "smsTemplate":"SMS_7730199",
        "smsParam":{"code":"{{code}}","product":"{{product}}"}
      }

```


### 使用消息服务

引入消息服务:

```
@Autowired
private MsgService msgService;
    
```

调用服务方法:

```java
class Users{
    
    @Autowired
    private MsgService msgService;
    
    public void doSendSms(String mobile, String template, String code){
        try{
            String receivers = mobile;
            //MsgContext为一个帮助类,方便参数构造
            String result=msgService.send(receivers, template, MsgContext.of("code", code), null);
            log.info("sendSms result={}, mobile={}, message={}", result, mobile, code);
        }catch (MsgException e){
            log.error("sms send failed, mobile={}, cause:{}", mobile, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("sms.send.fail");
        }
    }
}
```