### 商品模块

1.  强大的商品描述能力 (类目, 属性, 属性组, SPU, 商品, sku等)

    在这些实体中, 只有类目, 商品, 以及 sku 是必须具备的, 属性, 属性组, SPU 是可选的, 但是使用这些会大大增强商品的描述能力
    
2. 商品属性本身也支持元信息, 可以规范属性以及属性值, 比如: 属性是否必填, 属性值的类型(数字,日期, 文本等), 是否支持属性自定义, 以及是否可搜索等
    
3.  通过规则链来对商品进行不同程度的管控, 在当前商品模块中, 支持的管控粒度从全开放(允许自定义属性)到全管控(通过spu).此外, 规则链可定制扩展. 并内置了可适应大部分应用场景的规则链.

4. 可插拔的搜索体系, 既可以使用内建的基于 elasticsearch 的分布式搜索引擎, 也可以对接外部搜索引擎

5. 开放的商品体系, 便于和第三方系统集成和对接



### 交易模块

1. 将交易流程以及数据模型区分开来, 交易流程一般变化比较大, 而数据模型一般是稳定的.

2. 定义了交易流程状态机的抽象接口, 不仅内置支持常见的交易流程, 也支持扩展定制的交易流程

3. 支持多套交易流程并存, 系统可以根据下单信息选择不同的交易流程, 从而支持各种业务(比如, 虚拟商品和实物商品的交易流程是不同的, 但一个业务系统可能同时支持虚拟商品和实物商品)

4. 交易流程中涉及到的实体包括但不限于 订单, 子订单, 支付单, 发货单, 退款单等, 系统在底层数据模型中将这些概念进行了区分, 便于流程和数据的跟踪和扩展

5. 支持可插拔的订单校验规则.

6. 运费模板, 交易快照, 多种支付渠道......


### 营销模块

1. 将一个营销工具划分为如下几个要素: 营销适用的商品范围, 营销适用的用户范围, 营销的条件和方式

2. 为每个营销工具的要素设计了接口, 业务系统可以根据内置的营销工具要素, 组合成新的营销工具, 也可以自行实现符合需要的营销要素接口, 定制新的营销工具

3. 系统内置了优惠券, 直降, 折扣等营销工具, 满足大部分场景的需要

4. 明确区分了营销工具的作用范围, 以及营销工具之间的关系, 如哪些营销工具可以叠加, 哪些营销工具必须互斥, 从而使得营销的计算和效果评估更清晰