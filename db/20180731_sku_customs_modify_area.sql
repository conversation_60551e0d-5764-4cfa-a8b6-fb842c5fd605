alter table parana_area add column `status` tinyint(1) default '1' Comment '状态 1正常，-1删除';
update parana_area set status=1 where delFlag=0;
update parana_area set status=-1 where delFlag=1;
alter table parana_area drop column `delFlag`;

alter table parana_items add column `is_bonded` tinyint(3) default NULL COMMENT '是否保税(1:保税，0:完税)' after `shop_id`;

update parana_items set is_bonded=0;

DROP TABLE IF EXISTS `parana_sku_customs`;

CREATE TABLE `parana_sku_customs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `sku_id` bigint(20) unsigned NOT NULL COMMENT '关联的sku的id',
  `hs_code` varchar(64) default NULL COMMENT '海关HS编码',
  `custom_origin_id` bigint(20) default NULL COMMENT '海关信息-原产地Id',
  `custom_origin` varchar(64) default NULL COMMENT '海关信息-原产地',
  `custom_tax_holder` tinyint(3) default NULL COMMENT '税金承担方(1:买家承担，2:卖家承担)',
  `province_id` bigint(20) default NULL COMMENT '所在地-省Id',
  `province` varchar(100) default NULL COMMENT '所在地-省',
  `city_id` bigint(20) default NULL COMMENT '所在地-市Id',
  `city` varchar(100) default NULL COMMENT '所在地-市',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态 1正常, -1删除',
  `created_at` datetime default NULL COMMENT '创建时间',
  `updated_at` datetime default NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='sku与海关信息的关联表';