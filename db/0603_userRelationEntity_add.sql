CREATE TABLE parana.parana_user_relation_entity (
	id BIGINT NULL AUTO_INCREMENT,
	user_id BIGINT NOT NULL,
	relation_id BIGINT NULL,
	`type` INT NOT NULL,
	status INT NULL,
	created_at DATE NULL,
	updated_at DATE NULL,
	extra_json varchar(255) NULL,
	CONSTRAINT parana_user_relation_entity_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
CREATE INDEX parana_user_relation_entity_user_id_IDX USING BTREE ON parana.parana_user_relation_entity (user_id);
CREATE INDEX parana_user_relation_entity_type_IDX USING BTREE ON parana.parana_user_relation_entity (`type`,relation_id);
CREATE INDEX parana_user_relation_entity_status_IDX USING BTREE ON parana.parana_user_relation_entity (status);
