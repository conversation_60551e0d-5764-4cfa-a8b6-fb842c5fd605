CREATE TABLE parana.parana_sub_store (
	id BIGINT NULL AUTO_INCREMENT COMMENT 'primary key',
	shop_id BIGINT NOT NULL,
	user_id BIGINT NOT NULL,
	name varchar(255) NULL,
	mobile varchar(100) NOT NULL,
	logo_url varchar(255) NULL,
	province varchar(100) NULL,
	city varchar(100) NULL,
	couty varchar(100) NULL,
	address varchar(255) NULL,
	auth_at DATETIME NULL,
	banner_list_str varchar(255) NULL,
	east_longtitude BIGINT NULL,
	north_latitude BIGINT NULL,
	extra_str varchar(255) NULL,
	created_at DATETIME NULL,
	updated_at varchar(255) NULL,
	`status`  INT NULL,
	CONSTRAINT parana_sub_store_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
CREATE INDEX parana_sub_store_id_IDX USING BTREE ON parana.parana_sub_store (id);
CREATE INDEX parana_sub_store_shop_id_IDX USING BTREE ON parana.parana_sub_store (shop_id,user_id);
CREATE INDEX parana_sub_store_user_id_IDX USING BTREE ON parana.parana_sub_store (user_id);

ALTER TABLE parana.parana_sub_store CHANGE couty county varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL;
