alter table parana_sku_orders
add column shipment_at datetime default null comment '发货时间';
###清空数据
UPDATE parana_sku_orders set shipment_at=null;
###获取数据shop级别
UPDATE parana_sku_orders o set o.shipment_at = (
select s.created_at  from
										parana_shop_orders as so
									   ,parana_order_shipments as s
										   											where
																						so.id=s.order_id
																				 and s.status>=1
											 									 and s.order_type=1
																				 and so.id=o.order_id
																				) where shipment_at is null;
###获取数据sku级别
UPDATE parana_sku_orders o set o.shipment_at = (
select s.created_at  from
									   parana_order_shipments as s
									   											   where
									   											   		o.id=s.order_id
									   											 and s.status>=1
									   											 and s.order_type=2
									   											 ) where shipment_at is null;