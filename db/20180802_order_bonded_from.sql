alter table parana_sku_orders add column `is_bonded` tinyint(3) default NULL COMMENT '是否保税(1:保税，0:完税)' after `status`;
alter table parana_sku_orders change column `out_sku_id` `outer_sku_id` varchar(32) default NULL COMMENT '外部sku编号';
alter table parana_sku_orders add column `outer_from` tinyint(3) default NULL COMMENT '外部平台(0：其他，1：洋800)' after `outer_sku_id`;
alter table parana_sku_orders add column `push_status` tinyint(3) default NULL COMMENT '推送状态(0:不需要推送，1：待推送，2：完成推送)' after `outer_from`;
alter table parana_sku_orders add column `push_time` datetime default NULL COMMENT '推送时间' after `push_status`;

update parana_sku_orders set is_bonded=0;

alter table parana_sku_orders drop column `outer_from`;
alter table parana_skus drop column `outer_from`;

alter table parana_skus add column `tags_json` varchar(2048) default null comment 'tag信息,json表示' after `extra`;