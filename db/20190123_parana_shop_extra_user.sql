CREATE TABLE `parana_shop_extra_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '流水id',
  `shop_id` bigint(20) NOT NULL COMMENT 'parana内部商店id',
  `api_id` varchar(100) NOT NULL COMMENT '外部商城api_id',
  `api_secret` varchar(100) NOT NULL COMMENT '外部商城api_secret',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '状态,1为有效,-1为删除',
  `extra` varchar(1024) DEFAULT NULL COMMENT '额外信息,json',
  `version` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `parana_shop_extra_user_apiid_IDX` (`api_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='以shop为主键进行对外部商城的api_id api_secret信息进行用户绑定\n';
