CREATE TABLE `parana_out_import_order_shop_order` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `shop_order_id` bigint(20) unsigned NOT NULL COMMENT '店铺级订单Id',
  `out_order_id` varchar(100) NOT NULL COMMENT '外部订单id',
  `shop_id` bigint(20) unsigned NOT NULL COMMENT '店铺id',
  `extra_json` varchar(500) DEFAULT NULL,
  `created_at` date NOT NULL,
  `updated_at` date NOT NULL,
  PRIMARY KEY (`id`),
  KEY `parana_out_import_order_shop_order_shop_order_id_IDX` (`shop_order_id`) USING BTREE,
  KEY `parana_out_import_order_shop_order_out_order_id_IDX` (`out_order_id`,`shop_id`) USING BTREE,
  CONSTRAINT `parana_out_import_order_shop_order_parana_shop_orders_FK` FOREIGN KEY (`shop_order_id`) REFERENCES `parana_shop_orders` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='外部导单存在判断表';
