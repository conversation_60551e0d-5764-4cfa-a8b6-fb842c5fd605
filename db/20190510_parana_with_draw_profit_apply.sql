CREATE TABLE parana.parana_with_draw_profit_apply (
	id BIGINT NULL AUTO_INCREMENT,
	fee BIGINT NOT NULL,
	with_draw_at DATETIME NULL,
	pay_serial_no varchar(255) NULL,
	user_id BIGINT NOT NULL,
	`status` INT NULL,
	extra_str varchar(255) NULL,
	created_at DATETIME NULL,
	updated_at DATETIME NULL,
	CONSTRAINT parana_with_draw_profit_apply_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
CREATE INDEX parana_with_draw_profit_apply_user_id_IDX USING BTREE ON parana.parana_with_draw_profit_apply (user_id);
CREATE INDEX parana_with_draw_profit_apply_pay_serial_no_IDX USING BTREE ON parana.parana_with_draw_profit_apply (pay_serial_no);
