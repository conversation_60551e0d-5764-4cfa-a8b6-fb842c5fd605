CREATE TABLE parana.parana_distribution_tax (
	id BIGINT(20) NULL AUTO_INCREMENT,
	shop_id bigint(20) NULL,
	we_shop_id bigint(20) NULL,
	tax_rate bigint(20) NOT NULL,
	CONSTRAINT parana_distribution_tax_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;

ALTER TABLE parana.parana_distribution_tax ADD status INT DEFAULT 1 NOT NULL;
ALTER TABLE parana.parana_distribution_tax ADD created_at DATE NULL;
ALTER TABLE parana.parana_distribution_tax ADD updated_at DATE NULL;

-- 此处根据情况设置税率
-- 【澳新】insert into parana.parana_distribution_tax (shop_id,we_shop_id,tax_rate,status)values(-1,null,17,1);
insert into parana.parana_distribution_tax (shop_id,we_shop_id,tax_rate,status)values(-1,null,0,1);