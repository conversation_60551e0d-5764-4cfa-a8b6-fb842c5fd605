# Dump of table parana_user_wx
# ------------------------------------------

DROP TABLE IF EXISTS `parana_user_wx`;

CREATE TABLE `parana_user_wx` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `app_id` varchar(255) DEFAULT NULL,
  `open_id` varchar(60) DEFAULT NULL,
  `access_token` varchar(255) DEFAULT NULL,
  `refresh_token` varchar(255) DEFAULT NULL,
  `access_time` bigint(20) DEFAULT NULL,
  `refresh_time` bigint(20) DEFAULT NULL,
  `scope` varchar(32) DEFAULT NULL,
	`nick_name` varchar(32) DEFAULT NULL COMMENT '用户昵称',
	`avatar_url` varchar(255) DEFAULT NULL COMMENT '用户头像url',
  `union_id` varchar(32) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL COMMENT '对应用户id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `open_id` (`open_id`,`app_id`) USING BTREE,
  KEY `index_user_id` (`user_id`),
  KEY `index_union_id` (`union_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT '用户微信关联表';

# Dump of table parana_shop_wxa
# ------------------------------------------

DROP TABLE IF EXISTS `parana_shop_wxa`;

CREATE TABLE `parana_shop_wxa` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺id',
  `name` varchar(32) DEFAULT NULL COMMENT '小程序名称',
  `app_id` varchar(255) DEFAULT NULL COMMENT '小程序appId',
  `app_secret` varchar(255) DEFAULT NULL COMMENT '小程序秘钥',
  `authorizer_appid` varchar(255) DEFAULT NULL COMMENT '授权的小程序appId',
  `authorization_code` varchar(255) DEFAULT NULL COMMENT '授权码，用于换取公众号的接口调用凭据',
  `authorization_code_expired_time` bigint(20) DEFAULT NULL COMMENT '授权码过期时间',
  `authorizer_access_token` varchar(255) DEFAULT NULL COMMENT '授权方令牌',
  `access_token_expires_in` bigint(20) DEFAULT NULL COMMENT '有效期，为2小时',
  `authorizer_refresh_token` varchar(255) DEFAULT NULL COMMENT '刷新令牌',
  `status` tinyint(3) DEFAULT '1' COMMENT '状态,-1删除,1正常,2发布',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT '店铺小程序信息表';

insert into parana_shop_wxa(`shop_id`,`name`,`app_id`,`app_secret`,`status`,`created_at`,`updated_at`) value(0,'但丁商城','wx90e48a315747ccb9','ebb83ced7885897e4fd5ecfc403ed92d',2,now(),now());