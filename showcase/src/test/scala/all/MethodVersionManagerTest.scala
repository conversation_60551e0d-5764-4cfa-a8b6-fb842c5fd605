package all

import moonstone.showcase.{ShowcaseApplication, ShowcaseConfiguration}
import moonstone.thirdParty.impl.service.ThirdPartyJobServiceImpl
import moonstone.web.core.component.{CustomEventProducers, MethodVersionManager}
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.ImportAutoConfiguration

@ActiveProfiles(Array("dev"))
@SpringBootTest(classes = Array(classOf[ShowcaseApplication]))
@RunWith(classOf[SpringRunner])
@ImportAutoConfiguration(Array(classOf[ShowcaseConfiguration]))
case class MethodVersionManagerTest() {
  @Autowired
  var customEventProducers: CustomEventProducers = _
  @Autowired
  var methodVersionManager: MethodVersionManager = _

  @Test
  def invoke() {
    customEventProducers.switchMethodVersion(classOf[ThirdPartyJobServiceImpl].getSimpleName, "synchronize", "old")
    Assert.assertEquals(true, methodVersionManager.readMethodVersionInfo.filter(_.className.equals(classOf[ThirdPartyJobServiceImpl].getSimpleName))
      .exists(_.methodVersion.get("synchronize").exists(_ == "old")))

    customEventProducers.switchMethodVersion(classOf[ThirdPartyJobServiceImpl].getSimpleName, "synchronize", "new")
    Assert.assertEquals(true, methodVersionManager.readMethodVersionInfo.filter(_.className.equals(classOf[ThirdPartyJobServiceImpl].getSimpleName))
      .exists(_.methodVersion.get("synchronize").exists(_ == "new")))

    customEventProducers.switchMethodVersion(classOf[ThirdPartyJobServiceImpl].getSimpleName, "synchronize", "new")
    Assert.assertEquals(true, methodVersionManager.readMethodVersionInfo.filter(_.className.equals(classOf[ThirdPartyJobServiceImpl].getSimpleName))
      .exists(_.methodVersion.get("synchronize").exists(_ == "new")))
    customEventProducers.switchMethodVersion(classOf[ThirdPartyJobServiceImpl].getSimpleName, "synchronize", "old")
    Assert.assertEquals(true, methodVersionManager.readMethodVersionInfo.filter(_.className.equals(classOf[ThirdPartyJobServiceImpl].getSimpleName))
      .exists(_.methodVersion.get("synchronize").exists(_ == "old")))
  }

  @Test
  def after(): Unit = {
    Assert.assertEquals(true, methodVersionManager.readMethodVersionInfo.filter(_.className.equals(classOf[ThirdPartyJobServiceImpl].getSimpleName))
      .exists(_.methodVersion.get("synchronize").exists(_ == "old")))
  }
}
