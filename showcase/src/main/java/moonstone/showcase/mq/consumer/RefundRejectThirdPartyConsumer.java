package moonstone.showcase.mq.consumer;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.model.Refund;
import moonstone.order.model.ShopOrder;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.dto.RefundThirdPartyDto;
import moonstone.web.core.fileNew.logic.RefundLogic;
import moonstone.web.core.fileNew.logic.ShopOrderLogic;
import moonstone.web.core.fileNew.strategy.api.RefundStrategy;
import moonstone.web.core.fileNew.strategy.factory.RefundStrategyFactory;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@RocketMQMessageListener(
		topic = RocketMQConstant.APP_MERCHANT_TOPIC,
		consumerGroup = RocketMQConstant.REFUND_REJECT_THIRD_PARTY_CONSUMER_GROUP,
		selectorExpression = RocketMQConstant.REFUND_REJECT_THIRD_PARTY_TAG
)
@Slf4j
public class RefundRejectThirdPartyConsumer implements RocketMQListener<String> {

	@Resource
	private RefundStrategyFactory refundStrategyFactory;

	@Resource
	private RefundLogic refundLogic;

	@Resource
	private ShopOrderLogic shopOrderLogic;


	@Override
	public void onMessage(String msg) {
		log.info("拒绝退款申请与三方联动消息 {}", msg);
		JSONObject msgJson = JSONUtil.parseObj(msg);
		Long refundId = msgJson.get("refundId", Long.class);
		Long orderId = msgJson.get("orderId", Long.class);
		Refund refund = refundLogic.getById(refundId);
		Map<String, Object> query = new HashMap<>();
		query.put("id", orderId);
		ShopOrder shopOrder = shopOrderLogic.getOne(query);
		RefundStrategy refundStrategy = refundStrategyFactory.getRefundStrategy(refund.getSourceType());
		RefundThirdPartyDto refundThirdPartyDto = new RefundThirdPartyDto();
		refundThirdPartyDto.setShopOrder(shopOrder);
		refundThirdPartyDto.setRefund(refund);
		refundStrategy.refundRejectThirdPartyInteraction(refundThirdPartyDto);
	}
}
