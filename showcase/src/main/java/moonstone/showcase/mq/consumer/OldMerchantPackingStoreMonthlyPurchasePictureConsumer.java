package moonstone.showcase.mq.consumer;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.logic.StoreMonthlyPunchLogic;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * author：书生
 */
@Component
@RocketMQMessageListener(
        topic = RocketMQConstant.APP_MERCHANT_TOPIC,
        consumerGroup = "merchant_packing_store_monthly_purchase_picture_consumer_group",
        selectorExpression = "merchant_packing_store_monthly_purchase_picture_tag"
)
@Slf4j
public class OldMerchantPackingStoreMonthlyPurchasePictureConsumer implements RocketMQListener<String> {


    @Resource
    private StoreMonthlyPunchLogic storeMonthlyPunchLogic;


    @Override
    public void onMessage(String msg) {
        log.info("old 收到打包门店月度照片的消息 传入参数 {}",msg);
        storeMonthlyPunchLogic.packingPicture(msg);
    }
}
