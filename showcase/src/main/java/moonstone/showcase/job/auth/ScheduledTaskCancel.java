package moonstone.showcase.job.auth;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor;
import org.springframework.scheduling.config.ScheduledTask;
import org.springframework.scheduling.config.ScheduledTaskHolder;

import java.util.*;

@Slf4j
@Configuration
public class ScheduledTaskCancel {

    @Autowired
    private ScheduledAnnotationBeanPostProcessor postProcessor;

    @Bean
    public CommandLineRunner logScheduledTasks(ScheduledTaskHolder scheduledTaskHolder) {
        return args -> {
            Set<ScheduledTask> scheduledTasks = scheduledTaskHolder.getScheduledTasks();

            System.out.printf("共发现 %d 个定时任务:\n", scheduledTasks.size());

            List<String> cancelTaskList = new ArrayList<>();
            List<String> taskList = new ArrayList<>();

            scheduledTasks.forEach(task -> {
                String taskStr = task.getTask().toString();
                String classMethod = extractClassMethodHashFormat(taskStr);

                if(XxlJobWhitelist.isAllowed(classMethod)){
                    task.cancel();
                    cancelTaskList.add(taskStr);
                }else {
                    taskList.add(taskStr);
                }
            });
            log.info("取消的定时任务 {} {}", cancelTaskList.size(), JSONObject.toJSONString(cancelTaskList));
            log.info("正常的定时任务 {} {}", taskList.size(), JSONObject.toJSONString(taskList));
        };
    }

    /**
     * 从全路径（全限定类名.方法名）中提取类名和方法名，并返回 "类名#方法名" 格式
     *
     * @param fullMethodPath 格式如 "com.example.MyClass.myMethod"
     * @return "类名#方法名"（如 "MyClass#myMethod"），如果输入不合法则返回 null
     */
    public static String extractClassMethodHashFormat(String fullMethodPath) {
        if (Objects.isNull(fullMethodPath) || fullMethodPath.isEmpty()) {
            return null;
        }

        int lastDotIndex = fullMethodPath.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == 0 || lastDotIndex == fullMethodPath.length() - 1) {
            return null; // 不合法格式，如 "method" 或 "com." 或 ".method"
        }

        String classPath = fullMethodPath.substring(0, lastDotIndex);
        String methodName = fullMethodPath.substring(lastDotIndex + 1);

        int lastClassDotIndex = classPath.lastIndexOf('.');
        String className = (lastClassDotIndex == -1) ? classPath : classPath.substring(lastClassDotIndex + 1);

        return className + "#" + methodName;
    }

    /**
     * 测试示例
     */
    public static void main(String[] args) {
        String fullPath = "moonstone.web.front.order.RefundNotifies.scanRefund";
        String result = extractClassMethodHashFormat(fullPath);

        if (result != null) {
            System.out.println(result); // 输出: RefundNotifies#scanRefund
        } else {
            System.out.println("输入格式不合法！");
        }
    }
}