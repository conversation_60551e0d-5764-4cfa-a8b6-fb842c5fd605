package moonstone.showcase.aop;

import moonstone.common.api.Result;
import moonstone.common.utils.EventSender;
import moonstone.user.dto.UserUpdateEvent;
import moonstone.web.core.events.item.ItemDeletedEvent;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestController {

    @GetMapping("api/scan/eventTest")
    public Result test(){
        EventSender.publish(new UserUpdateEvent(1L));
        return Result.success("ok");
    }
}
