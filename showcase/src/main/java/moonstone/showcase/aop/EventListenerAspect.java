//package moonstone.showcase.aop;
//
//import com.alibaba.fastjson.JSON;
//import lombok.extern.slf4j.Slf4j;
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.annotation.Before;
//import org.aspectj.lang.annotation.Pointcut;
//import org.aspectj.lang.reflect.MethodSignature;
//import org.springframework.context.event.EventListener;
//import org.springframework.stereotype.Component;
//
//import java.lang.reflect.Method;
//
///**
// * <AUTHOR>
// * @since 2022-05-09 15:31
// * 日志切面
// */
//@Aspect
//@Slf4j
//@Component
//public class EventListenerAspect {
//
////    @Pointcut("@annotation(org.springframework.context.event.EventListener)")
//    @Pointcut("execution(public * *(..)) && @annotation(org.springframework.context.event.EventListener)")
//    public void logAspect() {
//    }
//
//
//    @Before(value = "logAspect()")
//    public void logAround(JoinPoint joinPoint) {
//        try {
//            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
//            Method method = signature.getMethod();
//            String className = joinPoint.getTarget().getClass().getName();
//            String methodName = joinPoint.getSignature().getName();
//
//            EventListener annotation = method.getAnnotation(EventListener.class);
//            Object[] args = joinPoint.getArgs();
//            if(annotation == null){
//                log.info("{} EventListenerAspect 切点 {}.{} 入参 {}", Thread.currentThread().getId(), className, methodName, JSON.toJSONString(args));
//            }else{
//                Class<?>[] eventClass = annotation.value();
//                try {
//                    if(eventClass != null && eventClass.length > 0){
//                        log.info("{} EventListenerAspect 切点 {}.{} event {} 入参 {}", Thread.currentThread().getId(), className, methodName, eventClass[0].getSimpleName(), JSON.toJSONString(args));
//                    }else{
//                        log.info("{} EventListenerAspect 切点 {}.{} event is null 入参 {}", Thread.currentThread().getId(), className, methodName, JSON.toJSONString(args));
//                    }
//                }catch (Exception e){}
//            }
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//
//    }
//
//
//}
