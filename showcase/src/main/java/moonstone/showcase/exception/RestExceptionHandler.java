package moonstone.showcase.exception;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.exception.ApiException;
import moonstone.common.exception.WxRuntimeException;
import moonstone.web.core.advices.CustomExceptionHandler;
import moonstone.web.core.advices.ExceptionRecordResolver;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

/**
 * 已有的异常处理逻辑在
 * {@link ExceptionRecordResolver}中，且只处理{@link CustomExceptionHandler}实现类指定的泛型类型
 * java.lang.Exception
 * java.lang.NullPointerException（NPEResolver）
 * moonstone.common.exception.InvalidException（InvalidExceptionResolver）
 * io.terminus,common.exceptionJonResponseException（JsonExceptionResolver）
 * io.terminus.common.exception.ServiceException（ServiceExceptionResolver）
 * io.vertx.core.impl.NoStackTraceThrowable（VertxExceptionResolver）
 *
 * 其他的异常返回格式为
 * {
 * "timestamp": 1700105922570,
 * "status": 500,
 * "error": "Internal Server Error",
 * "message": "adc",
 * "path": "/allinPay/sendPhoneCode"
 * }
 *
 * 新增 {@link WxRuntimeException}异常处理 使用{@link Result} 结构
 * {
 * "code": -1,
 * "success": false,
 * "errorMsg": "user.not.login"
 * }
 * 注：@ExceptionHandler方式会比【ExceptionRecordResolver implements
 * HandlerExceptionResolver】优先级更高
 * 注：前端已经存在了多种响应结构，持续着具体接口具体处理的方式。
 */
@Slf4j
@RestControllerAdvice
public class RestExceptionHandler {

    private static final List<String> commonShopUrl = new ArrayList<>();

    static {
        commonShopUrl.add("/api/news/search-in-shop");
        commonShopUrl.add("/api/news/search-in-shop/category");
        commonShopUrl.add("/api/new/carts");
        commonShopUrl.add("/api/new/order/preview");
        commonShopUrl.add("/api/new/order");
        commonShopUrl.add("/api/new/order/pay");
        commonShopUrl.add("/api/membershipByPid/order/preview");
        commonShopUrl.add("/api/new/userCertification/get");
        commonShopUrl.add("/api/news/item/{itemId}/for-view"); // 动态itemId，实际项目路径
        commonShopUrl.add("/api/news/item/share");
        commonShopUrl.add("/api/new/order/{id}/detail"); // 动态id，实际项目路径
        commonShopUrl.add("/api/new/order/delete/{orderId}"); // 动态orderId，实际项目路径
        commonShopUrl.add("/api/new/buyer/order/refund");
        commonShopUrl.add("/api/new/buyer/refund/{orderId}/cancel"); // 动态orderId，实际项目路径
        commonShopUrl.add("/api/new/buyer/order/cancel/{orderId}"); // 动态orderId，实际项目路径
        commonShopUrl.add("/api/new/buyer/confirm/{id}"); // 动态id，后续特殊处理
        commonShopUrl.add("/api/user/change-mobile");
    }

    @ExceptionHandler(NullPointerException.class)
    public Result<String> handleNullPointerException(NullPointerException e) {
        log.error("【空指针异常】 {}", e.getMessage(), e);
        return Result.fail(e.getMessage());
    }

    /**
     * 校验错误拦截处理
     *
     * @param e 错误信息集合
     * @return 错误信息
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<String> validException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        String errMsg = bindingResult.getFieldErrors().get(0).getDefaultMessage();
        return Result.fail(errMsg);
    }

    /**
     * 参数类型转换错误
     *
     * @param exception 错误
     * @return 错误信息
     */
    @ExceptionHandler(HttpMessageConversionException.class)
    public Result parameterTypeException(HttpMessageConversionException exception) {
        log.error("参数类型转换错误 {}", exception.getMessage(), exception);
        return Result.data(Collections.emptyList());
    }

    @ExceptionHandler(WxRuntimeException.class)
    public Result tmsServiceException(WxRuntimeException exception) {
        return Result.fail(exception.getMessage());
    }

    @ExceptionHandler(ApiException.class)
    public Result<Object> customException(ApiException e) {
        log.error("【系统错误】 {}", e.getMsg(), e);
        return Result.fail(e.getCode(), e.getMsg());
    }

    /**
     * 智能匹配请求路径，支持{id}动态参数
     */
    private boolean isShopUrlMatched(String requestURI) {
        for (String urlPattern : commonShopUrl) {
            if (urlPattern.contains("{")) {
                // 支持多个动态参数的智能正则匹配
                String regex = urlPattern.replaceAll("\\{[^/]+\\}", "[^/]+");
                if (requestURI.matches(regex)) {
                    return true;
                }
            } else {
                if (urlPattern.equals(requestURI)) {
                    return true;
                }
            }
        }
        return false;
    }

    @ExceptionHandler(Exception.class)
    public Result<String> exception(Exception e) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .getRequest();
        String requestURI = request.getRequestURI();
        log.error("【系统异常】请求路径: {}, 异常信息: {}", requestURI, e.getMessage(), e);
        if (isShopUrlMatched(requestURI)) {
            return Result.commonFail(e.getMessage());
        }
        return Result.fail(e.getMessage());
    }

}
