import moonstone.common.utils.ImageResizeHelper;
import moonstone.web.core.util.FontUtil;
import moonstone.web.core.util.Png2PdfUtil;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.ArrayList;
import java.util.List;

public interface SignTest {
    class Limit {
        static int MAX_SIZE = 300 * 1024;
    }

    static void main(String[] args) throws Exception {
        ByteArrayOutputStream buff = new ByteArrayOutputStream();
        BufferedImage origin = ImageIO.read(new FileInputStream("/home/<USER>/page_2.png"));
        final double scale = 1.45;
        ImageIO.write(origin, "png", buff);
        if (buff.size() < Limit.MAX_SIZE) {
            return;
        }
        final double requireScale = buff.size() * 1.0 / Limit.MAX_SIZE / scale;
        buff.reset();
        BufferedImage draw = ImageResizeHelper.resize(origin, (int) (origin.getWidth() / requireScale), (int) (origin.getHeight() / requireScale));
        ImageIO.write(draw, "png", buff);
        buff.writeTo(new FileOutputStream("/home/<USER>/out.png"));
    }

    static void main2(String[] args) throws Exception {
        BufferedImage img = ImageIO.read(new FileInputStream("/home/<USER>/page_1.png"));
        String address = "测试地址";
        String identityCode = "3123123123213213123";
        String mobile = "1231231231233";
        BufferedImage sign = ImageIO.read(new FileInputStream("/home/<USER>/sign.png"));
        Graphics2D graphics2D = img.createGraphics();
        int x = 320 * 2, y = 2 * 300, w = 2 * 180, h = 2 * 180;
        int textX = 2 * 160, textY = 2 * 385;
        graphics2D.setFont(FontUtil.getSiYuanFont().deriveFont(Font.PLAIN, 20));
        graphics2D.setColor(Color.BLACK);
        graphics2D.drawString(address, textX, textY);
        graphics2D.drawString(identityCode, textX, textY + 34);
        graphics2D.drawString(mobile, textX, textY + 34 * 2);
        graphics2D.drawImage(sign, x, y, w, h, null, null);
        graphics2D.dispose();
        List<BufferedImage> imageList = new ArrayList<>();
        imageList.add(img);
        for (int i = 2; i < 6; i++) {
            imageList.add(ImageIO.read(new FileInputStream("/home/<USER>/page_" + i + ".png")));
        }
        Png2PdfUtil.png2pdf(imageList)
                .writeTo(new FileOutputStream("/home/<USER>/out.pdf"));
    }


    static void t(String[] args) throws Exception {
        OutputStream outputStream = new FileOutputStream(System.getenv("HOME") + "/test.pdf");
        List<BufferedImage> images = new ArrayList<>();
        for (int i = 1; i < 3; i++) {
            images.add(ImageIO.read(new File(System.getenv("HOME") + "/Desktop/Page_" + i + ".png")));
        }
        Png2PdfUtil.png2pdf(images).writeTo(outputStream);
        outputStream.flush();
        outputStream.close();
    }
}
