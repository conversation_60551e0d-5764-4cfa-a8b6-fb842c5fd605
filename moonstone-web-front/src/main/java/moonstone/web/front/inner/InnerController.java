package moonstone.web.front.inner;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.utils.EncryptHelper;
import moonstone.common.utils.EventSender;
import moonstone.event.OrderRefundEvent;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.model.Payment;
import moonstone.order.model.Refund;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.PayerInfoService;
import moonstone.order.service.RefundReturnDetailService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.user.model.PayerInfo;
import moonstone.web.core.component.order.PaymentLogic;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.logic.RefundLogic;
import moonstone.web.core.fileNew.logic.SubStoreLogic;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.core.refund.service.RefundCallBackService;
import moonstone.web.front.order.service.impl.ShipmentConfirmService;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 内部接口
 * author：书生
 */
@RestController
@Slf4j
@RequestMapping("api/inner")
public class InnerController {

    @Resource
    private SubStoreLogic subStoreLogic;

    @Resource
    private ShipmentConfirmService shipmentConfirmService;

    @Resource
    private PaymentLogic paymentLogic;

    @Resource
    private PayerInfoService payerInfoService;

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private RocketMQProducer rocketMQProducer;

    @Resource
    private RefundReturnDetailService refundReturnDetailService;

    @Resource
    private RefundLogic refundLogic;
    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Resource
    private RefundCallBackService refundCallBackService;

    /**
     * 手动触发退款回调
     * @param outId 对面的id
     * @param refundAt 退款时间
     * @return 是否成功
     */
    @GetMapping("/trigger/refund/callback")
    public Result<Boolean> triggerRefundCallback(@RequestParam("outId") String outId,
                                                 @RequestParam("refundAt") Long refundAt) {
        log.info("手动触发退款回调 outId {} refundAt {}",outId, DateUtil.formatDateTime(new Date(refundAt)));
        refundCallBackService.refundCallBack(outId, new Date(refundAt));
        return Result.data(true);
    }

    /**
     * 发送退款请求
     * @param refundId 退款id
     * @return 是否成功
     */
    @GetMapping("send/refund/msg")
    public Result<String> sendRefund(@RequestParam("refundId") Long refundId) {
        EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, OrderEvent.REFUND.toOrderOperation()));
        return Result.data("发送成功");
    }

    /**
     * 发送退款成功请求
     * @param refundId 退款id
     * @return 是否成功
     */
    @GetMapping("send/refund/success/msg")
    public Result<String> sendRefundSuccess(@RequestParam("refundId") Long refundId) {
        EventSender.sendApplicationEvent(new OrderRefundEvent(refundId, OrderEvent.REFUND_SUCCESS.toOrderOperation()));
        return Result.data("发送成功");
    }

    /**
     * 发送商家端MQ
     * @param tag 标签
     * @param msg 消息
     * @return 返回值
     */
    @GetMapping("send/mq")
    public Result<Boolean> sendMQ(@RequestParam("tag") String tag,
                                  @RequestParam("msg") String msg) {
        rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, tag, msg);
        return Result.data(true);
    }

    /**
     * 测试
     */
    @GetMapping("/get/id")
    public Result<Boolean> getById( ) {
        Long refundId = 27187L;
        Refund refund = refundLogic.getById(refundId);
        System.out.println(JSONUtil.toJsonStr(refund));
        Map<String, Object> query = new HashMap<>();
        query.put("orderId", 1358678L);
        SkuOrder skuOrder = skuOrderReadService.getOne(query);
        System.out.println(JSONUtil.toJsonStr(skuOrder));
        return Result.data(true);
    }

    /**
     * 插入数据
     */
    @GetMapping("insert")
    public Result<Boolean> insert(@RequestParam("sql") String sql) {
        log.info("传入的sql {}",sql);
        refundReturnDetailService.execute(sql);
        return Result.data(true);
    }


    /**
     * 发送生成订单真实利润消息
     *
     * @param orderId 订单id
     * @return 是否发送成功
     */
    @GetMapping("send/generate/order/foresee/profit/message")
    public Result<Boolean> sendGenerateOrderForeseeProfitMessage(@RequestParam Long orderId) {
        log.info("发送生成订单真实利润消息 订单id {}", orderId);
        rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.GENERATE_ORDER_FORESEE_PROFIT_TAG, String.valueOf(orderId));
        log.info("发送完成生成订单真实利润消息 订单id {}", orderId);
        return Result.data(true);
    }

    /**
     * 手动确认收货
     */
    @GetMapping("/confirm/order")
    public Result<Boolean> confirmOrder(@RequestParam("orderId") Long orderId) {
        log.info("手动确认收货 订单id {}", orderId);
        shipmentConfirmService.confirmOrder(orderId);
        return Result.data(true);
    }

    /**
     * 删除 redisKey
     */
    @GetMapping("/remove/redis/key")
    public Result<Boolean> removeKey(@RequestParam("key") String key) {
        return Result.data(true);
    }

    /**
     * 修改mongo数据
     */
    @GetMapping("update/mongo/db")
    public Result<String> updateMongo(@RequestParam("flag") Boolean flag) {
        return Result.data("修改完成");
    }

    /**
     * 修改mysql数据
     */
    @GetMapping("update/mysql/db")
    public Result<String> updateMysql(@RequestParam("flag") Boolean flag) {
        log.info("修改mysql数据");
        ThreadUtil.safeSleep(10000);
        return Result.data("修改完成");
    }

    /**
     * 手动触发 重置门店打卡标记
     */
    @GetMapping("reset/subStore/punchMark")
    public Result<String> resetSubStorePunchMark() {
        log.info("手动触发 重置门店打卡标记");
        subStoreLogic.resetPunchMark();
        return Result.data("success");
    }


    /**
     * 手动触发支付回调
     * @param payment 支付信息
     * @return 回调结果
     */
    @PostMapping("manual/pay/notify")
    public Result<Boolean> payNotify(@RequestBody Payment payment) {
        log.info("手动触发 支付回调 请求参数 {}", JSONUtil.toJsonStr(payment));
        return Result.data(paymentLogic.postPay(payment));
    }

    /**
     * 修改支付人实名信息
     * @return 是否成功
     */
    @GetMapping("update/payerInfo")
    public Result<Boolean> updatePayerInfo(){
        String identityName = "刘宇坤";
        String identityCode = "610402200510207018";
        Long shopId = 274L;
        Long shopOrderId = 1360629L;
        PayerInfo payerInfo = PayerInfo.Helper.create(EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey),
                identityName, identityCode, shopOrderId, shopId);
//        payerInfoService.save(payerInfo);

        mongoTemplate.upsert(Query.query(Criteria.where("orderId").is(shopOrderId)),
                Update.update("password", payerInfo.getPassword())
                        .set("payerName", payerInfo.getPayerName())
                        .set("payerNo", payerInfo.getPayerNo())
                        .set("hash", payerInfo.getHash())
                        .set("shopId", payerInfo.getShopId())
                , PayerInfo.class);
        return Result.data(true);
    }

}