/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.front.promotions.dto;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-06-16
 */
@Data
public class ShopPromotionRequest implements Serializable {

    private static final long serialVersionUID = 440450510475880077L;

    private Long shopId;

    private Integer channel;

    private Long receiverInfoId;

    private Long promotionId;

    private List<SkuOrderInfo> skuOrderInfos;
}
