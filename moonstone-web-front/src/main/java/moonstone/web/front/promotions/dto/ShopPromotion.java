package moonstone.web.front.promotions.dto;

import lombok.Data;
import moonstone.promotion.model.Promotion;

import java.io.Serializable;
import java.util.Map;

/**
 * Author:cp
 * Created on 03/11/2016.
 */
@Data
public class ShopPromotion implements Serializable {

    private static final long serialVersionUID = -6280900892971007663L;

    /**
     * 营销活动信息
     */
    private Promotion promotion;

    /**
     * 营销信息
     */
    private Map<String, Object> promotionInfo;

}
