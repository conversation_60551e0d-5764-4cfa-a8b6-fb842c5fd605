package moonstone.web.front.promotions;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.service.ShopOrderReadService;
import moonstone.promotion.component.SubStoreOnlyManager;
import moonstone.promotion.dto.UserPromotionDetail;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.view.PromotionView;
import moonstone.promotion.service.UserPromotionReadService;
import moonstone.promotion.service.UserPromotionWriteService;
import moonstone.web.front.promotions.convert.PromotionDecorator;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Author:cp
 * Created on 7/7/16.
 */
@RestController
@Slf4j
public record UserPromotions(UserPromotionReadService userPromotionReadService
        , UserPromotionWriteService userPromotionWriteService
        , ShopOrderReadService shopOrderReadService
        , SubStoreOnlyManager subStoreOnlyManager
        , PromotionDecorator promotionDecorator) {

    @RequestMapping(value = "/api/buyer/promotion/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    Paging<UserPromotionDetail> findUserPromotions(@RequestParam(value = "type", required = false) Integer type,
                                                   @RequestParam(value = "status", required = false) Integer status,
                                                   @RequestParam(value = "usedState", required = false) Integer usedState,
                                                   @RequestParam(value = "usedStatus", required = false) Integer usedStatus,
                                                   @RequestParam(value = "shopId", required = false) Long shopId,
                                                   @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                   @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        if (usedStatus != null) usedState = usedStatus;
        CommonUser buyer = UserUtil.getCurrentUser();
        if (buyer == null) {
            throw new RuntimeException("用户未登录");
        }
        Response<Paging<UserPromotionDetail>> findResp = userPromotionReadService.findUserPromotions(buyer.getId(), type, status, usedState, shopId, pageNo, pageSize);
        if (!findResp.isSuccess()) {
            log.error("buyer(id={}) fail to find user promotions with params:type={},status={},usedState={},pageNo={},pageSize={},cause:{}",
                    buyer.getId(), type, status, usedState, pageNo, pageSize, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        if (removePromotionThatRequireFirstBought(findResp.getResult().getData()))
            findResp = userPromotionReadService.findUserPromotions(buyer.getId(), type, status, usedState, shopId, pageNo, pageSize);
        if (!findResp.isSuccess()) {
            log.error("buyer(id={}) fail to find user promotions with params:type={},status={},usedState={},pageNo={},pageSize={},cause:{}",
                    buyer.getId(), type, status, usedState, pageNo, pageSize, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }

        if (findResp.getResult() != null && !findResp.getResult().isEmpty()) {
            decorateSubStoreOnly(findResp.getResult().getData());
        }

        return findResp.getResult();
    }

    /**
     * 移除已使用卷
     *
     * @param promotionDetailList 卷列表
     */
    private boolean removePromotionThatRequireFirstBought(List<UserPromotionDetail> promotionDetailList) {
        OrderCriteria orderCriteria = new OrderCriteria();
        orderCriteria.setStatus(OrderStatus.getValidStatusForFirstBuyPromotion().stream().map(OrderStatus::getValue).collect(Collectors.toList()));
        List<UserPromotionDetail> removed = new ArrayList<>();
        for (UserPromotionDetail userPromotionDetail : promotionDetailList) {
            if (userPromotionDetail.promotion() == null) {
                continue;
            }
            var condition = Optional.ofNullable(userPromotionDetail.promotion().getConditionParams()).orElse(new HashMap<>());
            if (!condition.getOrDefault("firstBought", "false").equals("true"))
                continue;
            orderCriteria.setBuyerId(userPromotionDetail.userPromotion().getUserId());
            orderCriteria.setShopId(userPromotionDetail.promotion().getShopId());
            if (shopOrderReadService.findBy(1, 0, orderCriteria).getResult().getTotal() == 0)
                continue;
            removed.add(userPromotionDetail);
        }
        promotionDetailList.removeAll(removed);
        for (UserPromotionDetail detail : removed) {
            userPromotionWriteService.updateStatus(detail.userPromotion().getId(), -2);
        }
        return !removed.isEmpty();
    }

    private void decorateSubStoreOnly(List<UserPromotionDetail> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        var promotions = list.stream()
                .map(UserPromotionDetail::promotion)
                .filter(promotion -> PromotionType.SUB_STORE_ONLY.getValue() == promotion.getType())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(promotions)) {
            return;
        }

        appendLimitedItemName(promotions);
    }

    private void appendLimitedItemName(List<PromotionView> sourceList) {
        var itemIds = sourceList.stream()
                .filter(Objects::nonNull)
                .map(subStoreOnlyManager::getLimitedItemId)
                .flatMap(Collection::stream)
                .toList();

        var itemMap = promotionDecorator.findItemMap(itemIds);
        if (CollectionUtils.isEmpty(itemMap)) {
            return;
        }

        sourceList.forEach(source -> promotionDecorator.appendLimitedItemName(source, o -> o, itemMap));
    }
}
