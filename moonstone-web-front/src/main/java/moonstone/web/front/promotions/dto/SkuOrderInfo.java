/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.front.promotions.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-06-16
 */
@Data
public class SkuOrderInfo implements Serializable{
    private static final long serialVersionUID = -7154781889109728951L;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * 对应sku的数量
     */
    private Integer quantity;

    /**
     * 对应skuOrder优惠后的费用
     */
    private Long fee;
}
