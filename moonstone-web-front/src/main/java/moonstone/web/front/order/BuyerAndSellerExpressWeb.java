package moonstone.web.front.order;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.base.Objects;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.EmptyUtils;
import moonstone.common.utils.R;
import moonstone.common.utils.UserUtil;
import moonstone.express.model.ExpressCompany;
import moonstone.express.service.ExpressCompanyReadService;
import moonstone.order.api.ExpressTrackInfo;
import moonstone.order.model.SkuOrder;
import moonstone.web.core.express.dto.ExpressTrack;
import moonstone.web.core.express.dto.OrderExpressTrack;
import moonstone.web.core.express.service.OrderExpressDetailCenter;
import moonstone.web.core.express.service.impl.OrderExpress;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Mail: <EMAIL>
 * Data: 16/6/28
 * Author: yangzefeng
 */
@RestController
@Slf4j
public class BuyerAndSellerExpressWeb {

    @Resource
    ExpressCompanyReadService expressCompanyReadService;
    @Autowired
    OrderExpress orderExpress;
    @Autowired
    OrderExpressDetailCenter orderExpressDetailCenter;


    @RequestMapping(value = "/api/express/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Paging<ExpressCompany>> findBy(@RequestParam(value = "pageNo", required = false) Integer pageNo,
                                                   @RequestParam(value = "size", required = false) Integer size,
                                                   @RequestParam(value = "name", required = false) String name,
                                                   @RequestParam(value = "code", required = false) String code,
                                                   @RequestParam(value = "status", required = false) Integer status) {
        return expressCompanyReadService.paging(pageNo, size, name, code, status);
    }

    @RequestMapping(value = "/api/order/express", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<? extends ExpressTrackInfo> orderExpress(@RequestParam("orderId") Long orderId,
                                                         @RequestParam("orderType") Integer orderType) {
        return orderExpressDetailCenter.queryOrderExpressDetail(orderId, orderType);
    }

    @RequestMapping(value = "/api/new/order/express", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public R orderExpressNew(@RequestParam("orderId") Long orderId,
                             @RequestParam(value = "orderType", defaultValue = "1") Integer orderType) {
        List<OrderExpressTrack> orderExpressTracks = orderExpress.frontFindExpressTrack(UserUtil.getCurrentUser(), orderId, orderType);
        if (EmptyUtils.isEmpty(orderExpressTracks)) {
            return R.error(-1, "未查询到物流信息");
        }
        List<ExpressVO> list = new ArrayList<>();
        for (OrderExpressTrack entity : orderExpressTracks) {
            ExpressVO expressVO = new ExpressVO();
            BeanUtils.copyProperties(entity, expressVO);
            expressVO.setSteps(copySteps(entity.getSteps()));
            list.add(expressVO);
        }
        return R.ok().add("data", list);
    }

    private List<Step> copySteps(List<ExpressTrack.ExpressStep> step) {
        if (EmptyUtils.isNotEmpty(step)) {
            List<Step> list = new ArrayList<>();
            for (ExpressTrack.ExpressStep entity : step) {
                Step steps = new Step();
                BeanUtils.copyProperties(entity, steps);
                list.add(steps);
            }
            return list;
        }
        return null;
    }

    @Data
    class ExpressVO {
        String state;
        Long orderId;
        String shipmentCorpName;
        String shipmentSerialNo;
        Long shipmentId;
        List<Step> steps;
    }

    @Data
    class Step {
        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "CTT")
        Date time;
        String desc;
    }


    /**
     * 物流信息查询
     *
     * @param orderId
     * @return
     */
    @RequestMapping(value = "/api/order/express/findWmsId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<JSONObject> orderFindExpress(@RequestParam("orderId") Long orderId, @RequestParam("orderType") Integer orderType) {
        var jsonObject = new JSONObject();
        jsonObject.put("code", "success");
        jsonObject.put("data", orderExpressDetailCenter.queryOrderExpressDetailV2Compatible(orderId, orderType));
        jsonObject.put("actionCode", "success");
        jsonObject.put("errorMsg", "请求成功");

        return Response.ok(jsonObject);
    }

    @RequestMapping(value = "/api/new/order/express/findWmsId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public R orderFindExpressNew(@RequestParam("orderId") Long orderId
            , @RequestParam("orderType") Integer orderType) {
        Response<JSONObject> jsonObjectResponse = orderExpress.frontFindExpressFindWmsId(orderId, orderType);
        if (jsonObjectResponse.isSuccess() && !ObjectUtils.isEmpty(jsonObjectResponse.getResult())) {
            return R.ok().add("data", jsonObjectResponse.getResult().get("data"));
        }
        return R.error(-1, "未查询到物流信息");
    }


    @RequestMapping(value = "/api/buyer/shipment/{id}/sku-orders", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SkuOrder> findSkuOrdersForBuyer(@PathVariable("id") Long shipmentId) {
        Long buyerId = UserUtil.getUserId();
        List<SkuOrder> skuOrders = orderExpress.findSkuOrders(shipmentId);
        for (SkuOrder skuOrder : skuOrders) {
            if (!Objects.equal(skuOrder.getBuyerId(), buyerId)) {
                log.error("the skuOrder(id={}) not belong to buyer(id={})",
                        skuOrder.getId(), buyerId);
                throw new JsonResponseException("sku.order.not.belong.to.buyer");
            }
        }
        return skuOrders;
    }

    @RequestMapping(value = "/api/seller/shipment/{id}/sku-orders", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SkuOrder> findSkuOrdersForSeller(@PathVariable("id") Long shipmentId) {
        Long shopId = UserUtil.<CommonUser>getCurrentUser().getShopId();
        List<SkuOrder> skuOrders = orderExpress.findSkuOrders(shipmentId);
        for (SkuOrder skuOrder : skuOrders) {
            if (!Objects.equal(skuOrder.getShopId(), shopId)) {
                log.error("the skuOrder(id={}) not belong to shop(id={})",
                        skuOrder.getId(), shopId);
                throw new JsonResponseException("sku.order.not.belong.to.seller");
            }
        }
        return skuOrders;
    }
}
