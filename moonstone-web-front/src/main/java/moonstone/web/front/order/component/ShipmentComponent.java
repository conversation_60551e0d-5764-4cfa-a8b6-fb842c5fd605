package moonstone.web.front.order.component;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.BaseUser;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ExpressCompanyCacher;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.event.OrderShipmentEvent;
import moonstone.express.model.ExpressCompany;
import moonstone.express.service.ExpressCompanyReadService;
import moonstone.item.emu.ThirdPartyItemType;
import moonstone.order.api.FlowPicker;
import moonstone.order.dto.fsm.Flow;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.model.*;
import moonstone.order.service.ReceiverInfoReadService;
import moonstone.order.service.ShipmentWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.model.Shop;
import moonstone.web.front.component.dto.ShipmentDTO;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Predicate;

/**
 * 导入发货单的控件,
 * 手动导入发货单
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public record ShipmentComponent(ShipmentWriteService shipmentWriteService,
                                ShopOrderReadService shopOrderReadService,
                                SkuOrderReadService skuOrderReadService,
                                ReceiverInfoReadService receiverInfoReadService,
                                ExpressCompanyReadService expressCompanyReadService,
                                FlowPicker flowPicker,
                                ObjectMapper objectMapper,
                                ShopCacheHolder shopCacheHolder,
                                ExpressCompanyCacher expressCompanyCacher) {

    static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public Long createShipmentCallBack(OrderBase order, String shipmentCorpCode, String shipmentSerialNo, String originShipmentCorpCode) {
        return createShipmentCallBack(order, shipmentCorpCode, shipmentSerialNo, LocalDateTime.now(), originShipmentCorpCode);
    }

    /**
     * 调用发货回调
     *
     * @param order            订单
     * @param shipmentCorpCode 物流公司代码
     * @param shipmentSerialNo 物流单号
     * @return 发货单号
     */
    public Long createShipmentCallBack(OrderBase order, String shipmentCorpCode, String shipmentSerialNo, LocalDateTime shipmentAt,
                                       String originShipmentCorpCode) {
        OrderLevel orderLevel;
        if (order instanceof SkuOrder) {
            orderLevel = OrderLevel.SKU;
        } else {
            orderLevel = OrderLevel.SHOP;
        }
        checkIfCanShip(order, orderLevel);

        return doCreateShipment(order.getId(), orderLevel, shipmentCorpCode, shipmentSerialNo, shipmentAt, originShipmentCorpCode);
    }

    /**
     * 导入发货单
     * 由于是单人导入操作 所以不会出现多线程, 无需恐惧
     *
     * @return 导入结果
     */
    public Either<Long> importShipment(BaseUser operator, InputStream dataStream) {
        // 首先读取订单与发货单数据鉴权
        List<ShipmentAndOrder> shipmentAndOrderList = readStream(dataStream);
        log.debug("{} import order [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(shipmentAndOrderList));
        List<ShipmentDTO> shipmentDTOS = new ArrayList<>(shipmentAndOrderList.size());
        try {
            for (ShipmentAndOrder shipmentAndOrder : shipmentAndOrderList) {
                Long orderId = Long.parseLong(shipmentAndOrder.getDeclareOrderId());
                Long shopId = shopOrderReadService.findById(orderId).getResult().getShopId();
                Shop shop = shopCacheHolder.findShopById(shopId);
                // 判断店铺是否属于自己
                if (!Objects.equals(shop.getUserId(), operator.getId())) {
                    return Either.error(Translate.of("该订单[%s]不属于你", shipmentAndOrder.getDeclareOrderId()));
                }
                // 判断是否订单是否允许导入发货单 只有特定商品可以
                for (SkuOrder skuOrder : skuOrderReadService.findByShopOrderId(orderId).getResult()) {
                    if (!Objects.equals(skuOrder.getIsThirdPartyItem(), ThirdPartyItemType.MANUAL_PARTY_ITEM.getType())) {
                        return Either.error(Translate.of("订单[%s] 不能手动导入发货单", skuOrder.getOrderId()));
                    }
                }
                shipmentDTOS.add(generate(orderId, shipmentAndOrder));
            }
            shipmentDTOS.forEach(dto -> doCreateShipment(dto.getOrderId(), OrderLevel.SHOP, dto.getExpressCompany().getCode(),
                    dto.getShipmentSerialCode(), dto.getShipmentAt(), dto.getExpressCompany().getCode()));
            return Either.ok((long) shipmentDTOS.size());
        } catch (Exception exception) {
            log.error("{} Fail to ship Order[{}]", LogUtil.getClassMethodName(), JSON.toJSON(shipmentAndOrderList), exception);
            return Either.error(Translate.of("导入发货单失败[%s]", exception.getMessage()));
        }
    }

    /**
     * 生成发货请求参数
     *
     * @param orderId          订单
     * @param shipmentAndOrder 原始数据
     * @return 参数
     */
    private ShipmentDTO generate(Long orderId, ShipmentAndOrder shipmentAndOrder) {
        ShipmentDTO shipmentDTO = new ShipmentDTO();

        Predicate<ExpressCompany> match = express -> express.getCode().equals(shipmentAndOrder.getShipmentCorpCode())
                || express.getName().startsWith(shipmentAndOrder.getShipmentCorpName())
                || shipmentAndOrder.getShipmentCorpName().startsWith(express.getName());
        ExpressCompany expressCompany = expressCompanyCacher.listAllActiveExpressCompanies().stream()
                .filter(match).findFirst().orElseThrow(() -> new RuntimeException(Translate.of("快递公司[%s]未找到", Optional.ofNullable(shipmentAndOrder.getShipmentCorpCode()).orElseGet(shipmentAndOrder::getShipmentCorpName))));

        shipmentDTO.setOrderId(orderId);
        shipmentDTO.setExpressCompany(expressCompany);
        shipmentDTO.setShipmentSerialCode(shipmentAndOrder.getShipmentSerialCode());
        shipmentDTO.setShipmentAt(Optional.ofNullable(parseDate(shipmentAndOrder.getShipmentAt())).orElseGet(LocalDateTime::now));
        return shipmentDTO;
    }

    /**
     * 转译发货时间
     *
     * @param dateStr 发货时间
     * @return 发货时间
     */
    LocalDateTime parseDate(String dateStr) {
        try {
            return LocalDateTime.parse(dateStr, FORMATTER);
        } catch (Exception ignored) {
        }
        try {
            return LocalDateTime.parse(dateStr);
        } catch (Exception ignored) {
        }
        return null;
    }

    private String getCellValue(Cell cell) {
        switch (cell.getCellType()) {
            case NUMERIC -> {
                BigDecimal value = BigDecimal.valueOf(cell.getNumericCellValue());
                String str = value.toString();
                if (str.lastIndexOf(".") > 0) {
                    if (str.substring(str.lastIndexOf(".")).matches("\\.0*")) {
                        return str.substring(0, str.lastIndexOf("."));
                    }
                }
                return str;
            }
            case FORMULA -> {
                return DateFormat.getDateInstance().format(cell.getDateCellValue());
            }
            default -> {
                return cell.getStringCellValue();
            }
        }
    }

    /**
     * 读取订单数据流
     *
     * @param dataStream 原始Excel数据流
     * @return 订单数据流
     */
    private List<ShipmentAndOrder> readStream(InputStream dataStream) {
        Workbook workbook;
        ByteArrayOutputStream buffStream = new ByteArrayOutputStream();
        try {
            for (int c = dataStream.read(); c >= 0; c = dataStream.read()) {
                buffStream.write(c);
            }
        } catch (Exception exception) {
            log.error("{} fail to read", LogUtil.getClassMethodName(), exception);
            return new ArrayList<>();
        }
        try {
            workbook = new HSSFWorkbook(new ByteArrayInputStream(buffStream.toByteArray()));
        } catch (Exception exception) {
            try {
                workbook = new XSSFWorkbook(new ByteArrayInputStream(buffStream.toByteArray()));
            } catch (Exception exception1) {
                try {
                    workbook = new XSSFWorkbook(new ByteArrayInputStream(buffStream.toByteArray()));
                } catch (Exception exception2) {
                    log.error("Fail to parse Data", exception2);
                    return new ArrayList<>();
                }
            }
        }
        try {
            LinkedList<ShipmentAndOrder> shipmentAndOrders = new LinkedList<>();
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                for (int line = 1; line <= sheet.getLastRowNum(); line++) {
                    Row row = sheet.getRow(line);
                    try {
                        ShipmentAndOrder shipmentAndOrder = new ShipmentAndOrder();
                        shipmentAndOrder.setDeclareOrderId(getCellValue(row.getCell(0)));
                        shipmentAndOrder.setShipmentSerialCode(getCellValue(row.getCell(1)));
                        shipmentAndOrder.setShipmentCorpName(getCellValue(row.getCell(2)));
                        shipmentAndOrder.setShipmentAt(org.joda.time.LocalDateTime.fromDateFields(row.getCell(3).getDateCellValue()).toString("yyyy-MM-dd HH:mm:ss"));
                        shipmentAndOrders.add(shipmentAndOrder);
                    } catch (Exception e) {
                        log.warn("{} fail to parse value[{}]", LogUtil.getClassMethodName(), e);
                    }
                }
            }
            return shipmentAndOrders;
        } catch (Exception exception) {
            log.error("{} Fail to import shipment", LogUtil.getClassMethodName(), exception);
            throw new RuntimeException(exception);
        }
    }

    /**
     * 创建发货单
     *
     * @param orderId          订单Id
     * @param orderLevel       订单类型
     * @param shipmentCorpCode 发货公司
     * @param shipmentSerialNo 物流单号
     * @return 快递单Id
     */
    public Long doCreateShipment(Long orderId, OrderLevel orderLevel, String shipmentCorpCode, String shipmentSerialNo,
                                 String originShipmentCorpCode) {
        return doCreateShipment(orderId, orderLevel, shipmentCorpCode, shipmentSerialNo, LocalDateTime.now(), originShipmentCorpCode);
    }

    /**
     * 创建发货单
     *
     * @param orderId          订单Id
     * @param orderLevel       订单类型
     * @param shipmentCorpCode 发货公司
     * @param shipmentSerialNo 物流单号
     * @return 快递单Id
     */
    public Long doCreateShipment(Long orderId, OrderLevel orderLevel, String shipmentCorpCode, String shipmentSerialNo, LocalDateTime shipmentAt,
                                 String originShipmentCorpCode) {
        Map<Long, Integer> skuInfoListMap = findSkuInfos(orderId, orderLevel);

        Response<ExpressCompany> expressCompanyResp = expressCompanyReadService.findExpressCompanyByCode(shipmentCorpCode);
        if (!expressCompanyResp.isSuccess()) {
            log.error("fail to find express company by code:{},cause:{}", shipmentCorpCode, expressCompanyResp.getError());
            throw new JsonResponseException(expressCompanyResp.getError());
        }
        ExpressCompany expressCompany = expressCompanyResp.getResult();

        Shipment shipment = new Shipment();
        shipment.setShipmentSerialNo(shipmentSerialNo);
        shipment.setShipmentCorpCode(shipmentCorpCode);
        shipment.setOriginShipmentCorpCode(originShipmentCorpCode);
        shipment.setShipmentCorpName(expressCompany.getName());
        shipment.setSkuInfos(skuInfoListMap);
        shipment.setShipmentAt(Date.from(shipmentAt.atZone(ZoneId.systemDefault()).toInstant()));
        shipment.setReceiverInfos(findReceiverInfos(orderId, orderLevel));

        Response<Long> createResp = shipmentWriteService.create(shipment, Collections.singletonList(orderId), orderLevel);
        if (!createResp.isSuccess()) {
            log.error("fail to create shipment:{} for order(id={}),and level={},cause:{}",
                    shipment, orderId, orderLevel.getValue(), createResp.getError());
            throw new JsonResponseException(createResp.getError());
        }
        Long shipmentId = createResp.getResult();

        EventSender.sendApplicationEvent(new OrderShipmentEvent(shipmentId, shipmentAt));
        return shipmentId;
    }

    /**
     * 查找订单的单品详情
     *
     * @param orderId    订单Id
     * @param orderLevel 订单级别
     * @return 订单单品详情内容 {(skuId,quantity)}
     */
    private Map<Long, Integer> findSkuInfos(Long orderId, OrderLevel orderLevel) {
        Map<Long, Integer> skuInfos = new HashMap<>(8);
        switch (orderLevel) {
            case SHOP:
                Response<List<SkuOrder>> skuOrdersResp = skuOrderReadService.findByShopOrderId(orderId);
                if (!skuOrdersResp.isSuccess()) {
                    log.error("fail to find sku orders by shop order id:{},cause:{}", orderId, skuOrdersResp.getError());
                    throw new JsonResponseException(skuOrdersResp.getError());
                }
                List<SkuOrder> skuOrders = skuOrdersResp.getResult();
                for (SkuOrder skuOrder : skuOrders) {
                    skuInfos.put(skuOrder.getSkuId(), skuOrder.getQuantity());
                }
                break;
            case SKU:
                SkuOrder skuOrder = findSkuOrder(orderId);
                skuInfos.put(skuOrder.getSkuId(), skuOrder.getQuantity());
                break;
            default:
        }
        return skuInfos;
    }

    private SkuOrder findSkuOrder(Long orderId) {
        Response<SkuOrder> skuOrderResp = skuOrderReadService.findById(orderId);
        if (!skuOrderResp.isSuccess()) {
            log.error("fail to find sku order by sku order id:{},cause:{}", orderId, skuOrderResp.getError());
            throw new JsonResponseException(skuOrderResp.getError());
        }
        return skuOrderResp.getResult();
    }

    public void checkIfCanShip(OrderBase order, OrderLevel orderLevel) {
        Flow flow = flowPicker.pick(order, orderLevel);
        if (!flow.operationAllowed(order.getStatus(), OrderEvent.SHIP.toOrderOperation())) {
            log.error("{} can not do ship operation at current status:{} where order id={},and level={}", LogUtil.getClassMethodName(),
                    order.getStatus(), order.getId(), orderLevel.getValue());
            throw new JsonResponseException("订单已发货，不要重复操作");
        }
    }

    private String findReceiverInfos(Long orderId, OrderLevel orderLevel) {
        Long orderIdOfFindReceiverInfo = orderId;
        OrderLevel orderLevelOfFindReceiverInfo = orderLevel;

        List<ReceiverInfo> receiverInfos = doFindReceiverInfos(orderId, orderLevel);
        //找不到子订单收货地址,则用总订单的
        if (CollectionUtils.isEmpty(receiverInfos) && orderLevel == OrderLevel.SKU) {
            orderIdOfFindReceiverInfo = findSkuOrder(orderId).getOrderId();
            orderLevelOfFindReceiverInfo = OrderLevel.SHOP;
            receiverInfos = doFindReceiverInfos(orderIdOfFindReceiverInfo, orderLevelOfFindReceiverInfo);
        }

        if (CollectionUtils.isEmpty(receiverInfos)) {
            log.error("receiverInfo not found where orderId={}", orderId);
            throw new JsonResponseException("receiver.info.not.found");
        }

        if (receiverInfos.size() > 1) {
            log.error("the order(id={},level={}) should has a receiver only,but {} found",
                    orderIdOfFindReceiverInfo, orderLevelOfFindReceiverInfo.getValue(), receiverInfos.size());
            throw new JsonResponseException("too.many.receiver");
        }
        ReceiverInfo receiverInfo = receiverInfos.get(0);

        try {
            return objectMapper.writeValueAsString(receiverInfo);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    private List<ReceiverInfo> doFindReceiverInfos(Long orderId, OrderLevel orderLevel) {
        Response<List<ReceiverInfo>> receiversResp = receiverInfoReadService.findByOrderId(orderId, orderLevel);
        if (!receiversResp.isSuccess()) {
            log.error("fail to find receiver info by order id={},and order level={},cause:{}",
                    orderId, orderLevel.getValue(), receiversResp.getError());
            throw new JsonResponseException(receiversResp.getError());
        }
        return receiversResp.getResult();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShipmentAndOrder {
        String declareOrderId;
        String shipmentSerialCode;
        String shipmentCorpName;
        String shipmentCorpCode;
        String shipmentAt;
    }
}
