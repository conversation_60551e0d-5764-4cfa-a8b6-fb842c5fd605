package moonstone.web.front.order.web;

import moonstone.common.enums.OrderOutFrom;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.ShopOrderReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class ProfitController {
    @Autowired
    BalanceDetailManager balanceDetailManager;
    @Autowired
    ShopOrderReadService shopOrderReadService;

    @PostMapping("/api/profit/substore")
    public List<BalanceDetail> viewProfit(Long orderId) {
        ShopOrder shopOrder = shopOrderReadService.findById(orderId).getResult();
        return balanceDetailManager.calculateProfit(shopOrder, OrderOutFrom.SUB_STORE, false);
    }
}
