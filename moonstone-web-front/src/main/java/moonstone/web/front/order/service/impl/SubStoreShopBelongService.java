package moonstone.web.front.order.service.impl;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.OrderOutFrom;
import moonstone.order.model.OrderBase;
import moonstone.web.front.order.domain.api.BelongShop;
import moonstone.web.front.order.service.api.ShopBelongFactory;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * todo complete subStore check domain
 */
@Component
@Slf4j
public class SubStoreShopBelongService implements ShopBelongFactory {
    @Override
    public BelongShop build(OrderBase order) {
        throw new RuntimeException("not impl");
    }

    @Override
    public OrderOutFrom getOrderOutFrom() {
        return OrderOutFrom.SUB_STORE;
    }
}
