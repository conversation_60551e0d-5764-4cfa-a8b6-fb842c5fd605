package moonstone.web.front.order.v2.web;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.exception.ApiException;
import moonstone.common.model.vo.DropDownBoxVo;
import moonstone.common.model.vo.PageVo;
import moonstone.common.utils.UserUtil;
import moonstone.order.vo.PayInfoVO;
import moonstone.user.enums.DataExportTaskTypeEnum;
import moonstone.user.model.DataExportTask;
import moonstone.web.core.fileNew.dto.ShopOrderDto;
import moonstone.web.core.fileNew.dto.ShopOrderPageDto;
import moonstone.web.core.fileNew.logic.ShopOrderLogic;
import moonstone.web.core.fileNew.vo.ShopOrderVo;
import moonstone.web.front.component.order.OrderExporter;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 订单相关接口
 * author：书生
 */
@RestController
@Slf4j
@RequestMapping("/api/v2/shopOrder")
public class ShopOrderV2Controller {

    @Resource
    private ShopOrderLogic shopOrderLogic;

    @Resource
    private OrderExporter orderExporter;

    /**
     * 分页查询订单信息列表
     *
     * @param req 请求参数
     * @return 订单信息列表
     */
    @PostMapping("pagesWithSnapshot")
    public Result<PageVo<ShopOrderVo>> pagesWithSnapshot(@RequestBody ShopOrderPageDto req) {
        log.info("分页查询订单信息列表 请求参数：{}", JSONUtil.toJsonStr(req));
        return Result.data(shopOrderLogic.pagesWithSnapshot(req));
    }

    /**
     * 异步导出订单信息
     * @param req 请求参数
     * @return 任务ID
     */
    @PostMapping("exportWithSnapshot")
    public Result<Long> exportWithSnapshot(@RequestBody ShopOrderDto req) {
        log.info("导出订单信息列表 请求参数：{}", JSONUtil.toJsonStr(req));
        if (req.getShopId() == null) {
            Long currentShopId = UserUtil.getCurrentShopId();
            if (currentShopId == null) {
                throw new ApiException("当前用户没有绑定任何店铺");
            }
            req.setShopId(currentShopId);
        }
        List<Long> orderIds = shopOrderLogic.listIdsWithSnapshot(req);
        //导出文件名
        String fileName = DataExportTaskTypeEnum.ORDER_MANAGEMENT.getDescription() + moonstone.common.utils.DateUtil.getDateString() + ".xlsx";
        //查询参数
        OrderExporter.OrderExportParameter parameter = new OrderExporter.OrderExportParameter(orderIds);
        //执行导出
        DataExportTask task = orderExporter.asyncExport(UserUtil.getCurrentUser(), fileName, parameter);
        return Result.data(task.getId());
    }

    /**
     * 获取发货仓类型下拉框列表
     * @return 获取发货仓类型下拉框列表
     */
    @PostMapping("shippingWarehouse/drop/down/box")
    public Result<List<DropDownBoxVo>> getShippingWarehouseDropDownBox() {
        log.info("获取发货仓类型下拉框列表");
        return Result.data(shopOrderLogic.getShippingWarehouseDropDownBox());
    }

    /**
     * 获取标记下拉框列表
     * @return 标记下拉框列表
     */
    @PostMapping("flag/drop/down/box")
    public Result<List<DropDownBoxVo>> getFlagDropDownBox() {
        log.info("获取标记下拉框");
        return Result.data(shopOrderLogic.getFlagDropDownBox());
    }

    /**
     * 获取交易状态下拉框列表
     * @return 交易状态下拉框列表
     */
    @PostMapping("trade/status/drop/down/box")
    public Result<List<DropDownBoxVo>> getTradeStatusDropDownBox() {
        log.info("获取交易状态下拉框");
        return Result.data(shopOrderLogic.getTradeStatusDropDownBox());
    }

    /**
     * 获取异常类型下拉框列表
     * @return 异常类型下拉框列表
     */
    @PostMapping("exception/type/drop/down/box")
    public Result<List<DropDownBoxVo>> getExceptionTypeDropDownBox() {
        log.info("获取异常类型下拉框");
        return Result.data(shopOrderLogic.getExceptionTypeDropDownBox());
    }

    /**
     * 获取解密后订单支付人信息
     * @param orderId 订单id
     * @return 解密后订单支付人信息
     */
    @GetMapping("/payInfo/of/decode")
    public Result<PayInfoVO> getPayInfoOfDecode(@RequestParam("orderId") Long orderId) {
        log.info("获取订单支付人信息解密 请求参数 {}",orderId);
        return Result.data(shopOrderLogic.getPayInfoOfDecode(orderId));
    }


    /**
     * 获取支付方式下拉框
     * @param shopId 店铺id
     * @return 支付方式下拉框
     */
    @GetMapping("payment/way/drop/down/box")
    public Result<List<DropDownBoxVo>> getPaymentWayDropDownBox(
            @RequestParam("shopId") Long shopId) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            throw new ApiException("下单失败");
        }
        HttpServletRequest request = requestAttributes.getRequest();
        String appType = request.getHeader("x-app-type");
        log.info("获取支付方式下拉框 请求参数 shopId:{} appType:{}",shopId,appType);
        return Result.data(shopOrderLogic.getPaymentWayDropDownBox(shopId, Integer.valueOf(appType)));
    }
}
