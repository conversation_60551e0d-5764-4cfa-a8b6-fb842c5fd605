/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.front.order.api;

import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.order.dto.RichOrder;
import moonstone.web.front.order.dto.UnGroupedSubmittedOrder;

/**
 * 将用户提交的订单信息组装成为RichOrder, 便于各种下单校验和拆单等逻辑
 *
 * <AUTHOR>
 * Date: 2016-04-26
 */
public interface ImportOrderConstructMaker {

    /**
     * 提交创建订单请求, 此时应包括创建订单所需要的全部信息
     *
     * @param submittedOrder 包括skuId和购买数量信息, 以及收货地址, 发票, 各级别优惠等信息
     * @param buyer          买家id
     * @return 组装好的订单信息
     */
    Either<RichOrder> full(UnGroupedSubmittedOrder submittedOrder, CommonUser buyer);
}
