package moonstone.web.front.order.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;

@Data
public class ReceiverPreViewVO implements Serializable {

    Long id;
    Long userId;
    Boolean isDefault;
    String mobile;
    //    String province;
//    Integer provinceId;
//    String city;
//    Integer cityId;
//    String region;
//    Integer regionId;
    JSONObject province;
    JSONObject city;
    JSONObject region;
    String street;
    Integer streetId;
    String receiveUserName;
    String detail;

}
