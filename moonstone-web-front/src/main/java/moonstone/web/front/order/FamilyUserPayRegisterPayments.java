/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.front.order;

import com.alibaba.fastjson.JSON;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.constants.RequestParams;
import io.terminus.pay.exception.PayException;
import io.terminus.pay.model.TradeResult;
import io.terminus.pay.service.PayChannel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.model.*;
import moonstone.web.core.component.order.PaymentLogic;
import moonstone.web.core.component.pay.allinpay.user.message.UserPayRegisterMessage;
import moonstone.web.core.util.HttpServletUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.MediaType;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 支付相关操作
 * todo: 修改AppId获取方案
 * <p>
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
public class FamilyUserPayRegisterPayments {

    @Resource
    private ChannelRegistry channelRegistry;
    @Autowired
    private PaymentLogic paymentLogic;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @RequestMapping(value = "/api/order/paid/userRegister/{channel}/account/{accountNo}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public String paid(@PathVariable("channel") String channel, @PathVariable("accountNo") String accountNo, HttpServletRequest request) {
        try {
            log.info("付费支付回调 {} {}", channel, accountNo);
            PayChannel paymentChannel = channelRegistry.findChannel(channel);
            request.setAttribute(RequestParams.ACCOUNT, accountNo);
            request.setAttribute("channel", channel);

            paymentChannel.verify(request);

            TradeResult tradeResult = paymentChannel.paymentCallback(request);
            log.info("付费支付回调 {}", JSON.toJSONString(tradeResult));
            //如果通知不是代表支付成功事件，则直接返回
            if (!tradeResult.isPaySuccess()) {
                log.warn("付费支付回调 trade result:{} is not a pay success event,skip to update payment",
                        tradeResult);
                return tradeResult.getCallbackResponse();
            }
            if (tradeResult.isFail()) {
                log.warn("付费支付回调 payment callback result={}", tradeResult);
                return "fail";
            }
            UserPayRegisterMessage userPayRegisterMessage = new UserPayRegisterMessage();
            userPayRegisterMessage.setOrderNo(tradeResult.getMerchantSerialNo());
            try {
                // 201_pay_register
                String shopId = extractNumber(accountNo);
                userPayRegisterMessage.setShopId(Long.valueOf(shopId));
            }catch (Exception e){
                log.error("处理失败", e);
            }
            Message<UserPayRegisterMessage> message = MessageBuilder.withPayload(userPayRegisterMessage).build();
            SendResult sendResult = rocketMQTemplate.syncSend("mall_user_pay_register_callback_topic", message);
            if(SendStatus.SEND_OK.equals(sendResult.getSendStatus())){
                log.info("付费支付回调 消息发送成功 {} {}", accountNo, sendResult.getMsgId());
            }else{
                log.info("付费支付回调 消息发送失败 {}", accountNo);
            }
            return tradeResult.getCallbackResponse();
        } catch (PayException payException) {
            log.error("{} fail to handle payment [channel => {}] callback, cause:", channel, LogUtil.getClassMethodName()
                    , new RuntimeException(payException.getError(), payException));
            return "fail";
        } catch (Exception e) {
            log.error("failed to handle payment callback, request params is:{}, cause is:",
                    HttpServletUtils.convertRequestToMap(request), e);
            return "fail";
        }
    }


    public static String extractNumber(String input) {
        // 使用正则表达式匹配数字部分
        String number = input.replaceAll("[^0-9]", "");
        return number;
    }

    private boolean readParameterMap(String channel) {
        return channel.contains(PayChannelName.alipay.name()) ||
                channel.contains(PayChannelName.mockpay.name()) ||
                PaymentChannelEnum.ALLINPAY.getCode().equals(channel) ||
                PaymentChannelEnum.ALLINPAY_YST.getCode().equals(channel);
    }

    private enum PayChannelName {
        /**
         * 用于匹配支付渠道
         */
        wechat, alipay, mockpay, wechatpay_qr, wechatpay;

        String getName() {
            return name().replace("_", "-");
        }
    }

    /**
     * 禁止修改类名
     *
     * @see MongoTemplate#query 查询对这个有要求
     */
    @Data
    static class WechatMQData {
        String appId;
        String secret;
        Long shopId;
        Integer status = 1;
    }
}
