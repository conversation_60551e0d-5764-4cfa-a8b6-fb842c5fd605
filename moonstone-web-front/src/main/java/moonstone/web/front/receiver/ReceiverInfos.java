package moonstone.web.front.receiver;

import com.google.common.base.Objects;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.UserUtil;
import moonstone.order.model.ReceiverInfo;
import moonstone.order.service.ReceiverInfoReadService;
import moonstone.order.service.ReceiverInfoWriteService;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 用户地址相关接口
 */
@Slf4j
@RestController
@RequestMapping("/api/buyer/receiver-infos")
public class ReceiverInfos {

    @RpcConsumer
    private ReceiverInfoReadService receiverInfoReadService;

    @RpcConsumer
    private ReceiverInfoWriteService receiverInfoWriteService;

    /**
     * 添加地址信息
     */
    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long createReceiverInfo(@RequestBody ReceiverInfo receiverInfo) {
        Long userId = UserUtil.getUserId();
        receiverInfo.setUserId(userId);
        receiverInfo.setStatus(1);

        judgeMobile(receiverInfo.getMobile());

        if (!ObjectUtils.isEmpty(receiverInfo.getPaperNo())) {
            judgePaperNo(receiverInfo.getPaperNo());
        }
        Response<Long> createResp = receiverInfoWriteService.createReceiverInfo(receiverInfo);
        if (receiverInfo.getIsDefault()) {
            setDefaultId(createResp.getResult());
        }

        if (!createResp.isSuccess()) {
            log.error("fail to create receiver info({}) for user(id={}),cause:{}",
                    receiverInfo, userId, createResp.getError());
            throw new JsonResponseException(createResp.getError());
        }
        return createResp.getResult();
    }

    /**
     * 修改地址信息
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean updateReceiverInfo(@PathVariable("id") Long receiverInfoId, @RequestBody ReceiverInfo receiverInfo) {
        checkAuth(receiverInfoId);

        receiverInfo.setId(receiverInfoId);
        receiverInfo.setStatus(null);
        receiverInfo.setUserId(null);

        if (receiverInfo.getMobile().contains("*")) {
            receiverInfo.setMobile(null);
        }

        if (receiverInfo.getPaperNo() != null && receiverInfo.getPaperNo().contains("*")) {
            receiverInfo.setPaperNo(null);
        }

        if (!ObjectUtils.isEmpty(receiverInfo.getMobile())) {
            judgeMobile(receiverInfo.getMobile());
        }

        if (!ObjectUtils.isEmpty(receiverInfo.getPaperNo())) {
            judgePaperNo(receiverInfo.getPaperNo());
        }
        if (receiverInfo.getIsDefault() != null && receiverInfo.getIsDefault()) {
            setDefaultId(receiverInfoId);
        }

        Response<Boolean> updateResp = receiverInfoWriteService.updateReceiverInfo(receiverInfo);
        if (!updateResp.isSuccess()) {
            log.error("fail to update receiver info:{},cause:{}", receiverInfo, updateResp.getError());
            throw new JsonResponseException(updateResp.getError());
        }
        return updateResp.getResult();
    }

    @RequestMapping(value = "/{id}/set-paper-no", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean updatePaperNo(@PathVariable("id") Long receiverInfoId, @RequestBody ReceiverInfo receiverInfo) {
        checkAuth(receiverInfoId);

        receiverInfo.setId(receiverInfoId);

        if (receiverInfo.getPaperNo().contains("*")) {
            receiverInfo.setPaperNo(null);
        }

        Response<Boolean> updateResp = receiverInfoWriteService.updateReceiverPaper(receiverInfo);
        if (!updateResp.isSuccess()) {
            log.error("fail to update receiver info id:{},cause:{}", receiverInfoId, updateResp.getError());
            throw new JsonResponseException(updateResp.getError());
        }
        return updateResp.getResult();
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean deleteReceiverInfo(@PathVariable("id") Long receiverInfoId) {
        Response<ReceiverInfo> findResp = receiverInfoReadService.findById(receiverInfoId);
        if (!findResp.isSuccess()) {
            log.error("fai to find receiver info by id:{},cause:{}", receiverInfoId, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        ReceiverInfo receiverInfo = findResp.getResult();

        Long userId = UserUtil.getUserId();
        if (!Objects.equal(receiverInfo.getUserId(), userId)) {
            log.error("the receiver info where id={} not belong to the user(id={})", receiverInfo, userId);
            throw new JsonResponseException("receiver.info.not.belong.to.user");
        }

        if (receiverInfo.getIsDefault() == Boolean.TRUE) {
            throw new JsonResponseException("default.receiver.info.can.not.delete");
        }

        Response<Boolean> deleteResp = receiverInfoWriteService.deleteAddressByAddressIdAndUserId(receiverInfoId, userId);
        if (!deleteResp.isSuccess()) {
            log.error("fail to delete receiver info where id={} for user(id={}),cause:{}",
                    receiverInfoId, userId, deleteResp.getError());
            throw new JsonResponseException(deleteResp.getError());
        }
        return deleteResp.getResult();
    }

    @RequestMapping(value = "/{id}/set-default", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean setDefault(@PathVariable("id") Long receiverInfoId) {
        checkAuth(receiverInfoId);

        Long userId = UserUtil.getUserId();
        Response<Boolean> setResp = receiverInfoWriteService.makeDefault(receiverInfoId, userId);
        if (!setResp.isSuccess()) {
            log.error("fail to set receiver info(id={}) as default for user(id={}),cause:{}",
                    receiverInfoId, userId, setResp.getError());
            throw new JsonResponseException(setResp.getError());
        }
        return setResp.getResult();
    }

    public boolean setDefaultId(Long receiverInfoId) {
        checkAuth(receiverInfoId);

        Long userId = UserUtil.getUserId();
        Response<Boolean> setResp = receiverInfoWriteService.makeDefaultById(receiverInfoId, userId);
        if (!setResp.isSuccess()) {
            log.error("fail to set receiver info(id={}) as default for user(id={}),cause:{}",
                    receiverInfoId, userId, setResp.getError());
            throw new JsonResponseException(setResp.getError());
        }
        return setResp.getResult();
    }


    private void checkAuth(Long receiverInfoId) {
        Response<ReceiverInfo> findResp = receiverInfoReadService.findById(receiverInfoId);
        if (!findResp.isSuccess()) {
            log.error("fai to find receiver info by id:{},cause:{}", receiverInfoId, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        ReceiverInfo receiverInfo = findResp.getResult();

        Long userId = UserUtil.getUserId();
        if (!Objects.equal(receiverInfo.getUserId(), userId)) {
            log.error("the receiver info where id={} not belong to the user(id={})", receiverInfo, userId);
            throw new JsonResponseException("receiver.info.not.belong.to.user");
        }
    }

    private void judgeMobile(String mobile) {
//        if (!mobile.matches("^((13[0-9])|(14[0-9])|(15[0-9])|(17[0-9])|(18[0-9]))\\d{8}$")) {
        if (!mobile.matches("^\\d{11}$") && !mobile.matches("^0\\d{2}-\\d{7,8}$")) {
            throw new JsonResponseException(400, "receiver.info.mobile.invalid");
        }
    }

    private void judgePaperNo(String paperNo) {
        if (!paperNo.matches("(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{4}\\*{9}\\d{4}$)")) {
            throw new JsonResponseException(400, "receiver.info.paperNo.invalid");
        }
    }

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ReceiverInfo> findByUserId(@RequestParam(required = false, name = "pageNo") Integer pageNo,
                                           @RequestParam(required = false, name = "pageSize") Integer pageSize) {
        final Long userId = UserUtil.getUserId();

        List<ReceiverInfo> list = null;
        if (pageNo == null || pageSize == null) {
            list = receiverInfoReadService.findByUserId(userId).getResult();
        } else {
            var result = receiverInfoReadService.findPageByUserId(userId, pageNo, pageSize).getResult();
            if (result != null) {
                list = result.getData();
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<ReceiverInfo> ris = new ArrayList<>();
        for (ReceiverInfo receiverInfo : list) {
            String mobile = receiverInfo.getMobile();
            String paperNo = receiverInfo.getPaperNo();
//            if(!ObjectUtils.isEmpty(mobile)) {
//                receiverInfo.setMobile(mobile.substring(0, 3) + "****" + mobile.substring(7));
//            }
            if (!ObjectUtils.isEmpty(paperNo)) {
                receiverInfo.setPaperNo(paperNo.substring(0, 5) + "*********" + paperNo.substring(14));
            }
            ris.add(receiverInfo);
        }
        ReceiverInfo defaultReciverInfo = null;
        for (ReceiverInfo subInfo : ris) {
            if (subInfo.getIsDefault()) {
                defaultReciverInfo = subInfo;
                break;
            }
        }
        if (defaultReciverInfo != null) {
            ris.remove(defaultReciverInfo);
            ris.add(0, defaultReciverInfo);
        }

        return ris;
    }

    /**
     * 获取用户默认地址
     * @return 用户默认地址
     */
    @RequestMapping(value = "/default", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<ReceiverInfo> findByUserIdLimitOne() {
        final Long userId = UserUtil.getUserId();

        Response<List<ReceiverInfo>> findResp = receiverInfoReadService.findByUserIdLimitOne(userId);
        if (!findResp.isSuccess()) {
            log.error("fail to find receiver info for buyer(id={}),cause:{}",
                    userId, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }

        if (findResp.getResult().isEmpty()) {
            log.error("fail to find receiver info is empty for buyer(id={}),cause:{}",
                    userId, findResp.getError());
            return Response.ok(new ReceiverInfo());
        }

        List<ReceiverInfo> ris = new ArrayList<>();
        for (ReceiverInfo receiverInfo : findResp.getResult()) {
            String mobile = receiverInfo.getMobile();
            String paperNo = receiverInfo.getPaperNo();
//            if(!ObjectUtils.isEmpty(mobile)) {
//                receiverInfo.setMobile(mobile.substring(0, 3) + "****" + mobile.substring(7));
//            }
            if (!ObjectUtils.isEmpty(paperNo)) {
                receiverInfo.setPaperNo(paperNo.substring(0, 5) + "*********" + paperNo.substring(14));
            }
            ris.add(receiverInfo);
        }
        ReceiverInfo defaultReciverInfo = null;
        for (ReceiverInfo subInfo : ris) {
            if (subInfo.getIsDefault()) {
                defaultReciverInfo = subInfo;
                break;
            }
        }
        if (defaultReciverInfo != null) {
            ris.remove(defaultReciverInfo);
            ris.add(0, defaultReciverInfo);
        }

        return Response.ok(ris.get(0));
    }


    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ReceiverInfo findById(@PathVariable Long id) {
        final Long userId = UserUtil.getUserId();
        ReceiverInfo receiverInfo = null;

        if (id == 0) {
            Response<Optional<ReceiverInfo>> roResp = receiverInfoReadService.findDefaultByUserId(userId);
            if (!roResp.isSuccess()) {
                log.error("fail to find default receiver info by userId={}, cause:{}",
                        userId, roResp.getError());
                throw new JsonResponseException(roResp.getError());
            }
            Optional<ReceiverInfo> receiverInfoOptional = roResp.getResult();
            if (receiverInfoOptional.isPresent()) {
                receiverInfo = receiverInfoOptional.get();
            }
        } else {
            Response<ReceiverInfo> findResp = receiverInfoReadService.findById(id);
            if (!findResp.isSuccess()) {
                log.error("fail to find receiver info by id={}, cause:{}",
                        id, findResp.getError());
                throw new JsonResponseException(findResp.getError());
            }
            receiverInfo = findResp.getResult();
            if (!Objects.equal(receiverInfo.getUserId(), userId)) {
                log.error("the receiver info where id={} not belong to the user(id={})", id, userId);
                throw new JsonResponseException("receiver.info.not.belong.to.user");
            }
            String mobile = receiverInfo.getMobile();
            String paperNo = receiverInfo.getPaperNo();
//            if(!ObjectUtils.isEmpty(mobile)) {
//                receiverInfo.setMobile(mobile.substring(0, 3) + "****" + mobile.substring(7));
//            }
            if (!ObjectUtils.isEmpty(paperNo)) {
                receiverInfo.setPaperNo(paperNo.substring(0, 5) + "*********" + paperNo.substring(14));
            }
        }
        return receiverInfo;
    }
}
