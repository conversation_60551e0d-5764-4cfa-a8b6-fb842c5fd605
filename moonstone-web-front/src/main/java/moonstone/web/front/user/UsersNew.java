package moonstone.web.front.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.enums.CountryCode;
import moonstone.common.model.CommonUser;
import moonstone.common.model.WxCommonUser;
import moonstone.common.utils.*;
import moonstone.shop.service.ShopReadService;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.user.dto.UserUpdateEvent;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.model.WXSendSecret;
import moonstone.user.model.WxLoginBody;
import moonstone.user.model.view.UserTagView;
import moonstone.user.service.*;
import moonstone.web.core.AppConstants;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.config.FunctionSwitch;
import moonstone.web.core.events.user.LoginEvent;
import moonstone.web.core.events.user.UserProfileUpdateEvent;
import moonstone.web.core.events.user.WxLoginEvent;
import moonstone.web.core.user.api.LoginService;
import moonstone.web.core.util.ParanaUserMaker;
import moonstone.web.front.constants.Sessions;
import moonstone.web.core.decoration.model.WxUserDomain;
import moonstone.web.core.decoration.api.WxUserDomainFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.Optional;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/8 13:55
 * /api/news/user 拦截器不拦截
 */
@Slf4j
@RestController
@RequestMapping("")
public class UsersNew {

	@Autowired
	UserProfileReadService userProfileReadService;
	@Autowired
	UserProfileWriteService userProfileWriteService;
	@Autowired
	UserReadService<User> userReadService;
	@Autowired
	UserWriteService<User> userWriteService;
	@Autowired
	UserWxReadService userWxReadService;
	@Autowired
	LoginService loginService;
	@Autowired
	ShopReadService shopReadService;
	@Autowired
	ShopWxaReadService shopWxaReadService;
	@Autowired
	ShopWxaProjectReadService shopWxaProjectReadeService;
	@Autowired
	UserTypeBean userTypeBean;

	@Autowired
	ShopWxaCacheHolder shopWxaCacheHolder;
	@Autowired
	Users users;
	@Autowired
	FunctionSwitch functionSwitch;
	@Autowired
	WxUserDomainFactory wxUserDomainFactory;

	public static String filterEmoji(String source) {
		if (source != null && source.length() > 0) {
			return source.replaceAll("[\ud800\udc00-\udbff\udfff\ud800-\udfff]", "");
		} else {
			return source;
		}
	}

	/**
	 * 装修小程序登录同时更新用户信息
	 *
	 * @param wxLoginBody 登录请求
	 * @param request     请求头
	 * @param response    回应头
	 */
	@RequestMapping(value = "/api/news/user/login-by-mobile", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public R loginByMobileMsg(@RequestBody WxLoginBody wxLoginBody,
	                          HttpServletRequest request,
	                          HttpServletResponse response) {
		log.debug("[op:loginByMobileMsg] mobile={}, NickName={}", wxLoginBody.getMobile(), wxLoginBody.getNickName());
		try {
			if (java.util.Objects.isNull(wxLoginBody.getProjectId())
					|| StringUtil.isBlank(wxLoginBody.getWxCode())) {
				return R.error(-1, "必要参数缺失");
			}
			String countryCode = wxLoginBody.getCountryCode().trim();
			String mobile = "";
			if (!StringUtil.isBlank(wxLoginBody.getMobile())) {
				mobile = judgeMobile(countryCode, wxLoginBody.getMobile());
			}

			String nickName;
			if (StringUtil.isBlank(wxLoginBody.getNickName())) {
				nickName = "微信用户";
			} else {
				wxLoginBody.setNickName(filterEmoji(wxLoginBody.getNickName()));
				nickName = wxLoginBody.getNickName();
			}

			HttpSession session = request.getSession();
			ShopWxa shopWxa = shopWxaReadService.findById(shopWxaProjectReadeService.findById(wxLoginBody.getProjectId()).getResult().getShopWxaId()).getResult();
			WxCommonUser wxParanaUser;
			if (shopWxa.getAppSecret() == null) {
				// use open platform login
				wxParanaUser = new WxCommonUser();
				WxUserDomain domain = wxUserDomainFactory.create(wxLoginBody.getWxCode(), wxLoginBody.getProjectId());
				Long userId = domain.login().take();
				BeanUtils.copyProperties(ParanaUserMaker.from(userReadService.findById(userId).getResult())
						, wxParanaUser);
				wxParanaUser.setIsNewUser(UserTagView.build(wxParanaUser).isNewWxUser());
				wxParanaUser.setOpenId(domain.getUserWx().getOpenId());
			} else {
				wxParanaUser = loginService.wxaLoginNew(wxLoginBody.getWxCode(), shopWxa.getAppId(), shopWxa.getAppSecret(), mobile, nickName);
			}
			if (userTypeBean.isSeller(wxParanaUser)) {
				val shopResp = shopReadService.findByUserId(wxParanaUser.getId());
				if (!shopResp.isSuccess()) {
					log.warn("[op:wxaProjectLogin] find shop by userId={} failed, error={}", wxParanaUser.getId(), shopResp.getError());
					return R.error(-1, "系统异常");
				}
				wxParanaUser.setShopId(shopResp.getResult().getId());
			}
			log.debug("[op:wxaProjectLogin] result={}", JSON.toJSONString(wxParanaUser));
			UserUtil.putCurrentUser(wxParanaUser);
			//更新用户parana_user_profile
			updateParanaUserProfile(wxParanaUser.getId(), wxLoginBody.getNickName(), wxLoginBody.getAvatar());
			//处理回话进入redis
			session.setAttribute(Sessions.USER_ID, wxParanaUser.getId());
			session.setAttribute(Sessions.OPEN_ID, wxParanaUser.getOpenId());
			EventSender.sendApplicationEvent(new LoginEvent(request, response, wxParanaUser));
			EventSender.sendApplicationEvent(new WxLoginEvent(response, wxParanaUser));
			log.debug("[op:loginByMobileMsguserid] userid={}", wxParanaUser.getId());
			boolean isPhone = false;
			if (!ObjectUtils.isEmpty(wxParanaUser.getId())) {
				Response<User> userResponse = userReadService.findById(wxParanaUser.getId());
				if (!ObjectUtils.isEmpty(java.util.Objects.requireNonNull(userResponse.getResult()).getMobile())) {
					isPhone = true;
				}
			}
			JSONObject json = new JSONObject();
			json.put("userId", wxParanaUser.getId());
			json.put("msid", "msid=" + session.getId());
			json.put("isPhone", isPhone);
			log.debug("[op:loginByMobileMsgjson] userId:{} json={} date:{}", wxParanaUser.getId(), json, new Date());
			EventSender.publish(new UserUpdateEvent(wxParanaUser.getUserId()));
			return R.ok().add("data", json);
		} catch (Exception e) {
			log.error("failed to login by mobile {}, cause:{}", wxLoginBody, Throwables.getStackTraceAsString(e));
			return R.error(-1, "登录失败");
		}
	}

	/**
	 * 更新微信手机号
	 * TODO  appId换成  projectId
	 *
	 */
	@RequestMapping(value = "/api/news/user/{mobile}/{projectId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public R UpdateUserMobile(@PathVariable("mobile") String mobile, @PathVariable("projectId") Long projectId,
							  HttpServletRequest request, @RequestParam(defaultValue = "0086") String countryCode) {
		var shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);

		// 处理错误
		try {
			final String SESSION_NAME = "msid";
			final String SESSION_PREFIX = "msid=";
			if (EmptyUtils.isEmpty(shopWxa)) {
				return R.error(-1, "小程序信息未配置");
			}
			if (EmptyUtils.isEmpty(UserUtil.getUserId())) {
				return R.error(-1001, "not login");
			}
			log.info("UsersNew.UpdateUserMobile, userId={}, mobile={}, projectId={}", UserUtil.getUserId(), mobile, projectId);

			request.getSession().setAttribute(AppConstants.SESSION_SMS_CODE_LOGIN, "NOT_NEED_VERIFY");
			try {
				return R.ok().add("data", ImmutableMap.of(AppConstants.SESSION_USER_ID, users.newWxUserBind(countryCode, mobile, null, null, projectId, request),
						SESSION_NAME, SESSION_PREFIX + UserUtil.getCurrentSession().getId()));
			} catch (JsonResponseException jsonResponseException) {
				if ("user.bind.illegal".equals(jsonResponseException.getMessage())) {
					return R.ok().add("data"
							, ImmutableMap.of(AppConstants.SESSION_USER_ID, userWxReadService.findByOpenIdAndAppIdAndAppType(
									UserOpenIdUtil.getOpenId(), shopWxaCacheHolder.findShopWxaByProjectId(projectId).getAppId(), AppTypeEnum.from(shopWxa.getAppType())).getResult().getUserId(),
									SESSION_NAME, SESSION_PREFIX + UserUtil.getCurrentSession().getId()));
				}
				return R.error(jsonResponseException.getMessage());
			}
		} catch (Exception e) {
			if ("查找项目失败".equals(e.getMessage())) {
				return R.error(-1, "小程序信息未配置");
			}
			log.error("{} fail to update user[{}] Mobile[{}] project[{}]", LogUtil.getClassMethodName(), UserUtil.getUserId(), mobile, projectId);
			return R.error(e.getMessage());
		}
	}

	void updateParanaUserProfile(Long userId, String nickName, String avatar) {
		if (userId == null) {
			throw new JsonResponseException(401, "user.not.login");
		}
		log.info("[ParanaUserProfile] profile.userId:{} nickname:{} avatar:{}", userId, nickName, avatar);

		if (!Strings.isNullOrEmpty(nickName)) {
			User u = new User();
			u.setId(userId);
			u.setName(nickName + "_" + userId);
			val updateUserResp = userWriteService.update(u);
			if (!updateUserResp.isSuccess()) {
				log.warn("update user username in profile failed, error={}", updateUserResp.getError());
				throw new JsonResponseException(updateUserResp.getError());
			}
		}
		val existResp = userProfileReadService.findProfileByUserId(userId);
		if (!existResp.isSuccess()) {
			throw new JsonResponseException(existResp.getError());
		}
		if (existResp.getResult() == null) {
			existResp.setResult(new UserProfile());
		}
		UserProfile toUpdate = new UserProfile(userId);
		toUpdate.setAvatar(avatar);
		toUpdate.setRealName(nickName);
		Response<Boolean> resp;
		log.info("[ParanaUserProfile] profile.userId:{} getRealName:{} id:{}", userId, nickName, existResp.getResult().getId());
		if (existResp.getResult().getId() == null) {
			resp = userProfileWriteService.createProfile(toUpdate);
			log.info("[ParanaUserProfile] profile.userId:{} getRealName:{} resp:{} ", userId, nickName, resp.getResult());
		} else {
			resp = userProfileWriteService.updateProfile(toUpdate);
		}
		if (!resp.isSuccess()) {
			throw new JsonResponseException(resp.getError());
		}
		EventSender.publish(new UserProfileUpdateEvent(userId));
	}

	String judgeMobile(String countryCode, String mobile) {
		countryCode = countryCode.trim();
		if (countryCode.startsWith("+")) {
			countryCode = countryCode.replaceAll("^\\+", CountryCode.PREFIX_CODE);
		}
		if (countryCode.equals(CountryCode.China.getCode())) {
			judgeMobile(mobile);
			return countryCode + mobile;
		}
		if (!functionSwitch.getForeignSms()) {
			throw new JsonResponseException(400, "user.mobile.unsupport");
		}
		if (countryCode.equals(CountryCode.Australia.getCode())) {
			if (!mobile.matches("^\\d{9}$")) {
				throw new JsonResponseException(400, "user.mobile.invalid");
			}
			return countryCode + mobile;
		}
		throw new JsonResponseException(400, "user.mobile.invalid");
	}

	void judgeMobile(String mobile) {
		if (!mobile.matches("^\\d{11}$")) {
			throw new JsonResponseException(400, "user.mobile.invalid");
		}
	}

	/**
	 * 获取用户解密key
	 */
	@RequestMapping(value = "/api/news/user/get-secret", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
	public R getSercert(@RequestBody WXSendSecret wXSendSecret) {
		String url = "https://api.weixin.qq.com/sns/jscode2session";
		if (java.util.Objects.isNull(wXSendSecret.getProjectId())
				|| StringUtil.isBlank(wXSendSecret.getCode())) {
			return R.error(-1, "参数不能为空");
		}
		ShopWxa shopWxa = shopWxaReadService.findById(shopWxaProjectReadeService.findById(wXSendSecret.getProjectId()).getResult().getShopWxaId()).getResult();
		if (shopWxa.getAppSecret() == null) {
			Long userId = wxUserDomainFactory.create(wXSendSecret.getCode(), wXSendSecret.getProjectId()).login().take();
			JSONObject wxReturnType = new JSONObject();
			userWxReadService.findByAppIdAndUserId(shopWxaCacheHolder.findShopWxaByProjectId(wXSendSecret.getProjectId()).getAppId(), userId)
					.orElseGet(Optional::empty)
					.ifPresent(userWx -> {
						wxReturnType.put("openid", userWx.getOpenId());
						wxReturnType.put("unionid", userWx.getUnionId());
						wxReturnType.put("session_key", userWx.getAccessToken());
						wxReturnType.put("errcode", 0);
						wxReturnType.put("errmsg", "success");
					});
			return R.ok().add("data", wxReturnType);
		}
		String param = "appid=" + shopWxa.getAppId() + "&secret=" +
				shopWxa.getAppSecret() + "&js_code=" + wXSendSecret.getCode() + "&grant_type=authorization_code";
		try {
			HttpRequest request = HttpRequest.get(url + "?" + param);
			if (request.ok()) {
				String data = request.body();
				log.info("wx-data:{}", data);
				return R.ok().add("data", data);
			}
		} catch (Exception e) {
			log.error("failed to get Secret {}, cause:{}", wXSendSecret, Throwables.getStackTraceAsString(e));
			return R.error(-1, "请求失败");
		}
		return R.error(-1, "请求失败");
	}

	@RequestMapping(value = "/api/new/user/current", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
	public R findCurrentUser() {
		CommonUser user = UserUtil.getCurrentUser();
		if (user == null) {
			return R.error(-1001, "user not login");
		}
		return R.ok().add("user", user);
	}
}
