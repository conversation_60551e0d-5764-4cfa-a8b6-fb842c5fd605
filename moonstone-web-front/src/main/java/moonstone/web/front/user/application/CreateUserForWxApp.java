package moonstone.web.front.user.application;

import com.google.common.collect.ImmutableMap;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;
import moonstone.common.constants.ParanaConstants;
import moonstone.common.enums.UserRole;
import moonstone.common.enums.UserStatus;
import moonstone.common.utils.EventSender;
import moonstone.user.dto.UserUpdateEvent;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.service.UserProfileWriteService;
import moonstone.user.service.UserWriteService;
import moonstone.web.core.events.user.UserProfileUpdateEvent;
import moonstone.web.front.user.application.api.CreateUserForWxAppApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.Collections;
import java.util.Optional;

@Component
public class CreateUserForWxApp extends AbstractVerticle implements CreateUserForWxAppApi {
    @Autowired
    UserProfileWriteService userProfileWriteService;
    @Autowired
    UserWriteService<User> userWriteService;
    @Autowired
    UserTypeBean userTypeBean;

    @Override
    public void start() throws Exception {
        vertx.eventBus().<JsonObject>localConsumer(CreateUserForWxAppApi.address(Action.ADD))
                .handler(msg -> createUser(msg.body().getString("name"), msg.body().getString("mobile"))
                        .onSuccess(id -> msg.reply(new JsonObject().put("id", id)))
                        .onFailure(e -> msg.fail(-1, e.getMessage())));
    }

    private Future<Long> createUser(String name, String mobile) {
        User user = new User();
        user.setName("导" + name + "@" + LocalDate.now().getDayOfYear() + System.currentTimeMillis() % 100);
        user.setMobile(mobile);
        user.setType(userTypeBean.getBuyerType());
        user.setRoles(Collections.singletonList(UserRole.BUYER.name()));
        user.setTags(ImmutableMap.of(ParanaConstants.USER_IS_WX_USER, Boolean.TRUE.toString(),
                ParanaConstants.USER_IS_NEW_WX_USER, Boolean.TRUE.toString()));
        user.setStatus(UserStatus.NORMAL.value());
        // profile require user id, so create it after user is saved
        UserProfile profile = new UserProfile();
        profile.setRealName(name);
        return vertx.<Long>executeBlocking(promise -> {
            Optional.ofNullable(
                    userWriteService.create(user).getResult()).ifPresent(promise::complete);
            // todo: check if user's mobile is used
            promise.tryFail("用户创建失败, 手机号可能已注册过用户");
        })
                .onSuccess(profile::setUserId)
                // create profile and send update event
                .onSuccess(userId ->
                        vertx.executeBlocking(promise -> promise.complete(userProfileWriteService.createProfile(profile)))
                                .onSuccess(done ->
                                        EventSender.publish(new UserProfileUpdateEvent(userId)))
                )
                .onSuccess(userId -> EventSender.publish(new UserUpdateEvent(userId)));
    }
}
