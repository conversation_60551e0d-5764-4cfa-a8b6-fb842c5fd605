package moonstone.web.front.user;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.AppResponse;
import moonstone.common.utils.UserUtil;
import moonstone.user.model.UserCertification;
import moonstone.user.service.UserCertificationReadService;
import moonstone.user.service.UserCertificationWriteService;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> shark
 * @date 2020/5/25 14:50
 */
@Slf4j
@RestController
@RequestMapping("/api/app/v1")
public class UserCertificationsApp {
    @RpcConsumer
    private UserCertificationReadService userCertificationReadService;

    @RpcConsumer
    private UserCertificationWriteService userCertificationWriteService;

    /**
     * 获得用户实名信息
     *
     * @return
     */
    @RequestMapping(value = "/user/realname/findByUserId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public AppResponse findByUserId() {
        Long userId = UserUtil.getUserId();
        Response<List<UserCertification>> findResp = userCertificationReadService.findByUserId(userId);
        if (!findResp.isSuccess()) {
            log.error("failed to find user certification for user(id={}), cause:{}",
                    userId, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        List<UserCertification> ucs = findResp.getResult();
        if (CollectionUtils.isEmpty(ucs)) {
            return AppResponse.ok();
        }
        UserCertification userCertification = ucs.get(0); // 默认一个用户只有一条实名数据
        userCertification.encryptPaperNo();
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("id", userCertification.getId());
        dataMap.put("no", userCertification.getPaperNo());
        dataMap.put("name", userCertification.getPaperName());
        return AppResponse.ok(dataMap);
    }


    /**
     * 新增或修改实名信息
     *
     * @param userCertification
     * @return
     */
    @RequestMapping(value = "/user/realname/addAndupd", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public AppResponse update(@RequestBody UserCertification userCertification) {
        if (userCertification.getPaperNo().contains("*")) {
            userCertification.setPaperNo(null);
        }
        if (!ObjectUtils.isEmpty(userCertification.getPaperNo())) {
            judgePaperNo(userCertification.getPaperNo());
            uppercasePaperNo(userCertification);
        }

        Long userId = UserUtil.getUserId();
        // 根据userId查询实名信息，若无实名则新增；否则修改
        Response<List<UserCertification>> findResp = userCertificationReadService.findByUserId(userId);
        if (!findResp.isSuccess()) {
            log.error("failed to find user certification for user(id={}), cause:{}",
                    userId, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        List<UserCertification> ucs = findResp.getResult();
        Long ucId = null;
        if (CollectionUtils.isEmpty(ucs)) { // 若无实名则新增
            userCertification.setUserId(userId);
            userCertification.setIsDefault(true);
            userCertification.setStatus(1); // 状态，1：正常，-1：删除
            userCertification.setCreatedAt(new Date());
            userCertification.setUpdatedAt(new Date());
            Response<Long> ucResponse = userCertificationWriteService.create(userCertification);
            if (!ucResponse.isSuccess()) {
                throw new JsonResponseException("user.certification.create.fail");
            }
            ucId = ucResponse.getResult();
        } else { // 已存在实名则修改
            ucId = ucs.get(0).getId(); // 默认一个用户只有一条实名数据
            userCertification.setId(ucId);
            if (!userCertificationWriteService.update(userCertification).isSuccess()) {
                throw new JsonResponseException("user.certification.update.fail");
            }
        }
        return AppResponse.ok(ucId);
    }


    private void judgePaperNo(String paperNo) {
        if (!paperNo.matches("(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{4}\\*{9}\\d{4}$)")) {
            throw new JsonResponseException(400, "user.certification.paperNo.invalid");
        }
    }

    private void uppercasePaperNo(UserCertification userCertification) {
        String paperNo = userCertification.getPaperNo();
        if (paperNo.endsWith("x")) {
            userCertification.setPaperNo(paperNo.substring(0, paperNo.length() - 1) + "X");
        }
    }

}
