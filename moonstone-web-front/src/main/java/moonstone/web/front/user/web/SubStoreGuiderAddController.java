package moonstone.web.front.user.web;

import io.terminus.common.exception.JsonResponseException;
import io.vertx.core.Future;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.enums.CountryCode;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.shop.model.SubStore;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.web.core.events.item.ShopItemDumpEvent;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.front.user.application.api.SubStoreGuiderApi;
import moonstone.web.front.user.dto.JoinSubStoreRequest;
import moonstone.web.front.user.dto.SubStoreGuider;
import moonstone.web.front.user.dto.SubStoreGuiderAddRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/subStore")
public class SubStoreGuiderAddController {

	@Autowired
	private Vertx vertx;

	@Resource
	private UserReadService<User> userReadService;

	@Resource
	private SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;

	@Autowired
	private GuiderCache guiderCache;

	@Autowired
	private SubStoreCache subStoreCache;

	@Autowired
	private ServiceProviderCache serviceProviderCache;

	/**
	 * 增加导购员
	 *
	 * @param guider 导购员信息
	 * @return 是否添加成功
	 */
	@PostMapping("/guider-add")
	public boolean addGuider(@RequestBody SubStoreGuider guider) {
		if (UserUtil.getUserId() == null) {
			throw new JsonResponseException(Translate.of("用户未登录"));
		}
		if (guider.getShopId() == null) {
			throw new JsonResponseException(Translate.of("平台信息丢失, 请重新扫描二维码"));
		}
		if (ObjectUtils.isEmpty(guider.getMobile())) {
			throw new JsonResponseException(Translate.of("缺失手机号"));
		}
		long currentUserId = UserUtil.getUserId();
		if (!subStoreCache.findByShopIdAndUserId(guider.getShopId(), currentUserId)
				.orElseThrow(() -> Translate.exceptionOf("门店不存在"))
				.isAuthed()) {
			throw Translate.exceptionOf("门店审核未通过");
		}

		try {
			return vertx.<Optional<User>>executeBlocking(promise -> promise.complete(Optional.ofNullable(userReadService.findByMobile(guider.getMobile()).getResult())))
					.flatMap(user -> {
						if (user.isPresent()) {
							// check if the user is register as guider
							return vertx.<List<SubStoreTStoreGuider>>executeBlocking(promise -> promise.complete(subStoreTStoreGuiderReadService.findByStoreGuiderUserId(user.get().getId(), guider.getShopId()).getResult()))
									.map(list -> Objects.isNull(list) ? Collections.<SubStoreTStoreGuider>emptyList() : list)
									.map(filterOtherShop -> filterOtherShop.stream().filter(checkShop -> guider.getShopId().equals(checkShop.getShopId())).collect(Collectors.toList()))
									.flatMap(exists -> exists.isEmpty() ? createGuider(user.get(), guider, currentUserId)
											: Future.failedFuture(Translate.of("该导购已经隶属于门店[%s], 不能同时属于多个门店",
											subStoreCache.findById(exists.get(0).getSubStoreId()).map(SubStore::getName).orElse(null))));
						}
						return createGuider(null, guider, currentUserId);
					})
					.map(Objects::nonNull)
					.onSuccess(v -> EventSender.publish(new ShopItemDumpEvent(guider.getShopId())))
					.toCompletionStage()
					.toCompletableFuture()
					.join();
		}catch (Exception ex){
			log.error("SubStoreGuiderAddController.addGuider error: ", ex);
			throw new RuntimeException(ex.getMessage() != null ?
					ex.getMessage().replace("(RECIPIENT_FAILURE,-1) ", "") : ex.getMessage());
		}
	}

	/**
	 * 用户扫码加入门店成为导购
	 *
	 * @param request
	 * @return
	 */
	@PostMapping("/joinSubStore")
	public APIResp<Boolean> joinSubStore(@RequestBody JoinSubStoreRequest request) {
		//前置校验
		Long currentUserId = UserUtil.getUserId();
		if (currentUserId == null) {
			return APIResp.error("用户未登录");
		}
		if (request == null || request.getSubStoreId() == null) {
			return APIResp.error("门店id缺失");
		}
		if (StringUtils.isBlank(request.getGuiderName()) || StringUtils.isBlank(request.getGuiderMobile())) {
			return APIResp.error("导购姓名与手机号皆不能为空");
		}

		var user = userReadService.findById(currentUserId).getResult();
		var subStore = subStoreCache.findById(request.getSubStoreId());
		String errorMessage = checkForJoinSubStore(user, subStore);
		if (StringUtils.isNotBlank(errorMessage)) {
			return APIResp.error(errorMessage);
		}

		try {
			//构造入参
			SubStoreGuider guider = new SubStoreGuider();
			guider.setMobile(request.getGuiderMobile());
			guider.setName(request.getGuiderName());
			guider.setShopId(subStore.get().getShopId());

			//执行
			createGuider(isTheSameMobile(guider, user) ? user : null, guider, subStore.get().getUserId())
					.onSuccess(v -> EventSender.publish(new ShopItemDumpEvent(guider.getShopId())))
					.toCompletionStage()
					.toCompletableFuture()
					.join();

			return APIResp.ok(true);
		} catch (Exception ex) {
			log.error("SubStoreGuiderAddController.joinSubStore error: ", ex);
			return APIResp.error(ex.getMessage());
		}
	}

	/**
	 * 判断两者的手机号是否一致
	 *
	 * @param guider
	 * @param user
	 * @return
	 */
	private boolean isTheSameMobile(SubStoreGuider guider, User user) {
		var guiderMobile = guider.getMobile().startsWith(CountryCode.PREFIX_CODE) ?
				guider.getMobile().substring(CountryCode.PREFIX_CODE_LEN) : guider.getMobile();

		return guiderMobile.equals(user.getMobile());
	}

	/**
	 * 用户加入门店成为导购时需要通过的校验
	 *
	 * @return
	 */
	private String checkForJoinSubStore(User currentUser, Optional<SubStore> subStore) {
		if (currentUser == null) {
			return "当前用户不存在";
		}
		if (StringUtils.isBlank(currentUser.getMobile())) {
			return "当前用户还没有绑定手机号";
		}

		if (subStore.isEmpty()) {
			return "目标门店不存";
		}
		if (!subStore.get().isAuthed()) {
			return "门店审核未通过";
		}

		if (serviceProviderCache.findServiceProviderByShopIdAndUserId(subStore.get().getShopId(), currentUser.getId()).isPresent()) {
			return "当前您属于服务商角色，不可注册导购";
		}
		if (subStoreCache.findByShopIdAndUserId(subStore.get().getShopId(), currentUser.getId()).isPresent()) {
			return "当前您属于门店角色，不可注册导购";
		}

		var guider = guiderCache.findByShopIdAndUserId(subStore.get().getShopId(), currentUser.getId());
		if (guider.isPresent()) {
			var belongStore = subStoreCache.findById(guider.get().getSubStoreId());
			return String.format("当前您属于%s门店，如需修改请联系门店", belongStore.isPresent() ? belongStore.get().getName() : StringUtils.EMPTY);
		}

		return StringUtils.EMPTY;
	}

	private Future<Long> createGuider(User existsUser, SubStoreGuider guider, long currentUserId) {
		return vertx.eventBus().<JsonObject>request(SubStoreGuiderApi.address(SubStoreGuiderApi.Action.ADD),
						JsonObject.mapFrom(new SubStoreGuiderAddRequest(existsUser == null ? null : existsUser.getId(), currentUserId,
								guider.getName(), guider.getMobile(), guider.getShopId(), null)))
				.map(msg -> msg.body().getLong("id"));
	}

}
