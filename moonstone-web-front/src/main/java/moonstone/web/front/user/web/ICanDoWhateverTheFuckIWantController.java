package moonstone.web.front.user.web;

import com.github.jknack.handlebars.internal.Files;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.utils.UserUtil;
import moonstone.event.OrderAutoRefundEvent;
import moonstone.user.service.SellerWriteService;
import moonstone.web.core.events.trade.listener.OrderAutoRefundListener;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

@Profile({"dev", "test", "integration", "stag"})
@Slf4j
@RestController
public class ICanDoWhateverTheFuckIWantController {

    @Resource
    private SellerWriteService sellerWriteService;

    @Resource
    private OrderAutoRefundListener orderAutoRefundListener;

    @PostMapping("/api/test/justDoIt/iCanDoWhateverTheFuckIWant")
    public APIResp<Boolean> doAnything(@RequestBody DoAnythingRequest request) {
        if (UserUtil.getUserId() == null) {
            return APIResp.notLogin();
        }

        if ("update".equals(request.type())) {
            sellerWriteService.updateAnything(request.sql());
        } else if ("insert".equals(request.type())) {
            sellerWriteService.insertAnything(request.sql());
        } else if ("delete".equals(request.type())) {
            sellerWriteService.deleteAnything(request.sql());
        } else if ("ddl".equals(request.type())) {
            sellerWriteService.ddlAnything(request.sql());
        }

        return APIResp.ok(true);
    }

    @PostMapping("/api/test/justDoIt/doAnythingByFile")
    public APIResp<Boolean> doAnythingByFile(@RequestParam(value = "file") MultipartFile file) {
        if (UserUtil.getUserId() == null) {
            return APIResp.notLogin();
        }
        if (file == null || file.isEmpty()) {
            return APIResp.error("入参为空");
        }

        try {
            var sql = Files.read(file.getInputStream());
            sellerWriteService.ddlAnything(sql);
        } catch (Exception ex) {
            log.error("ICanDoWhateverTheFuckIWantController.doAnythingByFile error: ", ex);
            return APIResp.error(ex.getMessage());
        }

        return APIResp.ok(true);
    }

    @PostMapping("/api/test/justDoIt/autoRefund")
    public APIResp<Boolean> autoRefund(@RequestBody AutoRefundRequest request) {
        var event = new OrderAutoRefundEvent(request.shopOrderId, request.orderPushErrorType);
        orderAutoRefundListener.onOrderAutoRefund(event);
        return APIResp.ok(true);
    }

    record AutoRefundRequest(Long shopOrderId, String orderPushErrorType) {
    }

    record DoAnythingRequest(String type, String sql) {
    }
}
