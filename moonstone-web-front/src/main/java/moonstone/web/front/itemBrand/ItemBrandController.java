package moonstone.web.front.itemBrand;

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.model.vo.ComboBoxVo;
import moonstone.common.model.vo.DropDownBoxVo;
import moonstone.common.model.vo.PageVo;
import moonstone.common.utils.UserUtil;
import moonstone.itemBrand.dto.ItemBrandPageDto;
import moonstone.itemBrand.model.ItemBrand;
import moonstone.itemBrand.service.ItemBrandService;
import moonstone.web.core.fileNew.logic.ItemBrandLogic;
import moonstone.web.core.fileNew.logic.ItemsLogic;
import moonstone.web.front.itemBrand.dto.ItemBrandReqDto;
import moonstone.itemBrand.dto.ItemBrandDto;

/**
 * @Author: yousx
 * @Date: 2025/02/21
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping("/api/itemBrand")
@Api("品牌管理")
public class ItemBrandController {

    @Resource
    private ItemBrandLogic itemBrandLogic;
    @Resource
    private ItemsLogic itemsLogic;


    @ApiOperation("保存品牌")
    @PostMapping("/save")
    public Result<String> save(@RequestBody ItemBrandReqDto itemBrandReqDto) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();
        if (shopId == null) {
            throw new ApiException("请先登录");
        }
        if (StringUtils.isBlank(itemBrandReqDto.getName())) {
            throw new ApiException("品牌名称不能为空");
        }
        itemBrandReqDto.setName(StringUtils.trim(itemBrandReqDto.getName()));
        ItemBrandDto dbBrand = itemBrandLogic.findByName(itemBrandReqDto.getName(), shopId);
        if (dbBrand != null) {
            throw new ApiException("品牌已存在");
        }
        ItemBrand itemBrand = new ItemBrand();
        itemBrand.setName(itemBrandReqDto.getName());
        itemBrand.setShopId(shopId);
        itemBrand.setCreatedBy(commonUser.getName());
        itemBrandLogic.save(itemBrand);
        return Result.data("保存成功");
    }

    @ApiOperation("更新品牌")
    @PostMapping("/update")
    public Result<String> update(@RequestBody ItemBrandReqDto itemBrandReqDto) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();
        if (shopId == null) {
            throw new ApiException("请先登录");
        }
        if (itemBrandReqDto.getId() == null) {
            throw new ApiException("id不能为空");
        }
        if (StringUtils.isBlank(itemBrandReqDto.getName())) {
            throw new ApiException("品牌名称不能为空");
        }
        itemBrandReqDto.setName(StringUtils.trim(itemBrandReqDto.getName()));
        ItemBrandDto idBrand = itemBrandLogic.findById(itemBrandReqDto.getId());
        if (idBrand == null) {
            throw new ApiException("品牌不存在");
        }
        if (StringUtils.equals(idBrand.getName(), itemBrandReqDto.getName())) {
            return Result.data("保存成功");
        }


        ItemBrandDto dbBrand = itemBrandLogic.findByName(itemBrandReqDto.getName(), shopId);
        if (dbBrand != null) {
            throw new ApiException("品牌已存在");
        }
        ItemBrand itemBrand = new ItemBrand();
        itemBrand.setId(itemBrandReqDto.getId());
        itemBrand.setName(itemBrandReqDto.getName());
        itemBrandLogic.update(itemBrand);
        return Result.data("保存成功");
    }

    @ApiOperation("品牌详情")
    @PostMapping("/detail")
    public Result<ItemBrandDto> detail(@RequestBody ItemBrandReqDto itemBrandReqDto) {
        if (itemBrandReqDto.getId() == null) {
            throw new ApiException("id 为空");
        }
        ItemBrandDto itemBrandDto = itemBrandLogic.findById(itemBrandReqDto.getId());
        return Result.data(itemBrandDto);
    }


    @ApiOperation("删除品牌")
    @PostMapping("/delete")
    public Result<String> delete(@RequestBody ItemBrandReqDto itemBrandReqDto) {
        if (itemBrandReqDto.getId() == null) {
            throw new ApiException("id 为空");
        }
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();
        Long count = itemsLogic.countItemByBrand(shopId, itemBrandReqDto.getId());
        if (count > 0) {
            throw new ApiException("品牌下有商品，无法删除");
        }
        itemBrandLogic.delete(itemBrandReqDto.getId());
        return Result.data("删除成功");
    }

    @ApiOperation("品牌分页列表")
    @PostMapping("/pages")
    public Result<PageVo<ItemBrandDto>> pages(@RequestBody ItemBrandReqDto itemBrandReqDto) {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            Long shopId = commonUser.getShopId();
            itemBrandReqDto.setShopId(shopId);
            itemBrandReqDto.setName(StringUtils.trim(itemBrandReqDto.getName()));
            PageVo<ItemBrandDto> pages = itemBrandLogic.pages(BeanUtil.copyProperties(itemBrandReqDto, ItemBrandPageDto.class));
            return Result.data(pages);
        } catch (Exception e) {
            log.error("品牌分页列表异常", e);
            throw new RuntimeException(e);
        }
    }


    @ApiOperation("品牌下拉")
    @PostMapping("/drop/down/box")
    public Result<List<ComboBoxVo>> getDropDownBox() {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();
        if (shopId == null) {
            throw new ApiException("请先登录");
        }
        return Result.data(itemBrandLogic.getDropDownBox(shopId));
    }

}
