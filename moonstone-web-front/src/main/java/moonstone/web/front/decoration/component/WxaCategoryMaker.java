package moonstone.web.front.decoration.component;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.open.bean.ma.WxOpenMaCategory;
import me.chanjar.weixin.open.bean.result.WxOpenMaCategoryListResult;
import moonstone.wxOpen.dto.WxaCategory;
import moonstone.wxOpen.dto.WxaFirstCategory;
import moonstone.wxOpen.dto.WxaSecondCategory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/11/23.
 */
@Slf4j
@Component
public class WxaCategoryMaker {
    public List<WxaFirstCategory> make(WxOpenMaCategoryListResult wxOpenMaCategoryListResult){
        List<WxaFirstCategory> firstCategoryList = new ArrayList<>();
        Map<Integer, Integer> firstCategoryMap = new HashMap<>();
        Map<Integer, Map<Integer, Integer>> firstSecondCategoryMap = new HashMap<>();
        for (WxOpenMaCategory wxOpenMaCategory:wxOpenMaCategoryListResult.getCategoryList()) {
            WxaFirstCategory wxaFirstCategory;
            Integer firstIndex = firstCategoryMap.get(wxOpenMaCategory.getFirstId());
            if (firstIndex == null){
                wxaFirstCategory = new WxaFirstCategory();
                wxaFirstCategory.setFirstId(wxOpenMaCategory.getFirstId());
                wxaFirstCategory.setFirstClass(wxOpenMaCategory.getFirstClass());
                List<WxaSecondCategory> secondCategoryList = new ArrayList<>();

                if (wxOpenMaCategory.getSecondId() != null) {   //如果没有二级目录，则跳过
                    WxaSecondCategory wxaSecondCategory = new WxaSecondCategory();
                    wxaSecondCategory.setSecondId(wxOpenMaCategory.getSecondId());
                    wxaSecondCategory.setSecondClass(wxOpenMaCategory.getSecondClass());
                    List<WxaCategory> wxaCategoryList = new ArrayList<>();

                    if (wxOpenMaCategory.getThirdId() != null) {    //如果没有三级目录，则跳过
                        WxaCategory wxaCategory = new WxaCategory();
                        wxaCategory.setCategoryId(wxOpenMaCategory.getThirdId());
                        wxaCategory.setCategoryClass(wxOpenMaCategory.getThirdClass());
                        wxaCategoryList.add(wxaCategory);
                    }

                    wxaSecondCategory.setThirdCategoryList(wxaCategoryList);
                    secondCategoryList.add(wxaSecondCategory);
                    Map<Integer, Integer> secondCategoryMap = new HashMap<>();
                    secondCategoryMap.put(wxaSecondCategory.getSecondId(), secondCategoryList.size() - 1);
                    firstSecondCategoryMap.put(wxaFirstCategory.getFirstId(), secondCategoryMap);
                }

                wxaFirstCategory.setSecondCategoryList(secondCategoryList);
                firstCategoryList.add(wxaFirstCategory);
                firstCategoryMap.put(wxaFirstCategory.getFirstId(), firstCategoryList.size()-1);
            } else {
                if (wxOpenMaCategory.getSecondId() == null){    //若没有二级目录，直接继续循环
                    continue;
                }
                wxaFirstCategory = firstCategoryList.get(firstIndex);
                List<WxaSecondCategory> secondCategoryList = wxaFirstCategory.getSecondCategoryList();
                Integer secondIndex = firstSecondCategoryMap.get(wxaFirstCategory.getFirstId()).get(wxOpenMaCategory.getSecondId());
                WxaSecondCategory wxaSecondCategory;
                if (secondIndex == null){
                    wxaSecondCategory = new WxaSecondCategory();
                    wxaSecondCategory.setSecondId(wxOpenMaCategory.getSecondId());
                    wxaSecondCategory.setSecondClass(wxOpenMaCategory.getSecondClass());
                    List<WxaCategory> wxaCategoryList = new ArrayList<>();

                    if (wxOpenMaCategory.getThirdId() != null) {    //如果没有三级目录，则跳过
                        WxaCategory wxaCategory = new WxaCategory();
                        wxaCategory.setCategoryId(wxOpenMaCategory.getThirdId());
                        wxaCategory.setCategoryClass(wxOpenMaCategory.getThirdClass());
                        wxaCategoryList.add(wxaCategory);
                    }

                    wxaSecondCategory.setThirdCategoryList(wxaCategoryList);
                    secondCategoryList.add(wxaSecondCategory);

                    Map<Integer,Integer> secondCategoryMap = new HashMap<>();
                    secondCategoryMap.put(wxaSecondCategory.getSecondId(), secondCategoryList.size()-1);
                    firstSecondCategoryMap.put(wxaFirstCategory.getFirstId(), secondCategoryMap);
                } else {
                    if (wxOpenMaCategory.getThirdId() == null) {    //若没有三级目录，直接继续循环
                        continue;
                    }
                    wxaSecondCategory = secondCategoryList.get(secondIndex);
                    List<WxaCategory> wxaCategoryList = wxaSecondCategory.getThirdCategoryList();
                    WxaCategory wxaCategory = new WxaCategory();
                    wxaCategory.setCategoryId(wxOpenMaCategory.getThirdId());
                    wxaCategory.setCategoryClass(wxOpenMaCategory.getThirdClass());
                    wxaCategoryList.add(wxaCategory);
                    wxaSecondCategory.setThirdCategoryList(wxaCategoryList);
                }
                wxaFirstCategory.setSecondCategoryList(secondCategoryList);
            }
        }
        return firstCategoryList;
    }
}
