package moonstone.web.front.open.app;

import io.terminus.pampas.openplatform.annotations.OpenBean;
import io.terminus.pampas.openplatform.annotations.OpenMethod;
import io.terminus.pampas.openplatform.exceptions.OPClientException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.UserUtil;
import moonstone.web.front.component.promotion.CouponCenter;
import moonstone.web.front.promotions.dto.CouponState;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * Author:cp
 * Created on 13/02/2017.
 */
@OpenBean
@Slf4j
public class CouponApi {

    private final CouponCenter couponCenter;

    @Autowired
    public CouponApi(CouponCenter couponCenter) {
        this.couponCenter = couponCenter;
    }

    /**
     * 查询适用于指定商品的优惠券列表(给商品详情用)
     *
     * @param itemId 商品id
     * @return 适用的优惠券列表
     */
    @OpenMethod(key = "item.coupon.list", paramNames = {"itemId"}, httpMethods = RequestMethod.GET)
    public List<CouponState> findAvailableItemCouponByItemId(Long itemId) {
        return couponCenter.findAvailableItemCouponByItemId(itemId);
    }

    /**
     * 查询适用于指定店铺的优惠券列表(给购物车页面使用)
     *
     * @param shopId 店铺id
     * @return 适用的优惠券列表
     */
    @OpenMethod(key = "shop.coupon.list", paramNames = {"shopId"}, httpMethods = RequestMethod.GET)
    public List<CouponState> findAvailableShopCouponByShopId(Long shopId) {
        return couponCenter.findAvailableShopCouponByShopId(shopId);
    }

    /**
     * 根据店铺id查询店铺的运费券列表
     *
     * @param shopId 店铺id
     * @return 店铺的运费券列表
     */
    @OpenMethod(key = "shop.shipment.coupon.list", paramNames = {"shopId"}, httpMethods = RequestMethod.GET)
    public List<CouponState> findAvailableShipmentCouponByShopId(Long shopId) {
        return couponCenter.findAvailableShipmentCouponByShopId(shopId);
    }

    @OpenMethod(key = "coupon.receive", paramNames = {"promotionId"}, httpMethods = RequestMethod.POST)
    public boolean receiveCoupon(Long promotionId) {
        if (UserUtil.getCurrentUser() == null) {
            throw new OPClientException(401, "user.not.login");
        }
        return couponCenter.receiveCoupon(promotionId);
    }

}
