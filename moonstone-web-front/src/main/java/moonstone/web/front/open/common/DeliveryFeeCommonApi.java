package moonstone.web.front.open.common;

import com.google.common.base.MoreObjects;
import io.terminus.pampas.openplatform.annotations.OpenBean;
import io.terminus.pampas.openplatform.annotations.OpenMethod;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.api.DeliveryFeeCharger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.constraints.NotNull;

/**
 * Author:cp
 * Created on 04/11/2016.
 */
@OpenBean
@Slf4j
public class DeliveryFeeCommonApi {

    @Autowired
    private DeliveryFeeCharger deliveryFeeCharger;

    @OpenMethod(key = "delivery.fee.charge.for.sku", paramNames = {"skuId", "quantity", "addressId"}, httpMethods = RequestMethod.GET)
    public Integer chargeForSku(@NotNull(message = "sku.id.not.null") Long skuId,
                                Integer quantity,
                                @NotNull(message = "address.id.not.null") Integer addressId) {
        return deliveryFeeCharger.charge(skuId, MoreObjects.firstNonNull(quantity, 1), addressId);
    }

}
