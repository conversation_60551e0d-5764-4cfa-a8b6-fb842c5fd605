package moonstone.web.front.open.outer;

import com.google.common.base.MoreObjects;
import com.google.common.base.Objects;
import com.google.common.base.Strings;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import io.terminus.common.utils.BeanMapper;
import io.terminus.pampas.openplatform.annotations.OpenBean;
import io.terminus.pampas.openplatform.annotations.OpenMethod;
import io.terminus.pampas.openplatform.exceptions.OPClientException;
import io.terminus.pampas.openplatform.exceptions.OPServerException;
import io.terminus.pampas.openplatform.utils.ClientUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.*;
import moonstone.brand.model.Brand;
import moonstone.brand.service.BrandReadService;
import moonstone.brand.service.BrandWriteService;
import moonstone.common.model.CommonUser;
import moonstone.item.api.InitialItemInfoFiller;
import moonstone.item.dto.FullItem;
import moonstone.item.dto.ImageInfo;
import moonstone.item.model.Item;
import moonstone.item.model.ItemDetail;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.ItemWriteService;
import moonstone.item.service.SkuCustomReadService;
import moonstone.item.service.SkuReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.web.front.open.dto.OpenItem;
import moonstone.web.front.open.dto.OpenSku;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;
import java.util.Map;

/**
 * Author:cp
 * Created on 8/29/16.
 */
@OpenBean
@Slf4j
public class ItemApi {

    @RpcConsumer
    private ItemWriteService itemWriteService;

    @RpcConsumer
    private ItemReadService itemReadService;

    @RpcConsumer
    private BrandReadService brandReadService;

    @RpcConsumer
    private BrandWriteService brandWriteService;

    @RpcConsumer
    private ShopReadService shopReadService;

    @RpcConsumer
    private SkuReadService skuService;

    @RpcConsumer
    private SkuCustomReadService skuCustomReadService;

    @Autowired
    private InitialItemInfoFiller initialItemInfoFiller;

    @OpenMethod(key = "item.nospu.create", paramNames = {"data"}, httpMethods = RequestMethod.POST)
    public Long createItemByCode(OpenItem openItem) {
        Long userId = ClientUtil.get().getClientId();

        OpenItem.TinyItem tinyItem = openItem.getItem();
        String itemCode = tinyItem.getItemCode();
        if (Strings.isNullOrEmpty(itemCode)) {
            throw new OPClientException(400, "item.code.not.provided");
        }

        Shop shop = findShopByUserId(userId);

        //检查是否重复创建
        Item existedItem = findItemByShopIdAndCode(shop.getId(), itemCode);
        if (existedItem != null) {
            log.warn("duplicated item for user(id={}) and itemCode={}", userId, itemCode);
            return existedItem.getId();
        }

        //检查是否需要创建品牌
        String brandName = tinyItem.getBrandName();
        Brand brand = createBrandIfNecessary(userId, brandName);

        //构建商品
        Item item = new Item();
        BeanMapper.copy(tinyItem, item);
        item.setBrandId(brand.getId());
        item.setBrandName(brand.getName());

        initialItemInfoFiller.fill(item);
        fillShopInfo(item, shop);

        //构建sku
        List<Sku> skus = buildSku(openItem.getSkus());

        extractInfoFromSkus(item, skus);

        FullItem fullItem = new FullItem();
        fullItem.setItem(item);
        fullItem.setSkuWithCustoms(skuCustomReadService.makeSkuWithCustomsBySkus(skus));
        fullItem.setItemDetail(buildItemDetail(openItem.getItemDetail()));
        fullItem.setGroupedSkuAttributes(groupSkuAttribute(openItem.getSkuAttributes()));
        fullItem.setGroupedOtherAttributes(groupOtherAttribute(openItem.getOtherAttributes()));

        //创建商品信息
        Response<Long> rcItem = itemWriteService.create(fullItem);
        if (!rcItem.isSuccess()) {
            log.error("failed to create item:{} for user(id={})", fullItem, userId);
            throw new OPServerException(500, rcItem.getError());
        }
        return rcItem.getResult();
    }

    private void extractInfoFromSkus(Item item, List<Sku> skus) {
        //计算商品库存
        if (item.getStockType() == 1) {
            int stockQuantity = 0;
            for (Sku sku : skus) {
                stockQuantity += sku.getStockQuantity();
            }
            item.setStockQuantity(stockQuantity);
        }

        int highPrice = -1;
        int lowPrice = -1;

        for (Sku sku : skus) {
            if (sku.getPrice() != null) {
                if (sku.getPrice() > highPrice) {
                    highPrice = sku.getPrice();
                }
                if (sku.getPrice() < lowPrice || lowPrice < 0) {
                    lowPrice = sku.getPrice();
                }
            }
        }
        if (highPrice > 0) {
            item.setHighPrice(highPrice);
        }
        if (lowPrice > 0) {
            item.setLowPrice(lowPrice);
        }
    }

    private Brand createBrandIfNecessary(Long userId, String brandName) {
        if (Strings.isNullOrEmpty(brandName)) {
            log.error("no brand name specified for user(id={})'s when create item", userId);
            throw new OPServerException(500, "item.brand.not.provided");
        }

        Response<Boolean> checkBrand = brandReadService.exist(brandName);
        if (!checkBrand.isSuccess()) {
            log.error("failed to check brand if existed with brandName({}),error code:{}", brandName, checkBrand.getError());
            throw new OPServerException(500, checkBrand.getError());
        }

        Brand brand;
        if (checkBrand.getResult()) {
            Response<Brand> rBrand = brandReadService.findByName(brandName);
            if (!rBrand.isSuccess()) {
                log.error("failed to find brand by brandName({}), error code:{}", brandName, rBrand.getError());
                throw new OPServerException(500, rBrand.getError());
            }
            brand = rBrand.getResult();
        } else {
            brand = new Brand();
            brand.setName(brandName);
            Response<Long> r = brandWriteService.create(brand);
            if (!r.isSuccess()) {
                log.error("failed to create brand(name={}), error code:{}", brandName, r.getError());
                throw new OPServerException(500, r.getError());
            }
            brand.setId(r.getResult());
        }
        return brand;
    }

    private List<Sku> buildSku(List<OpenSku> skus) {
        List<Sku> result = Lists.newArrayListWithCapacity(skus.size());
        for (OpenSku openSku : skus) {
            Sku sku = new Sku();
            BeanMapper.copy(openSku, sku);
            sku.setStockQuantity(openSku.getQuantity());
            result.add(sku);
        }
        return result;
    }

    private void fillShopInfo(Item item, Shop shop) {
        item.setShopId(shop.getId());
        item.setShopName(shop.getName());
    }


    @OpenMethod(key = "item.update.by.code", paramNames = {"data"}, httpMethods = RequestMethod.POST)
    public Boolean updateItemByCode(OpenItem openItem) {
        OpenItem.TinyItem tinyItem = openItem.getItem();
        String itemCode = tinyItem.getItemCode();
        if (Strings.isNullOrEmpty(itemCode)) {
            throw new OPClientException(400, "item.code.not.provided");
        }

        Long userId = ClientUtil.get().getClientId();
        Shop shop = findShopByUserId(userId);

        Item existedItem = findItemByShopIdAndCode(shop.getId(), itemCode);
        if (existedItem == null) {
            log.error("item not found by shop id={},itemCode={}", shop.getId(), itemCode);
            throw new OPClientException(400, "item.not.found");
        }

        Long itemId = existedItem.getId();
        tinyItem.setId(itemId);

        //检查是否需要创建品牌
        String brandName = tinyItem.getBrandName();
        Brand brand = createBrandIfNecessary(userId, brandName);

        //构建商品
        Item item = new Item();
        BeanMapper.copy(tinyItem, item);
        item.setStockType(existedItem.getStockType());
        item.setBrandId(brand.getId());
        item.setBrandName(brand.getName());

        fillShopInfo(item, shop);

        //构建sku
        List<Sku> skus = buildSku(openItem.getSkus());

        extractInfoFromSkus(item, skus);

        Response<List<Sku>> rSku = skuService.findSkusByItemId(itemId);
        if (!rSku.isSuccess()) {
            log.error("failed to find sku of item(id={})", itemId);
            throw new OPServerException(500, rSku.getError());
        }

        for (Sku sku : skus) {
            if (Strings.isNullOrEmpty(sku.getSkuCode())) {
                log.error("sku code not provided for user(id={}) and data={}", userId, openItem);
                throw new OPClientException(500, "sku.code.not.provided");
            }
            for (Sku skuInDB : rSku.getResult()) {
                if (Objects.equal(skuInDB.getSkuCode(), sku.getSkuCode())) {
                    sku.setId(skuInDB.getId());
                    break;
                } else {
                    sku.setStatus(-1);
                }
            }
        }

        FullItem fullItem = new FullItem();
        fullItem.setItem(item);
        fullItem.setSkuWithCustoms(skuCustomReadService.makeSkuWithCustomsBySkus(skus));

        OpenItem.TinyItemDetail tinyItemDetail = openItem.getItemDetail();
        if (tinyItemDetail != null) {
            ItemDetail itemDetail = new ItemDetail();
            itemDetail.setPacking(itemDetail.getPacking());
            itemDetail.setService(itemDetail.getService());
            itemDetail.setImages(buildImage(tinyItemDetail.getImages()));
            fullItem.setItemDetail(itemDetail);
        }

        fullItem.setGroupedSkuAttributes(groupSkuAttribute(openItem.getSkuAttributes()));
        fullItem.setGroupedOtherAttributes(groupOtherAttribute(openItem.getOtherAttributes()));

        Response<Boolean> ruItem = itemWriteService.update(fullItem);
        if (!ruItem.isSuccess()) {
            log.error("failed to update item({}) for user(id={}), error code:{}", fullItem, userId, ruItem.getError());
            throw new OPServerException(500, ruItem.getError());
        }

        //update richText
        if (tinyItemDetail != null) {
            String richText = tinyItemDetail.getDetail();
            if (!Strings.isNullOrEmpty(richText)) {
                Response<Boolean> editResp = itemWriteService.editRichText(itemId, richText);
                if (!editResp.isSuccess()) {
                    log.error("fail to edit rich text({}) where itemId={},cause:{}", richText, itemId, editResp.getError());
                    throw new OPServerException(500, editResp.getError());
                }
            }
        }

        return Boolean.TRUE;
    }

    @OpenMethod(key = "item.status.update.by.code", paramNames = {"itemCode", "status"}, httpMethods = RequestMethod.POST)
    public Boolean updateItemStatusByCode(String itemCode, Integer status) {

        Long userId = ClientUtil.get().getClientId();
        if (status > 1 || status < -3) {
            log.error("illegal request param (item status={}) ", status);
            throw new OPClientException(400, "illegal.item.status");
        }

        Shop shop = findShopByUserId(userId);
        Item existedItem = findItemByShopIdAndCode(shop.getId(), itemCode);
        if (existedItem == null) {
            log.error("item not found by shop id={},itemCode={}", shop.getId(), itemCode);
            throw new OPClientException(400, "item.not.found");
        }

        Response<Boolean> updateResp = itemWriteService.updateStatusByItemId(existedItem.getId(), status);
        if (!updateResp.isSuccess()) {
            log.error("fail to update item status to {} by itemCode={},cause:{}",
                    status, itemCode, updateResp.getError());
            throw new OPServerException(updateResp.getError());
        }
        return updateResp.getResult();
    }

    private Shop findShopByUserId(Long userId) {
        Response<Shop> rShop = shopReadService.findByUserId(userId);
        if (!rShop.isSuccess()) {
            log.error("failed to find shop for user(id={}), error code:{}", userId, rShop.getError());
            throw new OPServerException(500, rShop.getError());
        }
        return rShop.getResult();
    }

    private Item findItemByShopIdAndCode(Long shopId, String itemCode) {
        CommonUser commonUser = new CommonUser();
        commonUser.setShopId(shopId);

        Response<List<Item>> rItems = itemReadService.findByShopIdAndCode(commonUser.getShopId(), itemCode);
        if (!rItems.isSuccess()) {
            log.error("fail to find item by shopId:{} and itemCode:{},cause:{}", shopId, itemCode, rItems.getError());
            throw new OPServerException(500, rItems.getError());
        }

        if (rItems.getResult().isEmpty()) {
            return null;
        }

        return Iterables.getFirst(rItems.getResult(), null);
    }

    private List<GroupedSkuAttribute> groupSkuAttribute(List<SkuAttribute> skuAttributes) {
        if (skuAttributes == null) {
            return null;
        }

        Map<String, List<SkuAttribute>> skuAttrGroup = Maps.newHashMap();
        for (SkuAttribute skuAttribute : skuAttributes) {
            final String keyGroup = MoreObjects.firstNonNull(skuAttribute.getAttrKey(), "default");
            List<SkuAttribute> attrs = skuAttrGroup.get(keyGroup);
            if (attrs == null) {
                attrs = Lists.newArrayList();
                skuAttrGroup.put(keyGroup, attrs);
            }
            if (attrs.contains(skuAttribute)) {
                log.warn("duplicated sku attribute: {}", skuAttribute);
                continue;
            }
            attrs.add(skuAttribute);
        }

        List<GroupedSkuAttribute> groupedSkuAttributes = Lists.newArrayList();
        for (Map.Entry<String, List<SkuAttribute>> entry : skuAttrGroup.entrySet()) {
            GroupedSkuAttribute groupedSkuAttribute = new GroupedSkuAttribute(entry.getKey(), entry.getValue());
            groupedSkuAttributes.add(groupedSkuAttribute);
        }

        return groupedSkuAttributes;
    }

    private List<GroupedOtherAttribute> groupOtherAttribute(List<OtherAttribute> otherAttributes) {
        if (otherAttributes == null) {
            return null;
        }

        Map<String, List<OtherAttribute>> otherAttrGroup = Maps.newHashMap();
        for (OtherAttribute otherAttribute : otherAttributes) {
            if (isSkippedAttribute(otherAttribute.getAttrKey())) {
                continue;
            }
            final String keyGroup = MoreObjects.firstNonNull(otherAttribute.getGroup(), PreservedGroup.DEFAULT.name());
            List<OtherAttribute> attrs = otherAttrGroup.get(keyGroup);
            if (attrs == null) {
                attrs = Lists.newArrayList();
                otherAttrGroup.put(keyGroup, attrs);
            }
            if (attrs.contains(otherAttribute)) {
                log.warn("duplicated other attribute: {}", otherAttribute);
                continue;
            }
            attrs.add(otherAttribute);
        }

        List<GroupedOtherAttribute> groupedOtherAttributes = Lists.newArrayList();
        for (Map.Entry<String, List<OtherAttribute>> entry : otherAttrGroup.entrySet()) {
            GroupedOtherAttribute groupedOtherAttribute = new GroupedOtherAttribute(entry.getKey(), entry.getValue());
            groupedOtherAttributes.add(groupedOtherAttribute);
        }
        return groupedOtherAttributes;
    }

    private boolean isSkippedAttribute(String attrKey) {
        return "品牌".equals(attrKey);
    }

    private List<ImageInfo> buildImage(List<String> images) {
        if (images == null) {
            return null;
        }

        List<ImageInfo> imageInfos = Lists.newArrayListWithCapacity(images.size());
        for (String image : images) {
            ImageInfo imageInfo = new ImageInfo();
            imageInfo.setUrl(image);
            imageInfos.add(imageInfo);
        }
        return imageInfos;
    }

    private ItemDetail buildItemDetail(OpenItem.TinyItemDetail tinyItemDetail) {
        if (tinyItemDetail == null) {
            return null;
        }
        ItemDetail itemDetail = new ItemDetail();
        itemDetail.setService(tinyItemDetail.getService());
        itemDetail.setPacking(tinyItemDetail.getPacking());
        itemDetail.setDetail(tinyItemDetail.getDetail());
        itemDetail.setImages(buildImage(tinyItemDetail.getImages()));
        return itemDetail;
    }

}
