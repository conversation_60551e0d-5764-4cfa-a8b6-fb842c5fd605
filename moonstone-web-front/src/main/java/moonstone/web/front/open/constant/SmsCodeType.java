package moonstone.web.front.open.constant;

import lombok.Getter;

/**
 * Author:cp
 * Created on 9/6/16.
 */
public enum SmsCodeType {

    USER_SIGN(0, "user_sign_mobile_code", "注册验证"),
    FORGOT_PASSWORD(1, "forgot_password_mobile_code", "身份验证"),
    CHANGE_MOBILE(2, "change_mobile_mobile_code", "手机号验证");

    /**
     * 类型
     */
    @Getter
    private final int type;

    /**
     * session中保存验证码的key
     */
    @Getter
    private final String key;

    /**
     * 短信模板的key
     */
    @Getter
    private final String template;

    SmsCodeType(int type, String key, String template) {
        this.type = type;
        this.key = key;
        this.template = template;
    }

    public static SmsCodeType from(int type) {
        for (SmsCodeType smsCodeType : SmsCodeType.values()) {
            if (smsCodeType.type == type) {
                return smsCodeType;
            }
        }
        throw new IllegalArgumentException("unknown sms code type");
    }

    @Override
    public String toString() {
        return key;
    }
}
