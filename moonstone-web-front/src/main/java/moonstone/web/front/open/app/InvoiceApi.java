package moonstone.web.front.open.app;

import com.google.common.base.Objects;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.pampas.openplatform.annotations.OpenBean;
import io.terminus.pampas.openplatform.annotations.OpenMethod;
import io.terminus.pampas.openplatform.exceptions.OPClientException;
import io.terminus.pampas.openplatform.exceptions.OPServerException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.UserUtil;
import moonstone.order.model.Invoice;
import moonstone.order.service.InvoiceReadService;
import moonstone.order.service.InvoiceWriteService;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * Author:cp
 * Created on 8/26/16.
 */
@OpenBean
@Slf4j
public class InvoiceApi {

    @RpcConsumer
    private InvoiceReadService invoiceReadService;

    @RpcConsumer
    private InvoiceWriteService invoiceWriteService;

    @OpenMethod(key = "invoice.list", httpMethods = RequestMethod.GET)
    public List<Invoice> listInvoice() {
        Long userId = checkIfLogin();
        Response<List<Invoice>> findInvoice = invoiceReadService.findByUserId(userId);
        if (!findInvoice.isSuccess()) {
            log.error("fail to find invoice for user(id={}),cause:{}",
                    userId, findInvoice.getError());
            throw new JsonResponseException(findInvoice.getError());
        }
        return findInvoice.getResult();
    }

    @OpenMethod(key = "invoice.create", paramNames = {"invoice"}, httpMethods = RequestMethod.POST)
    public Long createInvoice(Invoice invoice) {
        final Long userId = checkIfLogin();
        invoice.setUserId(userId);
        invoice.setStatus(1);

        Response<Long> createResp = invoiceWriteService.createInvoice(invoice);
        if (!createResp.isSuccess()) {
            log.error("fail to create invoice:{},cause:{}", invoice, createResp.getError());
            throw new OPServerException(createResp.getError());
        }
        return createResp.getResult();
    }

    @OpenMethod(key = "invoice.delete", paramNames = {"id"}, httpMethods = RequestMethod.POST)
    public boolean deleteInvoice(Long invoiceId) {
        checkInvoice(invoiceId);

        Response<Boolean> deleteResp = invoiceWriteService.deleteInvoice(invoiceId);
        if (!deleteResp.isSuccess()) {
            log.error("fail to delete invoice where id={},cause:{}",
                    invoiceId, deleteResp.getError());
            throw new OPServerException(deleteResp.getError());
        }
        return deleteResp.getResult();
    }

    @OpenMethod(key = "invoice.default", paramNames = {"id"}, httpMethods = RequestMethod.PUT)
    public boolean setDefaultInvoice(Long invoiceId) {
        checkInvoice(invoiceId);

        final Long userId = UserUtil.getUserId();
        Response<Boolean> setResp = invoiceWriteService.setDefault(userId, invoiceId);
        if (!setResp.isSuccess()) {
            log.error("user(id={}) fail to set invoice(id={}) as default,cause:{}",
                    userId, invoiceId, setResp.getError());
            throw new OPServerException(setResp.getError());
        }
        return setResp.getResult();
    }

    private void checkInvoice(Long invoiceId) {
        final Long userId = checkIfLogin();

        Response<Invoice> findResp = invoiceReadService.findInvoiceById(invoiceId);
        if (!findResp.isSuccess()) {
            log.error("fail to find invoice by id:{},cause:{}", invoiceId, findResp.getError());
            throw new OPServerException(findResp.getError());
        }
        Invoice invoice = findResp.getResult();

        if (!Objects.equal(invoice.getUserId(), userId)) {
            log.error("the invoice(id={}) is not belong to buyer(id={})",
                    invoiceId, userId);
            throw new OPServerException("invoice.not.belong.to.buyer");
        }
    }

    private Long checkIfLogin() {
        final Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new OPClientException("user.not.login");
        }
        return userId;
    }

}
