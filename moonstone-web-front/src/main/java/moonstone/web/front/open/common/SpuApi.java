package moonstone.web.front.open.common;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.pampas.openplatform.annotations.OpenBean;
import io.terminus.pampas.openplatform.annotations.OpenMethod;
import io.terminus.pampas.openplatform.exceptions.OPServerException;
import lombok.extern.slf4j.Slf4j;
import moonstone.component.dto.spu.EditSpu;
import moonstone.component.spu.component.SpuReader;
import moonstone.spu.model.Spu;
import moonstone.spu.service.SpuReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * Author:cp
 * Created on 8/29/16.
 */
@OpenBean
@Slf4j
public class SpuApi {

    @Autowired
    private SpuReader spuReader;

    @RpcConsumer
    private SpuReadService spuReadService;

    @OpenMethod(key = "spu.find.by.category.id", paramNames = {"categoryId", "keyword", "pageNo", "pageSize"}, httpMethods = RequestMethod.GET)
    public Paging<Spu> findByCategoryId(Long categoryId, String keyword,
                                        Integer pageNo, Integer pageSize) {
        Response<Paging<Spu>> findSpu = spuReadService.findByCategoryId(categoryId, keyword, pageNo, pageSize);
        if (!findSpu.isSuccess()) {
            log.error("failed to find spu by categoryId={},keyword={},pageNo={},pageSize={},error code:{}",
                    categoryId, keyword, pageNo, pageSize, findSpu.getError());
            throw new OPServerException(findSpu.getError());
        }
        return findSpu.getResult();
    }

    @OpenMethod(key = "spu.detail.find.by.id", paramNames = {"id"}, httpMethods = RequestMethod.GET)
    public EditSpu findForEdit(Long id) {
        Response<EditSpu> findResp = spuReader.findForEdit(id);
        if (!findResp.isSuccess()) {
            log.error("fail to find spu(id={}) for edit,cause:{}",
                    id, findResp.getError());
            throw new OPServerException(findResp.getError());
        }
        return findResp.getResult();
    }

}
