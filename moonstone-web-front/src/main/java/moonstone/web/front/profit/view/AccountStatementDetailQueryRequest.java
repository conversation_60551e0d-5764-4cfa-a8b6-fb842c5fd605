package moonstone.web.front.profit.view;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class AccountStatementDetailQueryRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 2291173344940785441L;

    /**
     * 账单id
     */
    private Long accountStatementId;

    private Integer currentPage;

    private Integer pageSize;
}
