package moonstone.web.front.profit.slice;

import moonstone.web.core.image.application.ImageCache;
import moonstone.web.core.util.FontUtil;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface SignDrawSlice {

    /**
     * 为特定合同合成图片
     *
     * @param backgroundImgAddress 图片地址
     * @param sign                 签名
     * @param address              所在地址
     * @param identityCode         身份证号
     * @param mobile               手机号
     * @return 合成完毕的图片
     * @throws IOException 错误异常
     */
    static BufferedImage drawSignImage(String backgroundImgAddress, BufferedImage sign, String name, String address, String identityCode, String mobile) throws IOException {
        BufferedImage img = ImageCache.readImage(backgroundImgAddress);
        Graphics2D graphics2D = img.createGraphics();
        int x = 320 * 2, y = 2 * 300, w = 2 * 180, h = 2 * 180;
        int textX = 2 * 160, textY = 2 * 385;
        graphics2D.setFont(FontUtil.getSiYuanFont().deriveFont(Font.PLAIN, 20));
        graphics2D.setColor(Color.BLACK);
        graphics2D.drawString(Optional.ofNullable(name).orElse(""), textX, textY - 34);
        graphics2D.drawString(Optional.ofNullable(address).orElse(""), textX, textY);
        graphics2D.drawString(Optional.ofNullable(identityCode).orElse(""), textX, textY + 34);
        graphics2D.drawString(Optional.ofNullable(mobile).orElse(""), textX, textY + 34 * 2);
        graphics2D.drawImage(sign, x, y, w, h, null, null);
        graphics2D.dispose();
        return img;
    }
}
