package moonstone.web.front.profit.application;

import lombok.extern.slf4j.Slf4j;
import moonstone.order.model.mongo.AgentPayOrderRecord;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AgentPayOrderCreateRecordApp {

    @Resource
    private MongoTemplate mongoTemplate;

    private static final Long TRIGGER_INTERVAL = 3 * 60 * 1000L;

    public void createRecord(Long shopOrderId) {
        var result = mongoTemplate.upsert(Query.query(Criteria.where("shopOrderId").is(shopOrderId))
                        .addCriteria(Criteria.where("status").is(AgentPayOrderRecord.AgentPayOrderRecordStatus.NOT_CREATED.getCode())),
                Update.update("createdAt", new Date())
                        .set("triggerAfter", System.currentTimeMillis() + TRIGGER_INTERVAL)
                        .set("turn", 1), AgentPayOrderRecord.class).getUpsertedId();

        if (result == null) {
            throw new RuntimeException("AgentPayOrderCreateRecord创建失败");
        }
    }

    public List<AgentPayOrderRecord> findNotCreated(Long now, Integer pageSize) {
        var query = Query.query(Criteria.where("triggerAfter").lte(now))
                .addCriteria(Criteria.where("status").is(AgentPayOrderRecord.AgentPayOrderRecordStatus.NOT_CREATED.getCode()))
                .skip(0)
                .limit(pageSize);

        return mongoTemplate.find(query, AgentPayOrderRecord.class);
    }

    public void updateCreateStatus(AgentPayOrderRecord record, AgentPayOrderRecord.AgentPayOrderRecordStatus status) {
        updateCreateStatus(record.getShopOrderId(), status);
    }

    public void updateCreateStatus(Long shopOrderId, AgentPayOrderRecord.AgentPayOrderRecordStatus status) {
        mongoTemplate.updateFirst(Query.query(Criteria.where("shopOrderId").is(shopOrderId)),
                new Update().set("status", status.getCode()), AgentPayOrderRecord.class);
    }

    public void delayTriggerAfter(AgentPayOrderRecord record) {
        mongoTemplate.upsert(Query.query(Criteria.where("shopOrderId").is(record.getShopOrderId())),
                new Update().inc("turn", 1)
                        .set("triggerAfter", System.currentTimeMillis() + Math.min(10 * TRIGGER_INTERVAL, record.getTurn() * TRIGGER_INTERVAL)),
                AgentPayOrderRecord.class);
    }

    /**
     * @param shopOrderIds
     * @return key = shopOrderId
     */
    public Map<Long, AgentPayOrderRecord> findMapByShopOrderIds(List<Long> shopOrderIds) {
        if (CollectionUtils.isEmpty(shopOrderIds)) {
            return Collections.emptyMap();
        }

        var list = mongoTemplate.find(
                Query.query(Criteria.where("shopOrderId").in(shopOrderIds)), AgentPayOrderRecord.class);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(AgentPayOrderRecord::getShopOrderId, o -> o, (k1, k2) -> k1));
    }
}
