package moonstone.web.front.profit.view;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class WithdrawProfitApplyQueryRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = -539405561378482094L;

    private Long shopId;

    private Integer currentPage;

    private Integer pageSize;

    /**
     * 用户手机号
     */
    private String userMobile;

    /**
     * 用户角色
     */
    private Integer userRole;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 提现单状态
     */
    private Integer status;
}
