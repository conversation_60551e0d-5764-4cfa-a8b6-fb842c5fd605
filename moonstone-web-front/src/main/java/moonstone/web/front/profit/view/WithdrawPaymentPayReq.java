package moonstone.web.front.profit.view;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class WithdrawPaymentPayReq implements Serializable {
    @Serial
    private static final long serialVersionUID = 5428858564012171378L;

    /**
     * 提现申请单id
     */
    private Long withdrawApplyId;


    /**
     * 批量提现申请单id List
     */
    private List<Long> withdrawApplyIdList;
}
