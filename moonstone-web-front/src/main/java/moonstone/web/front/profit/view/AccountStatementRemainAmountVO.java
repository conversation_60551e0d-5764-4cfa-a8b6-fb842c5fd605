package moonstone.web.front.profit.view;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class AccountStatementRemainAmountVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 662469541346522863L;

    /**
     * 可提现的账单余额
     */
    private Long remainAmount;
}
