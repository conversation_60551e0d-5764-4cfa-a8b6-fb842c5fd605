package moonstone.web.front.profit.application;

import lombok.extern.slf4j.Slf4j;
import moonstone.order.api.payoperation.withdraw.WithdrawCallbackResult;
import moonstone.order.api.payoperation.withdraw.WithdrawOperation;
import moonstone.order.api.payoperation.withdraw.WithdrawRequestResult;
import moonstone.order.dto.PaymentForEnterpriseWithdrawCriteria;
import moonstone.order.enu.EnterpriseWithdrawStatusEnum;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.model.EnterpriseWithdraw;
import moonstone.order.model.EnterpriseWithdrawDetail;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.result.PaymentForEnterpriseWithdrawDO;
import moonstone.order.service.*;
import moonstone.web.front.profit.convert.EnterpriseWithdrawConvertor;
import moonstone.web.front.profit.view.PaymentAndOrderPairVO;
import moonstone.web.front.profit.view.WaitingWithdrawOrderSelectAllVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通联支付云商通，小B提现
 */
@Slf4j
@Component
public class EnterpriseWithdrawApp {

    @Resource
    private PaymentReadService paymentReadService;

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Resource
    private EnterpriseWithdrawConvertor enterpriseWithdrawConvertor;

    @Resource
    private EnterpriseWithdrawWriteService enterpriseWithdrawWriteService;

    @Resource
    private EnterpriseWithdrawReadService enterpriseWithdrawReadService;

    @Resource
    private EnterpriseWithdrawDetailWriteService enterpriseWithdrawDetailWriteService;

    @Resource
    private EnterpriseWithdrawDetailReadService enterpriseWithdrawDetailReadService;

    @Autowired
    private List<WithdrawOperation> enterpriseWithdrawOperations;

    /**
     * 前端页面的全选操作
     *
     * @param criteria
     * @param selectedMap key = paymentId, value = shopOrderId, 用户在前端勾选的订单信息
     * @return
     */
    public WaitingWithdrawOrderSelectAllVO selectAllWaitingOrder(PaymentForEnterpriseWithdrawCriteria criteria, Map<Long, Long> selectedMap) {
        var resultList = new ArrayList<PaymentAndOrderPairVO>();

        criteria.setPageSize(500);
        for (int currentPage = 1; ; currentPage++) {
            criteria.setPageNo(currentPage);
            var page = paymentReadService.pageListForEnterpriseWithdraw(criteria).getResult();
            if (CollectionUtils.isEmpty(page)) {
                break;
            }

            var subList = buildSelected(page, selectedMap);
            if (!CollectionUtils.isEmpty(subList)) {
                resultList.addAll(subList);
            }
        }

        return convert(resultList);
    }

    /**
     * 发起小B提现
     *
     * @param shopId
     * @param paymentOrderMap key = paymentId, value = shopOrderId, 要发起提现的支付单订单数据
     * @return 提现单id
     */
    @Transactional(rollbackFor = Exception.class)
    public Long withdraw(Long shopId, Long userId, Map<Long, Long> paymentOrderMap) {
        // 数据校验
        checkForWithdraw(paymentOrderMap.keySet());

        // 构造
        var target = enterpriseWithdrawConvertor.buildWithdraw(shopId, userId, paymentOrderMap);

        // 插入
        return create(target.getLeft(), target.getRight());
    }

    /**
     * 发起线上支付
     *
     * @param enterpriseWithdrawId
     */
    public void pay(Long enterpriseWithdrawId) {
        var withdraw = enterpriseWithdrawReadService.findById(enterpriseWithdrawId).getResult();
        if (withdraw == null) {
            throw new RuntimeException(String.format("[id=%s]小B提现申请单不存在", enterpriseWithdrawId));
        }

        // 校验
        checkForPay(withdraw);

        // 发起支付
        var requestResult = getTargetService(withdraw).withdrawRequest(withdraw);

        // 更新结果
        updateByPayRequest(withdraw, requestResult);
    }

    /**
     * 支付回结果回调
     *
     * @param withdrawId
     * @param request
     */
    public void withdrawCallback(Long withdrawId, HttpServletRequest request) {
        var withdraw = enterpriseWithdrawReadService.findById(withdrawId).getResult();
        if (withdraw == null) {
            throw new RuntimeException(String.format("[id=%s]小B提现申请单不存在", withdrawId));
        }

        // 校验
        checkForCallback(withdraw);

        // 解析结果
        var callbackResult = getTargetService(withdraw).withdrawCallback(withdraw, request);

        // 更新
        updateByCallback(withdraw, callbackResult);
    }

    private void updateByCallback(EnterpriseWithdraw withdraw, WithdrawCallbackResult callbackResult) {
        var updateObject = new EnterpriseWithdraw();
        updateObject.setId(withdraw.getId());
        updateObject.setPayCallback(callbackResult.getRawCallbackJson());
        if (callbackResult.isPaySuccess()) {
            updateObject.setStatus(EnterpriseWithdrawStatusEnum.PAID.getCode());
            updateObject.setPaidAt(new Date());
        } else {
            updateObject.setStatus(EnterpriseWithdrawStatusEnum.FAILURE.getCode());
            updateObject.setFailureReason(callbackResult.getErrorMessage());
        }

        enterpriseWithdrawWriteService.update(updateObject);
    }

    private void checkForCallback(EnterpriseWithdraw withdraw) {
        if (EnterpriseWithdrawStatusEnum.PAID.getCode().equals(withdraw.getStatus())) {
            throw new RuntimeException(String.format("[id=%s]小B提现申请单已经完成支付", withdraw.getId()));
        }
    }

    /**
     * 支付失败
     *
     * @param enterpriseWithdrawId
     * @param failureReason
     */
    public void payFailed(Long enterpriseWithdrawId, String failureReason) {
        var update = new EnterpriseWithdraw();
        update.setId(enterpriseWithdrawId);
        update.setStatus(EnterpriseWithdrawStatusEnum.FAILURE.getCode());
        update.setFailureReason(failureReason);
        enterpriseWithdrawWriteService.update(update);
    }

    private void updateByPayRequest(EnterpriseWithdraw withdraw, WithdrawRequestResult requestResult) {
        var updateObject = new EnterpriseWithdraw();
        updateObject.setId(withdraw.getId());
        updateObject.setPayRequest(requestResult.getRawRequestJson());
        updateObject.setPayResponse(requestResult.getRawResponseJson());
        updateObject.setTradeNo(requestResult.getTradeNo());
        if (requestResult.isSuccess()) {
            updateObject.setStatus(EnterpriseWithdrawStatusEnum.PAYING.getCode());
        } else {
            updateObject.setStatus(EnterpriseWithdrawStatusEnum.FAILURE.getCode());
            updateObject.setFailureReason(requestResult.getErrorMessage());
        }

        enterpriseWithdrawWriteService.update(updateObject);
    }

    private WithdrawOperation getTargetService(EnterpriseWithdraw withdraw) {
        if (CollectionUtils.isEmpty(enterpriseWithdrawOperations)) {
            throw new RuntimeException("小B提现操作服务列表为空");
        }

        return enterpriseWithdrawOperations.stream()
                .filter(operation -> operation.supportedChannel(withdraw.getPayChannel()))
                .findAny()
                .orElseThrow(() -> new RuntimeException("目标支付渠道的小B提现操作服务不存在"));
    }

    private void checkForPay(EnterpriseWithdraw withdraw) {
        if (!EnterpriseWithdrawStatusEnum.NOT_PAID.getCode().equals(withdraw.getStatus())) {
            throw new RuntimeException(String.format("[id=%s]小B提现单状态已不处于待支付", withdraw.getId()));
        }
    }

    private Long create(EnterpriseWithdraw withdraw, List<EnterpriseWithdrawDetail> details) {
        var response = enterpriseWithdrawWriteService.create(withdraw);
        if (StringUtils.isNotBlank(response.getError()) || !Boolean.TRUE.equals(response.getResult()) || withdraw.getId() == null) {
            throw new RuntimeException("小B提现单创建失败");
        }
        updateOrderNo(withdraw);

        details.forEach(entity -> entity.setEnterpriseWithdrawId(withdraw.getId()));
        var detailResponse = enterpriseWithdrawDetailWriteService.creates(details);
        if (StringUtils.isNotBlank(detailResponse.getError()) || !Boolean.TRUE.equals(detailResponse.getResult())) {
            throw new RuntimeException("小B提现单明细创建失败");
        }

        return withdraw.getId();
    }

    private void updateOrderNo(EnterpriseWithdraw withdraw) {
        var update = new EnterpriseWithdraw();
        update = new EnterpriseWithdraw();
        update.setId(withdraw.getId());
        update.setOrderNo(enterpriseWithdrawConvertor.buildWithdrawOrderNo(withdraw.getId()));

        var response = enterpriseWithdrawWriteService.update(update);
        if (StringUtils.isNotBlank(response.getError()) || !Boolean.TRUE.equals(response.getResult())) {
            throw new RuntimeException("小B提现单更新失败");
        }
    }

    private void checkForWithdraw(Set<Long> paymentIds) {
        // 同一支付单，只能存在一次有效的提现
        var list = enterpriseWithdrawDetailReadService.findValidPaymentIds(new ArrayList<>(paymentIds)).getResult();
        if (!CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("部分支付单已经发起过提现了");
        }
    }

    private List<PaymentAndOrderPairVO> buildSelected(List<PaymentForEnterpriseWithdrawDO> list, Map<Long, Long> selectedMap) {
        var paymentIds = list.stream().map(PaymentForEnterpriseWithdrawDO::getPaymentId).toList();

        // shopOrder信息, key = paymentId
        var shopOrderMap = shopOrderReadService.findMapWithPayment(paymentIds);
        if (CollectionUtils.isEmpty(shopOrderMap)) {
            return Collections.emptyList();
        }
        enterpriseWithdrawConvertor.filterByConfirm(shopOrderMap);

        // key = shopOrderId
        var skuOrderMap = skuOrderReadService.getSkuOrderMap(shopOrderMap.values().stream().flatMap(Collection::stream)
                .map(ShopOrder::getId).toList());

        return shopOrderMap.entrySet().stream()
                .map(entry -> enterpriseWithdrawConvertor.convert(entry.getKey(), entry.getValue(), skuOrderMap, selectedMap))
                .collect(Collectors.toList());
    }

    private WaitingWithdrawOrderSelectAllVO convert(ArrayList<PaymentAndOrderPairVO> resultList) {
        var target = new WaitingWithdrawOrderSelectAllVO();

        if (CollectionUtils.isEmpty(resultList)) {
            target.setTotalAmount(0L);
            target.setSelectedList(Collections.emptyList());
        } else {
            target.setTotalAmount(resultList.stream().mapToLong(PaymentAndOrderPairVO::getFee).sum());
            target.setSelectedList(resultList);
        }

        return target;
    }
}
