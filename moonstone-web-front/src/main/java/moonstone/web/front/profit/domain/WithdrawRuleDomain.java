package moonstone.web.front.profit.domain;


import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.ToString;
import moonstone.common.model.AuthAble;
import moonstone.order.model.UserWithdrawSum;
import moonstone.order.model.WithdrawPrinciple;
import moonstone.shop.slice.ShopFunctionSlice;
import moonstone.web.core.model.enu.CertificationEnu;
import moonstone.web.front.profit.domain.value.object.WithdrawRuleResultVO;
import org.joda.time.DateTime;
import org.joda.time.base.AbstractDateTime;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@ToString
@AllArgsConstructor
public class WithdrawRuleDomain {
    UserWithdrawSum withdrawSum;
    WithdrawPrinciple withdrawPrinciple;
    AuthAble userCertificationAuthStatus;
    final LocalDateTime sliceAt = LocalDateTime.now();
    /**
     * 店铺功能开启
     *
     * @see ShopFunctionSlice#isWithdrawRequireCertification() 是否启用实名验证
     */
    ShopFunctionSlice shopFunctionSlice;

    /**
     * 实名审核状态检测
     * 决策树
     */
    private CertificationEnu certificationCheck() {
        if (!shopFunctionSlice.isWithdrawRequireCertification()) {
            return CertificationEnu.NONE_NEED;
        }
        if (Objects.isNull(userCertificationAuthStatus)) {
            return CertificationEnu.REQUIRE;
        }
        if (userCertificationAuthStatus.isAuthed()) {
            return CertificationEnu.PASSED;
        }
        if (userCertificationAuthStatus.isReject()) {
            return CertificationEnu.REJECT;
        }
        return CertificationEnu.WAITING;
    }

    /**
     * 拉取出提现规则
     *
     * @return 提现规则
     */
    public WithdrawRuleResultVO withdrawRuleResultVO() {
        CertificationEnu userCertificationStatus = certificationCheck();
        Map<String, String> validCashWithdrawLeft = new HashMap<>(3);
        Map<String, String> validWithdrawTimeLeft = new HashMap<>(3);

        for (WithdrawPrinciple.IndexEnum index : WithdrawPrinciple.IndexEnum.values()) {
            BigDecimal moneyMax = withdrawPrinciple.getMoneyLimit().getOrDefault(index.getIndex(), BigDecimal.valueOf(Long.MAX_VALUE));
            Long countMax = withdrawPrinciple.getTimeLimit().getOrDefault(index.getIndex(), Long.MAX_VALUE);
            BigDecimal moneyUsed = withdrawSum.getSum().getOrDefault(index.getIndex(), BigDecimal.valueOf(0L));
            Long timeUsed = withdrawSum.getTimes().getOrDefault(index.getIndex(), 0L);
            Map<WithdrawPrinciple.IndexEnum, Function<DateTime, Integer>> comparePattern =
                    ImmutableMap.of(WithdrawPrinciple.IndexEnum.DAY, AbstractDateTime::getDayOfYear
                            , WithdrawPrinciple.IndexEnum.YEAR, AbstractDateTime::getYear
                            , WithdrawPrinciple.IndexEnum.MONTH, AbstractDateTime::getMonthOfYear
                    );
            Function<DateTime, Integer> comparePrefix = comparePattern.get(index);
            boolean compareResult = Objects.isNull(withdrawSum.getLastWithdrawAt())
                    || !comparePrefix.apply(new DateTime(withdrawSum.getLastWithdrawAt()))
                    .equals(comparePrefix.apply(new DateTime(sliceAt.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli())));
            if (compareResult) {
                timeUsed = 0L;
                moneyUsed = BigDecimal.ZERO;
            }
            BigDecimal leftWithdrawAbleAmount = moneyMax.subtract(moneyUsed).max(BigDecimal.ZERO);
            long leftWithdrawAbleTime = Math.max(countMax - timeUsed, 0);
            validCashWithdrawLeft.put(index.getIndex(), moneyMax.equals(BigDecimal.valueOf(Long.MAX_VALUE)) ? "无限制" : leftWithdrawAbleAmount.toString());
            validWithdrawTimeLeft.put(index.getIndex(), countMax == Long.MAX_VALUE ? "无限制" : leftWithdrawAbleTime + "");
        }
        return new WithdrawRuleResultVO(userCertificationStatus, validCashWithdrawLeft, validWithdrawTimeLeft);
    }
}
