package moonstone.web.front.profit.view;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class WithdrawProfitApplyAuditRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = -6032708471253567322L;

    /**
     * 提现申请单的id
     */
    private Long id;

    /**
     * 审核操作
     *
     * @see WithdrawProfitApplyAuditOperationEnum
     */
    private Integer operation;

    /**
     * 备注
     */
    private String remark;

    public enum WithdrawProfitApplyAuditOperationEnum {
        agree(1, "审核通过"),
        reject(2, "驳回"),
        pay_success(3, "打款成功"),
        pay_failure(4, "打款失败");

        private Integer code;
        private String description;

        WithdrawProfitApplyAuditOperationEnum(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        public Integer getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static WithdrawProfitApplyAuditOperationEnum parse(Integer code) {
            for (WithdrawProfitApplyAuditOperationEnum current : WithdrawProfitApplyAuditOperationEnum.values()) {
                if (current.getCode().equals(code)) {
                    return current;
                }
            }

            return null;
        }
    }
}
