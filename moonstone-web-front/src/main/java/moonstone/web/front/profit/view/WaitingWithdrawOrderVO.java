package moonstone.web.front.profit.view;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class WaitingWithdrawOrderVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -427892256169155828L;

    /**
     * 支付单id
     */
    private Long paymentId;

    /**
     * 外部支付流水号
     */
    private String paymentPaySerialNo;

    /**
     * 支付单对应的订单列表
     */
    private List<WaitingWithdrawOrderDetailVO> shopOrderList;
}
