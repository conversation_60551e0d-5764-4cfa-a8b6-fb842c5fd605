package moonstone.web.front.profit.domain;

import lombok.ToString;
import moonstone.common.model.Either;
import moonstone.order.api.BankAccountJudge;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawAccount;
import moonstone.order.service.WithdrawAccountReadService;
import moonstone.web.core.model.dto.WithdrawAccountWithBankInfo;
import moonstone.web.front.profit.domain.value.object.WithdrawAccountVO;
import moonstone.web.front.profit.slice.WithdrawAccountQuerySlice;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@ToString
public abstract class AbstractWithdrawAccountDomain {

    Long userId;
    Long shopId;

    WithdrawAccountReadService withdrawAccountReadService;

    final LocalDateTime sliceAt = LocalDateTime.now();

    /**
     * 查询提现帐号
     *
     * @return 提现帐号
     */
    public abstract List<WithdrawAccount> withdrawAccountList();

    /**
     * 查询默认帐号
     *
     * @return 默认的帐号
     */
    public abstract WithdrawAccount defaultAccount();

    public WithdrawAccountQuerySlice withdrawAccountQuerySlice() {
        return () -> Either.ok(new WithdrawAccountVO(withdrawAccountList(), defaultAccount()));
    }

    /**
     * 美化修饰银行数据
     *
     * @param withdrawAccount  银行帐号
     * @param bankAccountJudge 银行帐号分辨器
     * @return 提现帐号
     */
    private static WithdrawAccount beautyBankInfo(WithdrawAccount withdrawAccount, BankAccountJudge bankAccountJudge) {
        Set<Integer> bankTypeSet = Stream.of(WithDrawProfitApply.WithdrawPaidType.BANK, WithDrawProfitApply.WithdrawPaidType.SelfBank).map(WithDrawProfitApply.WithdrawPaidType::getType).collect(Collectors.toSet());
        if (!bankTypeSet.contains(withdrawAccount.getType())) {
            return withdrawAccount;
        }
        WithdrawAccountWithBankInfo beautyAccount = new WithdrawAccountWithBankInfo();
        BeanUtils.copyProperties(withdrawAccount, beautyAccount);
        BankAccountJudge.BankType bankInfo = bankAccountJudge.judge(withdrawAccount.getAccount());
        if (bankInfo == BankAccountJudge.UNKNOW) {
            return withdrawAccount;
        }
        String account = withdrawAccount.getAccount();
        beautyAccount.setAccountTail(account.substring(account.length() - 4));
        beautyAccount.setBankImg(bankInfo.getImg());
        beautyAccount.setBankImgUrl(bankInfo.getImgUrl());
        beautyAccount.setBankName(bankInfo.getName());
        return beautyAccount;
    }

    public static AbstractWithdrawAccountDomain from(WithdrawAccountReadService withdrawAccountReadService,
                                                     BankAccountJudge bankAccountJudge, Long userId, Long shopId) {
        List<WithdrawAccount> withdrawAccounts = withdrawAccountReadService.findByShopIdAndUserId(shopId, userId).getResult()
                .stream().map(account -> beautyBankInfo(account, bankAccountJudge)).collect(Collectors.toList());
        WithdrawAccount defaultOne = withdrawAccounts.stream().filter(WithdrawAccount::isDefault).findFirst().orElse(null);
        AbstractWithdrawAccountDomain abstractWithdrawAccountDomain = new AbstractWithdrawAccountDomain() {
            @Override
            public List<WithdrawAccount> withdrawAccountList() {
                return withdrawAccounts;
            }

            @Override
            public WithdrawAccount defaultAccount() {
                return defaultOne;
            }
        };
        abstractWithdrawAccountDomain.withdrawAccountReadService = withdrawAccountReadService;
        abstractWithdrawAccountDomain.userId = userId;
        abstractWithdrawAccountDomain.shopId = shopId;
        return abstractWithdrawAccountDomain;
    }
}
