package moonstone.web.front.profit.job;


import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.XxlJobDesc;
import moonstone.web.front.profit.application.AgentPayOrderApp;
import moonstone.web.front.profit.application.AgentPayOrderCreateRecordApp;
import moonstone.order.model.mongo.AgentPayOrderRecord;
import moonstone.web.core.util.LockKeyUtils;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * 按需创建 代付订单
 */
@Slf4j
@Component
public class AgentPayOrderCreationJob {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private AgentPayOrderApp agentPayOrderApp;

    @Resource
    private AgentPayOrderCreateRecordApp agentPayOrderCreateRecordApp;

    /**
     * 按需创建 代付订单
     */
    @XxlJob("AgentPayOrderCreationJob#autoCreateAgentPayOrder")
    @XxlJobDesc(desc = "按需创建代付订单", core = "0 0/5 * * * ?")
//    @Scheduled(cron = "${scheduled.cron.autoCreateAgentPayOrder}")
    public ReturnT<String> autoCreateAgentPayOrder(String param) {
        var lock = redissonClient.getLock(LockKeyUtils.autoCreateAgentPayOrder());
        if (!lock.tryLock()) {
            return ReturnT.SUCCESS;
        }

        log.debug("AgentPayOrderCreationJob.autoCreateAgentPayOrder begin.");
        try {
            var now = System.currentTimeMillis();
            int pageSize = 100;

            while (true) {
                var list = agentPayOrderCreateRecordApp.findNotCreated(now, pageSize);
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }

                for (var current : list) {
                    process(current);
                }
            }
        } finally {
            lock.unlock();
            log.debug("AgentPayOrderCreationJob.autoCreateAgentPayOrder end.");
        }
        return ReturnT.SUCCESS;
    }

    private void process(AgentPayOrderRecord record) {
        var lock = redissonClient.getLock(LockKeyUtils.agentPayOrderCreate(record.getShopOrderId()));
        if (!lock.tryLock()) {
            log.error("AgentPayOrderCreationJob.process, shopOrderId={}, tryLock failed", record.getShopOrderId());
            return;
        }

        try {
            // 创建
            var id = agentPayOrderApp.create(record.getShopOrderId());

            // 更新 record
            agentPayOrderCreateRecordApp.updateCreateStatus(record, id != null ?
                    AgentPayOrderRecord.AgentPayOrderRecordStatus.CREATED : AgentPayOrderRecord.AgentPayOrderRecordStatus.NO_NEED);
        } catch (Exception ex) {
            log.error("AgentPayOrderCreationJob.process error, record={}", JSON.toJSONString(record), ex);
            agentPayOrderCreateRecordApp.delayTriggerAfter(record);
        } finally {
            lock.unlock();
        }
    }
}
