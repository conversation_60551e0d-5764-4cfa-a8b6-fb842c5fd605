package moonstone.web.front.profit.domain.value.object;

import lombok.AllArgsConstructor;
import lombok.Data;
import moonstone.order.model.WithdrawAccount;
import moonstone.web.core.model.dto.CurrentProfitVO;

import java.util.List;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Data
public class WithdrawAccountVO {
    List<WithdrawAccount> withdrawAccountList;
    WithdrawAccount defaultAccount;

    /**
     * 将自身数据装饰入目前可提现利润value object
     *
     * @param currentProfitVO 目前可提现利润
     */
    public void decorate(CurrentProfitVO currentProfitVO) {
        currentProfitVO.setWithdrawAccountList(withdrawAccountList);
        currentProfitVO.setDefaultAccount(defaultAccount);
    }
}
