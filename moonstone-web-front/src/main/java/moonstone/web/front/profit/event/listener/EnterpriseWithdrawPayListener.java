package moonstone.web.front.profit.event.listener;

import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.util.LockKeyUtils;
import moonstone.web.front.profit.application.EnterpriseWithdrawApp;
import moonstone.web.front.profit.event.EnterpriseWithdrawPayEvent;
import org.redisson.api.RedissonClient;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 发起小B提现单的线上支付
 */
@Slf4j
@Component
public class EnterpriseWithdrawPayListener {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private EnterpriseWithdrawApp enterpriseWithdrawApp;

    /**
     * 触发支付
     *
     * @param event
     */
    @EventListener(EnterpriseWithdrawPayEvent.class)
    public void triggerPay(EnterpriseWithdrawPayEvent event) {
        log.info("EnterpriseWithdrawPayListener.triggerPay receive event, withdrawId={}", event.getEnterpriseWithdrawId());

        var lock = redissonClient.getLock(LockKeyUtils.payEnterpriseWithdraw(event.getEnterpriseWithdrawId()));
        if (!lock.tryLock()) {
            log.error("EnterpriseWithdrawPayListener.triggerPay, withdrawId={}, fail to lock", event.getEnterpriseWithdrawId());
            return;
        }

        try {
            enterpriseWithdrawApp.pay(event.getEnterpriseWithdrawId());
        } catch (Exception ex) {
            log.error("EnterpriseWithdrawPayListener.triggerPay, withdrawId={}, fail to pay.", event.getEnterpriseWithdrawId(), ex);
            enterpriseWithdrawApp.payFailed(event.getEnterpriseWithdrawId(), ex.getMessage());
        } finally {
            lock.unlock();
        }
    }
}
