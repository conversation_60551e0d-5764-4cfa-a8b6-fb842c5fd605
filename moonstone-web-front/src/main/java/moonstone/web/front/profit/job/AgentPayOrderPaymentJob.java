package moonstone.web.front.profit.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.XxlJobDesc;
import moonstone.order.dto.AgentPayOrderCriteria;
import moonstone.order.enu.AgentPayOrderStatusEnum;
import moonstone.order.model.AgentPayOrder;
import moonstone.order.service.AgentPayOrderReadService;
import moonstone.web.front.profit.event.listener.AgentPayCreationListener;
import moonstone.web.core.util.LockKeyUtils;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 按需发起代付订单支付
 */
@Slf4j
@Component
public class AgentPayOrderPaymentJob {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private AgentPayOrderReadService agentPayOrderReadService;

    @Resource
    private AgentPayCreationListener agentPayCreationListener;

    /**
     * 按需发起代付订单支付
     */
    @XxlJob("AgentPayOrderPaymentJob#autoAgentPayPayment")
    @XxlJobDesc(desc = "按需发起代付订单支付", core = "0 0/8 * * * ?")
//    @Scheduled(cron = "${scheduled.cron.autoAgentPayPayment}")
    public ReturnT<String> autoAgentPayPayment(String param) {
        var lock = redissonClient.getLock(LockKeyUtils.autoPayAgentPayOrder());
        if (!lock.tryLock()) {
            return ReturnT.SUCCESS;
        }

        log.debug("AgentPayOrderPaymentJob.autoAgentPayPayment begin.");
        try {
            var now = new Date();
            for (int pageNum = 1, pageSize = 100; ; pageNum++) {
                var list = findNotPaid(now, pageNum, pageSize);
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }

                for (var current : list) {
                    agentPayCreationListener.agentPay(current.getId());
                }
            }
        } finally {
            lock.unlock();
            log.debug("AgentPayOrderPaymentJob.autoAgentPayPayment end.");
        }
        return ReturnT.SUCCESS;
    }

    private List<AgentPayOrder> findNotPaid(Date now, int pageNum, int pageSize) {
        var criteria = new AgentPayOrderCriteria();
        criteria.setStatus(AgentPayOrderStatusEnum.NOT_PAID.getCode());
        criteria.setCreatedAtEnd(now);
        criteria.setPageNo(pageNum);
        criteria.setPageSize(pageSize);

        return agentPayOrderReadService.pageList(criteria).getResult();
    }
}
