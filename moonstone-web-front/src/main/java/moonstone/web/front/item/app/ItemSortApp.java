package moonstone.web.front.item.app;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import moonstone.item.service.ItemReadService;
import moonstone.web.core.events.item.ItemUpdateEvent;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class ItemSortApp {
    @Autowired
    SqlSession sqlSession;
    @Resource
    ItemReadService itemReadService;

    public void sort(Long itemId, String action) {
        var item = itemReadService.findById(itemId).getResult();
        if (item == null) {
            throw new RuntimeException("item.not.found");
        }

        var itemOldIndex = item.getIndex();
        if (itemOldIndex == null) itemOldIndex = 0;
        var index = action.equalsIgnoreCase("UP") ? itemOldIndex + 1 : itemOldIndex - 1;
        Long swapId = sqlSession.selectOne("Item.findIdByIndex", Map.of("shopId", item.getShopId(),
                "index", index));
        log.debug("ITEM[Id: {}, Index: {}] Swap[Id: {}, Index: {}]", itemId, itemOldIndex, swapId, index);

        if (swapId != null) {
            sqlSession.update("Item.updateIndex", Map.of("id", swapId, "newIndex", itemOldIndex));
        }
        sqlSession.update("Item.updateIndex", Map.of("id", itemId, "newIndex", index));

        //发送商品更新事件
        sendItemUpdateEvent(itemId, swapId);
    }

    /**
     * 发送商品更新事件
     *
     * @param itemId
     * @param swapItemId
     */
    private void sendItemUpdateEvent(Long itemId, Long swapItemId) {
        if (swapItemId != null) {
            EventSender.publish(new ItemUpdateEvent(swapItemId));
        }

        EventSender.publish(new ItemUpdateEvent(itemId));
    }
}
