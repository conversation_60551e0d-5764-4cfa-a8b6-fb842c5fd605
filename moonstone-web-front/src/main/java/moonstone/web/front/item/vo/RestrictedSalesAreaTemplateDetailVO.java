package moonstone.web.front.item.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class RestrictedSalesAreaTemplateDetailVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1034332871732050911L;

    private Long id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板状态(1-启用，2-停用)
     *
     * @see moonstone.item.emu.RestrictedSalesAreaTemplateStatusEnum
     */
    private Integer status;

    /**
     * 是否为默认模板(1-是，2-否)
     *
     * @see moonstone.item.emu.DefaultOneEnum
     */
    private Integer defaultOne;

    /**
     * 模板说明
     */
    private String description;

    /**
     * 允许的省份(名称数组)
     */
    private List<String> allowProvince;
}
