package moonstone.web.front.item;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Paging;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.RestrictedSalesAreaTemplateCache;
import moonstone.common.api.APIResp;
import moonstone.common.api.Result;
import moonstone.common.model.CommonUser;
import moonstone.common.model.vo.ComboBoxVo;
import moonstone.common.utils.UserUtil;
import moonstone.item.emu.RestrictedSalesAreaTemplateStatusEnum;
import moonstone.item.service.ItemWriteService;
import moonstone.item.service.RestrictedSalesAreaTemplateReadService;
import moonstone.item.service.RestrictedSalesAreaTemplateWriteService;
import moonstone.web.front.item.app.RestrictedSalesAreaTemplateConvertor;
import moonstone.web.front.item.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 限售区域模板配置相关接口
 */
@Slf4j
@RestController
public class RestrictedSalesAreaTemplateController {

    @Resource
    private RestrictedSalesAreaTemplateReadService restrictedSalesAreaTemplateReadService;

    @Resource
    private RestrictedSalesAreaTemplateWriteService restrictedSalesAreaTemplateWriteService;

    @Resource
    private RestrictedSalesAreaTemplateConvertor restrictedSalesAreaTemplateConvertor;

    @Resource
    private ItemWriteService itemWriteService;

    @Resource
    private RestrictedSalesAreaTemplateCache restrictedSalesAreaTemplateCache;

    /**
	 * 分页查询区域限售模板
     *
     * @return
     */
    @PostMapping("/api/restrictedSalesAreaTemplate/find")
    public APIResp<Paging<RestrictedSalesAreaTemplateVO>> find(@RequestBody RestrictedSalesAreaTemplatePagingRequest request) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            return APIResp.notLogin();
        }
        if (request == null) {
            return APIResp.error("入参缺失");
        }

        try {
            int pageNo = request.getCurrentPage() == null ? 1 : request.getCurrentPage();
            int pageSize = request.getPageSize() == null ? 20 : request.getPageSize();

            var result = restrictedSalesAreaTemplateReadService.find(user.getShopId(),
                    request.getName(), request.getStatus(), pageNo, pageSize);
            if (!result.isSuccess()) {
                return APIResp.error(result.getError());
            }

            return APIResp.ok(new Paging<>(result.getResult().getTotal(),
                    restrictedSalesAreaTemplateConvertor.convert(result.getResult().getData())));
        } catch (Exception ex) {
            log.error("RestrictedSalesAreaTemplateController.find error, request={}", JSON.toJSONString(request));
            return APIResp.error(ex.getMessage());
        }
    }

    /**
     * 新增
     *
     * @param request
     * @return
     */
    @PostMapping("/api/restrictedSalesAreaTemplate/create")
    public APIResp<Boolean> create(@RequestBody RestrictedSalesAreaTemplateCreateRequest request) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            return APIResp.notLogin();
        }
        if (request == null || StringUtils.isBlank(request.getName()) || CollectionUtils.isEmpty(request.getAllowProvince())) {
            return APIResp.error("入参缺失");
        }

        try {
            var createObject = restrictedSalesAreaTemplateConvertor.convert(user, request);

            return APIResp.ok(restrictedSalesAreaTemplateWriteService.create(createObject).getResult());
        } catch (Exception ex) {
            log.error("RestrictedSalesAreaTemplateController.create error, request={}", JSON.toJSONString(request));
            return APIResp.error(ex.getMessage());
        }
    }

    /**
     * 更新
     *
     * @param request
     * @return
     */
    @PostMapping("/api/restrictedSalesAreaTemplate/update")
    public APIResp<Boolean> update(@RequestBody RestrictedSalesAreaTemplateUpdateRequest request) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            return APIResp.notLogin();
        }
        if (request == null || request.getId() == null || StringUtils.isBlank(request.getName()) ||
                CollectionUtils.isEmpty(request.getAllowProvince())) {
            return APIResp.error("入参缺失");
        }

        try {
            var existed = restrictedSalesAreaTemplateReadService.findById(request.getId()).getResult();

            return APIResp.ok(restrictedSalesAreaTemplateWriteService.update(restrictedSalesAreaTemplateConvertor.convert(
                    existed, user, request)).getResult());
        } catch (Exception ex) {
            log.error("RestrictedSalesAreaTemplateController.update error, userId={}, request={}",
                    user.getId(), JSON.toJSONString(request), ex);
            return APIResp.error(ex.getMessage());
        } finally {
            restrictedSalesAreaTemplateCache.invalidate(request.getId());
        }
    }

    /**
	 * 变更区域限售模板状态
     *
     * @param request
     * @return
     */
    @PostMapping("/api/restrictedSalesAreaTemplate/switchStatus")
    public APIResp<Boolean> switchStatus(@RequestBody RestrictedSalesAreaTemplateSwitchRequest request) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            return APIResp.notLogin();
        }
        if (request == null || request.getId() == null || request.getStatus() == null ||
                RestrictedSalesAreaTemplateStatusEnum.parse(request.getStatus()) == null) {
            return APIResp.error("入参缺失");
        }

        try {
            var existed = restrictedSalesAreaTemplateReadService.findById(request.getId()).getResult();

            return APIResp.ok(restrictedSalesAreaTemplateWriteService.update(restrictedSalesAreaTemplateConvertor.convert(
                    existed, user, request)).getResult());
        } catch (Exception ex) {
            log.error("RestrictedSalesAreaTemplateController.switchStatus error, userId={}, request={}",
                    user.getId(), JSON.toJSONString(request), ex);
            return APIResp.error(ex.getMessage());
        } finally {
            restrictedSalesAreaTemplateCache.invalidate(request.getId());
        }
    }

    /**
     * 查询指定模板的明细
     *
     * @return
     */
    @PostMapping("/api/restrictedSalesAreaTemplate/findDetail/{id}")
    public APIResp<RestrictedSalesAreaTemplateDetailVO> findDetail(@PathVariable Long id) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            return APIResp.notLogin();
        }
        if (id == null) {
            return APIResp.error("入参缺失");
        }

        try {
            return APIResp.ok(restrictedSalesAreaTemplateConvertor.convertDetail(
                    restrictedSalesAreaTemplateReadService.findById(id).getResult()));
        } catch (Exception ex) {
            log.error("RestrictedSalesAreaTemplateController.findDetail error, id={}", id, ex);
            return APIResp.error(ex.getMessage());
        }
    }

    /**
     * 给商品设置指定模板
     *
     * @param request
     * @return
     */
    @PostMapping("/api/restrictedSalesAreaTemplate/configure")
    public APIResp<Boolean> configure(@RequestBody RestrictedSalesAreaTemplateConfigureRequest request) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            return APIResp.notLogin();
        }
        if (request == null || request.getTemplateId() == null || CollectionUtils.isEmpty(request.getItemIdList())) {
            return APIResp.error("入参缺失");
        }

        try {
            var template = restrictedSalesAreaTemplateReadService.findById(request.getTemplateId()).getResult();
            if (template == null) {
                return APIResp.error("指定模板不存在");
            }
            if (!template.getShopId().equals(user.getShopId())) {
                return APIResp.error("模板不属于当前商家");
            }

            return APIResp.ok(itemWriteService.updateRestrictedSalesAreaTemplate(
                    user.getShopId(), request.getItemIdList(), request.getTemplateId()).getResult());
        } catch (Exception ex) {
            log.error("RestrictedSalesAreaTemplateController.configure error, userId={}, request={}",
                    user.getId(), JSON.toJSONString(request), ex);
            return APIResp.error(ex.getMessage());
        }
    }


    /**
     * 获取区域限售模板下拉框列表
     * @return 区域限售模板下拉框列表
     */
    @PostMapping("/api/restrictedSalesAreaTemplate/combo/box/list")
    public Result<List<ComboBoxVo>> comboBoxList(@RequestParam(value = "shopId",required = false) Long shopId) {
        log.info("获取区域限售模板下拉框列表 请求参数 shopId: {}",shopId);
        Long currentShopId = UserUtil.getCurrentShopId();
        if (currentShopId != null) {
            shopId = currentShopId;
        }
        return Result.data(restrictedSalesAreaTemplateReadService.comboBoxList(shopId));
    }



}
