package moonstone.web.front.item.app;

import lombok.extern.slf4j.Slf4j;
import moonstone.item.emu.ItemExtraIndex;
import moonstone.item.emu.ItemFrontEndFlag;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.emu.SkuExtraPriceIndex;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.promotion.component.SubStoreOnlyManager;
import moonstone.promotion.enums.PromotionStatus;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.service.PromotionReadService;
import moonstone.web.front.component.item.ItemPromotionMatcher;
import moonstone.web.front.item.search.ItemSearchesNew;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ItemSearchDecorateHelper {

    @Resource
    private ItemReadService itemReadService;

    @Resource
    private SkuReadService skuReadService;

    @Resource
    private PromotionReadService promotionReadService;

    @Resource
    private ItemPromotionMatcher itemPromotionMatcher;

    @Resource
    private PromotionConvertor promotionConvertor;

    @Resource
    private ItemConvertor itemConvertor;

    @Resource
    private SubStoreOnlyManager subStoreOnlyManager;

    /**
     * 填充额外的 item 信息
     *
     * @param sourceList
     */
    public void appendAdditionalItemInfo(List<ItemSearchesNew.ItemsNew> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return;
        }
        Long shopId = sourceList.get(0).getShopId();

        //查询商品基本信息（key=item.id）
        var itemMap = findItemMap(sourceList.stream().map(ItemSearchesNew.ItemsNew::getId).toList());

        // 所有当前进行中的商品优惠卷信息
        List<Promotion> promotions = promotionReadService.findOngoingByShopId(shopId, PromotionType.ITEM_COUPON).getResult();

        // 门店限定卷
        List<Promotion> subStoreOnly = promotionReadService.find(shopId, PromotionType.SUB_STORE_ONLY,
                List.of(PromotionStatus.PUBLISHED, PromotionStatus.ONGOING)).getResult();
        //填充
        sourceList.forEach(view -> {
            var item = itemMap.get(view.getId());

            appendMainPic(view, item);
            appendSellOutStatus(view, item);
            appendPromotion(view, item, promotions);

            // 打标
            itemConvertor.appendFlagBySubStoreOnly(view::setFlagList, subStoreOnly, item.getId());
        });
    }

    /**
     * 填充额外的 sku 信息
     *
     * @param sourceList
     */
    public void appendAdditionalSkuInfo(List<ItemSearchesNew.ItemsNew> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return;
        }

        //查询sku基本信息（key=item.id）
        var skuMap = itemConvertor.findSkuMap(sourceList.stream().map(ItemSearchesNew.ItemsNew::getId).toList());

        //填充
        sourceList.forEach(view -> {
            var skus = skuMap.get(view.getId());
            if (CollectionUtils.isEmpty(skus)) {
                return;
            }

            appendPrice(view, skus.get(0));
            appendCrossedPrice(view, skus.get(0));
        });
    }

    /**
     * 填充"售罄状态"
     *
     * @param target
     * @param item
     */
    private void appendSellOutStatus(ItemSearchesNew.ItemsNew target, Item item) {
        if (target == null || item == null || item.getSellOutStatus() == null) {
            return;
        }

        target.setSellOutStatus(item.getSellOutStatus());
    }

    /**
     * 填充划线价
     *
     * @param target
     * @param sku
     */
    private void appendCrossedPrice(ItemSearchesNew.ItemsNew target, Sku sku) {
        if (target == null || sku == null) {
            return;
        }

        if (CollectionUtils.isEmpty(sku.getExtraPrice())) {
            return;
        }

        var crossedPrice = sku.getExtraPrice().get(SkuExtraPriceIndex.crossedPrice.getCode());
        if (crossedPrice != null) {
            target.setCrossedPrice(crossedPrice);
        }
    }

    /**
     * 填充价格
     *
     * @param target
     * @param sku
     */
    private void appendPrice(ItemSearchesNew.ItemsNew target, Sku sku) {
        if (target == null || sku == null) {
            return;
        }

        if (!sku.isExtraActivityValid()) {
            //活动不生效
            return;
        }

        //当前活动生效中，取活动价
        String activitySalesPrice = sku.getExtraMap().get(SkuExtraIndex.activitySalesPrice.name());
        if (StringUtils.hasText(activitySalesPrice)) {
            target.setPrice(Integer.parseInt(activitySalesPrice));
        }
    }

    /**
     * 填充主图
     *
     * @param target
     * @param item
     */
    private void appendMainPic(ItemSearchesNew.ItemsNew target, Item item) {
        if (target == null || item == null) {
            return;
        }

        if (!item.isExtraActivityValid()) {
            //活动不生效
            return;
        }

        //当前活动生效中，取活动图
        String activityMainImage = item.getExtra().get(ItemExtraIndex.activityMainImage.name());
        if (StringUtils.hasText(activityMainImage)) {
            target.setMainPic(activityMainImage);
        }
    }

    /**
     * 查询商品信息
     *
     * @param itemIds
     * @return
     */
    private Map<Long, Item> findItemMap(List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }

        var list = itemReadService.findByIds(itemIds).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(Item::getId, o -> o, (k1, k2) -> k1));
    }

    /**
     * 填充商品优惠卷信息
     *
     * @param view
     * @param promotions
     */
    private void appendPromotion(ItemSearchesNew.ItemsNew view, Item item, List<Promotion> promotions) {
        if (view == null || item == null || CollectionUtils.isEmpty(promotions)) {
            return;
        }

        // 筛选出当前商品可用的营销活动
        List<Promotion> matchList = itemPromotionMatcher.match(item, promotions);
        if (CollectionUtils.isEmpty(matchList)) {
            return;
        }

        view.setPromotionList(promotionConvertor.convert(matchList));
    }

    private void appendFlag(ItemSearchesNew.ItemsNew view, Item item, List<Promotion> promotionList) {
        if (CollectionUtils.isEmpty(promotionList)) {
            return;
        }
        List<String> flag = new ArrayList<>();

        // 活动商品
        if (promotionList.stream().anyMatch(promotion -> subStoreOnlyManager.isActivityItem(promotion, item.getId()))) {
            flag.add(ItemFrontEndFlag.PROMOTION_ACTIVITY_ITEM.getCode());
        }
        // 限定商品
        if (promotionList.stream().anyMatch(promotion -> subStoreOnlyManager.isLimitedItem(promotion, item.getId()))) {
            flag.add(ItemFrontEndFlag.PROMOTION_LIMITED_ITEM.getCode());
        }

        view.setFlagList(flag);
    }
}
