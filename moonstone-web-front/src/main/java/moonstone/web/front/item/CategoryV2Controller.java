package moonstone.web.front.item;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCategoryCacher;
import moonstone.category.model.ShopCategory;
import moonstone.category.model.ShopCategoryItem;
import moonstone.category.service.ShopCategoryItemReadService;
import moonstone.common.api.APIResp;
import moonstone.common.enums.ShopCategoryVisibleEnum;
import moonstone.common.utils.UserUtil;
import moonstone.shop.model.SubStore;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.web.core.component.item.model.SubStoreAreaModel;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.core.shop.model.ServiceProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class CategoryV2Controller {
    @Autowired
    ShopCategoryCacher shopCategoryCacher;
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    SubStoreCache subStoreCache;
    @Autowired
    GuiderCache guiderCache;
    @Autowired
    ServiceProviderCache serviceProviderCache;
    @Autowired
    ShopCategoryItemReadService shopCategoryItemReadService;

    @GetMapping("/api/v2/search/category")
    public APIResp<List<CategoryNameAndId>> category(Long shopId, Long subStoreId) {
        List<CategoryNameAndId> categoryNameAndIds = new LinkedList<>();
        CategoryNameAndId category = new CategoryNameAndId();
        category.setName("全部");
        category.setId(0L);
        categoryNameAndIds.add(category);
        for (ShopCategory shopCategory : shopCategoryCacher.findByShopIdAndSort(shopId)) {
            if (ShopCategoryVisibleEnum.HIDDEN.getCode().equals(shopCategory.getVisible())) {
                continue;
            }

            CategoryNameAndId categoryNameAndId = new CategoryNameAndId();
            categoryNameAndId.setName(shopCategory.getName());
            categoryNameAndId.setId(shopCategory.getId());
            categoryNameAndIds.add(categoryNameAndId);
        }
        // rewind the shopCategory
        // 1. check the identity
        // 2. use the subStoreId to remove the category
        filterTheEmptyItemForCategory(categoryNameAndIds, shopId, UserUtil.getUserId(), subStoreId);

        return APIResp.ok(categoryNameAndIds);
    }

    private void filterTheEmptyItemForCategory(List<CategoryNameAndId> categoryNameAndIds, Long shopId, Long userId, Long subStoreId) {
        Optional<SubStore> subStore;
        if (userId != null) {
            Optional<SubStoreTStoreGuider> guider = guiderCache.findByShopIdAndUserId(shopId, userId);
            if (guider.isPresent()) {
                filterCategoryBySubStore(categoryNameAndIds, subStoreCache.findById(guider.get().getSubStoreId()).get());
                return;
            }
            subStore = subStoreCache.findByShopIdAndUserId(shopId, userId);
            if (subStore.isPresent()) {
                filterCategoryBySubStore(categoryNameAndIds, subStore.get());
                return;
            }
            ServiceProvider serviceProvider = serviceProviderCache.findServiceProviderByUserIdAndShopId(userId, shopId);
            if (serviceProvider != null) {
                filterCategoryByServiceProviderId(categoryNameAndIds, shopId, serviceProvider.getId());
                return;
            }
        }
        subStore = subStoreCache.findById(subStoreId);
        subStore.ifPresent(store -> filterCategoryBySubStore(categoryNameAndIds, store));
    }

    private void filterCategoryBySubStore(List<CategoryNameAndId> categoryNameAndIds, SubStore store) {
        // store to find the serviceProvider
        serviceProviderCache.findByShopIdAndUserId(store.getShopId(), store.getUserId())
                .map(view -> serviceProviderCache.findServiceProviderByUserIdAndShopId(view.getSupperUserId(), store.getShopId()))
                .ifPresent(s -> filterCategoryByServiceProviderId(categoryNameAndIds, store.getShopId(), s.getId()));
    }

    private void filterCategoryByServiceProviderId(List<CategoryNameAndId> categoryNameAndIds, Long shopId, String serviceProviderId) {
        List<SubStoreAreaModel> storeAreaModels = mongoTemplate.find(Query.query(Criteria.where("shopId").is(shopId)), SubStoreAreaModel.class);
        List<Long> itemIds = new ArrayList<>();
        for (SubStoreAreaModel storeAreaModel : storeAreaModels) {
            Set<String> set = new HashSet<>(storeAreaModel.getServiceProviderId());
            if (set.contains(serviceProviderId)) {
                if (!CollectionUtils.isEmpty(storeAreaModel.getItemId())) {
                    itemIds.addAll(storeAreaModel.getItemId());
                }
            }
        }
        List<ShopCategoryItem> categoryItems = shopCategoryItemReadService.findByShopIdAndItemIds(shopId, itemIds).getResult();

        Set<Long> categoryIdSet = categoryItems.stream().map(ShopCategoryItem::getShopCategoryId).collect(Collectors.toSet());

        categoryNameAndIds.removeAll(categoryNameAndIds.stream()
                .filter(c -> c.getId() != 0L && !categoryIdSet.contains(c.getId())).collect(Collectors.toSet()));
    }

    @Data
    static class CategoryNameAndId {
        String name;
        Long id;
    }
}