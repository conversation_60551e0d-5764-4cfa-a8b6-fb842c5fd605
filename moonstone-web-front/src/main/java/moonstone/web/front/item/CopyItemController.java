package moonstone.web.front.item;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api")
public record CopyItemController(ShopReadService shopReadService,
                                 ItemReadService itemReadService,
                                 SkuReadService skuReadService,
                                 ItemController itemController) {
    @RequestMapping(value = "/seller/items/copy", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Long> copy(Long itemId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();
        Response<Shop> rShop = shopReadService.findById(shopId);
        if (!rShop.isSuccess()) {
            log.error("failed to find shop(id={}), error code:{}", shopId, rShop.getError());
            throw new JsonResponseException(rShop.getError());
        }
        final Shop shop = rShop.getResult();
        if (!Objects.equals(shop.getStatus(), 1)) {
            log.error("shop(id={})'s status is {}, create item failed", shopId);
            throw new JsonResponseException("shop.status.abnormal");
        }

        val rItem = itemReadService.findById(itemId);
        if (!rItem.isSuccess() || ObjectUtils.isEmpty(rItem.getResult())) {
            return Response.fail("item.find.fail");
        }
        if (rItem.getResult().getStatus() != 1) {
            return Response.fail("item.status.error");
        }
        Response<Item> rGetItem = getItem(rItem.getResult());


        if (!rGetItem.isSuccess()) {
            return itemController.copy(rItem.getResult(), shop);
        } else {
            return Response.ok(rGetItem.getResult().getId());
        }
    }

    private Response<Item> getItem(Item outItem) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Long shopId = commonUser.getShopId();
        val rSku = skuReadService.findSkusByItemId(outItem.getId());
        if (!rSku.isSuccess()) {
            throw new JsonResponseException(500, rSku.getError());
        }
        String outerSkuId = "";
        for (Sku iSku : rSku.getResult()) {
            if (iSku.getShopId() == 0 && !ObjectUtils.isEmpty(iSku.getOuterSkuId())) {
                outerSkuId = iSku.getOuterSkuId();
                break;
            }
        }
        Long itemId = null;
        val originSkuListRes = skuReadService.findSkuByOuterSkuId(shopId, outerSkuId);
        for (Sku iSku : originSkuListRes.getResult()) {
            if (iSku.getExtraMap().containsKey("thirdPartySystem")) {
                if (iSku.getTags().containsKey("pushSystem")) {
                    List<ThirdPartySystem> pushSystem = Stream.of(iSku.getTags().get("pushSystem").split(","))
                            .map(Integer::parseInt)
                            .map(ThirdPartySystem::fromInt)
                            .collect(Collectors.toList());
                    itemId = iSku.getItemId();
                }
            }
        }
        val rItem = itemReadService.findById(itemId);
        if (rItem.getResult() == null) {
            return Response.fail("no found");
        }
        return Response.ok(rItem.getResult());
    }
}
