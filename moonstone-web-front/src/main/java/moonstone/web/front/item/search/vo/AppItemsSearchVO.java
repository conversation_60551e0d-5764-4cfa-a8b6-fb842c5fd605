package moonstone.web.front.item.search.vo;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * app通过类目id查询商品
 */
@Data
public class AppItemsSearchVO {
    /**
     * 商品id
     */
    private Long itemId;
    /**
     * 商品名
     */
    private String name;
    /**
     * 主图
     */
    private String mainImage;
    /**
     * 价格/分
     */
    private Integer price;
    /**
     * 是否跨境 是否保税(1:保税，0:完税)
     */
    private Integer isBonded;
    /**
     * 商品类型 商品类型 1, 普通商品, 2. 组合商品, 3 扫码积分商品  4 新客礼  5 累计礼
     */
    private Integer type;
    /**
     * 商品分享次数
     */
    private Integer shareNum;
    /**
     * 商品包邮 1:包邮 0：不包邮
     */
    private Integer freeShip;
    /**
     * 商品包邮 1:包税 0：不包税
     */
    private Integer freeTax;

    /**
     * 构建
     *
     * @param list
     * @return
     */
    public static List<AppItemsSearchVO> build(List<?> list) {
        if (list == null) {
            return null;
        }
        List<AppItemsSearchVO> resultList = new ArrayList<>();

        return resultList;
    }

}
