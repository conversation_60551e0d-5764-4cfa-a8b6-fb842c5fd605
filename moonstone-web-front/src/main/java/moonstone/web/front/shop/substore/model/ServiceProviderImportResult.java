package moonstone.web.front.shop.substore.model;


import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import moonstone.common.utils.ExcelField;
import moonstone.web.front.shop.substore.dto.ServiceProviderImportDTO;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class ServiceProviderImportResult {
    String uuid;
    List<ServiceProviderImportStruct> resultList;

    public ServiceProviderImportResult(String uuid, List<ServiceProviderImportDTO> structs) {
        List<ServiceProviderImportStruct> list = new ArrayList<>(structs.size());
        for (ServiceProviderImportDTO struct : structs) {
            ServiceProviderImportStruct a = new ServiceProviderImportStruct();
            BeanUtils.copyProperties(struct, a);
            list.add(a);
        }
        this.uuid = uuid;
        this.resultList = list;
    }


    @Data
    public static class ServiceProviderImportStruct {
        Long shopId;
        String mobile;
        String name;
        String province;
        String city;
        String county;
        String address;

        String businessImg;
        String frontImg;
        String backImg;

        /**
         * 银行名称
         */
        String bankName;

        /**
         * 银行账号
         */
        String bankAccountNo;

        /**
         * 收款公司名称
         */
        String bankAccountName;

        String reason;
    }
}
