package moonstone.web.front.shop.convert;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.shop.dto.shoppayinfo.ShopPayInfoDTO;
import moonstone.shop.dto.shoppayinfo.ShopPayInfoDetailDTO;
import moonstone.shop.dto.shoppayinfo.payConfig.*;
import moonstone.shop.enums.*;
import moonstone.shop.model.ShopPayInfo;
import moonstone.web.core.util.CommonValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class ShopPayInfoConvertor {

    @Resource
    private CommonValidator commonValidator;

    public ShopPayInfo convert(Long shopId, String payChannel, String detail, MultipartFile file) {
        var payChannelEnum = ShopPayInfoPayChannelEnum.parse(payChannel);
        if (payChannelEnum == null) {
            throw new RuntimeException("不支持的支付渠道");
        }

        var target = new ShopPayInfo();

        target.setHasChildren(false);
        target.setPayChannel(payChannel);
        target.setRecpCode(null);
        target.setRecpName(null);

        target.setShopId(shopId);
        target.setStatus(ShopPayInfoStatusEnum.INACTIVE.getCode());
        target.setUsageChannel(ShopPayInfoUsageChannelEnum.WECHAT_APP.getCode());
        target.setWxCertFileStatus(WxCertFileStatusEnum.NOT_UPLOAD.getCode());

        switch (payChannelEnum) {
            case UMF -> appendByUMF(target, detail);
            case ALIPAY -> appendByAliPay(target, detail);
            case WECHATPAY -> appendByWeChatPay(target, detail, file);
            case ALLINPAY -> appendByAllInPay(target, detail);
            case ALLINPAY_YST -> appendByAllInPayYST(target, detail);
        }

        return target;
    }

    private void appendByAliPay(ShopPayInfo target, String detail) {
        var config = JSON.parseObject(detail, AlipayConfigDetailDTO.class);
        if (config == null) {
            throw new RuntimeException("配置明细缺失");
        }
        if (StringUtils.isBlank(config.getPid()) || StringUtils.isBlank(config.getPaySecret())) {
            throw new RuntimeException("Pid与支付秘钥皆不能为空");
        }

        if (StringUtils.isNotBlank(config.getAccount())) {
            target.setAccountNo(config.getAccount());
        } else if (target.getId() == null) {
            target.setAccountNo(StringUtils.EMPTY);
        }

        target.setMchId(config.getPid());

        // 含星号，不更新
        if (!config.getPaySecret().contains("*") || target.getId() == null) {
            target.setSecretKey(config.getPaySecret());
        }
        target.setUsageChannel(ShopPayInfoUsageChannelEnum.PC_MALL.getCode());
    }

    private void appendByWeChatPay(ShopPayInfo target, String detail, MultipartFile file) {
        var config = JSON.parseObject(detail, WechatConfigDetailDTO.class);
        if (config == null) {
            throw new RuntimeException("配置明细缺失");
        }
        if (StringUtils.isBlank(config.getAppId()) || StringUtils.isBlank(config.getMchId()) || StringUtils.isBlank(config.getPaySecret())) {
            throw new RuntimeException("商户号/APPID/支付密钥，皆不能为空");
        }
        if (target.getId() == null && (file == null || file.isEmpty())) {
            throw new RuntimeException("必须上传证书");
        }

        target.setAccountNo(config.getAppId());
        target.setMchId(config.getMchId());

        // 含星号，不更新
        if (!config.getPaySecret().contains("*") || target.getId() == null) {
            target.setSecretKey(config.getPaySecret());
        }
        target.setWxCertFileStatus(WxCertFileStatusEnum.UPLOADED.getCode());
    }

    private void appendByUMF(ShopPayInfo target, String detail) {
        var config = JSON.parseObject(detail, UMFConfigDetailDTO.class);
        if (config == null) {
            throw new RuntimeException("配置明细缺失");
        }
        if (StringUtils.isBlank(config.getClientId()) || StringUtils.isBlank(config.getClientSecret())) {
            throw new RuntimeException("client_id与client_secret皆不能为空");
        }

        target.setAccountNo(config.getClientId());

        // 含星号的，不更新
        if (!config.getClientSecret().contains("*") || target.getId() == null) {
            target.setMchId(config.getClientSecret());
        }

        // 这个是瞎鸡脖填的, 当前umf是OAuth验证授权, 没有使用到什么支付密钥之类的。填这个只是为了让代码不报错？
        target.setSecretKey("https://dante-img.oss-cn-hangzhou.aliyuncs.com/online/3cf278355c161a7ec10146d819ba1bde.png");
    }

    public List<ShopPayInfoDTO> convert(List<ShopPayInfo> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }

        return sourceList.stream().map(this::convert).toList();
    }

    private ShopPayInfoDTO convert(ShopPayInfo source) {
        var target = new ShopPayInfoDTO();

        source.encryptionSecretKey();
        target.setDetail(convertDetail(source));

        target.setId(source.getId());
        target.setPayChannel(source.getPayChannel());
        target.setPayChannelName(ShopPayInfoPayChannelEnum.parseDescription(source.getPayChannel()));

        target.setShopId(source.getShopId());
        target.setShopName(null);
        target.setStatus(source.getStatus());
        target.setStatusName(ShopPayInfoStatusEnum.parseDescription(source.getStatus()));

        target.setUsageChannelName(ShopPayInfoUsageChannelEnum.parseDescription(source.getUsageChannel()));

        target.setDisable(Objects.equals(1, source.getStatus()) ? 1 : 2);
        return target;
    }

    private ShopPayInfoDetailDTO convertDetail(ShopPayInfo source) {
        var channel = ShopPayInfoPayChannelEnum.parse(source.getPayChannel());
        if (channel == null) {
            return null;
        }

        return switch (channel) {
            case UMF -> UMFConfigDetailDTO.from(source).encryptClientSecret();
            case WECHATPAY -> WechatConfigDetailDTO.from(source);
            case ALIPAY -> AlipayConfigDetailDTO.from(source);
            case ALLINPAY -> AllInPayConfigDetailDTO.from(source);
            case ALLINPAY_YST -> AllInPayYSTConfigDetailDTO.from(source);
            default -> null;
        };
    }

    public ShopPayInfo convert(Long shopId, ShopPayInfo existedObject, String payChannel, String detail, MultipartFile file) {
        if (existedObject == null) {
            throw new RuntimeException("指定配置已不存在");
        }
        if (!existedObject.getShopId().equals(shopId)) {
            throw new RuntimeException("该支付配置不属于当前用户");
        }
        if (!ShopPayInfoStatusEnum.INACTIVE.getCode().equals(existedObject.getStatus())) {
            throw new RuntimeException("当前配置不可编辑更新");
        }
        if (!existedObject.getPayChannel().equals(payChannel)) {
            throw new RuntimeException("支付渠道不能变更");
        }

        var payChannelEnum = ShopPayInfoPayChannelEnum.parse(payChannel);
        if (payChannelEnum == null) {
            throw new RuntimeException("不支持的支付渠道");
        }

        var updateObject = new ShopPayInfo();
        updateObject.setId(existedObject.getId());
        updateObject.setShopId(existedObject.getShopId());
        updateObject.setPayChannel(payChannel);
        updateObject.setExtra(existedObject.getExtra());

        switch (payChannelEnum) {
            case UMF -> appendByUMF(updateObject, detail);
            case ALIPAY -> appendByAliPay(updateObject, detail);
            case WECHATPAY -> appendByWeChatPay(updateObject, detail, file);
            case ALLINPAY -> appendByAllInPay(updateObject, detail);
            case ALLINPAY_YST -> appendByAllInPayYST(updateObject, detail);
        }

        return updateObject;
    }

    private void appendByAllInPay(ShopPayInfo target, String detail) {
        var config = JSON.parseObject(detail, AllInPayConfigDetailDTO.class);
        if (config == null) {
            throw new RuntimeException("配置明细缺失");
        }
        if (StringUtils.isBlank(config.getAppid()) || StringUtils.isBlank(config.getCusid()) ||
                StringUtils.isBlank(config.getSignType()) || StringUtils.isBlank(config.getPrivateKey()) ||
                StringUtils.isBlank(config.getPublicKey())) {
            throw new RuntimeException("通联商户号/通联APPID/签名方式/公钥/私钥，皆不能为空");
        }

        target.setMchId(config.getCusid());
        target.setAccountNo(config.getAppid());

        // 含星号的，不更新
        if (!config.getPrivateKey().contains("*") || target.getId() == null) {
            target.setSecretKey(config.getPrivateKey());
        }
        if (!config.getPublicKey().contains("*") || target.getId() == null) {
            target.setPublicKey(config.getPublicKey());
        }

        var extra = target.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        extra.put(ShopPayInfoExtraIndexEnum.SIGN_TYPE.getCode(), config.getSignType());
        target.setExtra(extra);
    }

    private void appendByAllInPayYST(ShopPayInfo target, String detail) {
        var config = JSON.parseObject(detail, AllInPayYSTConfigDetailDTO.class);
        if (config == null) {
            throw new RuntimeException("配置明细缺失");
        }
        String errorMessage = commonValidator.getFirstErrorMessage(config, AllInPayYSTConfigDetailDTO.class);
        if (StringUtils.isNotBlank(errorMessage)) {
            throw new RuntimeException(errorMessage);
        }

        var extra = target.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }

        target.setAccountNo(config.getAppId());

        if (target.getId() == null) {
            target.setMchId(StringUtils.EMPTY);
        } else if (StringUtils.isNotBlank(config.getBizUserId())) {
            target.setMchId(config.getBizUserId());
        }

        if (!config.getSecretKey().contains("*") || target.getId() == null) {
            target.setSecretKey(config.getSecretKey());
        }
        if (!config.getCertPassword().contains("*") || target.getId() == null) {
            extra.put(ShopPayInfoExtraIndexEnum.CERT_PASSWORD.getCode(), config.getCertPassword());
        }

        extra.put(ShopPayInfoExtraIndexEnum.ACCOUNT_SET_NO.getCode(), config.getAccountSetNo());
        extra.put(ShopPayInfoExtraIndexEnum.BIZ_USER_PERCENT.getCode(), config.getBizUserPercent().toString());
        extra.put(ShopPayInfoExtraIndexEnum.VSP_APP_ID.getCode(), config.getVspAppId());
        extra.put(ShopPayInfoExtraIndexEnum.VSP_CUSID.getCode(), config.getVspCusid());

        if (StringUtils.isNotBlank(config.getVspMerchantid())) {
            extra.put(ShopPayInfoExtraIndexEnum.VSP_MERCHANTID.getCode(), config.getVspMerchantid());
        } else {
            extra.remove(ShopPayInfoExtraIndexEnum.VSP_MERCHANTID.getCode());
        }

        if (StringUtils.isNotBlank(config.getVspTermid())) {
            extra.put(ShopPayInfoExtraIndexEnum.VSP_TERMID.getCode(), config.getVspTermid());
        } else {
            extra.remove(ShopPayInfoExtraIndexEnum.VSP_TERMID.getCode());
        }

        target.setExtra(extra);
    }
}
