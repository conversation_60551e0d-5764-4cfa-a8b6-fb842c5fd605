package moonstone.web.front.shop.substore.component;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.api.ProfitGain;
import moonstone.item.dto.ProfitReVO;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.search.dto.SearchedItem;
import moonstone.web.front.shop.substore.vo.ItemWithProfit;
import moonstone.web.front.shop.substore.vo.SkuWithProfit;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ItemVoPacker {
    @RpcConsumer
    private SkuReadService skuReadService;
    @RpcConsumer
    private ProfitGain profitGain;

    public ItemWithProfit packSearchedItemVo(SearchedItem entity) {
        if (entity == null) {
            return null;
        }
        ItemWithProfit itemWithProfit = new ItemWithProfit(entity);
        pack(itemWithProfit);
        return itemWithProfit;
    }
    public List<ItemWithProfit> packSearchedItemVo(List<SearchedItem> list) {
        return list.stream()
                .filter(Objects::nonNull)
                .map(ItemWithProfit::new)
                .peek(this::pack)
                .collect(Collectors.toList());
    }
    public List<ItemWithProfit> packItemVo(List<moonstone.item.model.Item> list) {
        return list.stream()
                .filter(Objects::nonNull)
                .map(ItemWithProfit::new)
                .peek(this::pack)
                .collect(Collectors.toList());
    }

    List<Sku> getSkusIO(ItemWithProfit itemWithProfit) {
        return Optional.ofNullable(skuReadService.findSkusByItemId(itemWithProfit.getId()).getResult()).orElseGet(() -> {
                    log.warn("[ItemVoPacker](getSkusIO) generate empty list for item:{}", itemWithProfit.getId());
                    return new ArrayList<>();
                }
        );
    }

    void pack(ItemWithProfit itemWithProfit) {
        if (itemWithProfit.getSkuList() == null || itemWithProfit.getSkuList().isEmpty()) {
            pack(itemWithProfit, getSkusIO(itemWithProfit));
        } else {
            pack(itemWithProfit, new ArrayList<>(itemWithProfit.getSkuList()));
        }
    }

    public SkuWithProfit pack(Sku sku) {
        SkuWithProfit skuWithProfit = new SkuWithProfit(sku);
        skuWithProfit.setProfit(
                new ProfitReVO(
                        Optional.ofNullable(
                                profitGain.getProfit(sku).getResult())
                                .orElseGet(() -> {
                                    log.error("[ItemVoPacker](pack) profit gain failed for SkuId:{}", sku.getId());
                                    return null;
                                })
                        , sku)
        );
        return skuWithProfit;
    }

    public void pack(ItemWithProfit itemWithProfit, List<Sku> skus) {
        Consumer<SkuWithProfit> packProfit = (skuDTO) -> {
            skuDTO.setProfit(new ProfitReVO(profitGain.getProfit(skuDTO).getResult(), skuDTO));
        };
        itemWithProfit.setSkuList(skus.stream().map(SkuWithProfit::new).peek(packProfit).collect(Collectors.toList()));
    }
}
