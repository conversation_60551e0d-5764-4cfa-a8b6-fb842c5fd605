package moonstone.web.front.shop.substore.dto;

import lombok.Getter;
import lombok.Setter;
import moonstone.shop.model.SubStoreTStoreGuider;
import org.springframework.beans.BeanUtils;

public class GuiderVO extends SubStoreTStoreGuider implements UserRelated {

    /**
     * 服务商名称
     */
    @Getter
    @Setter
    private String serviceProviderName;

    public GuiderVO(SubStoreTStoreGuider subStoreTStoreGuider) {
        BeanUtils.copyProperties(subStoreTStoreGuider, this);
    }

    @Override
    public Long getUserId() {
        return getStoreGuiderId();
    }
}
