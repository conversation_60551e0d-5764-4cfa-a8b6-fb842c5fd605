package moonstone.web.front.shop.substore.convert;

import moonstone.item.emu.ItemStatusEnum;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.web.core.component.item.model.SubStoreAreaModel;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.model.ServiceProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SubStoreAreaModelConvertor {

    @Autowired
    private ServiceProviderCache serviceProviderCache;

    @Resource
    private ItemReadService itemReadService;

    /**
     * 填充对应的服务商名称
     *
     * @param sourceList
     */
    public void appendServiceProviderName(List<SubStoreAreaModel> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return;
        }

        // 所有的服务商id
        Set<String> ids = sourceList.stream().map(SubStoreAreaModel::getServiceProviderId)
                .filter(list -> !CollectionUtils.isEmpty(list))
                .flatMap(List::stream).collect(Collectors.toSet());

        // 服务商map(key = ServiceProvider.id)
        var serviceProviderMap = findServiceProviderMap(ids);

        // 填充名称
        sourceList.forEach(model -> {
            if (CollectionUtils.isEmpty(model.getServiceProviderId())) {
                return;
            }

            model.setArea(model.getServiceProviderId().stream()
                    .map(serviceProviderMap::get)
                    .filter(Objects::nonNull)
                    .map(ServiceProvider::getName)
                    .collect(Collectors.toList()));
        });
    }

    /**
     * 填充对应的商品名称，并过滤掉已下架的商品
     *
     * @param sourceList
     */
    public void appendItemName(List<SubStoreAreaModel> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return;
        }

        // 所有的商品id
        Set<Long> ids = sourceList.stream().map(SubStoreAreaModel::getItemId)
                .filter(list -> !CollectionUtils.isEmpty(list))
                .flatMap(List::stream).collect(Collectors.toSet());

        // 商品map(key = item.id)
        var itemMap = findItemMap(ids);

        // 填充名称并过滤
        sourceList.forEach(model -> {
            if (CollectionUtils.isEmpty(model.getItemId())) {
                return;
            }

            // 只要已上架的
            var items = model.getItemId().stream()
                    .map(itemMap::get)
                    .filter(Objects::nonNull)
                    .filter(item -> ItemStatusEnum.ON_SHELF.getCode().equals(item.getStatus()))
                    .collect(Collectors.toList());

            model.setItemId(items.stream().map(Item::getId).collect(Collectors.toList()));
            model.setItem(items.stream().map(Item::getName).collect(Collectors.toList()));
        });
    }

    private Map<Long, Item> findItemMap(Set<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }

        var list = itemReadService.findByIds(new ArrayList<>(itemIds)).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(Item::getId, o -> o, (k1, k2) -> k1));
    }

    private Map<String, ServiceProvider> findServiceProviderMap(Set<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyMap();
        }

        var list = serviceProviderCache.findByIds(new ArrayList<>(idList));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(ServiceProvider::getId, o -> o, (k1, k2) -> k1));
    }
}
