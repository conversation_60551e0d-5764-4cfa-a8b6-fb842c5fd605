package moonstone.web.front.shop.substore.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.item.dto.ProfitReVO;
import moonstone.item.model.Sku;
import org.springframework.beans.BeanUtils;

@EqualsAndHashCode(callSuper = true)
@Data
public class SkuWithProfit extends Sku {
    ProfitReVO profit;

    public SkuWithProfit(Sku sku) {
        BeanUtils.copyProperties(sku, this);
    }
}
