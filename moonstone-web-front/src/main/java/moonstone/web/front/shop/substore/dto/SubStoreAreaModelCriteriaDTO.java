package moonstone.web.front.shop.substore.dto;

import lombok.Data;
import moonstone.common.model.PagingCriteria;

import java.io.Serial;

@Data
public class SubStoreAreaModelCriteriaDTO extends PagingCriteria {
    @Serial
    private static final long serialVersionUID = 4649216529729352074L;

    /**
     * 商家平台id
     */
    private Long shopId;

    /**
     * 服务商名称
     */
    private String serviceProviderName;

    /**
     * 省份
     */
    private String province;
}
