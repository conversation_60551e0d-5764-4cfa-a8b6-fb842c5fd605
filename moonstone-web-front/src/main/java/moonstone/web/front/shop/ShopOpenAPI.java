package moonstone.web.front.shop;

import com.danding.common.net.Response;
import moonstone.common.model.CommonUser;
import moonstone.common.model.ShopOpenApiToken;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UUID;
import moonstone.common.utils.UserUtil;
import moonstone.web.core.component.OutSystemServiceProvider;
import moonstone.web.core.model.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import scala.util.Either;


@Deprecated
public class ShopOpenAPI {
    final Logger log = LoggerFactory.getLogger(getClass());
    @Autowired
    private OutSystemServiceProvider outSystemServiceProvider;
    @Autowired
    private MongoTemplate mongoTemplate;

    @GetMapping("/getToken")
    public Response<ShopOpenApiToken> getToken() {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null || user.getShopId() == null) {
            return Response.fail(new Translate("请以商户身份登录").toString());
        }
        Query query = Query.query(Criteria.where("shopId").is(user.getShopId()));
        if (!mongoTemplate.exists(query, ShopOpenApiToken.class)) {
            String key = UUID.randomUUID().toString().substring(0, 12);
            if (mongoTemplate.exists(query, ShopOpenApiToken.class))
                return getToken();
            mongoTemplate.upsert(query, Update.update("key", key).set("secret", UUID.randomUUID()), ShopOpenApiToken.class);
        }
        return Response.ok(mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(user.getShopId())), ShopOpenApiToken.class));
    }

    @PostMapping("/integralQuery")
    public Response<IntegralQueryRes> integralQuery(@RequestBody OutSystemRequest outSystemRequest) {
        ShopOpenApiToken shopOpenApiToken = mongoTemplate.findOne(Query.query(Criteria.where("key").is(outSystemRequest.key())), ShopOpenApiToken.class);
        Either<String, IntegralQueryRequestDTO> requestDTOEither = outSystemRequest.decode(shopOpenApiToken.getSecret(), IntegralQueryRequestDTO.class);
        if (requestDTOEither.isLeft()) {
            return Response.fail(requestDTOEither.left().get());
        }
        return outSystemServiceProvider.findIntegralInfoBy(shopOpenApiToken.getShopId(), requestDTOEither.right().get())
                .right().toOption()
                .map(Response::ok)
                .getOrElse(() -> Response.fail(new Translate("查询失败,联系客服").toString()));
    }

    @PostMapping("/storeQuery")
    public Response<StoreQueryRes> storeQuery(@RequestBody OutSystemRequest outSystemRequest) {
        log.debug("{} request [{}]", LogUtil.getClassMethodName(), outSystemRequest);
        ShopOpenApiToken shopOpenApiToken = mongoTemplate.findOne(Query.query(Criteria.where("key").is(outSystemRequest.key())), ShopOpenApiToken.class);
        Either<String, StoreQueryRequestDTO> requestDTOEither = outSystemRequest.decode(shopOpenApiToken.getSecret(), StoreQueryRequestDTO.class);
        log.debug("{} decode-Request [{}]", LogUtil.getClassMethodName(), requestDTOEither);
        if (requestDTOEither.isLeft()) {
            return Response.fail(requestDTOEither.left().get());
        }
        return outSystemServiceProvider.findStoreProxyBy(shopOpenApiToken.getShopId(), requestDTOEither.right().get())
                .right().toOption()
                .map(Response::ok)
                .getOrElse(() -> Response.fail(new Translate("查询失败,联系客服").toString()));
    }

    @PostMapping("/userQuery")
    public Response<UserQueryRes> userQuery(@RequestBody OutSystemRequest outSystemRequest) {
        ShopOpenApiToken shopOpenApiToken = mongoTemplate.findOne(Query.query(Criteria.where("key").is(outSystemRequest.key())), ShopOpenApiToken.class);
        Either<String, UserRegisterRequestDTO> requestDTOEither = outSystemRequest.decode(shopOpenApiToken.getSecret(), UserRegisterRequestDTO.class);
        if (requestDTOEither.isLeft()) {
            return Response.fail(requestDTOEither.left().get());
        }
        return outSystemServiceProvider.findOrRegisterUser(shopOpenApiToken.getShopId(), requestDTOEither.right().get())
                .right().toOption()
                .map(Response::ok)
                .getOrElse(() -> Response.fail(new Translate("查询失败,联系客服").toString()));
    }

    @PostMapping("/orderQuery")
    public Response<OrderQueryRes> orderQuery(@RequestBody OutSystemRequest outSystemRequest) {
        ShopOpenApiToken shopOpenApiToken = mongoTemplate.findOne(Query.query(Criteria.where("key").is(outSystemRequest.key())), ShopOpenApiToken.class);
        Either<String, OrderQueryRequestDTO> requestDTOEither = outSystemRequest.decode(shopOpenApiToken.getSecret(), OrderQueryRequestDTO.class);
        if (requestDTOEither.isLeft()) {
            return Response.fail(requestDTOEither.left().get());
        }
        return outSystemServiceProvider.findOrderBy(shopOpenApiToken.getShopId(), requestDTOEither.right().get())
                .right().toOption()
                .map(Response::ok)
                .getOrElse(() -> Response.fail(new Translate("查询失败,联系客服").toString()));
    }
}
