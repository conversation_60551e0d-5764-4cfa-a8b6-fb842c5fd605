package moonstone.web.front.shop.jifen;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.IntegralStatus;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.IntegralUseRecord;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.IntegralUseRecordWriteService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.service.ShopReadService;
import moonstone.user.model.StoreIntegral;
import moonstone.user.model.StoreIntegralRecord;
import moonstone.user.service.StoreIntegralReadService;
import moonstone.user.service.StoreIntegralRecordReadService;
import moonstone.user.service.StoreIntegralRecordWriteService;
import moonstone.user.service.StoreIntegralWriteService;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/9/7 14:21
 * 处理累计积分账户从确认收货之后开启加积分
 */
@Component
@EnableScheduling
@Slf4j
public class IntegralAccountConutTimerJob {

    @RpcConsumer
    StoreIntegralWriteService storeIntegralWriteService;

    @RpcConsumer
    StoreIntegralReadService storeIntegralReadService;

    @RpcConsumer
    StoreIntegralRecordWriteService storeIntegralRecordWriteService;

    @RpcConsumer
    StoreIntegralRecordReadService storeIntegralRecordReadService;

    @RpcConsumer
    IntegralUseRecordWriteService integralUseRecordWriteService;

    @RpcConsumer
    private ShopReadService shopReadService;

    @RpcConsumer
    private SkuOrderReadService skuOrderReadService;

//    @Scheduled(cron = "0 */10 * * * ?")
    public void integralAccountConut() {
        try {
            Long shopId = 2L;
            Response<List<StoreIntegral>> availableGrade = storeIntegralReadService.findByShopId(shopId);
            if (!availableGrade.isSuccess() || availableGrade.getResult().isEmpty()) {
                return;
            }
            for (StoreIntegral stl : availableGrade.getResult()) {
                if (!Objects.equals(stl.getStatus(), IntegralStatus.NORMAL.value())) {
                    continue;
                }
                StoreIntegral storeIntegral = stl;
                Response<List<StoreIntegralRecord>> recordList = storeIntegralRecordReadService.findByIntegralId(storeIntegral.getId(), IntegralStatus.FROZEN.value());
                Long countFee = 0L;
                if (recordList.isSuccess() && !recordList.getResult().isEmpty()) {
                    for (StoreIntegralRecord s : recordList.getResult()) {
                        if (!ObjectUtils.isEmpty(s.getThirdId())) {
                            Response<SkuOrder> skuRes = skuOrderReadService.findById(Long.valueOf(s.getThirdId()));
                            if (skuRes.isSuccess()
                                    && !ObjectUtils.isEmpty(skuRes.getResult().getId())
                                    && Objects.equals(skuRes.getResult().getStatus(), OrderStatus.CONFIRMED.getValue())) {
                                countFee += s.getRemainValue();
                                StoreIntegralRecord storeIntegralRecord = new StoreIntegralRecord();
                                storeIntegralRecord.setId(s.getId());
                                storeIntegralRecord.setStatus(IntegralStatus.AVAILABLE.value());
                                storeIntegralRecordWriteService.update(storeIntegralRecord);

                                //记录扫码记录-mongodb
                                IntegralUseRecord integralUseRecord = new IntegralUseRecord();
                                integralUseRecord.setCreateAt(System.currentTimeMillis());
                                integralUseRecord.setUpdateAt(System.currentTimeMillis());
                                integralUseRecord.setStatus(IntegralStatus.AVAILABLE.value());
                                integralUseRecord.setUseIntegral(s.getRemainValue());
                                integralUseRecord.setUserId(storeIntegral.getUserId());
                                integralUseRecord.setShopId(shopId);
                                integralUseRecord.setTradeId(Long.valueOf(s.getThirdId()));
                                integralUseRecordWriteService.createIntegralUseRecord(integralUseRecord);

                            }
                        }
                    }
                }
                if (countFee > 0) {
                    storeIntegral.setAvailableGrade(storeIntegral.getAvailableGrade() + countFee);
                    storeIntegral.setTotalGrade(storeIntegral.getTotalGrade() + countFee);
                    Either<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral);
                    if (!booleanResult.isSuccess()) {
                        log.error("storeIntegral is update fail storeIntegral:{}", storeIntegral);
                        continue;
                    }
                }
            }
        } catch (Exception ex) {
            log.error("{} integralAccountConut-error  ex:{}", LogUtil.getClassMethodName("IntegralAccountConutTimerJob"),
                    Throwables.getStackTraceAsString(ex));
        }
    }

}
