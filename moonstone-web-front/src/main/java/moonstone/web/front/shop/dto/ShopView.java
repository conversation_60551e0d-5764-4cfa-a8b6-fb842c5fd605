package moonstone.web.front.shop.dto;

import lombok.Data;
import moonstone.common.constants.ShopExtra;
import moonstone.shop.model.Shop;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@Data

public class ShopView {
    String name;
    String userName;
    String phone;
    String shopType;

    public static List<ShopView> from(List<Shop> shopList) {
        List<ShopView> shopViewList = new ArrayList<>();
        for (Shop shop : shopList) {
            ShopView shopView = new ShopView();
            BeanUtils.copyProperties(shop, shopView);
            shopView.setShopType(Optional.ofNullable(shop.getExtra()).orElseGet(HashMap::new).get(ShopExtra.SalesPattern.getCode()));
            shopViewList.add(shopView);
        }
        return shopViewList;
    }
}
