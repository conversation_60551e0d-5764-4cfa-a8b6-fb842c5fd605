package moonstone.web.front.shop.substore;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.api.Result;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.enums.OpBusinessType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.*;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.dto.InComeDetail;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.SubProxySum;
import moonstone.order.service.HonestFanDataReadService;
import moonstone.order.service.NormalFanDataReadService;
import moonstone.order.service.OrderReadService;
import moonstone.order.service.SubProxyDataReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.shop.service.ShopWriteService;
import moonstone.user.dto.StoreProxyCriteria;
import moonstone.user.criteria.UserProfilesCriteria;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.model.*;
import moonstone.user.service.*;
import moonstone.web.core.component.order.BirthProfitRecorder;
import moonstone.web.core.exports.common.Exporter;
import moonstone.web.core.model.dto.CurrentProfitVO;
import moonstone.web.core.user.StoreProxyManager;
import moonstone.web.core.util.StoreProxyAuthProxy;
import moonstone.web.front.component.order.OrderReader;
import moonstone.web.front.shop.substore.component.DistributionUserExtractor;
import moonstone.web.front.shop.substore.vo.StoreProxyView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;

@RestController
@RequestMapping("/api/storeProxy")
@Slf4j
public class StoreProxyController {
    @Autowired
    StoreIntegralLogReadService storeIntegralLogReadService;
    @Autowired
    StoreProxyInfoReadService storeProxyInfoReadService;
    @Autowired
    OrderReadService orderReadService;
    @Autowired
    BalanceDetails balanceDetails;
    @Autowired
    OrderReader orderReader;
    @Autowired
    private UserReadService<User> userReadService;
    @Autowired
    private StoreProxyManager storeProxyManager;
    @Autowired
    private ShopReadService shopReadService;
    @Autowired
    private ShopWriteService shopWriteService;
    @Autowired
    private StoreProxyReadService storeProxyReadService;
    @Autowired
    private StoreProxyWriteService storeProxyWriteService;
    @Autowired
    private UserProfileReadService userProfileReadService;
    @Autowired
    private BalanceDetailManager balanceDetailManager;
    @Autowired
    private SubProxyDataReadService subProxyDataReadService;
    @Autowired
    private NormalFanDataReadService normalFanDataReadService;
    @Autowired
    private HonestFanDataReadService honestFanDataReadService;
    @Autowired
    private DistributionUserExtractor distributionUserExtractor;
    @Autowired
    private BirthProfitRecorder birthProfitRecorder;
    @Autowired
    private UserTypeBean userTypeBean;

    @Autowired
    private ShopCacheHolder shopCacheHolder;

    @Autowired
    private Exporter exporter;


    @GetMapping("/default")
    public Long getDefaultProxyId(Long shopId) {
        return Optional.ofNullable(shopCacheHolder.findShopById(shopId).getExtra())
                .map(extra -> extra.get("defaultProxy")).map(Long::parseLong).orElse(null);
    }

    @PostMapping("/default")
    public Boolean setDefaultProxyId(Long proxyId) {
        CommonUser operator = UserUtil.getCurrentUser();
        log.debug("{} operator [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(operator));
        if (!userTypeBean.isSeller(operator)) {
            throw new JsonResponseException(Translate.of("身份验证失败, 请重新登录"));
        }
        Shop update = new Shop();
        Map<String, String> shopExtra = Optional.ofNullable(shopCacheHolder.findShopById(operator.getShopId()).getExtra()).orElseGet(HashMap::new);
        shopExtra.put("defaultProxy", proxyId + "");
        update.setExtra(shopExtra);
        update.setId(operator.getShopId());
        shopWriteService.update(update);
        shopCacheHolder.invalidate(operator.getShopId());
        return true;
    }

    /**
     * 注册门店
     *
     * @param shopId    店铺Id
     * @param inviterId 邀请人Id
     * @return 是否注册成功
     */
    @RequestMapping("/invite-proxy")
    public Boolean inviteSubProxy(@RequestParam long shopId, @RequestParam(required = false) Long inviterId,
                                  @RequestParam Map<String, String> storeProxyInfo) {
        log.info("[invite-proxy] user_id:{} inviterId:{} shopId:{},s:{}", UserUtil.getUserId(), inviterId, shopId, storeProxyInfo);

        Response<Boolean> booleanResponse = shopReadService.checkExistByUserId(UserUtil.getUserId());
        if (booleanResponse.isSuccess() && booleanResponse.getResult()) {
            log.error("{} is account invite-proxy error", LogUtil.getClassMethodName());
            return false;
        }

        User user = Optional.ofNullable(userReadService.findById(UserUtil.getUserId()).getResult()).orElseThrow(() -> new JsonResponseException("user.not.login"));
        User inviter = Optional.ofNullable(inviterId).map(userReadService::findById).map(Response::getResult).orElse(null);
        Shop shop = Optional.ofNullable(shopReadService.findById(shopId).getResult()).orElseThrow(() -> new JsonResponseException("shop.not.find"));
        return storeProxyManager.inviteSubGuider(shop, inviter, user, storeProxyInfo).isSuccess();
    }

    /**
     * 合作接口
     * 用于设置是否合作
     *
     * @param ifCoop 是否合作
     * @param shopId 商店id
     */
    @PostMapping("/coop")
    public boolean coop(@RequestParam(defaultValue = "true") boolean ifCoop, Long shopId) {
        Long userId = Optional.ofNullable(UserUtil.getUserId()).orElseThrow(() -> new JsonResponseException("user.not.login"));
        Function<StoreProxy, StoreProxy> genUpdate = entity -> {
            StoreProxyAuthProxy authAble = StoreProxyAuthProxy.build(entity);
            if (authAble.isPending() || authAble.isReject()) {
                return null;
            }
            StoreProxy update = new StoreProxy();
            update.setId(entity.getId());
            if (ifCoop) {
                entity.freeze();
            } else {
                entity.unFreeze();
            }
            return entity;
        };
        return storeProxyManager.getStoreProxyByShopIdAndUserId(shopId, userId)
                .map(genUpdate)
                .map(storeProxyWriteService::updateWithStatus)
                .orElseThrow(() -> Translate.exceptionOf("数据丢失")).take();
    }

    /**
     * 注册经销商
     */
    @RequestMapping("/reg-proxy")
    public Boolean regProxy(@RequestParam long shopId,
                            @RequestParam Map<String, String> storeProxys) {
        Response<Boolean> shopOwnerCheckRes = shopReadService.checkExistByUserId(UserUtil.getUserId());
        if (shopOwnerCheckRes.isSuccess() && shopOwnerCheckRes.getResult()) {
            log.error("{} is account reg-proxy error", LogUtil.getClassMethodName());
            return false;
        }

        User user = Optional.ofNullable(userReadService.findById(UserUtil.getUserId()).getResult()).orElseThrow(() -> new JsonResponseException("user.not.login"));
        Shop shop = Optional.ofNullable(shopReadService.findById(shopId).getResult()).orElseThrow(() -> new JsonResponseException("shop.not.find"));
        return storeProxyManager.regStoreProxy(shop, user, storeProxys).isSuccess();
    }

    /**
     * 贝拉米同意申请
     */
    @GetMapping("/auth")
    @MyLog(title = "分销管理->品牌代言人审核", value = "商家同意申请", opBusinessType = OpBusinessType.UPDATE)
    public Boolean authProxy(long userId, String auditingRemark) {
        CommonUser user = UserUtil.getCurrentUser();
        return storeProxyManager.authStoreProxy(user.getShopId(), userId, auditingRemark).isSuccess();
    }

    /**
     * 经销商同意申请
     */
    @GetMapping("/jxAuth")
    public Boolean jxAuthProxy(long userId, long shopId, String jxRemark) {
        return storeProxyManager.jxAuthStoreProxy(shopId, userId, jxRemark).isSuccess();
    }

    /**
     * 获取累计收益
     */
    @GetMapping("/getSubProxySum")
    public Long getSubProxySum(@RequestParam(defaultValue = "33") Long shopId) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            throw new JsonResponseException(new Translate("请登录").toString());
        }
        return balanceDetailManager.getEarned(user.getId(), shopId).map(BalanceDetail::getFee).orElse(0L);
    }

    /**
     * 获取页面所有总数据
     */
    @GetMapping("/getAllSubProxySum")
    public Response<StoreProxyAllDetail> getAllSubProxySum(Long shopId) {
        StoreProxyAllDetail storeProxyAllDetail = new StoreProxyAllDetail();
        //获取累计总收益  总订单数  总销售额
        CommonUser user = UserUtil.getCurrentUser();
        Long userId = user.getId();

        Either<Optional<SubProxySum>> subProxySum = subProxyDataReadService.findByUserId(userId, shopId);
        if (!subProxySum.isSuccess() || subProxySum.take().isEmpty()) {
            storeProxyAllDetail.setAllProfit(0L);
            storeProxyAllDetail.setAllNum(0L);
            storeProxyAllDetail.setAllSales(0L);
        } else {
            storeProxyAllDetail.setAllProfit(subProxySum.take().get().getIncomeSum());
            storeProxyAllDetail.setAllNum(subProxySum.take().get().getOrderNum());
            storeProxyAllDetail.setAllSales(subProxySum.take().get().getTradeFeeSum());
        }
        //获取积分拉新佣金计入累计收益里面
        Long integralFee = 0L;
        Either<List<StoreIntegralLog>> storeIntegralLog = storeIntegralLogReadService.findIntegralBySupperId(userId);
        if (storeIntegralLog.isSuccess() && !storeIntegralLog.take().isEmpty()) {
            for (StoreIntegralLog storeIntegralLogList : storeIntegralLog.take()) {
                integralFee += storeIntegralLogList.getIntegralFee();
            }
        }
        storeProxyAllDetail.setAllProfit(storeProxyAllDetail.getAllProfit() + integralFee);

        //获取可提现金额  和待收益数据
        CurrentProfitVO currentProfitVO = getWithdrawAbleProfitBalance(shopId);
        if (currentProfitVO == null) {
            storeProxyAllDetail.setAvalidProfit(0L);
            storeProxyAllDetail.setForecastProfit(0L);
        } else {
            storeProxyAllDetail.setAvalidProfit(currentProfitVO.getAvalidProfit());
            storeProxyAllDetail.setForecastProfit(currentProfitVO.getForecastProfit());
        }
        //获取所有已赚取
        Long earned = balanceDetailManager.getEarned(userId, shopId).take().getFee();
        storeProxyAllDetail.setAllProfit(earned);
        //获取待审核的数
        Long num = getSubProxy(shopId);
        storeProxyAllDetail.setNum(num);
        //获取昨天的交易数据
        BalanceDetailManager.ProfitDataVO lastNum = balanceDetails.getOrderSum(shopId, 1);

        if (ObjectUtils.isEmpty(lastNum)) {
            storeProxyAllDetail.setLastNum(0L);
            storeProxyAllDetail.setLastForecastProfit(0L);
            storeProxyAllDetail.setLastSales(0L);
        } else {
            storeProxyAllDetail.setLastNum(lastNum.getCount());
            storeProxyAllDetail.setLastForecastProfit(lastNum.getUnsettledProfit());
            storeProxyAllDetail.setLastSales(lastNum.getPaymentSum());
        }
        //获取退款的数据
        OrderCriteria orderCriteria = new OrderCriteria();
        orderCriteria.setRefererId(userId);
        orderCriteria.setShopId(shopId);
        orderCriteria.setOutFrom(OrderOutFrom.LEVEL_Distribution.Code());
        Long refund = orderReader.countRefund(orderCriteria);
        storeProxyAllDetail.setRefund(refund);

        storeProxyAllDetail.setFrozenProfit(birthProfitRecorder.queryFrozeProfit(userId, shopId).orElse(0L));

        return Response.ok(storeProxyAllDetail);
    }

    /**
     * 获取可提现金额
     */
    public CurrentProfitVO getWithdrawAbleProfitBalance(Long sourceId) {
        /// 不需要保证门店的存在 因为是和导购员共用
        long userId = Optional.ofNullable(UserUtil.getUserId()).orElseThrow(() -> Translate.exceptionOf("用户未登录"));
        log.info("[profit_balance] userId={}", userId);
        val res = balanceDetailManager.getCashByStoreProxy(userId, sourceId);
        log.info("[profit_balance1] res={}", res);
        if (!res.isSuccess()) {
            log.error("[Profit](search) find cash error cause:{}", res.getError());
            throw new JsonResponseException(res.getError());
        }
        if (CollectionUtils.isEmpty(res.getResult())) {
            return new CurrentProfitVO();
        }
        CurrentProfitVO dto = new CurrentProfitVO();
        for (InComeDetail inComeDetail : res.getResult()) {
            if (inComeDetail.isPresent()) {
                dto.setAvalidProfit(inComeDetail.getFee());
            } else {
                dto.setForecastProfit(inComeDetail.getFee());
            }
        }
        dto.setProfit(dto.getAvalidProfit() + dto.getForecastProfit());
        return dto;
    }

    /**
     * 品牌代言人导出
     */
    @GetMapping("/export")
    public void pagingExport(StoreProxyCriteria criteria, HttpServletRequest request, HttpServletResponse response) {
        try {
            criteria.setPageSize(Integer.MAX_VALUE);
            Paging<StoreProxy> paging = paging(criteria, null);
            List<StoreProxyExport> list = new ArrayList<>();
            for (StoreProxy s : paging.getData()) {
                StoreProxyExport storeProxyExport = new StoreProxyExport();
                storeProxyExport.setId(s.getId() + "");
                storeProxyExport.setNickName(Optional.ofNullable(s.getRealName()).orElse(""));
                storeProxyExport.setReferenceName(Optional.ofNullable(s.getParentUserName()).orElse(""));
                storeProxyExport.setProxyCradName(Optional.ofNullable(s.getProxyShopName()).orElse(""));
                storeProxyExport.setProvince(Optional.ofNullable(s.getProvince()).orElse(""));
                storeProxyExport.setCity(Optional.ofNullable(s.getCity()).orElse(""));
                storeProxyExport.setCounty(Optional.ofNullable(s.getCounty()).orElse(""));
                if (Objects.equals(s.getFlag(), 1)) {
                    storeProxyExport.setFlag("合作");
                } else {
                    storeProxyExport.setFlag("不合作");
                }
                storeProxyExport.setType(Optional.ofNullable(s.getExtra()).orElseGet(HashMap::new).get("type"));
                storeProxyExport.setStatus(infoStatus(s.getStatus()));
                storeProxyExport.setShenheTime(infoTime(s.getUpdatedAt()));
                storeProxyExport.setCreateTime(infoTime(s.getUpdatedAt()));
                list.add(storeProxyExport);
            }
            exporter.export(list, StoreProxyExport.class, request, response);
        } catch (Exception e) {
            log.error("failed find to  cause:{}", Throwables.getStackTraceAsString(e));
        }
    }

    private String infoTime(Date date) {
        if (ObjectUtils.isEmpty(date)) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    private String infoStatus(Integer status) {
        if (ObjectUtils.isEmpty(status)) {
            return "";
        }
        String statusStr = "";
        switch (status) {
            case 3:
                statusStr = "审核通过";
                break;
            case 5:
                statusStr = "商家审核拒绝";
                break;
            case 7:
                statusStr = "待审核";
                break;
            case 9:
                statusStr = "经销商审核拒绝";
                break;
            case 11:
                statusStr = "经销商待审核";
                break;
            default:
        }
        return statusStr;
    }

    /**
     * 品牌代言人审核
     * infoType ---用于刷选身份信息变更审核  infoType-1
     */
    @GetMapping("/page")
    public Paging<StoreProxy> paging(StoreProxyCriteria criteria, @RequestParam(defaultValue = "0") Integer infoType) {
        log.info("[StoreProxyCriteria] StoreProxyCriteria:{} infoType:{}", criteria.toMap(), infoType);
        //查询parana_store_proxy数据
        criteria.setOrderByAuth(true);

        // 由于业务冻结, 将只显示通过审核的店铺
        //criteria.setStatus(3L);

        List<Long> userIdList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(criteria.getMobile())) {
            //查询用户表
            Response<User> rUser = userReadService.findByMobile(criteria.getMobile());

            if (rUser.isSuccess() && rUser.getResult() != null) {
                User user = rUser.getResult();
                userIdList.add(user.getId());
            }
        }
        if (!ObjectUtils.isEmpty(criteria.getNickName())) {
            Either<List<UserProfile>> rProfile = userProfileReadService.findProfileByNickName(criteria.getNickName());
            if (rProfile.isSuccess() && !rProfile.take().isEmpty()) {
                for (UserProfile rProfiles : rProfile.take()) {
                    userIdList.add(rProfiles.getUserId());
                }
            }
        }

        //推荐人supperIds
        if (!ObjectUtils.isEmpty(criteria.getRecommender())) {
            if (Objects.equals("平台", criteria.getRecommender())) {
                criteria.setLevel(1);
            } else {
                List<Long> supperIds = new ArrayList<>();
                Either<List<StoreProxy>> storeProxy = storeProxyReadService.findByProxyShopName(criteria.getRecommender());
                if (storeProxy.isSuccess() && !storeProxy.take().isEmpty()) {
                    for (StoreProxy s : storeProxy.take()) {
                        supperIds.add(s.getUserId());
                    }
                    criteria.setSupperIds(supperIds);
                }
            }
        }
        //身份信息变更审核查询
        if (!ObjectUtils.isEmpty(infoType) && Objects.equals(1, infoType)) {

            Either<List<StoreProxy>> listResults = storeProxyReadService.findByShopId(criteria.getShopId());
            List<Long> proxyId = new ArrayList<>();
            if (listResults.isSuccess() && !listResults.take().isEmpty()) {
                for (StoreProxy s : listResults.take()) {
                    proxyId.add(s.getId());
                }
                Either<List<StoreProxyInfo>> listResult = storeProxyInfoReadService.getCountSubProxyInfo(proxyId, 7);
                if (listResult.isSuccess() && !listResult.take().isEmpty()) {
                    List<Long> ids = new ArrayList<>();
                    for (StoreProxyInfo s : listResult.take()) {
                        ids.add(s.getProxyId());
                    }
                    criteria.setIds(ids);
                }
            }
        }


        if (!ObjectUtils.isEmpty(criteria.getMobile()) || !StringUtils.isEmpty(criteria.getNickName())) {
            if (userIdList.isEmpty()) {
                return new Paging<>();
            }
            criteria.setUserIds(userIdList);
        }


        Either<Paging<StoreProxy>> storeProxyPagingOpt = storeProxyReadService.paging(criteria);
        for (int i = 0; i < storeProxyPagingOpt.take().getData().size(); i++) {
            StoreProxy storeProxy = storeProxyPagingOpt.take().getData().get(i);
            //查询用户表
            Response<User> rUser = userReadService.findById(storeProxy.getUserId());
            if (!rUser.isSuccess() || ObjectUtils.isEmpty(rUser.getResult())) {
                log.warn("{} StoreProxy user find fail or not found, userId={}, error={}", LogUtil.getClassMethodName(), storeProxy.getUserId(), rUser.getError());
                continue;
            } else {
                User user = rUser.getResult();
                storeProxy.setMobile(user.getMobile());
            }
            //查询用户详情表
            Response<UserProfile> rProfile = userProfileReadService.findProfileByUserId(storeProxy.getUserId());

            if (!rProfile.isSuccess()) {
                log.warn("StoreProxy user find fail or not found, userId={}, error={}", storeProxy.getUserId(), rProfile.getError());
                rProfile = new Response<>();
            }
            if (!ObjectUtils.isEmpty(rProfile.getResult()) && rProfile.getResult() != null) {
                UserProfile userProfile = rProfile.getResult();
                storeProxy.setRealName(userProfile.getRealName());
                storeProxy.setAvatar(userProfile.getAvatar());
            } else {
                storeProxy.setRealName("");
                storeProxy.setAvatar("");
            }
            //查询父级的用户名
            if (!ObjectUtils.isEmpty(storeProxy.getSupperId())) {
                Response<StoreProxy> subStoreProxy = storeProxyReadService.findBySupperIdUseUserIdAndShopId(storeProxy.getSupperId(), storeProxy.getShopId());
                if (subStoreProxy.isSuccess() && subStoreProxy.getResult() != null) {
                    storeProxy.setParentUserName(Optional.ofNullable(subStoreProxy.getResult().getProxyShopName()).orElse(""));
                } else {
                    storeProxy.setParentUserName("");
                }

            } else {
                storeProxy.setParentUserName("平台");
            }
            //信息变更审核
            Either<Optional<StoreProxyInfo>> optionalResult = storeProxyInfoReadService.findByProxyId(storeProxy.getId());
            if (!optionalResult.isSuccess() || optionalResult.take().isEmpty()) {
                storeProxy.setIsAuth(0);
            } else if (Objects.equals(optionalResult.take().get().getStatus(), 7)) {
                storeProxy.setIsAuth(1);
            } else {
                storeProxy.setIsAuth(0);
            }
            StoreProxyView view = new StoreProxyView();
            BeanUtils.copyProperties(storeProxy, view);
            try {
                view.setType(Optional.ofNullable(view.getExtra()).orElseGet(HashMap::new).get("type"));
                if (view.getExtra().get("imageList") != null) {
                    view.setImageList(JSON.parseArray(view.getExtra().get("imageList"), String.class));
                }
            } catch (Exception ex) {
                EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("StoreProxy ImageList Fail", String.format("fail to parse storeProxy ImageList [%s]", JSON.toJSONString(view.getExtra())), ex, EmailReceiverGroup.DEVELOPER));
                log.error("{} fail to parse imageList[{}] for Proxy[shopId={}, userId={}]", LogUtil.getClassMethodName(), storeProxy.getExtra(), storeProxy.getShopId(), storeProxy.getUserId(), ex);
            }
            storeProxyPagingOpt.take().getData().set(i, view);
        }
        return storeProxyPagingOpt.take();
    }

    /**
     *
     */
    @GetMapping("/page/new")
    public Result<DataPage<StoreProxy>> pagingNew(StoreProxyCriteria criteria, @RequestParam(defaultValue = "0") Integer infoType) {
        log.info("[StoreProxyCriteria] StoreProxyCriteria:{} infoType:{}", criteria.toMap(), infoType);
        //查询parana_store_proxy数据
        criteria.setOrderByAuth(true);


        List<Long> userIdList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(criteria.getMobile())) {
            //查询用户表
            Response<User> rUser = userReadService.findByMobile(criteria.getMobile());

            if (rUser.isSuccess() && rUser.getResult() != null) {
                User user = rUser.getResult();
                userIdList.add(user.getId());
            }
        }
        if (!ObjectUtils.isEmpty(criteria.getNickName())) {
            Either<List<UserProfile>> rProfile = userProfileReadService.findProfileByNickName(criteria.getNickName());
            if (rProfile.isSuccess() && !rProfile.take().isEmpty()) {
                for (UserProfile rProfiles : rProfile.take()) {
                    userIdList.add(rProfiles.getUserId());
                }
            }
        }

        //推荐人supperIds
        if (!ObjectUtils.isEmpty(criteria.getRecommender())) {
            if (Objects.equals("平台", criteria.getRecommender())) {
                criteria.setLevel(1);
            } else {
                List<Long> supperIds = new ArrayList<>();
                Either<List<StoreProxy>> storeProxy = storeProxyReadService.findByProxyShopName(criteria.getRecommender());
                if (storeProxy.isSuccess() && !storeProxy.take().isEmpty()) {
                    for (StoreProxy s : storeProxy.take()) {
                        supperIds.add(s.getUserId());
                    }
                    criteria.setSupperIds(supperIds);
                }
            }
        }
        //身份信息变更审核查询
        if (!ObjectUtils.isEmpty(infoType) && Objects.equals(1, infoType)) {

            Either<List<StoreProxy>> listResults = storeProxyReadService.findByShopId(criteria.getShopId());
            List<Long> proxyId = new ArrayList<>();
            if (listResults.isSuccess() && !listResults.take().isEmpty()) {
                for (StoreProxy s : listResults.take()) {
                    proxyId.add(s.getId());
                }
                Either<List<StoreProxyInfo>> listResult = storeProxyInfoReadService.getCountSubProxyInfo(proxyId, 7);
                if (listResult.isSuccess() && !listResult.take().isEmpty()) {
                    List<Long> ids = new ArrayList<>();
                    for (StoreProxyInfo s : listResult.take()) {
                        ids.add(s.getProxyId());
                    }
                    criteria.setIds(ids);
                }
            }
        }


        if (!ObjectUtils.isEmpty(criteria.getMobile()) || !StringUtils.isEmpty(criteria.getNickName())) {
            if (userIdList.isEmpty()) {
                return Result.data(DataPage.empty(new StoreProxy()));
            }
            criteria.setUserIds(userIdList);
        }


        Either<Paging<StoreProxy>> storeProxyPagingOpt = storeProxyReadService.paging(criteria);
        for (int i = 0; i < storeProxyPagingOpt.take().getData().size(); i++) {
            StoreProxy storeProxy = storeProxyPagingOpt.take().getData().get(i);
            //查询用户表
            Response<User> rUser = userReadService.findById(storeProxy.getUserId());
            if (!rUser.isSuccess() || ObjectUtils.isEmpty(rUser.getResult())) {
                log.warn("{} StoreProxy user find fail or not found, userId={}, error={}", LogUtil.getClassMethodName(), storeProxy.getUserId(), rUser.getError());
                continue;
            } else {
                User user = rUser.getResult();
                storeProxy.setMobile(user.getMobile());
            }
            //查询用户详情表
            Response<UserProfile> rProfile = userProfileReadService.findProfileByUserId(storeProxy.getUserId());

            if (!rProfile.isSuccess()) {
                log.warn("StoreProxy user find fail or not found, userId={}, error={}", storeProxy.getUserId(), rProfile.getError());
                rProfile = new Response<>();
            }
            if (!ObjectUtils.isEmpty(rProfile.getResult()) && rProfile.getResult() != null) {
                UserProfile userProfile = rProfile.getResult();
                storeProxy.setRealName(userProfile.getRealName());
                storeProxy.setAvatar(userProfile.getAvatar());
            } else {
                storeProxy.setRealName("");
                storeProxy.setAvatar("");
            }
            //查询父级的用户名
            if (!ObjectUtils.isEmpty(storeProxy.getSupperId())) {
                Response<StoreProxy> subStoreProxy = storeProxyReadService.findBySupperIdUseUserIdAndShopId(storeProxy.getSupperId(), storeProxy.getShopId());
                if (subStoreProxy.isSuccess() && subStoreProxy.getResult() != null) {
                    storeProxy.setParentUserName(Optional.ofNullable(subStoreProxy.getResult().getProxyShopName()).orElse(""));
                } else {
                    storeProxy.setParentUserName("");
                }

            } else {
                storeProxy.setParentUserName("平台");
            }
            //信息变更审核
            Either<Optional<StoreProxyInfo>> optionalResult = storeProxyInfoReadService.findByProxyId(storeProxy.getId());
            if (!optionalResult.isSuccess() || optionalResult.take().isEmpty()) {
                storeProxy.setIsAuth(0);
            } else if (Objects.equals(optionalResult.take().get().getStatus(), 7)) {
                storeProxy.setIsAuth(1);
            } else {
                storeProxy.setIsAuth(0);
            }
            StoreProxyView view = new StoreProxyView();
            BeanUtils.copyProperties(storeProxy, view);
            try {
                if (view.getExtra().get("imageList") != null) {
                    view.setImageList(JSON.parseArray(view.getExtra().get("imageList"), String.class));
                }
            } catch (Exception ex) {
                EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("StoreProxy ImageList Fail", String.format("fail to parse storeProxy ImageList [%s]", JSON.toJSONString(view.getExtra())), ex, EmailReceiverGroup.DEVELOPER));
                log.error("{} fail to parse imageList[{}] for Proxy[shopId={}, userId={}]", LogUtil.getClassMethodName(), storeProxy.getExtra(), storeProxy.getShopId(), storeProxy.getUserId(), ex);
            }
            storeProxyPagingOpt.take().getData().set(i, view);
        }
        return Result.data(DataPage.build(TotalCalculation.build(criteria.getPageSize(), criteria.getPageNo(), storeProxyPagingOpt.getResult().getTotal()),
                storeProxyPagingOpt.take().getData()));
    }

    /**
     * 贝拉米拒绝申请
     */
    @GetMapping("/reject")
    @MyLog(title = "分销管理->品牌代言人审核", value = "商家拒绝申请", opBusinessType = OpBusinessType.UPDATE)
    public Boolean rejectProxy(long userId, String auditingRemark) {
        CommonUser user = UserUtil.getCurrentUser();
        return storeProxyManager.rejectStoreProxy(user.getShopId(), userId, auditingRemark).isSuccess();
    }

    /**
     * 经销商拒绝申请
     */
    @GetMapping("/jxReject")
    public Boolean jxRejectProxy(long userId, long shopId, String jxRemark) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            throw new JsonResponseException("user.not.login");
        }
        if (user.getShopId() != shopId) {
            log.error("{} operator[{}] try to reject Proxy(userId => {}, shopId => {}) by reason [{}]", LogUtil.getClassMethodName(), user.getId(), userId, shopId, jxRemark);
            throw new JsonResponseException(new Translate("不具有相应的权限").toString());
        }
        return storeProxyManager.jxRejectStoreProxy(shopId, userId, jxRemark).isSuccess();
    }

    /**
     * 分销用户
     */
    @GetMapping("/distributionUsers")
    public Paging<StoreProxy> distributionUsers(StoreProxyCriteria criteria) {

        CommonUser users = UserUtil.getCurrentUser();
        if (users == null) {
            throw new JsonResponseException("user.not.login");
        }
        return distributionUserExtractor.pagingDistributionUser(users, criteria);
    }

    /**
     * 分销用户
     */
    @GetMapping("/distributionUsers/new")
    public Result<DataPage<StoreProxy>> distributionUsersNew(StoreProxyCriteria criteria) {

        CommonUser users = UserUtil.getCurrentUser();
        if (users == null) {
            throw new JsonResponseException("user.not.login");
        }
        return Result.data(DataPage.build(
                TotalCalculation.build(criteria.getPageSize(), criteria.getPageNo(),
                        distributionUserExtractor.pagingDistributionUser(users, criteria).getTotal()),
                distributionUserExtractor.pagingDistributionUser(users, criteria).getData()));
    }

    /**
     * 分销用户导出
     */
    @GetMapping("/distributionUsers-export")
    public void distributionUsersExport(StoreProxyCriteria criteria,
                                        HttpServletRequest request, HttpServletResponse response) {
        try {
            criteria.setPageSize(10000);
            Paging<StoreProxy> storeProxyPaging = distributionUsers(criteria);
            List<DistributionUsersExport> dList = new ArrayList<>();
            if (!storeProxyPaging.isEmpty() && !storeProxyPaging.getData().isEmpty()) {
                for (StoreProxy s : storeProxyPaging.getData()) {
                    DistributionUsersExport d = new DistributionUsersExport();
                    BeanUtils.copyProperties(s, d);
                    Long sumPresentProfit = balanceDetailManager.getEarned(s.getUserId(), s.getShopId()).map(BalanceDetail::getFee).orElse(0L);
                    d.setIncomeSum(new BigDecimal(sumPresentProfit + "").divide(new BigDecimal(100 + ""), 2, RoundingMode.DOWN) + "");
                    d.setOutcomeSum(new BigDecimal(s.getOutcomeSum() + "").divide(new BigDecimal(100 + ""), 2, RoundingMode.DOWN) + "");
                    d.setFinishedTradeFeeSum(new BigDecimal(s.getFinishedTradeFeeSum()).divide(new BigDecimal("100"), 2, RoundingMode.DOWN));
                    if (Objects.equals(s.getLevel(), 1)) {
                        d.setLevel("经销商");
                    } else if (Objects.equals(s.getLevel(), 2)) {
                        d.setLevel("门店");
                    }
                    d.setParentUserName(s.getParentUserName());
                    d.setTimes(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(s.getCreatedAt()) + "");
                    dList.add(d);
                }
            }
            exporter.export(dList, DistributionUsersExport.class, request, response);
        } catch (Exception e) {
            log.error("failed export to  cause:{}", Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 下级代理人明细
     * userId--父级ID
     * shopId--店铺Id
     */
    @GetMapping("/distributionUsersProxy")
    public Paging<UserProfiles> distributionUsersProxy(long userId, long shopId,
                                                       @RequestParam(defaultValue = "1") Integer pageNo,
                                                       @RequestParam(defaultValue = "20") Integer pageSize) {
        //获取以及代理下面所有的子级代理
        Either<List<SubProxySum>> listResult = subProxyDataReadService.findByProxyId(userId, shopId);
        if (!listResult.isSuccess()) {
            log.error("{} userId:{} shopId:{}", LogUtil.getClassMethodName(), userId, shopId);
            return new Paging<>();
        }

        if (listResult.take().isEmpty()) {
            log.debug("{} userId:{} shopId:{}", LogUtil.getClassMethodName(), userId, shopId);
            return new Paging<>();
        }

        List<Long> list = new ArrayList<>();
        for (SubProxySum subProxySum : listResult.take()) {
            list.add(subProxySum.getUserId());
        }
        UserProfilesCriteria userProfilesCriteria = new UserProfilesCriteria();
        userProfilesCriteria.setUserIds(list);
        userProfilesCriteria.setPageNo(pageNo);
        userProfilesCriteria.setPageSize(pageSize);

        Either<Paging<UserProfile>> userProfile = userProfileReadService.paging(userProfilesCriteria);

        Paging<UserProfiles> userProfiles1 = new Paging<>();
        userProfiles1.setTotal(userProfile.take().getTotal());
        List<UserProfiles> userProfiles = new ArrayList<>();
        for (UserProfile userProfile1 : userProfile.take().getData()) {
            UserProfiles fanProfile = new UserProfiles();
            BeanUtils.copyProperties(userProfile1, fanProfile);
            Either<Optional<SubProxySum>> rEntity = subProxyDataReadService.findByUserId(userProfile1.getUserId(), shopId);

            if (!rEntity.isSuccess()) {
                log.error("{} userId:{} shopId:{}", LogUtil.getClassMethodName(), userProfile1.getUserId(), shopId);
                fanProfile.setTradeFeeSum(0L);
            } else if (rEntity.take().isPresent()) {
                fanProfile.setTradeFeeSum(Optional.ofNullable(rEntity.take().get().getTradeFeeSum()).orElse(0L));
            } else {
                fanProfile.setTradeFeeSum(0L);
            }
            //设置普粉
            Either<Long> normalFanNum = normalFanDataReadService.countByProxyId(userProfile1.getUserId(), shopId);
            fanProfile.setNormalFanNum(Optional.ofNullable(normalFanNum.take()).orElse(0L));
            //设置忠粉
            Either<Long> honestFanNum = honestFanDataReadService.countByProxyId(userProfile1.getUserId(), shopId);
            fanProfile.setHonestFanNum(Optional.ofNullable(honestFanNum.take()).orElse(0L));
            userProfiles.add(fanProfile);
            userProfiles1.setData(userProfiles);
        }
        return userProfiles1;
    }

    /**
     * 是否通过考核
     */
    @GetMapping("/isDistributionUsers")
    public Either<Boolean> isDistributionUsersProxy() {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            return Either.ok(false);
        }

        Response<Shop> rShop = shopReadService.findById(user.getShopId());
        if (!rShop.isSuccess()) {
            log.error("failed to find shop by id={}, error code: {}", user.getShopId(), rShop.getError());
            return Either.ok(false);
        }

        Either<Long> num = orderReadService.findShopOrderByRefererId(user.getId(), user.getShopId());

        if (!num.isSuccess()) {
            log.error("failed to find shop by id={}, error code: {}", user.getShopId(), num.getError());
            return Either.ok(false);
        }

        Shop existShop = rShop.getResult();
        Map<String, String> extra = existShop.getExtra();
        if (extra == null || extra.isEmpty()) {
            return Either.ok(false);
        } else {
            if (extra.containsKey(DistributionConstants.CHECK_FLAGS) && extra.containsKey(DistributionConstants.CHECK_NUMBER)
                    && "true".equals(extra.get(DistributionConstants.CHECK_FLAGS)) && num.take() >= Long.parseLong(extra.get(DistributionConstants.CHECK_NUMBER))) {
                log.info("success,num.getResult():{} .CHECK_NUMBER:{}", num.take(), Long.valueOf(extra.get(DistributionConstants.CHECK_NUMBER)));
                return Either.ok(true);
            }
        }
        log.info("fail,num.getResult():{} .CHECK_NUMBER:{}", num.take(), Long.valueOf(extra.get(DistributionConstants.CHECK_NUMBER)));

        return Either.ok(false);
    }

    /**
     * 查询是否是商家已审核过的代理人
     */
    @GetMapping("/isDistributionUsersProxy")
    public Either<Boolean> ifDistributionUsersProxy() {
        CommonUser user = UserUtil.getCurrentUser();
        Either<Optional<StoreProxy>> ifUsersProxy = storeProxyReadService.findByShopIdAndUserIdAndStatus(user.getShopId(), user.getId(), 3L);
        if (!ifUsersProxy.isSuccess()) {
            log.error("[isDistributionUsersProxy] {} userId:{} shopId:{}", LogUtil.getClassMethodName(), user.getId(), user.getShopId());
            return Either.ok(false);
        }
        if (!ifUsersProxy.take().isPresent()) {
            log.info("[isDistributionUsersProxy] {} userId:{} shopId:{}", LogUtil.getClassMethodName(), user.getId(), user.getShopId());
            return Either.ok(false);
        }
        Response<Shop> rShop = shopReadService.findById(user.getShopId());
        if (!rShop.isSuccess()) {
            log.error("failed to find shop by id={}, error code: {}", user.getShopId(), rShop.getError());
            return Either.ok(false);
        }
        Shop existShop = rShop.getResult();

        Map<String, String> extra = existShop.getExtra();

        if (extra == null || extra.isEmpty()) {
            return Either.ok(false);
        } else {
            if (extra.containsKey(DistributionConstants.CHECK_FLAGS) && "true".equals(extra.get(DistributionConstants.CHECK_FLAGS))) {
                log.info("success, CHECK_NUMBER:{}", Long.valueOf(extra.get(DistributionConstants.CHECK_FLAGS)));
                return Either.ok(true);
            }
        }

        return Either.ok(false);
    }

    /**
     * 统计经销商需要审核的人数
     */
    @GetMapping("/getSubProxy")
    public Long getSubProxy(Long shopId) {
        long userId = getLoginUserId();
        //身份审核数
        Either<Long> num = storeProxyReadService.getSubProxy(userId, shopId);
        //信息变更审核数
        Either<List<StoreProxy>> listResult = storeProxyReadService.findBySupperIdAndShopId(userId, shopId);

        if (!listResult.isSuccess()) {
            return num.take();
        }
        List<Long> ids = new ArrayList<>();
        if (!listResult.take().isEmpty()) {
            for (StoreProxy s : listResult.take()) {
                ids.add(s.getId());
            }
        } else {
            return num.take();
        }
        Either<Long> num1 = storeProxyInfoReadService.getSubProxyInfo(ids, 11L);
        if (!num1.isSuccess() || num1.take() == null) {
            return num.take();
        }

        return num.take() + num1.take();
    }

    private long getLoginUserId() {
        return Optional.ofNullable(UserUtil.getUserId()).orElseThrow(() -> new JsonResponseException("user.not.login"));
    }

    @Data
    static class DistributionUsersExport {
        Long userId;//
        String realName;//
        String proxyShopName;//店名
        String mobile;//
        String incomeSum;//累计佣金
        String outcomeSum;//可提现佣金
        String parentUserName;//推荐人
        String level;
        Long fanNum;//下级用户数量(门店数)
        Long honestFanNum;//忠实粉丝数量
        Long normalFanNum;//普通粉丝数量
        BigDecimal finishedTradeFeeSum;//完成的订单贸易额
        String times;
    }

    @Data
    static class StoreProxyExport {
        String id;
        String nickName;
        String referenceName;
        String proxyCradName;
        String province;
        String city;
        String county;
        String flag;
        String type;
        String status;
        String shenheTime;
        String createTime;

    }

}
