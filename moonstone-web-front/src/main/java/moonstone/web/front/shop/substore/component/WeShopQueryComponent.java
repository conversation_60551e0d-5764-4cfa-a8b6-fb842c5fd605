package moonstone.web.front.shop.substore.component;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.service.WeShopShopAccountReadService;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
@Slf4j
public class WeShopQueryComponent {
    private final WeShopShopAccountReadService weShopShopAccountReadService;
    private final WeShopCacheHolder weShopCacheHolder;

    /**
     * find WeShop by user and shopId
     *
     * @param userId userId
     * @param shopId shopId
     * @return WeShop
     */
    public Optional<WeShop> queryWeShopByUserIdAndShopId(Long userId, Long shopId) {
        return Optional.ofNullable(weShopShopAccountReadService.findByUserId(userId).getResult())
                .orElseGet(ArrayList::new)
                .stream().filter(weShopShopAccount -> Objects.equals(weShopShopAccount.getShopId(), Optional.ofNullable(shopId).orElse(0L)))
                .map(WeShopShopAccount::getWeShopId)
                .map(weShopCacheHolder::findByWeShopId)
                .findFirst().orElseGet(Optional::empty);
    }
}
