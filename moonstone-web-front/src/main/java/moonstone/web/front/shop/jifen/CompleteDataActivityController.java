package moonstone.web.front.shop.jifen;


import com.google.common.collect.ImmutableBiMap;
import io.terminus.common.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.*;
import moonstone.order.model.ShopVipInformation;
import moonstone.order.service.ShopVipInformationReadService;
import moonstone.user.model.StoreIntegral;
import moonstone.web.core.integral.IntegralAccount;
import moonstone.web.core.shop.application.CompleteDataActivityCacheHolder;
import moonstone.web.core.shop.application.CompleteDataActivityExecutor;
import moonstone.web.core.shop.dto.CompleteDataActivity;
import moonstone.web.core.shop.dto.CompleteDataActivityView;
import moonstone.web.core.util.MongoUtil;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RestController
@RequestMapping("/api/integral/activity/info")
//todo: need
public class CompleteDataActivityController {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CompleteDataActivityCacheHolder completeDataActivityCacheHolder;
    @Autowired
    private CompleteDataActivityExecutor completeDataActivityExecutor;
    @Autowired
    private ShopVipInformationReadService shopVipInformationReadService;
    @Autowired
    private IntegralAccount integralAccount;

    /**
     * 检查是否过期
     *
     * @param activity 活动
     * @return 有效
     */
    private boolean checkValid(CompleteDataActivity activity) {
        Date now = new Date();
        if (Objects.equals(activity.getLongTerm(), true)) {
            return true;
        }
        return !Optional.ofNullable(activity.getEndAt()).map(DateUtil::withTimeAtEndOfDay).orElse(now).before(now);
    }

    @PostMapping("/addComplete")
    public Response<Boolean> addCompleteActivity(@RequestBody CompleteDataActivityView completeDataActivity) {
        long shopId = Optional.ofNullable((CommonUser) UserUtil.getCurrentUser()).map(CommonUser::getShopId).orElseThrow(() -> new RuntimeException(new Translate("请以商户身份登录").toString()));

        completeDataActivity.setId(null);
        if (completeDataActivity.getName() == null) {
            completeDataActivity.setName(new Translate("首次完善个人信息活动").toString());
        }
        completeDataActivity.setClassName(ShopVipInformation.class.getName());
        completeDataActivity.setShopId(shopId);
        completeDataActivity.setCreatedAt(new Date());
        completeDataActivity.setUpdatedAt(new Date());
        completeDataActivity.setStatus(0);


        Optional<CompleteDataActivity> existsSameActivity = checkIfExists(completeDataActivity.completeDataActivity());
        if (existsSameActivity.isPresent()) {
            return Response.fail(new Translate("已经存在相应的活动[%s]", existsSameActivity.get().getName()).toString());
        }
        if (completeDataActivity.getInfoNeed() == null || completeDataActivity.getInfoNeed().isEmpty()) {
            return Response.fail(new Translate("所需信息不能为空").toString());
        }
        if (completeDataActivity.getRewardIntegral() == null || completeDataActivity.getRewardIntegral() < 0) {
            return Response.fail(new Translate("奖励积分不能为[%s] 需大于等于0", completeDataActivity.getRewardIntegral()).toString());
        }

        if (!checkValid(completeDataActivity.completeDataActivity())) {
            return Response.fail(new Translate("活动已过期[%s]-[%s]请修改有效日期再试"
                    , Optional.ofNullable(completeDataActivity.getStartAt()).map(this::dateToString).orElse("之前"), Optional.ofNullable(completeDataActivity.getEndAt()).map(this::dateToString).orElse("以后")).toString());
        }
        mongoTemplate.insert(completeDataActivity.completeDataActivity());

        completeDataActivityCacheHolder.invalidate(shopId);
        return Response.ok(true);
    }

    @PutMapping("/update")
    public Response<Boolean> update(@RequestBody CompleteDataActivityView completeDataActivity) {
        long shopId = Optional.ofNullable((CommonUser) UserUtil.getCurrentUser()).map(CommonUser::getShopId).orElseThrow(() -> new RuntimeException(new Translate("请以商户身份登录").toString()));
        if (completeDataActivity.getId() == null) {
            return Response.fail(new Translate("请选中要修改的活动").toString());
        }
        completeDataActivity.setShopId(shopId);
        completeDataActivity.setStatus(null);
        completeDataActivity.setUpdatedAt(new Date());
        completeDataActivity.setCreatedAt(null);

        CompleteDataActivity activity = mongoTemplate.findById(completeDataActivity.getId(), CompleteDataActivity.class);
        if (activity == null) {
            return Response.fail(new Translate("活动不存在").toString());
        }
        completeDataActivity.setClassName(activity.getClassName());

        Optional<CompleteDataActivity> exists = checkIfExists(completeDataActivity.completeDataActivity());
        if (!exists.isPresent()) {
            log.debug("{} fail to update id [{}]", LogUtil.getClassMethodName(), completeDataActivity.getId());
            return Response.fail(new Translate("修改目标不存在").toString());
        }
        if (exists.get().getShopId() != shopId) {
            return Response.fail(new Translate("活动[%s]不属于你", exists.get().getName()).toString());
        }

        boolean ok = mongoTemplate.updateFirst(Query.query(Criteria.where("id").is(exists.get().getId())),
                MongoUtil.generateUpdate(completeDataActivity, Stream.of("id", "status", "createdAt", "className").collect(Collectors.toSet()), false), CompleteDataActivity.class).getModifiedCount() > 0;

        completeDataActivityCacheHolder.invalidate(shopId);
        return Response.ok(ok);
    }

    /**
     * 转换日期到文本
     *
     * @param date 日期
     * @return 文本
     */
    private String dateToString(Date date) {
        return new DateTime(date).toString("YYYY-MM-dd HH:mm:SS");
    }

    @PutMapping("/switch")
    public Response<Boolean> switchActivity(String id) {
        long shopId = Optional.ofNullable((CommonUser) UserUtil.getCurrentUser()).map(CommonUser::getShopId).orElseThrow(() -> new RuntimeException(new Translate("请以商户身份登录").toString()));
        CompleteDataActivity activity = mongoTemplate.findById(id, CompleteDataActivity.class);
        if (activity == null) {
            log.debug("{} fail to update id [{}]", LogUtil.getClassMethodName(), id);
            return Response.fail(new Translate("修改目标不存在").toString());
        }
        if (activity.getStatus() == 0 && !checkValid(activity)) {
            return Response.fail(new Translate("活动已过期[%s]-[%s]请修改有效日期再试"
                    , Optional.ofNullable(activity.getStartAt()).map(this::dateToString).orElse("之前"), Optional.ofNullable(activity.getEndAt()).map(this::dateToString).orElse("以后")).toString());
        }
        if (activity.getShopId() != shopId) {
            return Response.fail(new Translate("活动[%s]不属于你", activity.getName()).toString());
        }
        boolean result = mongoTemplate.updateFirst(Query.query(Criteria.where("id").is(id)), Update.update("status", 1 - activity.getStatus()), CompleteDataActivity.class).getModifiedCount() > 0;
        completeDataActivityCacheHolder.invalidate(shopId);
        if (activity.getStatus() == 0) {
            autoTriggerActivity(shopId);
        }
        return Response.ok(result);
    }

    private void autoTriggerActivity(long shopId) {
        try {
            for (ShopVipInformation shopVipInformation : shopVipInformationReadService.findByShopId(shopId).take()) {
                try {
                    triggerCompleteActivity(shopVipInformation, shopVipInformation.getUserId());
                } catch (Exception iex) {
                    log.error("{} fail to trigger auto-give-complete-activity for shopId[{}] user[{}]", LogUtil.getClassMethodName(), shopId, shopVipInformation.getUserId(), iex);
                    EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("自动触发完善信息活动", String.format("店铺[%s]用户[%s]触发自动完善活动失败", shopId, shopVipInformation.getUserId()), iex, EmailReceiverGroup.DEVELOPER));
                }
            }
        } catch (Exception ex) {
            log.error("{} fail to auto-trigger-complete-activity shop[{}]", LogUtil.getClassMethodName(), shopId, ex);
            EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("信息完善活动自动触发失败", String.format("店铺[%s]自动触发完善信息失败", shopId), ex, EmailReceiverGroup.DEVELOPER));
        }
    }

    /**
     * 触发完善信息活动
     *
     * @param shopVipInformation 用户信息
     * @param userId             用户Id
     * @return 结果
     */
    private void triggerCompleteActivity(ShopVipInformation shopVipInformation, Long userId) {
        Optional<List<CompleteDataActivity>> completeDataActivityList = completeDataActivityCacheHolder.getCompleteActivity(shopVipInformation.getShopId());
        if (completeDataActivityList.orElse(new ArrayList<>()).isEmpty()) {
            return;
        }

        CompleteDataActivity activity = completeDataActivityList.get().get(0);
        log.debug("{} user[{}] should test with completeActivity[{}]", LogUtil.getClassMethodName(), userId, activity);
        if (completeDataActivityExecutor.canTrigger(userId, activity)) {
            Either<Boolean> result = completeDataActivityExecutor.execute(userId, shopVipInformation, activity);
            if (Objects.equals(true, result.orElse(false))) {
                addIntegral(userId, shopVipInformation.getShopId(), completeDataActivityList.get().get(0).getRewardIntegral());
            }
        }
    }

    private void addIntegral(Long userId, Long shopId, Long rewardIntegral) {
        //初始化积分账户--并添加积分--一段奶粉--冻结
        Map<String, String> maps = new HashMap<>();
        maps.put("userId", String.valueOf(userId));
        maps.put("shopId", String.valueOf(shopId));
        maps.put("integralFee", String.valueOf(rewardIntegral));
        maps.put("operatorId", "0");

        Either<StoreIntegral> storeIntegralResult = integralAccount.initializeIntegralAccount(maps, 11);
        if (!storeIntegralResult.isSuccess()) {
            throw new RuntimeException(storeIntegralResult.getError());
        }
    }

    @GetMapping
    public Response<List<CompleteDataActivityView>> listCompleteDataActivity(@RequestParam(required = false) Long shopId) {
        if (shopId == null) {
            shopId = Optional.ofNullable((CommonUser) UserUtil.getCurrentUser()).map(CommonUser::getShopId)
                    .orElseThrow(() -> new RuntimeException(new Translate("请以商户身份登录").toString()));
        }
        List<CompleteDataActivity> completeDataActivityList = mongoTemplate.find(Query.query(Criteria.where("shopId").is(shopId)).addCriteria(Criteria.where("status").gte(0)), CompleteDataActivity.class);
        List<CompleteDataActivityView> viewList = new ArrayList<>();
        for (CompleteDataActivity activity : completeDataActivityList) {
            if (!checkValid(activity)) {
                activity.setStatus(0);
            }
            viewList.add(CompleteDataActivityView.from(activity));
        }
        return Response.ok(viewList);
    }

    @GetMapping("/action")
    public Response<List<ActionView>> getActionList() {
        long shopId = Optional.ofNullable((CommonUser) UserUtil.getCurrentUser()).map(CommonUser::getShopId).orElseThrow(() -> new RuntimeException(new Translate("请以商户身份登录").toString()));

        return Response.ok(validActionForShop(shopId));
    }

    private List<ActionView> validActionForShop(Long shopId) {
        log.warn("{} no check for shop[{}] now, not impl,default send VIP info", LogUtil.getClassMethodName(), shopId);
        ActionView actionView = new ActionView();
        actionView.setTargetClass(ShopVipInformation.class.getName());
        actionView.setTargetNickName("会员中心个人信息");
        actionView.setAddButton(listCompleteDataActivity(shopId).getResult().isEmpty());
        actionView.setModifyButton(!listCompleteDataActivity(shopId).getResult().isEmpty());

        actionView.setColumn(new ArrayList<>());

        // 如果后续支持 请使用注解将其加载到成员身上
        List<String> columnPairs = completeDataActivityExecutor.getInfoMapList();
        for (int i = 0; i < columnPairs.size(); i += 2) {
            actionView.getColumn().add(ImmutableBiMap.of("name", columnPairs.get(i), "value", columnPairs.get(i + 1)));
        }
        return Collections.singletonList(actionView);
    }

    public Optional<CompleteDataActivity> checkIfExists(CompleteDataActivity completeDataActivity) {
        return Optional.ofNullable(mongoTemplate.findOne(Query.query(Criteria.where("className").is(completeDataActivity.getClassName()))
                        .addCriteria(Criteria.where("shopId").is(completeDataActivity.getShopId()))
                        .addCriteria(Criteria.where("status").gte(0))
                , CompleteDataActivity.class));
    }

    @Data
    public static class ActionView {
        String targetClass;
        String targetNickName;

        List<Map<String, String>> column;

        boolean addButton = false;
        boolean modifyButton = false;
        boolean deleteButton = false;
    }
}
