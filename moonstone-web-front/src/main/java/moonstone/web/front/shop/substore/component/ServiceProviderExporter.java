package moonstone.web.front.shop.substore.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.user.enums.DataExportTaskTypeEnum;
import moonstone.web.core.shop.model.ServiceProvider;
import moonstone.web.core.util.BaseExporter;
import moonstone.web.front.shop.substore.convert.ServiceProviderExportConvertor;
import moonstone.web.front.shop.substore.vo.ServiceProviderExcelExportView;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 服务商信息-导出
 */
@Slf4j
@Component
public class ServiceProviderExporter extends BaseExporter<ServiceProviderExcelExportView, ServiceProviderExporter.ExportParameter> {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    protected DataExportTaskTypeEnum getCurrentTaskType() {
        return DataExportTaskTypeEnum.SERVICE_PROVIDER_INFORMATION;
    }

    @Override
    protected List<ServiceProviderExcelExportView> findPageDataList(ExportParameter queryParameter, int pageNo, int pageSize) {
        //查询
        var sourceList = findList(queryParameter, pageNo, pageSize);
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }

        //模型转换
        return ServiceProviderExportConvertor.convert(sourceList);
    }

    /**
     * 按条件分页查询
     *
     * @param queryParameter
     * @param pageNo
     * @param pageSize
     * @return
     */
    private List<ServiceProvider> findList(ExportParameter queryParameter, int pageNo, int pageSize) {
        Query query = new Query();

        query.addCriteria(Criteria.where("shopId").is(queryParameter.shopId()));
        query.with(PageRequest.of(pageNo - 1, pageSize));

        if (StringUtils.isNotBlank(queryParameter.name())) {
            Pattern pattern = Pattern.compile("^.*" + queryParameter.name() + ".*$", Pattern.CASE_INSENSITIVE);
            query.addCriteria(Criteria.where("name").regex(pattern));
        }

        if (StringUtils.isNotBlank(queryParameter.mobile())) {
            Pattern pattern = Pattern.compile("^.*" + queryParameter.mobile() + ".*$", Pattern.CASE_INSENSITIVE);
            query.addCriteria(Criteria.where("mobile").regex(pattern));
        }

        return mongoTemplate.find(query, ServiceProvider.class);
    }

    public record ExportParameter(Long shopId, String name, String mobile) {
    }
}
