package moonstone.web.front.wechat.applet.dto;

import lombok.Getter;

import java.io.Serializable;

/**
 * Author:cp
 * Created on 13/01/2017.
 */
public class SessionMeta implements Serializable {

    private static final long serialVersionUID = 3956053304668288293L;

    /**
     * 用户唯一标识
     */
    @Getter
    private final String openId;

    /**
     * 会话密钥
     */
    @Getter
    private final String sessionKey;

    public SessionMeta(String openId, String sessionKey) {
        this.openId = openId;
        this.sessionKey = sessionKey;
    }

}
