package moonstone.web.front.util.excelData;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;


/**
 * easyExcel的服务商排行数据模型
 */
@Data
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class ServiceProviderRankData {

    /**
     * 名次
     */
    @ExcelProperty("排名")
    private Integer rank;

    /**
     * 服务商名字
     */
    @ColumnWidth(30)
    @ExcelProperty("服务商")
    private String serviceProviderName;

    /**
     * 服务商省份
     */
    @ColumnWidth(20)
    @ExcelProperty("省份")
    private String province;

    /**
     * 服务商GMV
     */
    @NumberFormat("#.00")
    @ExcelProperty("GMV")
    private Double gmv;

    /**
     * 服务商单量
     */
    @ExcelProperty("单量")
    private Long orderQuantity;



}
