package moonstone.web.front.util.excelData;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

/**
 * easyExcel的门店排行数据模型
 */
@Data
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class SubStoreRankData {

    /**
     * 名次
     */
    @ExcelProperty("排名")
    private Integer rank;

    /**
     * 门店名称
     */
    @ColumnWidth(30)
    @ExcelProperty("门店名称")
    private String subStoreName;

    /**
     * 门店所属服务商名字
     */
    @ColumnWidth(30)
    @ExcelProperty("所属服务商")
    private String serviceProviderName;

    /**
     * 省份
     */
    @ColumnWidth(20)
    @ExcelProperty("省份")
    private String province;

    /**
     * 门店GMV
     */
    @NumberFormat("#.00")
    @ExcelProperty("GMV")
    private double gmv;

    /**
     * 门店单量
     */
    @ExcelProperty("单量")
    private Long orderQuantity;
}
