package moonstone.web.front.distribution.web;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.utils.APIRespWrapper;
import moonstone.common.utils.LogUtil;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.web.core.decoration.app.WeiXinTemplateApp;
import moonstone.web.core.decoration.model.DraftView;
import moonstone.web.core.decoration.model.TemplateView;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * todo complete here
 *
 * <AUTHOR>
 * @startuml User -> User : submit Code Draft
 * User -> TemplateController : addDraftIntoTemplate
 * alt satisfied with code and preview
 * User -> AppController : submitCode
 * alt pass test
 * User -> AppController : submitAuth
 * group auth logical
 * WeiXinSystem --> AppController : authPass
 * User ->> AppController : deploy
 * AppController -> WeiXinSystem : deploy WeiXinMicroApp
 * end
 * else not pass
 * User -> User : modify code Draft
 * User -> AppController : repeat submitCode and submitAuth
 * end
 * end
 * @enduml
 * @see ShopWxaProject#getId() projectId represent different project
 */
@RequestMapping("/api/v1/open/template")
@RestController
@AllArgsConstructor
@Slf4j
public class TemplateController {
    WeiXinTemplateApp weiXinTemplateApp;

    /**
     * 操作草稿进入模板
     * 首先需要在微信小程序中完成代码编辑
     *
     * @param projectId 项目Id
     * @param draftId   草稿Id
     * @return 成功或者失败
     */
    @PostMapping("/add")
    public APIResp<Boolean> addDraftIntoTemplate(Long projectId, String draftId) {
        return weiXinTemplateApp.addDraftIntoTemplate(projectId, draftId)
                .map(APIResp::ok)
                .logException(e -> log.error("{} fail to execute for Project[Id => {}]", LogUtil.getClassMethodName(), projectId, e))
                .elseMap(e -> APIRespWrapper.error(e.getMessage()));
    }

    @GetMapping("/listDraft")
    public APIResp<List<DraftView>> queryDraftList(Long projectId) {
        return weiXinTemplateApp.queryDraftList(projectId)
                .map(APIResp::ok)
                .logException(e -> log.error("{} fail to execute for Project[Id => {}]", LogUtil.getClassMethodName(), projectId, e))
                .elseMap(e -> APIRespWrapper.error(e.getMessage()));
    }

    /**
     * 查询模板列表
     *
     * @param projectId 项目Id
     * @return 项目列表
     */
    @GetMapping("/list")
    public APIResp<List<TemplateView>> queryTemplateList(Long projectId) {
        return weiXinTemplateApp.queryTemplateList(projectId)
                .map(APIResp::ok)
                .logException(e -> log.error("{} fail to execute for Project[Id => {}]", LogUtil.getClassMethodName(), projectId, e))
                .elseMap(e -> APIRespWrapper.error(e.getMessage()));
    }

    /**
     * 删除模板
     *
     * @param projectId  项目Id
     * @param templateId 模板Id
     * @return 删除结果
     */
    @PostMapping("/delete")
    public APIResp<Boolean> deleteTemplate(Long projectId, Long templateId) {
        return weiXinTemplateApp.deleteTemplate(projectId, templateId)
                .map(APIResp::ok)
                .logException(e -> log.error("{} fail to execute for Project[Id => {}]", LogUtil.getClassMethodName(), projectId, e))
                .elseMap(e -> APIRespWrapper.error(e.getMessage()));
    }

}
