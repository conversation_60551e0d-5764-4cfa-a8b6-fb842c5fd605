package moonstone.web.front.component.decoration;

import com.google.common.base.Objects;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaReadService;
import org.springframework.stereotype.Component;

/**
 * Created by CaiZhy on 2018/11/23.
 */
@Slf4j
@Component
public class ShopWxaProjectChecker {
    @RpcConsumer
    private ShopWxaProjectReadService shopWxaProjectReadService;

    @RpcConsumer
    private ShopWxaReadService shopWxaReadService;

    public void checkPermission(Long shopWxaProjectId){
        CommonUser commonUser = UserUtil.getCurrentUser();
        Response<ShopWxaProject> rExist = shopWxaProjectReadService.findById(shopWxaProjectId);
        if (!rExist.isSuccess()){
            log.error("failed to find shopWxaProject by id={}, error code: {}", shopWxaProjectId, rExist.getError());
            throw new JsonResponseException(rExist.getError());
        }
        Response<ShopWxa> rShopWxa = shopWxaReadService.findById(rExist.getResult().getShopWxaId());
        if (!rShopWxa.isSuccess()){
            log.error("failed to find shopWxa by shopWxaId={}, error code: {}", rExist.getResult().getShopWxaId(), rShopWxa.getError());
            throw new JsonResponseException(rShopWxa.getError());
        }
        if (!Objects.equal(rShopWxa.getResult().getShopId(), commonUser.getShopId())){
            log.error("shopWxaProject(id={}) is not belong to user(Id={})", shopWxaProjectId, commonUser.getId());
            throw new JsonResponseException("shopWxaProject.not.belong.to.user");
        }
    }
}
