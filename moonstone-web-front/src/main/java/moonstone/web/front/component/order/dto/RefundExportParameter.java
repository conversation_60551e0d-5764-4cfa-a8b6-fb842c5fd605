package moonstone.web.front.component.order.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RefundExportParameter {

    /**
     * 商家平台id
     */
    private Long shopId;

    /**
     * 退款类型
     */
    private Integer refundType;

    /**
     * 退款单状态
     */
    private Integer status;

    /**
     * 创建时间的开始与结束
     */
    private Date createdAtStart;
    private Date createdAtEnd;

    /**
     * 退款单id列表
     */
    List<Long> refundIds;
}
