package moonstone.web.front.component.order;


import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.RespResult;
import moonstone.common.utils.UserUtil;
import moonstone.order.dto.RefundDetail;
import moonstone.order.dto.RefundList;
import moonstone.order.model.SkuOrder;
import moonstone.order.vo.RefunListPcVO;
import moonstone.order.vo.RefundDetailPcVO;
import moonstone.order.vo.RefundPcVO;
import moonstone.order.vo.SkuOrderPcVO;
import moonstone.web.core.order.RefundReadLogic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * 退货/款订单管理
 */
@Slf4j
@RestController
@RequestMapping("/api/gx")
public class RefundReaderPc {

    @Autowired
    private RefundReadLogic refundReadLogic;

    /**
     * 获取退货款订单
     *
     * @param refundCriteria
     * @return
     */
    @RequestMapping(value = "/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public RespResult findBy(@RequestParam Map<String, String> refundCriteria) {
        CommonUser users = UserUtil.getCurrentUser();
        if (users == null) {
            throw new JsonResponseException("user.not.login");
        }
        Response<Paging<RefundList>> pagingRes = refundReadLogic.refundPaging(refundCriteria);
        Paging<RefundList> refunds = pagingRes.getResult();
        return RespResult.data(setRefunListPcVO(refunds));
    }


    /**
     * 买家查看退款详情
     *
     * @param refundId
     * @return
     */
    @RequestMapping(value = "/detail", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public RespResult findDetailBuyer(Long refundId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Response<RefundDetail> detailRes = refundReadLogic.findDetailForBuyer(refundId, commonUser);
        if (!detailRes.isSuccess()) {
            log.error("fail to find refund detail by id={},error:{}",
                    refundId, detailRes.getError());
            throw new JsonResponseException(detailRes.getError());
        }
        return RespResult.data(setRefundDetailPcVO(detailRes));
    }


    private List<RefunListPcVO> setRefunListPcVO(Paging<RefundList> refunds) {
        List<RefunListPcVO> refunListPcVOList = new ArrayList<>();

        for (RefundList refundList : refunds.getData()) {
            RefunListPcVO refunListPcVO = new RefunListPcVO();

            /**
             * 写入退货款信息
             */
            RefundPcVO refundPcVO = new RefundPcVO();
            refundPcVO.setId(refundList.getRefund().getId());
            refundPcVO.setReasonType(refundList.getRefund().getReasonType());
            refundPcVO.setCreatedAt(refundList.getRefund().getCreatedAt());
            refundPcVO.setFee(refundList.getRefund().getFee());
            refundPcVO.setStatus(refundList.getRefund().getStatus());
            refunListPcVO.setRefund(refundPcVO);


            /**
             * 写入SKU商品相关信息
             */
            List<SkuOrderPcVO> provincesList = new ArrayList<>();
            for (SkuOrder sku : refundList.getSkuOrders()) {
                SkuOrderPcVO skuOrderPcVO = new SkuOrderPcVO();
                skuOrderPcVO.setSkuId(sku.getSkuId());
                skuOrderPcVO.setIsBonded(sku.getIsBonded());
                skuOrderPcVO.setItemId(sku.getItemId());
                skuOrderPcVO.setQuantity(sku.getQuantity());
                skuOrderPcVO.setSkuImage(sku.getSkuImage());
                skuOrderPcVO.setItemName(sku.getItemName());
                skuOrderPcVO.setTax(sku.getTax());
                provincesList.add(skuOrderPcVO);
            }
            refunListPcVO.setSkuOrders(provincesList);

            refunListPcVO.setOperations(refundList.getOperations());

            refunListPcVOList.add(refunListPcVO);
        }
        return refunListPcVOList;
    }


    private RefundDetailPcVO setRefundDetailPcVO(Response<RefundDetail> detailRes) {

        RefundDetailPcVO refundDetailPcVO = new RefundDetailPcVO();
        /**
         * 写入退款单信息
         */
        RefundPcVO refundPcVO = new RefundPcVO();
        refundPcVO.setId(detailRes.getResult().getRefund().getId());
        refundPcVO.setReasonType(detailRes.getResult().getRefund().getReasonType());
        refundPcVO.setCreatedAt(detailRes.getResult().getRefund().getCreatedAt());
        refundPcVO.setFee(detailRes.getResult().getRefund().getFee());
        refundPcVO.setStatus(detailRes.getResult().getRefund().getStatus());
        refundPcVO.setBuyerNote(detailRes.getResult().getRefund().getBuyerNote());
        refundDetailPcVO.setRefund(refundPcVO);

        /**
         * 写入skuOrder信息
         */
        List<SkuOrderPcVO> refundDetailPcVOS = new ArrayList<>();
        for (SkuOrder skuOrder : detailRes.getResult().getSkuOrderList()) {
            SkuOrderPcVO refundPc = new SkuOrderPcVO();
            refundPc.setSkuId(skuOrder.getSkuId());
            refundPc.setOrderId(skuOrder.getOrderId());
            refundPc.setTax(skuOrder.getTax());
            refundPc.setItemName(skuOrder.getItemName());
            refundPc.setQuantity(skuOrder.getQuantity());
            refundPc.setSkuImage(skuOrder.getSkuImage());
            refundPc.setIsBonded(skuOrder.getIsBonded());
            refundPc.setOriginFee(skuOrder.getOriginFee());
            refundPc.setItemId(skuOrder.getItemId());
            refundDetailPcVOS.add(refundPc);
        }
        refundDetailPcVO.setSkuOrders(refundDetailPcVOS);
        return refundDetailPcVO;
    }


}
