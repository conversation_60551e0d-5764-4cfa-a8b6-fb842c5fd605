package moonstone.web.front.component.item;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.google.common.collect.Lists;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.DistributionConstants;
import moonstone.common.enums.BondedType;
import moonstone.common.utils.CopyUtil;
import moonstone.common.utils.Json;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.item.dto.SkuWithItemForList;
import moonstone.item.dto.paging.ItemCriteria;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.model.*;
import moonstone.item.service.ItemAttributeReadService;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.SkuCustomReadService;
import moonstone.item.service.SkuReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.web.core.exports.common.Exporter;
import moonstone.web.core.exports.common.SheetMergeUtil;
import moonstone.web.core.shop.application.ShopManager;
import moonstone.web.front.component.convert.ItemExportExcelConvertor;
import moonstone.web.front.item.app.ItemConvertor;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.net.URL;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * Author:  CaiZhy
 * Date:    2019/2/20
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ItemReadLogic {
    @Autowired
    ItemReadService itemReadService;
    @Autowired
    SkuReadService skuReadService;
    @Autowired
    Exporter exporter;
    @Autowired
    ShopReadService shopReadService;
    @Autowired
    ShopCacheHolder shopCacheHolder;

    @Resource
    private ItemAttributeReadService itemAttributeReadService;

    @Resource
    private ItemExportExcelConvertor itemExportExcelConvertor;

    @Resource
    private SkuCustomReadService skuCustomReadService;

    @Resource
    private ShopManager shopManager;

    @Resource
    private ItemConvertor itemConvertor;

    public void exportExcel(ItemCriteria criteria, OutputStream outputStream) {
        try {
            if (ObjectUtils.isEmpty(criteria.getSortBy())) {
                criteria.setSortBy("id");
            }
            if (criteria.getSortType() == null) {
                criteria.setSortType(2);
            }
            Response<List<Item>> rItemList = itemReadService.list(criteria);
            if (!rItemList.isSuccess()) {
                log.error("failed to list items, param={}, error code: {}", criteria, rItemList.getError());
                throw new JsonResponseException(rItemList.getError());
            }
            List<Item> items = rItemList.getResult();
            List<Long> itemIds = Lists.transform(items, Item::getId);
            Response<List<Sku>> rSkus = skuReadService.findSkusByItemIds(itemIds);
            if (!rSkus.isSuccess()) {
                log.error("failed to find skus by itemIds={}, error code: {}", itemIds, rSkus.getError());
                throw new JsonResponseException(rSkus.getError());
            }
            List<Sku> skus = rSkus.getResult();
            Map<Long, List<Sku>> skusByItemId = new HashMap<>();
            for (Sku sku : skus) {
                List<Sku> skuList = skusByItemId.get(sku.getItemId());
                if (skuList == null) {
                    skuList = new ArrayList<>();
                }
                skuList.add(sku);
                skusByItemId.put(sku.getItemId(), skuList);
            }

            long shopId = items.get(0).getShopId();
            var itemAttributeMap = findItemAttributeMap(itemIds);
            var skuCustomMap = findSkuCustomMap(skus.stream().map(Sku::getId).toList());

            boolean isSubStore = shopManager.isSalePatternSubStore(shopId);
            var defaultConfig = findDefaultConfig(isSubStore, shopId);
            var configMap = findCommissionConfigs(isSubStore, itemIds);

            List<SkuWithItemForList> data = new ArrayList<>();
            for (Item item : items) {
                List<Sku> skuList = skusByItemId.get(item.getId());
                if (!CollectionUtils.isEmpty(skuList)) {
                    for (Sku sku : skuList) {
                        SkuWithItemForList skuWithItemForList = CopyUtil.copy(sku, SkuWithItemForList.class);
                        Map<String, Integer> extraPrice = sku.getExtraPrice() == null ? new HashMap<>() : sku.getExtraPrice();
                        Map<String, String> skuExtra = sku.getExtraMap() == null ? new HashMap<>() : sku.getExtraMap();
                        Map<String, String> itemExtra = item.getExtra() == null ? new HashMap<>() : item.getExtra();
                        skuWithItemForList.setDistributionPrice(extraPrice.get(DistributionConstants.SKU_DISTRIBUTION_PRICE));
                        skuWithItemForList.setProfit(extraPrice.get(DistributionConstants.SKU_PROFIT));
                        String attrsStr = "";
                        if (sku.getAttrs() != null) {
                            for (SkuAttribute skuAttribute : sku.getAttrs()) {
                                if (!"".equals(attrsStr)) {
                                    attrsStr += ", ";
                                }
                                attrsStr += skuAttribute.getAttrKey() + ": " + skuAttribute.getAttrVal();
                            }
                        }
                        skuWithItemForList.setAttrsStr(attrsStr);
                        skuWithItemForList.setUnitQuantity(ObjectUtils.isEmpty(skuExtra.get("unitQuantity")) ? null : Integer.parseInt(skuExtra.get("unitQuantity")));
                        skuWithItemForList.setSkuStatus(statusToString(sku.getStatus()));
                        skuWithItemForList.setItemCode(item.getItemCode());
                        skuWithItemForList.setIsBonded(BondedType.fromInt(item.getIsBonded()).isBonded() ? "保税" : "完税");
                        skuWithItemForList.setIsThirdPartyItem(item.getIsThirdPartyItem() == 1 ? "第三方商品" : "自建商品");
                        skuWithItemForList.setShopName(item.getShopName());
                        skuWithItemForList.setName(item.getName());
                        skuWithItemForList.setLowPrice(item.getLowPrice());
                        skuWithItemForList.setHighPrice(item.getHighPrice());
                        skuWithItemForList.setItemStockQuantity(item.getStockQuantity());
                        skuWithItemForList.setSaleQuantity(item.getSaleQuantity());
                        String sellInWeShop = itemExtra.get(DistributionConstants.SELL_IN_WE_SHOP);
                        skuWithItemForList.setSellInWeShop("true".equals(sellInWeShop) ? "是" : "否");
                        skuWithItemForList.setItemStatus(statusToString(item.getStatus()));
                        if (!new HashSet<>(Arrays.asList(28L, 33L)).contains(criteria.getShopId())) {
                            skuWithItemForList.setWxaUrl(criteria.getParanaWxaUrl() + "/goods/detail?storeId=" + criteria.getShopId() + "&skuSn=" + item.getId());
                        }

                        skuWithItemForList.setSkuOrderSplitLine(skuExtra.get(SkuExtraIndex.skuOrderSplitLine.getCode()));
                        skuWithItemForList.setOrderQuantityLimit(skuExtra.get(SkuExtraIndex.orderQuantityLimit.getCode()));
                        skuWithItemForList.setActivitySalesPrice(itemExportExcelConvertor.centToYuan(skuExtra.get(SkuExtraIndex.activitySalesPrice.getCode())));
                        skuWithItemForList.setActivityTime(itemExportExcelConvertor.convertActivityTime(skuExtra));
                        skuWithItemForList.setBarcode(item.getBarCode());

                        itemExportExcelConvertor.appendItemAttribute(skuWithItemForList, itemAttributeMap.get(item.getId()));
                        itemExportExcelConvertor.appendSkuCustom(skuWithItemForList, skuCustomMap.get(sku.getId()));
                        itemExportExcelConvertor.appendCommission(skuWithItemForList, defaultConfig, configMap.get(item.getId()));

                        data.add(skuWithItemForList);
                    }
                }
            }

            Map<String, String> shopExtra = Optional.ofNullable(shopReadService.findById(criteria.getShopId()).getResult()).map(Shop::getExtra).orElse(new HashMap<>());
            boolean displayQrCode = criteria.getShopId() != null && !new HashSet<>(Arrays.asList(28L, 33L, 82L)).contains(criteria.getShopId())
                    || !shopExtra.containsKey("removeWxAppUrl");
            Workbook workbook = exporter.exportWithDecorate(data, SkuWithItemForList.class,
                    (row, view) -> {
                        if (displayQrCode) {
                            transformUrlIntoQrCode(row, view);
                        } else {
                            // remove the target cell
                            removeQrCode().accept(row, view);
                        }
                    });
            SheetMergeUtil.mergeWorkbook(workbook).write(outputStream);
        } catch (Exception e) {
            log.error("{} export item excel fail, param={}, cause=", LogUtil.getClassMethodName(), criteria, e);
        }
    }

    BiConsumer<Row, SkuWithItemForList> removeQrCode() {
        AtomicInteger removeAt = new AtomicInteger(-1);
        return (row, view) -> {
            if (view == null) {
                for (int i = row.getFirstCellNum(); i < row.getLastCellNum(); i++) {
                    if (row.getCell(i).getStringCellValue().equals("微信小程序链接")) {
                        row.removeCell(row.getCell(i));
                        removeAt.set(i);
                        break;
                    }
                }
            } else {
                if (removeAt.get() > 0) {
                    row.removeCell(row.getCell(removeAt.get()));
                }
            }
        };
    }

    void transformUrlIntoQrCode(Row row, SkuWithItemForList view) {
        val draw = row.getSheet().createDrawingPatriarch();
        row.getSheet().setColumnWidth(8, 25 * 256);
        try {
            if (view == null) {
                Cell cell = row.createCell(row.getLastCellNum());
                cell.setCellValue(Translate.of("二维码"));
                return;
            }
            String url = view.getWxaUrl();
            row.setHeight((short) 3000);
            Long shopId = view.getShopId();
            Shop shop = shopCacheHolder.findShopById(shopId);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            BufferedImage logoImage = ImageIO.read(new URL(shop.getImageUrl()));
            BufferedImage image = QrCodeUtil.generate(url, QrConfig.create().setImg(logoImage));
            ImageIO.write(image, "png", outputStream);
            draw.createPicture(draw.createAnchor(0, 0, 0, 0, row.getLastCellNum(), row.getRowNum(), row.getLastCellNum() + 1, row.getRowNum() + 1), row.getSheet().getWorkbook().addPicture(outputStream.toByteArray(), HSSFWorkbook.PICTURE_TYPE_PNG));
        } catch (Exception ex) {
            log.error("{} fail to generate qrCode[Item => {}]", LogUtil.getClassMethodName(), Json.toJson(view), ex);
        }
    }

    private String statusToString(Integer status) {
        String skuStatus;
        switch (status) {
            case 1:
                skuStatus = "上架";
                break;
            case -1:
                skuStatus = "下架";
                break;
            case -2:
                skuStatus = "冻结";
                break;
            case -3:
                skuStatus = "删除";
                break;
            default:
                skuStatus = "非法状态";
        }
        return skuStatus;
    }

    private Map<Long, ItemAttribute> findItemAttributeMap(List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }

        var list = itemAttributeReadService.findByItemIds(itemIds).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(ItemAttribute::getItemId, o -> o, (k1, k2) -> k1));
    }

    private Map<Long, SkuCustom> findSkuCustomMap(List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.emptyMap();
        }

        var list = skuCustomReadService.findBySkuIds(skuIds).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(SkuCustom::getSkuId, o -> o, (k1, k2) -> k1));
    }

    private IntermediateInfo findDefaultConfig(boolean isSubStore, long shopId) {
        if (!isSubStore) {
            return null;
        }

        return itemConvertor.findDefaultConfig(shopId);
    }

    private Map<Long, List<IntermediateInfo>> findCommissionConfigs(boolean isSubStore, List<Long> itemIds) {
        if (!isSubStore) {
            return Collections.emptyMap();
        }

        return itemConvertor.findCommissionConfigs(itemIds);
    }
}
