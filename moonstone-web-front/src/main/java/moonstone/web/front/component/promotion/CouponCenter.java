package moonstone.web.front.component.promotion;

import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.google.common.collect.Multimaps;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.PromotionCacher;
import moonstone.cache.PromotionToolCacher;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.order.dto.RichSku;
import moonstone.promotion.api.Behavior;
import moonstone.promotion.api.PromotionTool;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.model.UserPromotion;
import moonstone.promotion.service.UserPromotionReadService;
import moonstone.web.core.component.promotion.CouponOutlet;
import moonstone.web.core.events.promotion.PromotionTrackChangeEvent;
import moonstone.web.front.promotions.dto.CouponState;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * Author:cp
 * Created on 13/02/2017.
 */
@Component
@Slf4j
public class CouponCenter {

    @Autowired
    private PromotionCacher promotionCacher;

    @Autowired
    private PromotionToolCacher promotionToolCacher;

    @Autowired
    private ItemCacheHolder itemCacheHolder;

    @Autowired
    private SkuCacheHolder skuCacheHolder;

    @Autowired
    private UserPromotionReadService userPromotionReadService;

    @Autowired
    private CouponOutlet couponOutlet;

    /**
     * 查询适用于指定商品的优惠券列表(给商品详情用)
     *
     * @param itemId 商品id
     * @return 适用的优惠券列表
     */
    public List<CouponState> findAvailableItemCouponByItemId(Long itemId) {
        CommonUser commonUser = UserUtil.getCurrentUser();

        Item item = itemCacheHolder.findItemById(itemId);
        List<Promotion> promotions = promotionCacher.findOngoingPromotionOf(item.getShopId());

        if (CollectionUtils.isEmpty(promotions)) {
            return Collections.emptyList();
        }

        List<Sku> skus = skuCacheHolder.findSkusByItemId(itemId);

        List<Promotion> itemCoupons = Lists.newArrayList();
        for (Promotion promotion : promotions) {
            if (PromotionType.from(promotion.getType()) != PromotionType.ITEM_COUPON) {
                continue;
            }
            final PromotionTool<? extends Behavior> promotionTool = promotionToolCacher.findByPromotionDefId(promotion.getPromotionDefId());

            for (Sku sku : skus) {
                if (promotionTool.skuScope().apply(makeRichSku(sku, item), commonUser, promotion.getSkuScopeParams())) {
                    itemCoupons.add(promotion);
                    break;
                }
            }
        }

        if (CollectionUtils.isEmpty(itemCoupons)) {
            return Collections.emptyList();
        }

        Set<Long> hasReceivedCoupons = findHasReceivedCoupons(commonUser, itemCoupons);
        List<CouponState> couponStates = Lists.newArrayList();
        for (Promotion promotion : itemCoupons) {
            CouponState couponState = makeCouponState(promotion, hasReceivedCoupons);
            couponStates.add(couponState);
        }
        return couponStates;
    }

    /**
     * 查询适用于指定店铺的优惠券列表(给购物车页面使用)
     *
     * @param shopId 店铺id
     * @return 适用的优惠券列表
     */
    public List<CouponState> findAvailableShopCouponByShopId(Long shopId) {
        return findCouponsByShopIdAndType(shopId, PromotionType.SHOP_COUPON);
    }

    /**
     * 根据店铺id查询店铺的运费券列表
     *
     * @param shopId 店铺id
     * @return 店铺的运费券列表
     */
    public List<CouponState> findAvailableShipmentCouponByShopId(Long shopId) {
        return findCouponsByShopIdAndType(shopId, PromotionType.SHIPMENT_COUPON);
    }

    private List<CouponState> findCouponsByShopIdAndType(Long shopId, PromotionType promotionType) {
        CommonUser commonUser = UserUtil.getCurrentUser();

        List<Promotion> promotions = promotionCacher.findOngoingPromotionOf(shopId);
        if (CollectionUtils.isEmpty(promotions)) {
            return Collections.emptyList();
        }

        List<Promotion> shopCoupons = Lists.newArrayList();
        for (Promotion promotion : promotions) {
            if (PromotionType.from(promotion.getType()) != promotionType) {
                continue;
            }
            shopCoupons.add(promotion);
        }

        if (CollectionUtils.isEmpty(shopCoupons)) {
            return Collections.emptyList();
        }

        Set<Long> hasReceivedCoupons = findHasReceivedCoupons(commonUser, shopCoupons);
        List<CouponState> couponStates = Lists.newArrayList();
        for (Promotion promotion : shopCoupons) {
            CouponState couponState = makeCouponState(promotion, hasReceivedCoupons);
            couponStates.add(couponState);
        }
        return couponStates;
    }

    public boolean receiveCoupon(Long promotionId) {
        final Long buyerId = UserUtil.getUserId();
        couponOutlet.receive(buyerId, promotionId);

        //发出事件更新promotionTrack的领券数量
        EventSender.sendApplicationEvent(new PromotionTrackChangeEvent(promotionId, 1, PromotionTrackChangeEvent.ChangeType.RECEIVED_QUANTITY));
        return true;
    }

    private Set<Long> findHasReceivedCoupons(CommonUser commonUser, List<Promotion> couponPromotions) {
        Set<Long> hasReceivedCoupons = new HashSet<>();

        if (commonUser != null) {
            List<Long> promotionIds = Lists.newArrayList();
            for (Promotion promotion : couponPromotions) {
                promotionIds.add(promotion.getId());
            }

            Response<List<UserPromotion>> findUserPromotionResp = userPromotionReadService.findByUserIdAndPromotionIds(commonUser.getId(), promotionIds);
            if (!findUserPromotionResp.isSuccess()) {
                log.error("fail to find user promotion by useId={},promotionIds={},cause:{}",
                        commonUser.getId(), promotionIds, findUserPromotionResp.getError());
                throw new JsonResponseException(findUserPromotionResp.getError());
            }
            List<UserPromotion> userPromotions = findUserPromotionResp.getResult();

            Multimap<Long, UserPromotion> userPromotionsByPromotionIdIndex = Multimaps.index(userPromotions, UserPromotion::getPromotionId);

            for (Promotion promotion : couponPromotions) {
                Collection<UserPromotion> hasReceivedByPromotionId = userPromotionsByPromotionIdIndex.get(promotion.getId());
                //从未领过
                if (CollectionUtils.isEmpty(hasReceivedByPromotionId)) {
                    continue;
                }
                //用户在此之前已经达到领取限制了,则认为已经领过了
                if (couponOutlet.hasMeetReceiveLimit(promotion, hasReceivedByPromotionId.size())) {
                    hasReceivedCoupons.add(promotion.getId());
                }
            }
        }
        return hasReceivedCoupons;
    }

    private CouponState makeCouponState(Promotion promotion, Set<Long> hasReceivedCoupons) {
        CouponState couponState = new CouponState();
        couponState.setPromotion(promotion);
        couponState.setHasReceived(hasReceivedCoupons.contains(promotion.getId()));
        return couponState;
    }

    private RichSku makeRichSku(Sku sku, Item item) {
        RichSku richSku = new RichSku();
        richSku.setSku(sku);
        richSku.setItem(item);
        return richSku;
    }

}
