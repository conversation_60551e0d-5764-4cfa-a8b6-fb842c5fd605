package moonstone.web.front.component.order;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import io.terminus.common.model.Paging;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemSnapShotCacheHolder;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.SkuCacheHolder;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.model.Either;
import moonstone.common.model.IsPersistAble;
import moonstone.common.model.IsPresent;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.*;
import moonstone.item.service.GbGroupInfoReadService;
import moonstone.item.service.GbGroupMemberReadService;
import moonstone.order.api.OrderAndOperation;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.dto.OrderGroup;
import moonstone.order.enu.OrderFlagEnum;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.enu.ProfitType;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.model.*;
import moonstone.order.model.result.OrderPaymentInfoDO;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.order.service.OrderRoleSnapshotReadService;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.PaymentReadService;
import moonstone.shop.model.SubStore;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.user.cache.UserProfileCacheHolder;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.UserProfile;
import moonstone.user.service.UserProfileReadService;
import moonstone.weShop.model.WeShop;
import moonstone.web.core.component.order.view.ShopOrderVO;
import moonstone.web.core.events.item.handler.OrderInventoryValidateUtil;
import moonstone.web.core.order.dto.DistributionProfitVO;
import moonstone.web.core.order.dto.OrderGroupViewObject;
import moonstone.web.core.order.enums.GbActivityStatusEnum;
import moonstone.web.core.order.enums.GbGroupMemberRoleEnum;
import moonstone.web.core.order.enums.GbGroupMemberStatusEnum;
import moonstone.web.core.order.enums.GbGroupStatusEnum;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.model.ServiceProvider;
import moonstone.web.core.user.StoreProxyManager;
import moonstone.web.core.order.dto.OrderActivityInfoVO;
import moonstone.web.front.component.order.view.SkuOrderView;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@AllArgsConstructor
public class OrderExtraInfoDecorateHelper {
    private final UserProfileCacheHolder profileCache;
    private final SubStoreReadService subStoreReadService;
    private final SkuCacheHolder skuCacheHolder;

    private final BalanceDetailReadService balanceDetailReadService;
    private final OrderWriteService orderWriteService;

    private final StoreProxyManager storeProxyManager;
    private final ShopCacheHolder shopCacheHolder;
    private final WeShopCacheHolder weShopCacheHolder;
    private final ItemSnapShotCacheHolder itemSnapShotCacheHolder;
    private final ServiceProviderCache serviceProviderCache;

    private final OrderRoleSnapshotReadService orderRoleSnapshotReadService;

    private final PaymentReadService paymentReadService;
    private final UserProfileReadService userProfileReadService;
    private final SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;

    private final GbGroupMemberReadService gbGroupMemberReadService;

    private final GbGroupInfoReadService gbGroupInfoReadService;

    private final OrderInventoryValidateUtil orderInventoryValidateUtil;

    private final Function<Long, ProfitView> profitViewCache;

    private final BigDecimal HUNDREDS = new BigDecimal(100);

    /**
     * 为门店打包门店数据
     * TODO: add the profit info into the ProfitView
     *
     * @see ProfitView the one should contain the profit data cache
     */
    public Paging<OrderGroupViewObject> decorateOrderWithExtraInfo(Paging<OrderGroupViewObject> orderGroupVos) {
        long cost = System.currentTimeMillis();
        try {
            // key = shopOrderId
            var paymentMap = findPaymentMap(orderGroupVos.getData());

            // key = userId
            var userProfileMap = findUserProfileMap(orderGroupVos.getData());

            for (OrderGroupViewObject orderGroupViewObject : orderGroupVos.getData()) {
                appendOrderPushErrorMessage(orderGroupViewObject);

                ShopOrderVO shopOrder = new ShopOrderVO(orderGroupViewObject.getShopOrder());
                var payment = paymentMap.get(orderGroupViewObject.getShopOrder().getId());
                var outId = payment != null ? payment.getOutId() : null;

                orderGroupViewObject.setShopOrder(shopOrder);

                appendWxUserName(shopOrder, userProfileMap.get(orderGroupViewObject.getShopOrder().getBuyerId()));

                orderGroupViewObject.setDeclaredNo(StringUtils.isNotBlank(shopOrder.getDeclaredId()) ?
                        shopOrder.getDeclaredId() : new Translate("获取申报单号失败").toString());

                if (outId != null) {
                    orderGroupViewObject.setPaymentOutId(outId.toString());
                    var shop = shopCacheHolder.findShopById(shopOrder.getShopId());
                    if (shop.getUserId().equals(UserUtil.getUserId())) {
                        orderGroupViewObject.setDeclaredNo(orderGroupViewObject.getDeclaredNo() + " | " + outId);
                        shopOrder.setDeclaredId(shopOrder.getDeclaredId() + " | " + outId);
                    }
                }

                setAllowRefund(orderGroupViewObject, payment);

                List<OrderAndOperation> skuOrderOperations = new LinkedList<>();
                for (OrderAndOperation originSkuOrderAndOperation : orderGroupViewObject.getSkuOrderAndOperations()) {
                    SkuOrder origin = (SkuOrder) originSkuOrderAndOperation.order();
                    try {
                        var sku = skuCacheHolder.findSkuById(origin.getSkuId());
                        var orderView = new SkuOrderView(origin,
                                Optional.of(origin).map(SkuOrder::getItemSnapshotId)
                                        .map(itemSnapShotCacheHolder::findBySnapShotId)
                                        .map(ItemSnapshot::getItemCode)
                                        .orElse(null));
                        if(ObjectUtil.isEmpty(sku.getSpecification())){
                            sku.setSpecification(sku.getName());
                        }
                        sku.setName(origin.getItemName());
                        Optional.of(sku)
                                .map(Sku::getImage)
                                .ifPresent(orderView::setSkuImage);
                        skuOrderOperations.add(new OrderGroup.SkuOrderAndOperation(orderView, originSkuOrderAndOperation.operation(),
                                sku, OrderFlagEnum.getFrontEndFlagCodes(orderView.getFlag())));
                    } catch (Exception exception) {
                        log.error("{} fail to decorate item code for order[{}, skuOrderId=>{}]", LogUtil.getClassMethodName(), origin.getOrderId(), origin.getId(), exception);
                    }
                }
                orderGroupViewObject.setSkuOrderAndOperations(skuOrderOperations);

                // 设置活动信息
                ShopOrder dbShopOrder = orderGroupViewObject.getShopOrder();
                setActivityInfo(dbShopOrder, orderGroupViewObject);
            }

            decorateProfitByBatch(orderGroupVos.getData());

            return orderGroupVos;
        } finally {
            cost = System.currentTimeMillis() - cost;
            if (cost > 200) {
                log.debug("OrderExtraInfoDecorateHelper.decorateOrderWithExtraInfo, decorate the Profit has cost a lot({})", cost);
            }
        }
    }

    private void setActivityInfo(ShopOrder dbShopOrder, OrderGroupViewObject orderGroupViewObject) {
        if(orderInventoryValidateUtil.isHasIndependentStock(dbShopOrder)){
            Integer activityId = dbShopOrder.getActivityId();

            GbActivityInfo gbActivityInfo = gbGroupInfoReadService.findActivityInfoById(activityId);

            Long userId = dbShopOrder.getBuyerId();
            Long shopOrderId = dbShopOrder.getId();
            GbGroupMember gbGroupMember = gbGroupMemberReadService.findByShopOrderId(shopOrderId, userId);
            if(gbGroupMember == null){
                log.error("团成员信息为空 {}", shopOrderId);
                return;
            }
            if(gbGroupMember.getGroupId() == null){
                log.error("团ID为空 {}", gbGroupMember.getId());
                return;
            }
            Long groupMemberNum = gbGroupMemberReadService.countGroupMemberNum(activityId, gbGroupMember.getGroupId());

            /**
             *   1-团长待支付：支付完成即可开团
             *   2-团员待支付：支付完成即可参团
             *   3-进行中：拼团中，还差N个人：点击可分享
             *   4-开团成功：拼团已成功
             *   5-开团失败：拼团失败
             */
            GbGroupInfo gbGroupInfo = gbGroupInfoReadService.findById(gbGroupMember.getGroupId());

            OrderActivityInfoVO orderActivityInfoVO = new OrderActivityInfoVO();
            orderActivityInfoVO.setMarketingToolId(dbShopOrder.getOrderSource());
            orderActivityInfoVO.setGroupId(gbGroupInfo.getId());
            orderActivityInfoVO.setActivityId(activityId);
            orderActivityInfoVO.setGroupStatus(gbGroupInfo.getGroupStatus());
            orderActivityInfoVO.setGroupStatusDesc(GbGroupStatusEnum.findDescByCode(gbGroupInfo.getGroupStatus()));
            orderActivityInfoVO.setMemberRole(gbGroupMember.getMemberRole());
            orderActivityInfoVO.setMemberRoleDesc(GbGroupMemberRoleEnum.findDescByCode(gbGroupMember.getMemberRole()));

            orderActivityInfoVO.setMemberStatus(gbGroupMember.getMemberStatus());
            orderActivityInfoVO.setMemberStatusDesc(GbGroupMemberStatusEnum.findDescByCode(gbGroupMember.getMemberStatus()));

            orderActivityInfoVO.setGroupMemberNum(groupMemberNum);
            orderActivityInfoVO.setActivityNum(gbActivityInfo.getActivityNum());
            orderActivityInfoVO.setActivityStatus(gbActivityInfo.getActivityStatus());
            orderActivityInfoVO.setActivityStatusDesc(GbActivityStatusEnum.findDescByCode(gbActivityInfo.getActivityStatus()));
            log.info("活动订单 {}", JSONObject.toJSONString(orderActivityInfoVO));
            orderGroupViewObject.setOrderActivityInfo(orderActivityInfoVO);
        }
    }

    private void appendOrderPushErrorMessage(OrderGroupViewObject target) {
        if (target == null || target.getShopOrder() == null || CollectionUtils.isEmpty(target.getShopOrder().getExtra())) {
            return;
        }
        var extra = target.getShopOrder().getExtra();

        target.setOrderPushErrorMessage(extra.get(ShopOrderExtra.orderPushErrorMessage.name()));
    }

    /**
     * 原 decorateProfit(shopOrder, orderGroupViewObject)
     *
     * @param list
     */
    private void decorateProfitByBatch(List<OrderGroupViewObject> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 按照 OutFrom 分组
        var grouped = list.stream().collect(Collectors.groupingBy(this::getOrderOutFrom));
        for (var entry : grouped.entrySet()) {
            var outFrom = entry.getKey();
            var orders = entry.getValue();

            // SUB_STORE 模式重点处理，其它模式都没人用了，先不管
            switch (outFrom) {
                case LEVEL_Distribution -> orders.forEach(this::appendByLevelDistribution);
                case SUB_STORE -> decorateSubStoreOrderExtraInfo(orders);
                case WE_SHOP -> orders.forEach(order -> decorateWeShopProfit((ShopOrderVO) order.getShopOrder(), order));
                default -> {
                }
            }
        }
    }

    private void appendWxUserName(ShopOrderVO shopOrder, UserProfile profile) {
        shopOrder.setWxUserName(Optional.ofNullable(profile).map(UserProfile::getRealName).orElse(shopOrder.getBuyerName()));
    }

    private Map<Long, UserProfile> findUserProfileMap(List<OrderGroupViewObject> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyMap();
        }

        return userProfileReadService.findMapByUserIds(data.stream().map(entry -> entry.getShopOrder().getBuyerId()).toList());
    }

    private Map<Long, OrderPaymentInfoDO> findPaymentMap(List<OrderGroupViewObject> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyMap();
        }

        return paymentReadService.findMapByOrderIds(data.stream().map(entry -> entry.getShopOrder().getId()).toList(),
                OrderLevel.SHOP);
    }

    private OrderOutFrom getOrderOutFrom(OrderGroupViewObject target) {
        if (ObjectUtils.isEmpty(target.getShopOrder().getOutFrom())) {
            return OrderOutFrom.UNKNOW;
        }

        try {
            return OrderOutFrom.fromCode(target.getShopOrder().getOutFrom());
        } catch (Exception ex) {
            log.error("OrderExtraInfoDecorateHelper.getOrderOutFrom error, shopOrderId={}, outFrom={}",
                    target.getShopOrder().getId(), target.getShopOrder().getOutFrom());
            return OrderOutFrom.UNKNOW;
        }
    }

    private void appendByLevelDistribution(OrderGroupViewObject target) {
        var shopOrder = (ShopOrderVO) target.getShopOrder();

        Optional.ofNullable(shopOrder.getReferenceId())
                .flatMap(proxyUserId -> storeProxyManager.getStoreProxyByShopIdAndUserId(shopOrder.getShopId(), proxyUserId))
                .map(StoreProxy::getProxyShopName)
                .ifPresent(shopOrder::setSubShopName);

        decorateLevelDistOrderWithInfoAndProfit(shopOrder, target);

        //阶梯分销的佣金明细打包
        decorateLevelDistOrderWithDetailProfit(target);
    }

    private void decorateWeShopProfit(ShopOrderVO shopOrder, OrderGroupViewObject orderGroupViewObject) {
        if (ObjectUtils.isEmpty(shopOrder.getOutShopId())) {
            return;
        }
        WeShop weShop = weShopCacheHolder.findByWeShopId(Long.parseLong(shopOrder.getOutShopId()))
                .orElse(null);
        if (Objects.isNull(weShop)) {
            return;
        }
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setRelatedId(shopOrder.getId());
        criteria.setType(ProfitType.InCome.getValue());
        criteria.setNotStatusBitMarks(Arrays.asList(BalanceDetail.maskBit.WithDrawRelated.getValue()
                , BalanceDetail.maskBit.RefundRelated.getValue()
                , IsPresent.presentMaskBit.Present.getValue()
        ));
        criteria.setStatusBitMarks(Arrays.asList(BalanceDetail.maskBit.OrderRelated.getValue()
                , BalanceDetail.orderRelatedMask.ShopOrder.getValue()
                , IsPersistAble.maskBit.PersistAble.getValue()
        ));
        criteria.setSourceId(shopOrder.getShopId());
        Optional<BalanceDetail> profitOrigin = Optional.ofNullable(balanceDetailReadService.paging(criteria).getResult()).map(Paging::getData).stream().flatMap(Collection::stream)
                .filter(notPresent -> !notPresent.isPresent() && notPresent.isPersistAble())
                .findFirst();
        BigDecimal profit = profitOrigin.map(BalanceDetail::getChangeFee).map(BigDecimal::valueOf)
                .orElse(BigDecimal.ZERO)
                .divide(HUNDREDS, 2, RoundingMode.DOWN);
        // hack: profit use the different way, because the display logical is different
        // warn: the order export depends on this
        orderGroupViewObject.getOrderProfitViewList().add(new OrderGroupViewObject.OrderProfitView(profitOrigin.map(BalanceDetail::getId).orElse(null), weShop.getName(), weShop.getId()
                , profit
                , Translate.of("利润")));
    }

    /**
     * 打包阶梯分销的佣金
     * (只需要统计待收入即可,已收入不要统计)
     *
     * @param orderGroupViewObject 订单详情
     */
    private void decorateLevelDistOrderWithDetailProfit(OrderGroupViewObject orderGroupViewObject) {
        // read profit-add from profit-profile
        ShopOrder shopOrder = orderGroupViewObject.getShopOrder();
        Long shopId = shopOrder.getShopId();
        if (orderGroupViewObject.getDistributionProfit() != null) {
            Long proxyUserId = orderGroupViewObject.getDistributionProfit().getLinkedReceiverUserId();
            if (proxyUserId != null) {
                orderGroupViewObject.setLinkedProfit(orderGroupViewObject.getDistributionProfit().getLinkedProfit().longValue() * 100);
                orderGroupViewObject.setLinkedProfitOwner(storeProxyManager.findValidStoreProxyFromRefererIdAndShopId(shopId, proxyUserId).map(StoreProxy::getProxyShopName).orElse(""));
                orderGroupViewObject.getOrderProfitViewList().add(new OrderGroupViewObject.OrderProfitView(orderGroupViewObject.getDistributionProfit().getLinkedProfitFrom(), orderGroupViewObject.getLinkedProfitOwner()
                        , proxyUserId
                        , orderGroupViewObject.getDistributionProfit().getLinkedProfit()
                        , Translate.of("忠诚佣金")));
            }
        }
        Map<String, String> extra = shopOrder.getExtra() == null ? new HashMap<>(8) : shopOrder.getExtra();
        String babyProfitIndex = "BabyProfit";
        if (!ObjectUtils.isEmpty(extra.get(babyProfitIndex))) {
            try {
                orderGroupViewObject.setExLinkedProfit(Long.valueOf(extra.get(babyProfitIndex)));
            } catch (Exception ex) {
                //fuck u!
            }
        }
        // set others
        if (!ObjectUtils.isEmpty(orderGroupViewObject.getShopOrder().getReferenceId())) {
            Either<Optional<StoreProxy>> orderProxyOpt = Either.ok(storeProxyManager.findValidStoreProxyFromRefererIdAndShopId(shopId, shopOrder.getRefererId()));
            if (!orderProxyOpt.isSuccess() || orderProxyOpt.take().isEmpty()) {
                return;
            }
            StoreProxy orderProxy = orderProxyOpt.take().get();
            Long proxyId = orderProxy.getUserId();
            if (!Objects.equals(shopOrder.getReferenceId(), proxyId)) {
                log.warn("{} redirect shopOrder[{}] refererId[{}] into [{}]", LogUtil.getClassMethodName(), shopOrder.getId(), shopOrder.getReferenceId(), proxyId);
                shopOrder.setRefererId(proxyId);
                orderWriteService.updateOrderRefererId(shopOrder.getId(), proxyId);
            }
            //经销商下面购买
            if (Objects.equals(orderProxy.getLevel(), 1)) {
                orderGroupViewObject.setSubStoreName(orderProxy.getProxyShopName());
                Optional.ofNullable(orderGroupViewObject.getDistributionProfit()).map(DistributionProfitVO::getReachProfit)
                        .map(bd -> bd.multiply(HUNDREDS).longValue())
                        .ifPresent(orderGroupViewObject::setSubStoreProfit);
            } else {
                //门店下面购买
                orderGroupViewObject.setGuiderName(orderProxy.getProxyShopName());
                Optional.ofNullable(orderGroupViewObject.getDistributionProfit()).map(DistributionProfitVO::getReachProfit)
                        .map(bd -> bd.multiply(HUNDREDS).longValue())
                        .ifPresent(orderGroupViewObject::setGuiderProfit);
            }
            orderGroupViewObject.getOrderProfitViewList().add(new OrderGroupViewObject.OrderProfitView(
                    orderGroupViewObject.getDistributionProfit().getReachFrom()
                    , orderProxy.getProxyShopName()
                    , orderProxy.getUserId()
                    , Optional.ofNullable(orderGroupViewObject.getDistributionProfit()).map(DistributionProfitVO::getReachProfit).orElse(null)
                    , Translate.of("一级佣金")));

            if (Objects.equals(orderProxy.getLevel(), 1)) {
                return;
            }
            Optional<StoreProxy> orderProxySuperOpt = storeProxyManager.findValidStoreProxyFromRefererIdAndShopId(shopId, orderProxy.getSupperId());

            if (orderProxySuperOpt.isEmpty()) {
                return;
            }
            StoreProxy orderProxySuper = orderProxySuperOpt.get();
            if (Objects.equals(orderProxySuper.getLevel(), 1)) {
                orderGroupViewObject.setSubStoreName(orderProxySuper.getProxyShopName());
                Optional.ofNullable(orderGroupViewObject.getDistributionProfit()).map(DistributionProfitVO::getTransmitProfit)
                        .map(bd -> bd.multiply(HUNDREDS).longValue())
                        .ifPresent(orderGroupViewObject::setSubStoreProfit);
            }
            orderGroupViewObject.getOrderProfitViewList().add(new OrderGroupViewObject.OrderProfitView(orderGroupViewObject.getDistributionProfit().getTransmitFrom()
                    , orderProxySuper.getProxyShopName()
                    , orderProxySuper.getUserId()
                    , Optional.ofNullable(orderGroupViewObject.getDistributionProfit()).map(DistributionProfitVO::getTransmitProfit).orElse(null)
                    , Translate.of("二级佣金")));
        }
    }

    /**
     * 读取Shop数据 判断是否允许退款    依赖ShopCache
     *
     * @param orderGroupViewObject 订单详情
     */
    private void setAllowRefund(OrderGroupViewObject orderGroupViewObject, OrderPaymentInfoDO paymentInfo) {
        // 未支付则退出
        if (orderGroupViewObject.getShopOrder().getStatus() == 0) {
            return;
        }
        // this shop is dead
        var staticShopId = 29L;
        var shopExtra = Optional.ofNullable(shopCacheHolder.findShopById(orderGroupViewObject.getShopOrder().getShopId()).getExtra()).orElseGet(HashMap::new);
        if (orderGroupViewObject.getShopOrder().getShopId() == staticShopId && shopExtra.get(ShopExtra.AllowRefund.getCode()) == null) {
            shopExtra.put(ShopExtra.AllowRefund.getCode(), "60");
        }
        Function<String, Integer> takePretendItIsNum = str -> {
            var resultStream = new ByteArrayOutputStream();
            try {
                for (byte aByte : str.getBytes()) {
                    if (aByte > '9' || aByte < '0') {
                        if (resultStream.size() == 0) {
                            continue;
                        } else {
                            break;
                        }
                    }
                    resultStream.write(aByte);
                }
            } catch (Exception ex) {
                log.error("{} fail to take str[{}] as num", LogUtil.getClassMethodName(), str);
            }
            if (str.length() != resultStream.size() && !Objects.equals(str, Boolean.FALSE.toString())) {
                log.warn("{} filter the str:{}", LogUtil.getClassMethodName(), str);
            }
            return resultStream.size() == 0 ? 0 : Integer.parseInt(resultStream.toString());
        };
        var allowRefundSet = shopExtra.getOrDefault(ShopExtra.AllowRefund.getCode(), Boolean.FALSE.toString()).trim();
        if (Boolean.TRUE.toString().equals(allowRefundSet)) {
            orderGroupViewObject.setRefund(true);
            return;
        }
        var orderCreatedAt = orderGroupViewObject.getShopOrder().getCreatedAt();
        if (Boolean.FALSE.toString().equals(allowRefundSet)) {
            orderGroupViewObject.setRefund(true);
            return;
        }
        // 3 天为允许用户正常退款的期限
        if (orderCreatedAt.before(Date.from(ZonedDateTime.now().minusDays(3).toInstant()))) {
            orderGroupViewObject.setRefund(false);
            return;
        }
        DateTime now = new DateTime(Date.from(Instant.now()));
        int refundAllowLimitMinutes = takePretendItIsNum.apply(allowRefundSet);

        Function<Payment, Boolean> allowRefund = allowRefundSet.isEmpty() ?
                payment -> false
                : payment -> Optional.ofNullable(payment.getPaidAt()).filter(paidAt -> !paidAt.before(now.minusMinutes(refundAllowLimitMinutes).toDate())).isPresent();

        orderGroupViewObject.setRefund(paymentInfo != null && allowRefund.apply(paymentInfo));
    }

    private void decorateLevelDistOrderWithInfoAndProfit(@NotNull ShopOrder shopOrder, @NotNull OrderGroupViewObject orderGroupViewObject) {
        var profile = profileCache.getUserProfileByUserId(shopOrder.getBuyerId());
        if (!ObjectUtils.isEmpty(profile)) {
            orderGroupViewObject.getShopOrder().setBuyerName(profile.getRealName());
            orderGroupViewObject.setBuyerAvatarUrl(profile.getAvatar_());
        }
        DistributionProfitVO profitVO = new DistributionProfitVO();
        // if the profit id is set, there is no need to query it from database anymore
        if (shopOrder.getProfitId() != null) {
            var profitView = profitViewCache.apply(shopOrder.getProfitId());
            profitVO.setReachProfit(showFee(profitView.profitA()));
            profitVO.setTransmitProfit(showFee(profitView.profitB()));
            profitVO.setLinkedProfit(showFee(profitView.profitC()));
        } else {
            BalanceDetailCriteria criteria = new BalanceDetailCriteria();
            criteria.setRelatedId(shopOrder.getId());
            criteria.setType(ProfitType.InCome.getValue());
            criteria.setNotStatusBitMarks(Arrays.asList(BalanceDetail.maskBit.WithDrawRelated.getValue()
                    , BalanceDetail.maskBit.RefundRelated.getValue()
                    , IsPresent.presentMaskBit.Present.getValue()
            ));
            criteria.setStatusBitMarks(Arrays.asList(BalanceDetail.maskBit.OrderRelated.getValue()
                    , BalanceDetail.orderRelatedMask.ShopOrder.getValue()
                    , IsPersistAble.maskBit.PersistAble.getValue()
            ));
            criteria.setSourceId(shopOrder.getShopId());
            criteria.setPageSize(1000);
            List<BalanceDetail> relateProfitRes = balanceDetailReadService.pageList(criteria).getResult();
            if (CollectionUtils.isEmpty(relateProfitRes)) {
                return;
            }
            final int relatedProfitNum = 3;
            if (relateProfitRes.size() > relatedProfitNum) {
                log.warn("{} shopOrderId:{} balanceListSize:{} list:{}", LogUtil.getClassMethodName(), shopOrder.getId(), relateProfitRes.size(), relateProfitRes);
            }
            relateProfitRes.forEach(generateAutoFillProfitVO(profitVO));
        }
        orderGroupViewObject.setDistributionProfit(profitVO);
    }

    private Consumer<BalanceDetail> generateAutoFillProfitVO(DistributionProfitVO profitView) {
        return balanceDetail -> {
            if (balanceDetail.isReach()) {
                profitView.setReachProfit(new BigDecimal(balanceDetail.getChangeFee()).divide(HUNDREDS, 2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_DOWN));
                profitView.setReachReceiverUserId(balanceDetail.getUserId());
            }
            if (balanceDetail.isLinked()) {
                profitView.setLinkedProfit(new BigDecimal(balanceDetail.getChangeFee()).divide(HUNDREDS, 2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_DOWN));
                profitView.setLinkedReceiverUserId(balanceDetail.getUserId());
            }
            if (balanceDetail.isTransmit()) {
                profitView.setTransmitProfit(new BigDecimal(balanceDetail.getChangeFee()).divide(HUNDREDS, 2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_DOWN));
                profitView.setTransmitReceiverUserId(balanceDetail.getUserId());
            }
        };
    }

    BigDecimal showFee(Long price) {
        if (price == null) return null;
        return BigDecimal.valueOf(price).divide(HUNDREDS, 2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_DOWN);
    }

    /**
     * 批处理<br/>
     * 原 private void decorateSubStoreOrderExtraInfo(@NotNull ShopOrder shopOrder, @NotNull OrderGroupViewObject orderGroupViewObject)
     *
     * @param orderList
     */
    private void decorateSubStoreOrderExtraInfo(List<OrderGroupViewObject> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        Long shopId = orderList.get(0).getShopOrder().getShopId();
        var shopOrderIds = orderList.stream().map(entry -> entry.getShopOrder().getId()).toList();

        // 角色快照信息, key = shopOrderId
        var snapshots = orderRoleSnapshotReadService.findMapByShopOrderIds(
                shopOrderIds, OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER);

        // 利润信息, key = shopOrderId
        var profitMap = balanceDetailReadService.getBalanceDetailMap(shopOrderIds, shopId);

        // 当前门店信息, key = shopOrderId
        var subStoreMap = findSubStoreMap(orderList);

        // 当前导购信息, key = shopOrderId
        var guiderMap = findGuiderMap(orderList);

        // 当前服务商信息, key = shopOrderId
        var serviceProviderMap = findServiceProviderMap(snapshots);

        orderList.forEach(order -> {
            Long shopOrderId = order.getShopOrder().getId();

            // 填充
            appendGuiderInfo(order, guiderMap.get(shopOrderId), subStoreMap.get(shopOrderId), profitMap.get(shopOrderId),
                    snapshots.get(shopOrderId));
            appendSubStoreInfo(order, subStoreMap.get(shopOrderId), profitMap.get(shopOrderId), snapshots.get(shopOrderId));
            appendServiceProviderInfo(order, serviceProviderMap.get(shopOrderId), subStoreMap.get(shopOrderId), profitMap.get(shopOrderId),
                    snapshots.get(shopOrderId));
        });
    }

    private void appendServiceProviderInfo(OrderGroupViewObject order, ServiceProvider serviceProvider, SubStore subStore,
                                           List<BalanceDetail> balanceDetailList, List<OrderRoleSnapshot> snapshots) {
        var serviceProviderSnapshot = getTargetRoleSnapshot(snapshots, SubStoreUserIdentityEnum.SERVICE_PROVIDER);

        if (serviceProvider != null) {
            order.setServiceProviderName(serviceProvider.getName());
        } else if (serviceProviderSnapshot != null) {
            order.setServiceProviderName(serviceProviderSnapshot.getName());
        } else {
            var bySubStore = findServiceProvider(balanceDetailList, subStore);
            if (bySubStore != null) {
                serviceProvider = bySubStore;
                order.setServiceProviderName(serviceProvider.getName());
            }
        }

        Long serviceProviderUserId = serviceProvider != null ? serviceProvider.getUserId() :
                (serviceProviderSnapshot != null ? serviceProviderSnapshot.getUserId() : null);
        if (serviceProviderUserId != null && !CollectionUtils.isEmpty(balanceDetailList)) {
            order.setServiceProviderProfit(balanceDetailList.stream()
                    .filter(detail -> detail.getUserId().equals(serviceProviderUserId) && !detail.isPresent() && !detail.isLinked())
                    .map(BalanceDetail::getChangeFee)
                    .reduce(Long::sum)
                    .orElse(null));

            order.setServiceProviderProfitFrom(balanceDetailList.stream()
                    .filter(detail -> detail.getUserId().equals(serviceProviderUserId) && !detail.isPresent() && !detail.isLinked())
                    .map(BalanceDetail::getId)
                    .reduce(Long::sum)
                    .orElse(null));
        }

        order.setServiceProviderRealProfit(order.getServiceProviderProfit() != null);
    }

    private void appendSubStoreInfo(OrderGroupViewObject order, SubStore subStore, List<BalanceDetail> balanceDetailList,
                                    List<OrderRoleSnapshot> snapshots) {
        var subStoreSnapshot = getTargetRoleSnapshot(snapshots, SubStoreUserIdentityEnum.SUB_STORE);

        if (subStore != null) {
            order.setSubStoreName(subStore.getName());
        } else if (subStoreSnapshot != null) {
            order.setSubStoreName(subStoreSnapshot.getName());
        }

        Long subStoreUserId = subStore != null ? subStore.getUserId() : (subStoreSnapshot != null ? subStoreSnapshot.getUserId() : null);
        if (subStoreUserId != null && !CollectionUtils.isEmpty(balanceDetailList)) {
            order.setSubStoreProfit(balanceDetailList.stream()
                    .filter(detail -> detail.getUserId().equals(subStoreUserId) && !detail.isPresent())
                    .map(BalanceDetail::getChangeFee)
                    .reduce(Long::sum)
                    .orElse(null));

            order.setSubStoreProfitFrom(balanceDetailList.stream()
                    .filter(detail -> detail.getUserId().equals(subStoreUserId) && !detail.isPresent())
                    .map(BalanceDetail::getId)
                    .reduce(Long::sum)
                    .orElse(null));
        }

        order.setSubStoreRealProfit(order.getSubStoreProfit() != null);
    }

    private void appendGuiderInfo(OrderGroupViewObject order, SubStoreTStoreGuider guider, SubStore subStore,
                                  List<BalanceDetail> balanceDetailList, List<OrderRoleSnapshot> snapshots) {
        var guiderSnapshot = getTargetRoleSnapshot(snapshots, SubStoreUserIdentityEnum.STORE_GUIDER);
        var subStoreSnapshot = getTargetRoleSnapshot(snapshots, SubStoreUserIdentityEnum.SUB_STORE);

        if (guider != null) {
            order.setGuiderName(guider.getStoreGuiderNickname());
        } else if (guiderSnapshot != null) {
            order.setGuiderName(guiderSnapshot.getName());
        }

        Long subStoreUserId = subStore != null ? subStore.getUserId() : (subStoreSnapshot != null ? subStoreSnapshot.getUserId() : null);
        if (order.getShopOrder().getRefererId() == null || CollectionUtils.isEmpty(balanceDetailList)) {
            order.setGuiderProfit(null);
        } else if (subStoreUserId != null) {
            order.setGuiderProfit(balanceDetailList.stream()
                    .filter(profit -> !profit.isPresent() && !subStoreUserId.equals(profit.getUserId()))
                    .filter(BalanceDetail::isLinked)
                    .map(BalanceDetail::getChangeFee)
                    .reduce(Long::sum)
                    .orElse(null));
        }

        order.setGuiderRealProfit(order.getGuiderProfit() != null);
    }

    private ServiceProvider findServiceProvider(List<BalanceDetail> balanceDetailList, SubStore subStore) {
        if (!CollectionUtils.isEmpty(balanceDetailList) && subStore != null) {
            var userId = balanceDetailList.stream()
                    .filter(profit -> !profit.isPresent() && !profit.getUserId().equals(subStore.getUserId()))
                    .map(BalanceDetail::getUserId)
                    .findAny()
                    .orElse(null);
            if (userId != null) {
                return serviceProviderCache.findServiceProviderByUserIdAndShopId(userId, balanceDetailList.get(0).getSourceId());
            }
        }

        if (subStore != null) {
            var userLevelView = serviceProviderCache.findByShopIdAndUserId(subStore.getShopId(), subStore.getUserId()).orElse(null);
            if (userLevelView != null) {
                return serviceProviderCache.findServiceProviderByUserIdAndShopId(userLevelView.getSupperUserId(), subStore.getShopId());
            }
        }

        return null;
    }

    private OrderRoleSnapshot getTargetRoleSnapshot(List<OrderRoleSnapshot> snapshots, SubStoreUserIdentityEnum role) {
        if (CollectionUtils.isEmpty(snapshots) || role == null) {
            return null;
        }

        return snapshots.stream().filter(entity -> role.getCode().equals(entity.getUserRole())).findAny().orElse(null);
    }

    private Map<Long, ServiceProvider> findServiceProviderMap(Map<Long, List<OrderRoleSnapshot>> snapshots) {
        if (CollectionUtils.isEmpty(snapshots)) {
            return Collections.emptyMap();
        }

        var serviceProviders = snapshots.values().stream()
                .filter(list -> !CollectionUtils.isEmpty(list))
                .flatMap(Collection::stream)
                .filter(entry -> SubStoreUserIdentityEnum.SERVICE_PROVIDER.getCode().equals(entry.getUserRole()))
                .toList();
        if (CollectionUtils.isEmpty(serviceProviders)) {
            return Collections.emptyMap();
        }

        // key = 服务商的userId
        var map = serviceProviderCache.findMapByUserIds(serviceProviders.stream().map(OrderRoleSnapshot::getUserId).toList(),
                serviceProviders.get(0).getShopId());
        if (CollectionUtils.isEmpty(map)) {
            return Collections.emptyMap();
        }

        return serviceProviders.stream().collect(HashMap::new,
                (resultMap, snapshot) -> resultMap.put(snapshot.getShopOrderId(), map.get(snapshot.getUserId())), HashMap::putAll);
    }

    private Map<Long, SubStoreTStoreGuider> findGuiderMap(List<OrderGroupViewObject> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyMap();
        }

        List<Long> guiderIdUserIs = orderList.stream()
                .map(entry -> entry.getShopOrder().getRefererId())
                .filter(Objects::nonNull)
                .toList();
        var guiderMap = subStoreTStoreGuiderReadService.findGuiderMap(guiderIdUserIs,
                orderList.get(0).getShopOrder().getShopId());
        if (CollectionUtils.isEmpty(guiderMap)) {
            return Collections.emptyMap();
        }

        return orderList.stream().map(OrderGroup::getShopOrder).collect(HashMap::new,
                (map, shopOrder) -> map.put(shopOrder.getId(), guiderMap.get(shopOrder.getRefererId())), HashMap::putAll);
    }

    private Map<Long, SubStore> findSubStoreMap(List<OrderGroupViewObject> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyMap();
        }

        List<Long> subStoreIds = orderList.stream()
                .map(entry -> stringToLong(entry.getShopOrder().getOutShopId()))
                .filter(Objects::nonNull)
                .toList();
        var subStoreMap = subStoreReadService.findMapByIds(subStoreIds);
        if (CollectionUtils.isEmpty(subStoreMap)) {
            return Collections.emptyMap();
        }

        return orderList.stream().map(OrderGroup::getShopOrder).collect(HashMap::new,
                (map, shopOrder) -> map.put(shopOrder.getId(), getSubStore(shopOrder.getOutShopId(), subStoreMap)), HashMap::putAll);
    }

    private SubStore getSubStore(String id, Map<Long, SubStore> map) {
        try {
            return map.get(stringToLong(id));
        } catch (Exception ex) {
            // ignore
            return null;
        }
    }

    private Long stringToLong(String string) {
        try {
            return Long.parseLong(string);
        } catch (Exception ex) {
            // ignore
            return null;
        }
    }
}
