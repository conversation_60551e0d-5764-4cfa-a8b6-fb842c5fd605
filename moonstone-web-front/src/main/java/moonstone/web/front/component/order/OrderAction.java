/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.front.component.order;

import io.terminus.common.exception.JsonResponseException;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.api.FlowPicker;
import moonstone.order.dto.fsm.Flow;
import moonstone.order.dto.fsm.OrderOperation;
import moonstone.order.model.OrderBase;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-11
 */
@Slf4j
@Component
public class OrderAction {

    protected final FlowPicker flowPicker;

    @Autowired
    public OrderAction(FlowPicker flowPicker) {
        this.flowPicker = flowPicker;
    }

    /**
     * 根据当前状态和操作, 获取下一个状态
     * @param order    (子)订单
     * @param orderLevel    订单级别
     * @param orderOperation   订单操作
     * @return   下一个状态
     */
    protected Integer target(OrderBase order, OrderLevel orderLevel, OrderOperation orderOperation){
        Flow flow;
        Integer source;
        switch (orderLevel){
            case SHOP:
                ShopOrder shopOrder = (ShopOrder)order;
                source = shopOrder.getStatus();
                break;
            case SKU:
                SkuOrder skuOrder = (SkuOrder)order;
                source = skuOrder.getStatus();
                break;
            default:
                log.error("unknown orderLevel:{}", orderLevel);
                throw new JsonResponseException("parameter.illegal");

        }
        flow = flowPicker.pick(order, orderLevel);
        if(flow.operationAllowed(source, orderOperation)){
            return flow.target(source, orderOperation);
        }else{
            log.error("{} is not allowed on status({}) of flow(name={})",
                    orderOperation, source, flow.getName());
            throw new JsonResponseException("operation.not.allowed");
        }
    }
}
