package moonstone.web.front.component.item;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.common.model.BaseUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.constants.SalePattern;
import moonstone.common.constants.ShopExtra;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.MatrixCalculator;
import moonstone.common.utils.NumberUtil;
import moonstone.user.model.StoreProxy;
import moonstone.user.service.StoreProxyReadService;
import moonstone.web.core.component.intermediate.IntermediateInfoComponent;
import moonstone.web.core.third.ThirdIntermediateInfo;
import moonstone.web.front.item.search.vo.SearchItemProfitView;
import moonstone.web.front.item.vo.ViewedItemForLevelDistribution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 负责打包出利润, 由于原利润逻辑已经陷入混乱 请尽可能不要修改
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SearchItemProfitViewHelper {
    @Autowired
    private StoreProxyReadService storeProxyReadService;
    @Autowired
    private ThirdIntermediateInfo thirdIntermediateInfo;
    @Autowired
    ShopCacheHolder shopCacheHolder;

    @Autowired
    private IntermediateInfoComponent intermediateInfoComponent;

    MatrixCalculator<BigDecimal> matrixCalculator = new MatrixCalculator<>(BigDecimal::new);
    private final LoadingCache<UserIdAndShopId, StoreProxy> storeProxyLoadingCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(10000)
            .build(userIdAndShopId -> storeProxyReadService.findByShopIdAndUserId(userIdAndShopId.shopId, userIdAndShopId.userId).orElse(Optional.empty()).orElse(null));

    /**
     * 设置可以分享出去能赚取的利润
     *
     * @param viewedItem 商品显示详情
     * @param user       用户数据
     */
    public void setViewedItemProfit(ViewedItemForLevelDistribution viewedItem, BaseUser user) {
        if (Objects.isNull(user)) {
            return;
        }
        viewedItem.setProfit(packProfitView(viewedItem.getItem().getId(), viewedItem.getItem().getShopId().toString(), viewedItem.getItem().getHighPrice(), user));
    }

    @AllArgsConstructor
    private static class UserIdAndShopId {
        Long userId;
        Long shopId;
    }

    /**
     * 设置佣金率
     *
     * @param shopId 店铺Id 对应商品的店铺
     * @param itemId 商品Id
     * @param price  价格
     *               **warn** 不要随便修改, 此处极度危险
     */
    public SearchItemProfitView packProfitView(Long itemId, String shopId, Integer price, BaseUser user) {
        String salePattern = Optional.ofNullable(shopCacheHolder.findShopById(Long.parseLong(shopId)).getExtra())
                .orElse(Collections.emptyMap()).get(ShopExtra.SalesPattern.getCode());
        if (!Objects.equals(salePattern, SalePattern.StoreProxy.getCode())){
            return new SearchItemProfitView();
        }
        if (SalePattern.WeShop.getCode().equals(salePattern)){
            return new SearchItemProfitView();
        }
        //设置分销的比率 目前显示--firstRate
        //objectList的json 数据为 firstRate 佣金  和 type=0 固定金额   type=1 比率
        List<JSONObject> objectList = intermediateInfoComponent.findRateByShopIdAndUserId(itemId, Long.valueOf(shopId));
        if (objectList == null || objectList.isEmpty()) {
            return new SearchItemProfitView();
        }
        //  佣金计算矩阵
        String matrixFee = null;
        //log.debug("{} profitResult:{}", LogUtil.getClassMethodName(), objectList.get(0));
        if (!ObjectUtils.isEmpty(objectList.get(0).getString("profitMatrix"))) {
            StoreProxy storeProxy = storeProxyLoadingCache.get(new UserIdAndShopId(user.getId(), NumberUtil.parseNumber(shopId, Long.TYPE).orElse(null)));
            if (storeProxy != null) {
                List<BigDecimal> profitPrice = new ArrayList<>();
                for (int i = 1; i < storeProxy.getLevel(); i++) {
                    profitPrice.add(null);
                }
                profitPrice.add(BigDecimal.valueOf(price));
                matrixFee = matrixCalculator.calWithMatrixByLine(objectList.get(0).getString("profitMatrix")
                        , profitPrice.toArray(new BigDecimal[]{}))
                        .map(bd -> bd.setScale(2, RoundingMode.DOWN))
                        .map(BigDecimal::toString).orElse(null);
                log.debug("{} profitPrice:{} matrixFee:{}", LogUtil.getClassMethodName(), JSON.toJSONString(profitPrice), matrixFee);
            }
        }
        if (objectList.get(0).containsKey("type") && Objects.equals(objectList.get(0).get("type"), 0)) {
            if (!ObjectUtils.isEmpty(matrixFee)) {
                return new SearchItemProfitView(matrixFee, matrixFee + "元");
            }
            BigDecimal firstRate = new BigDecimal(objectList.get(0).get("firstRate").toString());
            String fee = firstRate.divide(new BigDecimal("100"), 2, RoundingMode.DOWN) + "";
            return new SearchItemProfitView(fee, fee + "元");
        } else {
            if (!ObjectUtils.isEmpty(matrixFee)) {
                return new SearchItemProfitView(matrixFee, matrixFee + "元");
            }
            BigDecimal prices = new BigDecimal(String.valueOf(price));
            BigDecimal firstRate = new BigDecimal(objectList.get(0).get("firstRate").toString());
            BigDecimal fee = prices.multiply(firstRate.divide(new BigDecimal("1000000"), 6, RoundingMode.DOWN)).setScale(2, BigDecimal.ROUND_HALF_UP);
            String firstRateStr = firstRate.divide(new BigDecimal("100"), 2, RoundingMode.DOWN) + "%";
            return new SearchItemProfitView(fee.compareTo(BigDecimal.ZERO) == 0 ? "0" : fee.toString(), firstRateStr);
        }
    }
}
