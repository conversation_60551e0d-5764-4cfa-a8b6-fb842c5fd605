package moonstone.web.front.component.promotion;

import com.google.common.base.Predicate;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.PromotionCacher;
import moonstone.cache.PromotionToolCacher;
import moonstone.cache.SkuCacheHolder;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.order.dto.RichSku;
import moonstone.promotion.api.Behavior;
import moonstone.promotion.api.PromotionPicker;
import moonstone.promotion.api.PromotionTool;
import moonstone.promotion.api.SkuOrderBehavior;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.web.front.promotions.dto.SkuPromotion;
import moonstone.web.front.promotions.dto.SkuPromotionPrice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Author:cp
 * Created on 13/02/2017.
 */
@Component
public class ItemPromotionCenter {

    private final ItemCacheHolder itemCacheHolder;

    private final SkuCacheHolder skuCacheHolder;

    private final PromotionCacher promotionCacher;

    private final PromotionToolCacher promotionToolCacher;

    private final PromotionPicker promotionPicker;

    @Autowired
    public ItemPromotionCenter(ItemCacheHolder itemCacheHolder,
                               SkuCacheHolder skuCacheHolder,
                               PromotionCacher promotionCacher,
                               PromotionToolCacher promotionToolCacher,
                               PromotionPicker promotionPicker) {
        this.itemCacheHolder = itemCacheHolder;
        this.skuCacheHolder = skuCacheHolder;
        this.promotionCacher = promotionCacher;
        this.promotionToolCacher = promotionToolCacher;
        this.promotionPicker = promotionPicker;
    }

    /**
     * 查询商品的营销活动(给商品详情页用)
     *
     * @param itemId 商品id
     * @return 营销活动信息
     */
    public List<SkuPromotion> findAvailablePromotionByItemId(Long itemId) {
        Item item = itemCacheHolder.findItemById(itemId);
        List<Promotion> promotions = promotionCacher.findOngoingPromotionOf(item.getShopId());

        if (CollectionUtils.isEmpty(promotions)) {
            return Collections.EMPTY_LIST;
        }

        List<Sku> skus = skuCacheHolder.findSkusByItemId(itemId);

        List<SkuPromotion> skuPromotions = Lists.newArrayList();
        for (Promotion promotion : promotions) {
            //过滤用户营销
            if (PromotionType.isUserPromotion(promotion.getType())) {
                continue;
            }
            final PromotionTool<? extends Behavior> promotionTool = promotionToolCacher.findByPromotionDefId(promotion.getPromotionDefId());
            final Behavior behavior = promotionTool.behavior();

            //跳过非商品级别的营销
            if (!(behavior instanceof SkuOrderBehavior)) {
                continue;
            }

            List<SkuPromotion.SkuWithPromotion> skuWithPromotions = Lists.newArrayList();
            for (Sku sku : skus) {
                RichSku richSku = makeRichSku(sku, item);
                if (promotionTool.skuScope().apply(richSku, null, promotion.getSkuScopeParams())) {
                    SkuOrderBehavior skuOrderBehavior = (SkuOrderBehavior) behavior;
                    skuOrderBehavior.execute(richSku, null, promotion.getBehaviorParams());
                    Map<String, Object> promotionInfo = skuOrderBehavior.extractSkuOrderPromotionInfo(richSku, null, promotion.getConditionParams(), promotion.getBehaviorParams());

                    SkuPromotion.SkuWithPromotion skuWithPromotion = new SkuPromotion.SkuWithPromotion();
                    skuWithPromotion.setSku(sku);
                    skuWithPromotion.setPromotionPrice(richSku.getSkuPromotionPrice());
                    skuWithPromotion.setPromotionInfo(promotionInfo);
                    skuWithPromotions.add(skuWithPromotion);
                }
            }

            if (!CollectionUtils.isEmpty(skuWithPromotions)) {
                SkuPromotion skuPromotion = new SkuPromotion();
                skuPromotion.setPromotion(promotion);
                skuPromotion.setSkuWithPromotions(skuWithPromotions);
                skuPromotions.add(skuPromotion);
            }
        }

        return skuPromotions;
    }

    /**
     * 查询指定商品下每个sku采用默认营销时的价格(给商品详情用)
     *
     * @param itemId 商品id
     * @return 采用默认营销时sku的价格
     */
    public List<SkuPromotionPrice> findSkuPriceWithDefaultPromotion(Long itemId) {
        Item item = itemCacheHolder.findItemById(itemId);
        List<Promotion> promotions = promotionCacher.findOngoingPromotionOf(item.getShopId());
        if (CollectionUtils.isEmpty(promotions)) {
            return Collections.EMPTY_LIST;
        }

        //过滤掉用户营销
        filterUserPromotion(promotions);

        List<SkuPromotionPrice> skuPromotionPrices = Lists.newArrayList();
        List<Sku> skus = skuCacheHolder.findSkusByItemId(itemId);
        for (Sku sku : skus) {
            RichSku richSku = makeRichSku(sku, item);

            List<Promotion> skuPromotions = Lists.newArrayList();
            for (Promotion promotion : promotions) {
                final PromotionTool<? extends Behavior> promotionTool = promotionToolCacher.findByPromotionDefId(promotion.getPromotionDefId());

                //跳过非商品级别的营销
                if (!(promotionTool.behavior() instanceof SkuOrderBehavior)) {
                    continue;
                }

                if (promotionTool.skuScope().apply(richSku, null, promotion.getSkuScopeParams())) {
                    skuPromotions.add(promotion);
                }
            }
            Long defaultPromotionId = promotionPicker.defaultSkuPromoiton(skuPromotions);
            if (defaultPromotionId != null) {
                Promotion promotion = promotionCacher.findByPromotionId(defaultPromotionId);

                final PromotionTool<? extends Behavior> promotionTool = promotionToolCacher.findByPromotionDefId(promotion.getPromotionDefId());
                final Behavior behavior = promotionTool.behavior();
                SkuOrderBehavior skuOrderBehavior = (SkuOrderBehavior) behavior;
                skuOrderBehavior.execute(richSku, null, promotion.getBehaviorParams());

                SkuPromotionPrice skuPromotionPrice = new SkuPromotionPrice();
                skuPromotionPrice.setSkuId(sku.getId());
                skuPromotionPrice.setPromotionPrice(richSku.getSkuPromotionPrice());
                skuPromotionPrices.add(skuPromotionPrice);
            }
        }
        return skuPromotionPrices;
    }


    private RichSku makeRichSku(Sku sku, Item item) {
        RichSku richSku = new RichSku();
        richSku.setSku(sku);
        richSku.setItem(item);
        richSku.setQuantity(1);
        return richSku;
    }

    private void filterUserPromotion(List<Promotion> promotions) {
        Iterables.removeIf(promotions, new Predicate<Promotion>() {
            @Override
            public boolean apply(Promotion promotion) {
                return PromotionType.isUserPromotion(promotion.getType());
            }
        });
    }

}
