//package moonstone.web.front.component;
//
//
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import io.vertx.core.Vertx;
//import io.vertx.core.json.JsonObject;
//import lombok.extern.slf4j.Slf4j;
//import moonstone.common.StartEventTag;
//import moonstone.common.utils.XxlJobDesc;
//import moonstone.order.dto.PaymentCriteria;
//import moonstone.order.dto.fsm.OrderStatus;
//import moonstone.order.model.Payment;
//import moonstone.order.service.PaymentReadService;
//import moonstone.web.core.component.order.PaymentLogic;
//import moonstone.web.core.constants.EnvironmentConfig;
//import org.redisson.api.RedissonClient;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.event.ContextRefreshedEvent;
//import org.springframework.context.event.EventListener;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.concurrent.locks.Lock;
//
///**
// * {@link RefundPaymentPaidAfterClosedJob}
// * <AUTHOR>
// */
//@Component
//@Slf4j
//public class PaymentAutoRefundClosedOrderJob {
//    @Resource
//    private PaymentReadService paymentReadService;
//    @Autowired
//    private PaymentLogic paymentLogic;
//
//    @Resource
//    private RedissonClient redissonClient;
//
////    @Autowired
////    Vertx vertx;
////    @Autowired
////    EnvironmentConfig environmentConfig;
////
////    @StartEventTag(cache = false, desc = "创建消费者 创建内存定时任务")
////    @EventListener(ContextRefreshedEvent.class)
////    public void listenOnVertx() {
////        if (environmentConfig.getEnv().equalsIgnoreCase("ONLINE")) {
////            // 注册一个本地事件消费者，监听"payment::refund"地址
////            vertx.eventBus().localConsumer("payment::refund",
////                    e -> vertx.executeBlocking(p -> queryAndRefund()));
////            // 设置一个60秒的定时器，每分钟向"payment::refund"地址发送消息
////            vertx.setPeriodic(60 * 1000, t -> {
////                log.info("listenOnVertx vertx.setPeriodic");
////                vertx.eventBus().send("payment::refund", new JsonObject());
////            });
////        }
////    }
//
//}
