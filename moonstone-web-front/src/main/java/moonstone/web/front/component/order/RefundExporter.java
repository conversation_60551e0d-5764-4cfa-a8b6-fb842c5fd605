package moonstone.web.front.component.order;

import moonstone.order.api.RefundExpressService;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.RefundExpressInfo;
import moonstone.order.model.result.OrderShipmentInfoDO;
import moonstone.order.service.*;
import moonstone.user.enums.DataExportTaskTypeEnum;
import moonstone.web.core.util.BaseExporter;
import moonstone.web.front.component.order.convert.RefundExportConvertor;
import moonstone.web.front.component.order.dto.RefundExportParameter;
import moonstone.web.front.component.order.view.RefundExcelExportView;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商家后台 - 订单管理 - 退货款中心 - 导出
 */
@Component
public class RefundExporter extends BaseExporter<RefundExcelExportView, RefundExportParameter> {

    @Resource
    private RefundReadService refundReadService;

    @Resource
    private RefundExpressService refundExpressService;

    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private ReceiverInfoReadService receiverInfoReadService;

    @Resource
    private PaymentReadService paymentReadService;

    @Resource
    private ShipmentReadService shipmentReadService;

    @Resource
    private OrderRoleSnapshotReadService orderRoleSnapshotReadService;

    @Resource
    private RefundExportConvertor refundExportConvertor;

    @Override
    protected DataExportTaskTypeEnum getCurrentTaskType() {
        return DataExportTaskTypeEnum.REFUND_CENTER;
    }

    @Override
    protected List<RefundExcelExportView> findPageDataList(RefundExportParameter queryParameter, int pageNo, int pageSize) {
        if (queryParameter == null) {
            return Collections.emptyList();
        }

        // 查询
        var list = findSourceList(queryParameter, pageNo, pageSize);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        // 构造
        return buildResultList(list);
    }

    private List<RefundExcelExportView> buildResultList(List<RefundExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }

        // 退款单信息
        appendRefundInfo(resultList);

        // 退款单对应的物流信息 （早没用了吧）
        appendRefundExpressInfo(resultList);

        // 子订单信息
        appendSkuOrderInfo(resultList);

        // 主订单信息
        appendShopOrderInfo(resultList);

        // 收货人信息
        appendReceiverInfo(resultList);

        // 支付信息
        appendPaymentInfo(resultList);

        // 发货信息
        appendShipmentInfo(resultList);

        // 角色信息
        appendRoleInfo(resultList);

        return resultList;
    }

    /**
     * 填充角色信息
     *
     * @param resultList
     */
    private void appendRoleInfo(List<RefundExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        // 订单的角色快照信息
        var snapshotMap = orderRoleSnapshotReadService.findMapByShopOrderIds(
                resultList.stream().map(RefundExcelExportView::getShopOrderId).toList(), OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER);

        // 填充
        resultList.forEach(target -> refundExportConvertor.append(target, snapshotMap.get(target.getShopOrderId())));
    }

    /**
     * 填充发货信息
     *
     * @param resultList
     */
    private void appendShipmentInfo(List<RefundExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        // 发货信息 (key = shop_order_id)
        Map<Long, OrderShipmentInfoDO> shipmentMapByShopOrder = shipmentReadService.getShipmentMap(
                resultList.stream().map(RefundExcelExportView::getShopOrderId).collect(Collectors.toList()), OrderLevel.SHOP);

        // 发货信息( key = sku_order_id)
        Map<Long, OrderShipmentInfoDO> shipmentMapBySkuOrder = shipmentReadService.getShipmentMap(
                resultList.stream().map(RefundExcelExportView::getSkuOrderId).collect(Collectors.toList()), OrderLevel.SKU);

        // 填充
        resultList.forEach(target -> refundExportConvertor.append(target, shipmentMapByShopOrder.get(target.getShopOrderId()),
                shipmentMapBySkuOrder.get(target.getSkuOrderId())));
    }

    /**
     * 填充支付信息
     *
     * @param resultList
     */
    private void appendPaymentInfo(List<RefundExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        // 查询
        var paymentMap = paymentReadService.findMapByOrderIds(
                resultList.stream().map(RefundExcelExportView::getShopOrderId).toList(), OrderLevel.SHOP);
        if (CollectionUtils.isEmpty(paymentMap)) {
            return;
        }

        // 填充
        resultList.forEach(target -> refundExportConvertor.append(target, paymentMap.get(target.getShopOrderId())));
    }

    /**
     * 填充收货人信息
     *
     * @param resultList
     */
    private void appendReceiverInfo(List<RefundExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        // 查询
        var receiverMap = receiverInfoReadService.findMapByOrderIds(
                resultList.stream().map(RefundExcelExportView::getShopOrderId).toList(), OrderLevel.SHOP);
        if (CollectionUtils.isEmpty(receiverMap)) {
            return;
        }

        // 填充
        resultList.forEach(target -> refundExportConvertor.append(target, receiverMap.get(target.getShopOrderId())));
    }

    /**
     * 填充主订单信息
     *
     * @param resultList
     */
    private void appendShopOrderInfo(List<RefundExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        // 查询
        var orderMap = shopOrderReadService.getShopOrderMap(
                resultList.stream().map(RefundExcelExportView::getShopOrderId).toList());
        if (CollectionUtils.isEmpty(orderMap)) {
            return;
        }

        // 填充
        resultList.forEach(target -> refundExportConvertor.append(target, orderMap.get(target.getShopOrderId())));
    }

    /**
     * 填充子订单信息
     *
     * @param resultList
     */
    private void appendSkuOrderInfo(List<RefundExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        // 查询
        var orderMap = skuOrderReadService.findMapByIds(
                resultList.stream().map(RefundExcelExportView::getSkuOrderId).toList());
        if (CollectionUtils.isEmpty(orderMap)) {
            return;
        }

        // 填充
        resultList.forEach(target -> refundExportConvertor.append(target, orderMap.get(target.getSkuOrderId())));
    }

    /**
     * 填充退款单对应的物流信息
     *
     * @param resultList
     */
    private void appendRefundExpressInfo(List<RefundExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        // 查询
        var expressMap = findRefundExpressMap(
                resultList.stream().map(RefundExcelExportView::getRefundId).toList());
        if (CollectionUtils.isEmpty(expressMap)) {
            return;
        }

        // 填充
        resultList.forEach(target -> refundExportConvertor.append(target, expressMap.get(target.getRefundId())));
    }

    /**
     * 查询退款单对应的物流信息
     *
     * @param refundIds
     * @return
     */
    private Map<Long, RefundExpressInfo> findRefundExpressMap(List<Long> refundIds) {
        if (CollectionUtils.isEmpty(refundIds)) {
            return Collections.emptyMap();
        }

        var list = refundExpressService.findByRefundIds(refundIds).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(RefundExpressInfo::getRefundId, o -> o, (k1, k2) -> k1));
    }

    /**
     * 填充退款单相关信息
     *
     * @param resultList
     */
    private void appendRefundInfo(List<RefundExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        // 查询
        var refundMap = refundReadService.findMapByIds(
                resultList.stream().map(RefundExcelExportView::getRefundId).toList());
        if (CollectionUtils.isEmpty(refundMap)) {
            return;
        }

        // 填充
        resultList.forEach(target -> refundExportConvertor.append(target, refundMap.get(target.getRefundId())));
    }

    /**
     * 分页查询要导出的数据
     *
     * @param queryParameter
     * @param pageNo
     * @param pageSize
     * @return
     */
    private List<RefundExcelExportView> findSourceList(RefundExportParameter queryParameter, int pageNo, int pageSize) {
        var list = refundReadService.findForExport(
                refundExportConvertor.convert(queryParameter, pageNo, pageSize)).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().map(entry -> refundExportConvertor.convert(entry)).collect(Collectors.toList());
    }
}
