package moonstone.web.front.sales.vo;


import lombok.Data;

import java.util.List;

@Data
public class DataBoardShowVO {

    /**
     * 回显给前端的，告知日期类别（today, yesterday, month, optional）
     */
    private String type;


    /**
     * 数据总览
     */
    private TotalSalesShowVO totalSalesShowVO;

    /**
     * 服务商按GMV排名，暂定给前十
     */
    private List<ServiceProviderRankVO> serviceProviderGmvTop;

    /**
     * 服务商按单量排名，暂定给前十
     */
    private List<ServiceProviderRankVO> serviceProviderOrderTop;

    /**
     * 门店按GMV排名，暂定给前十
     */
    private List<SubStoreRankVO> subStoreGmvTop;

    /**
     * 门店按单量排名，暂定给前十
     */
    private List<SubStoreRankVO> subStoreOrderTop;

    /**
     * 商品按GMV排名，暂定给前十
     */
    private List<SkuRankVO> skuGmvTop;

    /**
     * 商品按单量排名，暂定给前十
     */
    private List<SkuRankVO> skuOrderTop;

    /**
     * 省份按GMV排名，暂定给前十
     */
    private List<ProvinceRankVO> provinceGmvTop;

    /**
     * 省份按单量排名，暂定给前十
     */
    private List<ProvinceRankVO> provinceOrderTop;

}
