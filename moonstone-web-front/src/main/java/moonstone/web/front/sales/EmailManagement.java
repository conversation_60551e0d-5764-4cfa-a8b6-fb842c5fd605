package moonstone.web.front.sales;


import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.api.APIResp;
import moonstone.common.enums.DataValidEnum;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.user.enums.EmailBusinessTypeEnum;
import moonstone.user.model.Email;
import moonstone.user.service.EmailReadService;
import moonstone.user.service.EmailWriteService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 邮件管理功能，对销量报表邮件接收名单的管理
 * Date: 2022/11/21
 * Author: yilang
 */


@RestController
@Slf4j
public class EmailManagement {

    @Resource
    private ShopCacheHolder shopCacheHolder;

    @Resource
    private EmailReadService emailReadService;

    @Resource
    private EmailWriteService emailWriteService;


    @RequestMapping(value = "/api/sales/email/show", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public APIResp<List<String>> showEmailList() {

        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            throw new JsonResponseException("user.not.login");
        }
        Long userId = UserUtil.getCurrentUser().getId();
        Long shopId = commonUser.getShopId();

        Response<List<Email>> emailListResp = emailReadService.findEmailsByShopId(shopId, EmailBusinessTypeEnum.DAILY_SALES_REPORT);
        if (!emailListResp.isSuccess()) {
            log.error("fail to find emailList, cause: {}", emailListResp.getError());
            throw new JsonResponseException(emailListResp.getError());
        }
        List<String> res = new ArrayList<>();
        for (Email salesEmail : emailListResp.getResult()) {
            res.add(salesEmail.getEmail());
        }

        return APIResp.ok(res);
    }

    @RequestMapping(value = "/api/sales/email/update", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public APIResp<List<String>> updateEmails(@RequestBody List<String> emailList) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (commonUser == null || commonUser.getShopId() == null) {
            throw new JsonResponseException("user.not.login");
        }
        Long userId = UserUtil.getCurrentUser().getId();
        Long shopId = commonUser.getShopId();

        List<Email> salesEmailList = new ArrayList<>();
        for (String email : emailList) {
            Email salesEmail = new Email();
            salesEmail.setEmail(email);
            salesEmail.setUserName(shopCacheHolder.findShopById(shopId).getUserName());
            salesEmail.setShopId(shopId);
            salesEmail.setBusinessType(EmailBusinessTypeEnum.DAILY_SALES_REPORT.getCode());
            salesEmail.setStatus(DataValidEnum.VALID.getCode());

            salesEmailList.add(salesEmail);
        }
        emailWriteService.updateEmails(salesEmailList, shopId, EmailBusinessTypeEnum.DAILY_SALES_REPORT);

        return APIResp.ok(emailList);
    }


}
