package moonstone.web.front.dev;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.model.IsPersistAble;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.related.OrderRelated;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.order.service.BalanceDetailWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.front.dev.dto.OrderProfitModifyDTO;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@RestController
public class OrderProfitModifyController {

    @Resource
    private BalanceDetailReadService balanceDetailReadService;

    @Resource
    private BalanceDetailWriteService balanceDetailWriteService;

    @Resource
    private SubStoreCache subStoreCache;

    @Resource
    private ServiceProviderCache serviceProviderCache;

    @Resource
    private OrderProfitModifyController self;

    @Resource
    private ShopOrderReadService shopOrderReadService;

    private List<Integer> targetOrderStatus = List.of(OrderStatus.PAID.getValue(), OrderStatus.SHIPPED.getValue());

    @PostMapping("/api/wtf/orderProfit/modifyByExcel")
    public APIResp<Integer> modifyByExcel(@RequestParam(value = "file") MultipartFile multipartFile,
                                          @RequestParam("shopId") Long shopId) {
        try {
            AtomicInteger count = new AtomicInteger();

            EasyExcel.read(multipartFile.getInputStream(), OrderProfitModifyDTO.class, new PageReadListener<OrderProfitModifyDTO>(dataList -> {
                for (var data : dataList) {
                    try {
                        if (self.modifyProfit(shopId, data)) {
                            count.getAndIncrement();
                        }
                    } catch (Exception ex) {
                        log.error("OrderProfitModifyController.modifyByExcel, data row failed, data={}", JSON.toJSONString(data), ex);
                    }
                }
            })).sheet().doRead();

            return APIResp.ok(count.get());
        } catch (Exception ex) {
            log.error("OrderProfitModifyController.modifyByExcel error ", ex);
            return APIResp.error(ex.getMessage());
        }
    }

    @PostMapping("/api/wtf/orderProfit/modifyStatusByExcel")
    public APIResp<Integer> modifyStatusByExcel(@RequestParam(value = "file") MultipartFile multipartFile,
                                                @RequestParam("shopId") Long shopId) {
        try {
            AtomicInteger count = new AtomicInteger();

            EasyExcel.read(multipartFile.getInputStream(), OrderProfitModifyDTO.class, new PageReadListener<OrderProfitModifyDTO>(dataList -> {
                for (var data : dataList) {
                    try {
                        if (self.modifyStatus(shopId, data)) {
                            count.getAndIncrement();
                        }
                    } catch (Exception ex) {
                        log.error("OrderProfitModifyController.modifyByExcel, data row failed, data={}", JSON.toJSONString(data), ex);
                    }
                }
            })).sheet().doRead();

            return APIResp.ok(count.get());
        } catch (Exception ex) {
            log.error("OrderProfitModifyController.modifyByExcel error ", ex);
            return APIResp.error(ex.getMessage());
        }
    }

    private boolean modifyStatus(Long shopId, OrderProfitModifyDTO modifyDTO) {
        if (modifyDTO == null || modifyDTO.getOrderId() == null ||
                (modifyDTO.getServiceProviderProfit() == null && modifyDTO.getSubStoreProfit() == null)) {
            log.error("OrderProfitModifyController.modifyProfit, 入参不完整, modifyDTO={}", JSON.toJSONString(modifyDTO));
            return false;
        }

        var list = balanceDetailReadService.findByRelatedIdAndSourceId(modifyDTO.getOrderId(), shopId).getResult();
        if (CollectionUtils.isEmpty(list)) {
            log.error("OrderProfitModifyController.modifyProfit, shopId={}, orderId={} has no profit data", shopId, modifyDTO.getOrderId());
            return false;
        }

        for (var balanceDetail : list) {
            if (balanceDetail.getStatus() == 1 && balanceDetail.getType() == 1 && balanceDetail.getChangeFee() > 0) {
                var updateObject = new BalanceDetail();
                updateObject.setId(balanceDetail.getId());
                updateObject.setStatus(updateObject.getStatus() | BalanceDetail.maskBit.OrderRelated.getValue() |
                        OrderRelated.orderRelatedMask.ShopOrder.getValue() | BalanceDetail.SourceMark.Reach.getBitMark() |
                        IsPersistAble.maskBit.PersistAble.getValue());

                balanceDetailWriteService.update(updateObject);
            }
        }

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean modifyProfit(Long shopId, OrderProfitModifyDTO modifyDTO) {
        if (modifyDTO == null || modifyDTO.getOrderId() == null ||
                (modifyDTO.getServiceProviderProfit() == null && modifyDTO.getSubStoreProfit() == null)) {
            log.error("OrderProfitModifyController.modifyProfit, 入参不完整, modifyDTO={}", JSON.toJSONString(modifyDTO));
            return false;
        }

        var list = balanceDetailReadService.findByRelatedIdAndSourceId(modifyDTO.getOrderId(), shopId).getResult();
        if (CollectionUtils.isEmpty(list)) {
            log.error("OrderProfitModifyController.modifyProfit, shopId={}, orderId={} has no profit data", shopId, modifyDTO.getOrderId());
            return false;
        }
        if (list.stream().anyMatch(BalanceDetail::isPresent)) {
            log.error("OrderProfitModifyController.modifyProfit, shopId={}, orderId={} has present profit", shopId, modifyDTO.getOrderId());
            return false;
        }

        var shopOrder = shopOrderReadService.findById(modifyDTO.getOrderId()).getResult();
        if (shopOrder == null) {
            log.error("OrderProfitModifyController.modifyProfit, shopOrderId={}, 订单数据不存在", modifyDTO.getOrderId());
            return false;
        }
        if (!targetOrderStatus.contains(shopOrder.getStatus())) {
            log.error("OrderProfitModifyController.modifyProfit, shopOrderId={}, 订单状态已发生改变", modifyDTO.getOrderId());
            return false;
        }

        for (var balanceDetail : list) {
            if (balanceDetail.getChangeFee() <= 0L) {
                continue;
            }

            if (subStoreCache.findByShopIdAndUserId(shopId, balanceDetail.getUserId()).isPresent()) {
                var newChangeFee = modifyDTO.getSubStoreProfit().multiply(new BigDecimal(100)).longValue();

                doUpdate(balanceDetail, newChangeFee);
            } else if (serviceProviderCache.findByShopIdAndUserId(shopId, balanceDetail.getUserId()).isPresent() &&
                    !balanceDetail.isLinked()) {
                var newChangeFee = modifyDTO.getServiceProviderProfit().multiply(new BigDecimal(100)).longValue();

                doUpdate(balanceDetail, newChangeFee);
            }
        }

        return true;
    }

    private void doUpdate(BalanceDetail balanceDetail, long newChangeFee) {
        var difference = newChangeFee - balanceDetail.getChangeFee();

        var updateObject = new BalanceDetail();
        updateObject.setId(balanceDetail.getId());
        updateObject.setChangeFee(newChangeFee);
        updateObject.setStatus(null);

        if (!balanceDetailWriteService.update(updateObject).getResult()) {
            throw new RuntimeException("利润记录更新失败");
        }
        if (!balanceDetailWriteService.increaseCash(balanceDetail.getUserId(), balanceDetail.getSourceId(), difference, false).getResult()) {
            throw new RuntimeException("余额更新失败");
        }
    }
}
