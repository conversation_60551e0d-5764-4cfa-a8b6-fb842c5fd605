package moonstone.web.front.address;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import io.terminus.ip.IPLibrary;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.ParanaDefaultThreadFactory;
import moonstone.user.area.model.Area;
import moonstone.user.area.service.AreaReadService;
import moonstone.web.front.address.view.CityView;
import moonstone.web.front.address.view.CountyView;
import moonstone.web.front.address.view.ProvinceView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * Author:cp
 * Created on 6/21/16.
 */
@SuppressWarnings("UnstableApiUsage")
@RestController
@Slf4j
@RequestMapping("/api/address")
public class Addresses {

    @Autowired(required = false)
    private IPLibrary ipLibrary;

    @RpcConsumer
    private AreaReadService areaService;

    private static final ExecutorService executor = Executors.newFixedThreadPool(8, new ParanaDefaultThreadFactory("addresses"));

    LoadingCache<Boolean, List<ProvinceView>> addressCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .build(new CacheLoader<Boolean, List<ProvinceView>>() {
                @Override
                public List<ProvinceView> load(@NotNull Boolean ignore) {
                    List<Future<Boolean>> jobs = new ArrayList<>();
                    List<ProvinceView> provinceViews = new ArrayList<>();
                    for (Area area : areaService.provinces().getResult()) {
                        ProvinceView view = new ProvinceView();
                        BeanUtils.copyProperties(area, view);
                        provinceViews.add(view);
                        List<CityView> cityViewList = new ArrayList<>();
                        view.setCityList(cityViewList);
                        jobs.add(executor.submit(() -> {
                            for (Area city : areaService.citiesOf(area.getId()).getResult()) {
                                CityView cityView = new CityView();
                                BeanUtils.copyProperties(city, cityView);
                                cityViewList.add(cityView);
                                List<CountyView> countyViewList = new ArrayList<>();
                                cityView.setCountyList(countyViewList);
                                for (Area regions : areaService.regionsOf(city.getId()).getResult()) {
                                    CountyView countyView = new CountyView();
                                    BeanUtils.copyProperties(regions, countyView);
                                    countyViewList.add(countyView);
                                }
                            }
                            return true;
                        }));
                    }
                    try {
                        for (Future<Boolean> job : jobs) {
                            job.get();
                        }
                    } catch (Exception ex) {
                        log.error("{} fail to load address", LogUtil.getClassMethodName(), ex);
                    }

                    return provinceViews;
                }
            });

    @DeleteMapping("/all")
    public boolean deleteCache() {
        addressCache.invalidate(true);
        return true;
    }

    @GetMapping("/all/.version")
    public String getAllAddressVersion() {
        return DigestUtils.md5DigestAsHex(JSON.toJSONString(addressCache.get(true)).getBytes());
    }

    @GetMapping("/all")
    public List<ProvinceView> getAllAddress() {
        return addressCache.get(true);
    }

    @RequestMapping(value = "/ip", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public String ip(HttpServletRequest request) {
        String ip = request.getRemoteAddr();
        log.debug("[op:ip] ip: {}", ip);
        return ip;
    }

    @RequestMapping(value = "/ip-info", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<List<Area>> ipInfo(@RequestParam(required = false) String ip, HttpServletRequest request) {
        String ipAddress = ip;
        if (!StringUtils.hasText(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }

        if (ipLibrary == null) {
            return Response.ok(Collections.emptyList());
        }

        try {
            String[] info = ipLibrary.ipInfo(ipAddress);
            if (info == null || info[0] == null) {
                return Response.ok(Collections.emptyList());
            }
            List<String> names = Splitter.on(CharMatcher.anyOf("省市区")).omitEmptyStrings().splitToList(info[0]);
            return Response.ok(convertToArea(names));
        } catch (Exception e) {
            log.warn("fail to load ip info, cause:{}", e.getMessage());
            return Response.ok(Collections.emptyList());
        }

    }

    private List<Area> convertToArea(List<String> names) {
        List<Area> areas = getAreas(names);

        if (CollectionUtils.isEmpty(areas) || areas.contains(null)) {
            return getAreas(Lists.newArrayList("浙江省", "杭州市"));
        }

        return areas;
    }

    private List<Area> getAreas(List<String> names) {
        List<Area> areas = Lists.newArrayListWithCapacity(names.size());

        for (String name : names) {
            Response<Area> res = areaService.findLikeName(name);
            if (res.isSuccess()) {
                areas.add(res.getResult());
            }
        }
        return areas;
    }

}