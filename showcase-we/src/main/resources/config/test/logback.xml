<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false" scanPeriod="60 seconds">
    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
    <springProperty scope="context" name="app.name" source="application.name"/>

    <!--	<logger name="io.terminus" level="debug" />-->
    <!--	&lt;!&ndash; Root Logger &ndash;&gt;-->
    <!--	<root level="info">-->
    <!--		<appender-ref ref="STDOUT" />-->
    <!--		<appender-ref ref="FILE" />-->
    <!--		<appender-ref ref="LOG_STASH" />-->
    <!--	</root>-->

    <springProperty scope="context" name="pod.name" source="POD_NAME"/>
    <property name="log.level" value="info" />
    <property name="CHARSET" value="utf-8"/>

    <!-- 控制台彩色日志格式 -->
    <property name="CONSOLE_LOG" value="${CONSOLE_LOG:-%clr(%X{trace.id}){blue} %clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}" />

    <!-- 简化输出过程：不使用颜色解析器 -->
    <property name="FILE_LOG" value="${FILE_LOG:-%X{trace.id} %d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} [%15.15t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}" />

    <!-- 导入spring的默认配置：含颜色解析器，异常解析器等-->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <!--测试开启debug及以上的日志-->
        <encoder>
            <pattern>${CONSOLE_LOG}</pattern>
            <charset>${CHARSET}</charset>
        </encoder>
    </appender>

    <!-- 按照每天生成日志文件 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名 注意: 预发中暂时合并为写一个文件 在多pod时会存在并发写情况 会存在问题-->
            <FileNamePattern>/nfs/${app.name}/%d{yyyy-MM-dd}/showcase-we.log.gz</FileNamePattern>
            <!--日志文件保留天数-->
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>${FILE_LOG}</pattern>
            <charset>${CHARSET}</charset>
        </encoder>
    </appender>

    <!--	mysql日志 预发先开启mysql日志-->
    <!--	<logger name="p6spy" level="error"/>-->
    <logger name="com.alibaba.nacos" level="warn"/>
    <logger name="com.alibaba.nacos.client.identify" level="error"/>
    <logger name="com.xxl.job" level="warn"/>
    <logger name="org.redisson" level="error"/>
    <logger name="ch.qos.logback.core" level="error"/>
    <logger name="ch.qos.logback.classic" level="warn"/>
    <logger name="ReconfigureOnChangeTask" level="warn"/>

    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>