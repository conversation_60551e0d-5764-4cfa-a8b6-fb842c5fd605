application:
  name: supply-shop-extra

environment: online

logging:
  config: classpath:config/online/logback.xml

ext.messages.classpath: messages_zh_CN

mybatis:
  mapperLocations:  classpath*:mapper/*Mapper.xml
  type-aliases-package: >
    moonstone.(item|category|brand|shop|spu|user|cart|order|express|promotion|delivery|file|settle|membership|countryImage|integral).model,
    moonstone.(adv|thirdParty|shopWxa|wxa).model,
    moonstone.(weCart|weShop|weDistributionApplication).model,
    moonstone.user.address.model,
    moonstone.user.area.model,
    moonstone.pay.mock.model,
    moonstone.auth.model
  typeHandlersPackage: moonstone.common.type.handler

server:
  context-path: /
  port: 8080

hazelcast:
  config: hazelcast-config.xml
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************
    #    url: *******************************************************************************************************************************
    username: y800
    password: Danding$%654
  data:
    mongodb:
      uri: mongodb://root:<EMAIL>:3717,dds-bp10fd1c07b1aac42264-pub.mongodb.rds.aliyuncs.com:3717/parana-online
      #uri: mongodb://root:<EMAIL>:3717,dds-bp10fd1c07b1aac42.mongodb.rds.aliyuncs.com:3717/parana-online
  redis:
    host: parana-redis-svc
    port: 6379
    timeout: 500
    database: 0
    jedis:
      pool:
        max-active: 100
        max-wait: -1
        max-idle: 8
        min-idle: 0

session:
  cookie-domain: none
  cookie-context-path: /
  cookie-name: msid
  cookie-max-age: 36000000
  source: redis
  serialize-type: json
  redis-host: parana-redis-svc
  redis-port: 6379
  redis-index: 0
  redis-cluster: false
  redis-test-on-borrow: true
  redis-max-total: 10
  redis-max-idle: 0
  redis-prefix: afsession
  #redis-auth: dd654321
  max-inactive-interval: 36000000

wechat:
  mp:
    appId: wx86fe5657944bcaa2
    secret: f72b211010c13e17141accd17fdbb23b
  authorize:
    redirectUrl: http://mall-new.yang800.com/api/wechat/auth/jump
    resultUrl: http://mall-new.yang800.com
  applet:
    enable: true
    mchId: 1488913512
    appId: wx86fe5657944bcaa2
    secret: 8ac1cbf290b0fe98ff1142acf94e7351

parana:
  mall:
    url: http://mall.yang800.com
  web:
    url: http://shop.mall.yang800.com
  h5:
    url: https://m.mall.yang800.com
  wxa:
    url: https://m.mall.yang800.com
    appId: wx90e48a315747ccb9
    appSecret: ebb83ced7885897e4fd5ecfc403ed92d
  we:
    seller:
      url: https://m.mall.yang800.com
      appId: wx0fb538f6dc093892
      appSecret: 5d260bcf67eecf19e96a341b7b4eb95a
    buyer:
      url: https://m.mall.yang800.com
      appId: wxb25691b47b974630
      appSecret: 088dedd8ec3382f5ba981a2560afc187
  wxopen:
    componentAppId: wxfb7d4a7ab73f6ba6
    componentSecret: a9a41dcfd8e7b564a2ec93c0ee58fb9f
    componentToken: wx569abcf3c77ff1de
    componentAesKey: 0987654321qazwsxedcrfvtgbyhnujmik1234567890
    requestdomain: https://m.mall.yang800.com;https://www.yang800.com;https://m.yang800.com
    wsrequestdomain: wss://m.mall.yang800.com;wss://www.yang800.com
    uploaddomain: https://m.mall.yang800.com;https://www.yang800.com
    downloaddomain: https://m.mall.yang800.com;https://www.yang800.com
  substore:
    appId: wx2b917de6e30d8357
    appSecret: b5abba8986b399d33c14f4e5ecc114a3
  levelDistribution:
    appId: wx2b917de6e30d8357
    appSecret: b5abba8986b399d33c14f4e5ecc114a3

search:
  host: es68-svc.db.svc.cluster.local
  #host: terminus-search-svc
  port: 9200

item.search:
  index-name: items
  index-type: item
  mapping-path: item_mapping.json
  full-dump-range: 3000
  batch-size: 100

shop.search:
  index-name: shops
  index-type: shop
  mapping-path: shop_mapping.json
  full-dump-range: 3000
  batch-size: 100

weShopItem.search:
  index-name: t_we_shop_items
  index-type: we_shop_item
  mapping-path: we_shop_item_mapping.json
  full-dump-range: 3000
  batch-size: 100

image:
  base:
    url: http://dante-img.oss-cn-hangzhou.aliyuncs.com
  protocol: http
  domain: dante-img.oss-cn-hangzhou.aliyuncs.com
  upload.allowedFileTypes: jpeg,jpg,gif,png

oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  appKey: LTAI5tNGqjjEfJ1n3xbV42v9
  appSecret: ******************************
  bucketName: dante-img

msg:
  current:
    smsService: aliYunSmsService
    emailService: javaxEmailService
  aliyun:
    appKey: LTAIMbTR37BKlN62
    appSecret: ******************************
  javaxemail:
    mailServerHost: smtp.exmail.qq.com
    mailServerPort: 465
    fromAddress: Y800<<EMAIL>>
    userName: <EMAIL>
    password: Dd111111

msg.template.list:
  - key: sms.user.login.code
    title: 登录验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_145495759","smsParam":{"code":"{{code}}"}}
  - key: sms.user.register.code
    title: 注册验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143863719","smsParam":{"code":"{{code}}"}}
  - key: sms.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143868717","smsParam":{"code":"{{code}}"}}
  - key: sms.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_143868720","smsParam":{"code":"{{code}}"}}
  - key: sms.we.shop.wallet.set.password
    title: 设置提现密码验证
    content: >
      {"smsName":"洋800","smsTemplate":"SMS_153325369","smsParam":{"code":"{{code}}"}}

msg.email:
  templates:
    - key: email.error.notice
      templateName: sendEmailErrorNotice
      subject: "但丁商城 - 异常通知"

cache.duration.in.minutes: 1

# open api配置开始
enable.open.api: true
# open api配置结束

pay:
  debug: false
  channel:
    alipay: enable
  notifyUrl: http://mall.yang800.com/backend/api/order/paid
  refundNotifyUrl: http://mall.yang800.com/backend/api/refund/notify
  returnUrl: http://mall.yang800.com/buyer/trade-success
  h5ReturnUrl: https://m.mall.yang800.com/order/pay-success
  unionpay.wap.token.verifyCerDir: /nfs/terminus-we
  unionpay.pc.token.verifyCerDir: /nfs/terminus-we
  unionpay.app.token.verifyCerDir: /nfs/terminus-we
  certFilePath: /nfs/certFiles/terminus-web
  xinbada:
    url: https://open.sinbada.com
  allinpay:
    pay: https://vsp.allinpay.com/apiweb/unitorder/pay
    refund: https://vsp.allinpay.com/apiweb/tranx/refund
    query: https://vsp.allinpay.com/apiweb/tranx/query
    ystBaseUrl: https://open.allinpay.com/gateway

alipay:
  pid: ****************
  key: pil5t34m4qsdor9ujuffkqfvrgfjt3mp
  account: <EMAIL>

Y800:
  partnerCode: webB2C
  partnerKey: webB2C123
  sourcePlatform: WEBSC
  sourcePlatformV3: WEBSC
  merchantCode:
  wd.support: http://wd.api.yang800.com
  yang.support: http://support.yang800.com
  finance.project.url: http://statics.yang800.com/xhr
  open.api.gate: ${Y800.yang.support}/api/
nio:
  post.url: http://sync.yang800.com/xhr

aoxin:
  gateway: http://www.kuajing.com.au/API

#微分销商城前台域名
m.mall.yang800: https://m.mall.yang800.com

#积分商城生成二维码的路径
m.mall.jifen.path: /nfs/certFiles/terminus-web/levelDistribution/image/
#积分商城生成二维码的url
m.mall.jifen.url: https://m.mall.yang800.com/ladder-distribution/credits-shop

pay.integral.token:
  returnUrl: http://mall.yang800.com/buyer/trade-success
  refundNotifyUrl: http://mall.yang800.com/backend/api/refund/notify

#功能开关
function:
  switch:
    aoXinPush: false
    financePush: true
    unifiedPayment: true

mercury:
  pay:
    host: http://ccs.order.yang800.com/mercury/pay/proxyAPI
    appCode: DDFXSC
    merchantCode: M2019032719271231031
    customs:
      notify: http://admin.mall.yang800.com//backend/api/customs/payment/declare/notify

ebpCode: 3301964J31

wx:
  backend:
    url: https://api.weixin.qq.com/cgi-bin

y800:
  gateApi: http://out.order.yang800.com/open/apiv3

gx.api.mall: http://supply-user.yang800.com
oms:
  api:
    admin: http://pangu-admin

express.100:
  key: bAWWUdnn9775
  customer: 837CEA86CF64D4FEC5B4CEC59589E2D5
  regularUrl: http://poll.kuaidi100.com/poll/query.do

express.tms:
  host: https://tms-server.dataeta.com/api/
  appKey: 19870
  secret: CBF1C96E6894B779
aliyun:
  realNameCertification:
    appKey: 204084473
    appSecret: ERZchuOkDUYUYhDEPz4upEaZlGEL6Q2p

order.auto.cancel.in.minutes: 15

withdraw.apply:
  onlinePay:
    certFilePath: /nfs/withdrawCertFiles/terminus-web
    callbackUrl: http://mall.yang800.com/backend/api/withdrawPayment

agentPay.payment:
  callbackUrl: http://mall.yang800.com/backend/api/agentPayPayment

fundsTransfer.payment:
  callbackUrl: http://mall.yang800.com/backend/api/fundsTransferPayment