application:
  name: AXFXSC_WEB_FRONT

environment: test

logging:
  file: /nfs/terminus-we/terminus-we.log
  level.*: INFO
  level.io.terminus: DEBUG

ext.messages.classpath: messages_zh_CN

mybatis:
  mapperLocations:  classpath*:mapper/*Mapper.xml
  type-aliases-package: >
    moonstone.(item|category|brand|shop|spu|user|cart|order|express|promotion|delivery|file|settle|membership|countryImage).model,
    moonstone.(adv|thirdParty|shopWxa|wxa).model,
    moonstone.(weCart|weShop|weDistributionApplication).model,
    moonstone.user.address.model,
    moonstone.user.area.model,
    moonstone.pay.mock.model,
    moonstone.auth.model

server:
  context-path: /
  port: 8080

spring:
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: ************************************************************************************************************************************
    username: y800
    password: Danding$%654
  data:
    mongodb:
      uri: mongodb://**************:27017/parana-test

session:
  cookie-domain: shop.c57849add598e4227a9cc79037dcb3358.cn-hangzhou.alicontainer.com
  cookie-context-path: /
  cookie-name: msid
  cookie-max-age: 1800
  source: redis
  serialize-type: json
  redis-host: redis-svc.test.svc.cluster.local
  redis-port: 6379
  redis-index: 0
  redis-cluster: false
  redis-test-on-borrow: true
  redis-max-total: 10
  redis-max-idle: 0
  redis-prefix: afsession
  redis-auth: dd654321

parana:
  web:
    url: http://shop.c57849add598e4227a9cc79037dcb3358.cn-hangzhou.alicontainer.com
  h5:
    url: https://m.myazgo.com
  wxa:
    url: https://m.myazgo.com
    appId: wx0d632a5ae5e0c817
    appSecret: aecc1425b4bcd850a2c635652d90b786
  we:
    indexDisplayNum: 30
    seller:
      url: https://m.myazgo.com
      appId: wxecba51a00597fa06
      appSecret: e47a924398259259af4b551851c2567c
    buyer:
      url: https://m.myazgo.com
      appId: wxf379b227de26d16f
      appSecret: 9063daeff996cfa259ea2386b7861c5a
  wxopen:
    componentAppId:
    componentSecret:
    componentToken:
    componentAesKey:
    requestdomain: https://m.mall.yang800.com;https://www.yang800.com;https://m.yang800.com
    wsrequestdomain: wss://m.mall.yang800.com;wss://www.yang800.com
    uploaddomain: https://m.mall.yang800.com;https://www.yang800.com
    downloaddomain: https://m.mall.yang800.com;https://www.yang800.com

search:
  host: terminus-search-svc
  port: 9200

item.search:
  index-name: items
  index-type: item
  mapping-path: item_mapping.json
  full-dump-range: 3000
  batch-size: 100

shop.search:
  index-name: shops
  index-type: shop
  mapping-path: shop_mapping.json
  full-dump-range: 3000
  batch-size: 100

weShopItem.search:
  index-name: t_we_shop_items
  index-type: we_shop_item
  mapping-path: we_shop_item_mapping.json
  full-dump-range: 3000
  batch-size: 100

image:
  base:
    url: http://dante-img.oss-cn-hangzhou.aliyuncs.com
  protocol: http
  domain: dante-img.oss-cn-hangzhou.aliyuncs.com
  upload.allowedFileTypes: jpeg,jpg,gif,png

oss:
  endpoint: oss-cn-hangzhou.aliyuncs.com
  appKey: LTAI5tNGqjjEfJ1n3xbV42v9
  appSecret: ******************************
  bucketName: dante-img

msg:
  current:
    smsService: aliYunSmsService
    emailService: javaxEmailService
  aliyun:
    appKey: LTAIWWKkoh4OumKF
    appSecret: ******************************
  javaxemail:
    mailServerHost: smtp.exmail.qq.com
    mailServerPort: 465
    fromAddress: Y800<<EMAIL>>
    userName: <EMAIL>
    password: Dd111111

msg.template.list:
  - key: sms.user.login.code
    title: 登录验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_158545082","smsParam":{"code":"{{code}}"}}
  - key: sms.user.register.code
    title: 注册验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_158545078","smsParam":{"code":"{{code}}"}}
  - key: sms.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_158490334","smsParam":{"code":"{{code}}"}}
  - key: sms.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_158490349","smsParam":{"code":"{{code}}"}}
  - key: sms.we.shop.wallet.set.password
    title: 设置提现密码验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_158545081","smsParam":{"code":"{{code}}"}}
  - key: sms.foreign.user.change.mobile
    title: 手机号验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_161570257","smsParam":{"code":"{{code}}"}}
  - key: sms.foreign.user.register.code
    title: 注册验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_161575124","smsParam":{"code":"{{code}}"}}
  - key: sms.foreign.user.forget.password
    title: 身份验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_161575129","smsParam":{"code":"{{code}}"}}
  - key: sms.foreign.we.shop.wallet.set.password
    title: 设置提现密码验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_161570250","smsParam":{"code":"{{code}}"}}
  - key: sms.foreign.user.login.code
    title: 登录验证
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_161570255","smsParam":{"code":"{{code}}"}}
  - key: sms.payment.identity.fail
    title: 订单实名验证失败
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_162198348","smsParam":{"name":"{{name}}","orderSn":"{{orderSn}}"}}
  - key: sms.foreign.payment.identity.fail
    title: 订单实名验证失败
    content: >
      {"smsName":"澳新购商城","smsTemplate":"SMS_162198356","smsParam":{"name":"{{name}}","orderSn":"{{orderSn}}"}}

msg.email:
  templates:
    - key: email.error.notice
      templateName: sendEmailErrorNotice
      subject: "澳新购商城 - 异常通知"
    - key: email.payment.pushError
      templateName: sendEmailErrorNotice
      subject: "澳新购商城 - 订单推送通知"

cache.duration.in.minutes: 1

# open api配置开始
enable.open.api: true
# open api配置结束

pay:
  debug: false
  channel:
    alipay: enable
  notifyUrl: http://web.c57849add598e4227a9cc79037dcb3358.cn-hangzhou.alicontainer.com/backend/api/order/paid
  refundNotifyUrl: http://web.c57849add598e4227a9cc79037dcb3358.cn-hangzhou.alicontainer.com/backend/api/refund/notify
  returnUrl: http://web.c57849add598e4227a9cc79037dcb3358.cn-hangzhou.alicontainer.com/buyer/trade-success
  h5ReturnUrl: http://m.c57849add598e4227a9cc79037dcb3358.cn-hangzhou.alicontainer.com/order/pay-success
  unionpay.wap.token.verifyCerDir: /nfs/terminus-web
  unionpay.pc.token.verifyCerDir: /nfs/terminus-web
  unionpay.app.token.verifyCerDir: /nfs/terminus-web
  certFilePath: /nfs/certFiles/terminus-web

Y800:
  partnerCode: AXwebB2C
  partnerKey: AXwebB2CAXwebB2C
  sourcePlatform: WEBSC
  sourcePlatformV3: webB2CV3
  merchantCode: 395E1926791741188213661AFD994824
  wd.support: http://wd.supporttest.yang800.cn
  yang.support: http://supporttest.yang800.cn
  finance.project.url: http://staticstest.yang800.cn/xhr
nio:
  post.url: http://sync.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com/xhr/

aoxin:
  gateway: http://************:61/API

#微分销商城前台域名
m.mall.yang800: https://m.myazgo.com

#功能开关
function:
  switch:
    aoXinPush: true
    financePush: false
    unifiedPayment: false
    foreignSms: true
    editPrivilegeRelevel: true
    thirdPartyStockLimit: false
    allowCreateItem: false

mercury:
  pay:
    host: http://pay.c0f0834732a854f67bb1b8ffac6095532.cn-hangzhou.alicontainer.com
    appCode: AXFXSC
    merchantCode: M2019032813263649350
    customs:
      notify: http://admin.c57849add598e4227a9cc79037dcb3358.cn-hangzhou.alicontainer.com/backend/api/customs/payment/declare/notify

ebpCode: 3301964J31
