package com.danding.profit.component

import java.io.File
import java.net.URL
import java.util.logging.Logger

import com.danding.profit.api.rule.RuleFactory
import com.danding.profit.api.rule.base.RuleBase
import com.danding.profit.component.rule.RuleConstant
import moonstone.common.utils.{LogUtil, Translate}
import javax.annotation.PostConstruct
import org.springframework.context.annotation.Configuration
import org.springframework.util.StopWatch

import scala.collection.{Map, mutable}


/**
 * 扫描搜索使用Ruler 的并且将具有RulerBase的注册进入Ruler
 */
@Configuration
class RuleRegister extends RuleFactory {
  val log: Logger = Logger.getLogger(getClass.getName)
  val ruleUrl: URL = getClass.getResource("rule")
  val files: File = new File(ruleUrl.getFile)
  val packageName: String = getClass.getPackage.getName
  val classMap: mutable.Map[String, Class[_ <: RuleBase]] = mutable.HashMap.empty[String, Class[_ <: RuleBase]]
  var rulerMapByType: collection.Map[String, collection.Map[String, _ >: RuleBase]] = Map.empty


  @PostConstruct
  def autoConfigure(): Unit = {
    loadClass(classOf[RuleBase], files, classMap)
    rulerMapByType = divideTheRule(classMap)
  }

  /**
   * 动态加载类 并且判断是否为指定类
   *
   * @param targetBaseClass 指定基类
   * @param file            文件
   * @param classStorageMap 储存Map
   */
  private def loadClass(targetBaseClass: Class[_], file: File, classStorageMap: mutable.Map[String, Class[_ <: RuleBase]]): Unit = {
    if (file.isDirectory) {
      file.listFiles().foreach(file => loadClass(targetBaseClass, file, classStorageMap))
    } else {
      if (file.getName.endsWith("class")) {
        try {
          getClassNameIfInPackage(file.getAbsolutePath).map(Class.forName)
          match {
            case Some(ruleClass) if targetBaseClass.isAssignableFrom(ruleClass) => classStorageMap.put(ruleClass.getName, ruleClass.asInstanceOf[Class[_ <: RuleBase]])
            case _ => log.warning("not suitable to load the class %s".format(file.getAbsolutePath))
          }
        } catch {
          case ex: Exception => log.severe("query class failed for %s".format(ex.getMessage))
        }
      }
    }
  }

  /**
   * 判断是否为这个包
   * return Class Name if in Package
   *
   * @param path 文件地址名
   * @return
   */
  private def getClassNameIfInPackage(path: String): Option[String] = path.replace("/", ".").indexOf(packageName)
  match {
    case -1 => Option.empty
    case place if path.toLowerCase.endsWith("class") => Option.apply(path.replace("/", ".").substring(place).substring(0, path.length - place - 6))
    case _ => Option.empty
  }

  /**
   * 根据不同的规则类型进行区分不同的规则
   *
   * @param classMap 储存规则的参数
   * @return
   */
  private def divideTheRule(classMap: Map[String, Class[_ <: RuleBase]]): Map[String, Map[String, _ >: RuleBase]]
  = {
    val stopWatch: StopWatch = new StopWatch("init_rule_component")
    val typeBaseNamedRuleMap = mutable.HashMap.empty[String, mutable.HashMap[String, _ >: RuleBase]]
    val mapResult = mutable.LinkedHashMap.empty[String, String]
    stopWatch.start()
    log.info("%s load Rule class".format(LogUtil.getClassMethodName()))
    for ((className, classType) <- classMap) {
      try {
        val ruler = classType.newInstance()
        if (!typeBaseNamedRuleMap.contains(ruler.rulerType)) typeBaseNamedRuleMap.put(ruler.rulerType, mutable.HashMap.empty)
        typeBaseNamedRuleMap(ruler.rulerType).put(ruler.rulerName, ruler)
        mapResult.put(className, "%s@%s".format(ruler.rulerType, ruler.rulerName))
      }
      catch {
        case ex: Exception =>
          mapResult.put(className, "load err:%s".format(ex.getMessage))
      }
    }
    stopWatch.stop()
    typeBaseNamedRuleMap
  }

  override def getRule(name: String): RuleBase = throw new IllegalArgumentException(new Translate("请提供规则类型 规则名:[%s]", name).toString)

  override def getRule[T](name: String, classType: Class[T]): T with RuleBase =
    (name.substring(0, 2) match {
      case "c:" => rulerMapByType.get(RuleConstant.ProfitRulerCondition.toString)
      case "r:" => rulerMapByType.get(RuleConstant.ProfitRulerGainer.toString)
    }).flatMap(map => map.get(name.substring(2, name.length)))
    match {
      case Some(bean) if classType.isInstance(bean) => bean.asInstanceOf[T with RuleBase]
      case _ => throw new ClassNotFoundException(new Translate("[%s]规则控件不存在", name).toString)
    }

  /**
   * return first one if contain more bean that is T type
   *
   * @param classType 规则类型
   * @tparam T 实体类
   * @return
   */
  override def getRule[T](classType: Class[T]): T with RuleBase = throw new NoSuchMethodError("not implement")

  override def getRules[T](classType: Class[T]): List[_ >: T with RuleBase] = throw new NoSuchMethodError("not implement")
}
