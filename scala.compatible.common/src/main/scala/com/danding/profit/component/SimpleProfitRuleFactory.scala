package com.danding.profit.component


import java.util.logging.Logger

import com.danding.profit.component.rule.profitowner.StoreProxyProfitOwnerRuleByIdentity
import com.danding.profit.model.ProfitRule
import javax.annotation.PostConstruct
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.stereotype.Component

@Component
class SimpleProfitRuleFactory {
  @Autowired
  var applicationContext: ApplicationContext = _
  @Autowired
  var ruleRegister: RuleRegister = _
  val log: Logger = Logger.getLogger(classOf[SimpleProfitRuleFactory].getName)

  @PostConstruct
  def init(): Unit = {
    val ruler = new StoreProxyProfitOwnerRuleByIdentity
  }

  /**
   * 获取一个规则实例
   * 理论上需要使用一层缓存层进行缓存该层
   *
   * @param ruleContent 规则内容  应该为{{{condition#profitGainerBean,...@calculator,...}}}
   * @param environment 规则环境
   * @return 规则实例
   */
  def getBean(ruleContent: String, environment: Map[String, Map[String, String]]): ProfitRule = {
    val conditionEndAt: Int = ruleContent.indexOf('#')
    val profitGainerBeanEndAt = ruleContent.indexOf('@')
    require((conditionEndAt > -1).&(profitGainerBeanEndAt > -1))
    val conditionRawStr = ruleContent.substring(0, conditionEndAt)
    val rawProfitGainerBean = ruleContent.substring(conditionEndAt + 1, profitGainerBeanEndAt)
    null
  }

}
