package moonstone.web.core.component

import java.util
import java.util.function.BiConsumer

import moonstone.common.event.ForceNewVersionByShopIdNotifyEvent
import moonstone.common.model.MethodVersionInfoContent
import moonstone.common.utils.EventSender
import moonstone.event.MethodSwitchNotify
import moonstone.web.core.model.MethodVersionInfo
import org.slf4j.{Logger, LoggerFactory}
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.{ContextRefreshedEvent, EventListener}
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.{Criteria, Query, Update}
import org.springframework.stereotype.Component

@Component
@Autowired
case class MethodVersionManager(mongoTemplate: MongoTemplate) {

  val log: Logger = LoggerFactory.getLogger(classOf[MethodVersionManager])


  /**
   * 在Spring准备完毕后 设定方法版本 发送初始化信息
   *
   * @param contextRefreshedEvent Spring容器内容刷新完毕
   */
  @EventListener(Array(classOf[ContextRefreshedEvent]))
  def sendMethodInitMsg(contextRefreshedEvent: ContextRefreshedEvent): Unit = {
    for (methodVersionInfo <- readMethodVersionInfo) {
      val taker: BiConsumer[java.lang.String, java.lang.String] = new BiConsumer[java.lang.String, java.lang.String] {
        override def accept(methodName: java.lang.String, version: java.lang.String): Unit = EventSender.sendApplicationEvent(MethodSwitchNotify(methodVersionInfo.getClassName, methodName, version))
      }
      methodVersionInfo.getMethodVersion.forEach(taker)
      if (mongoTemplate.count(Query.query(Criteria.where("className").is(methodVersionInfo.getClassName)), classOf[MethodVersionInfoContent]) != 1) {
        mongoTemplate.remove(Query.query(Criteria.where("className").is(methodVersionInfo.getClassName)), classOf[MethodVersionInfoContent])
        mongoTemplate.upsert(Query.query(Criteria.where("className").is(methodVersionInfo.getClassName)),
          Update.update("methodVersion", methodVersionInfo.getMethodVersion)
          , classOf[MethodVersionInfoContent])
      }
    }
    // 发送根据店铺选择的版本信息
    readForceVersionChoiceInfo.foreach(EventSender.sendApplicationEvent)
  }

  /**
   * 监听修改更新事件
   *
   * @param methodSwitchNotify 事件
   */
  @EventListener(Array(classOf[MethodSwitchNotify]))
  def methodSwitched(methodSwitchNotify: MethodSwitchNotify): Unit = {
    val query = Query.query(Criteria.where("className").is(methodSwitchNotify.className))
    val methodVersionInfo: MethodVersionInfoContent = Option.apply(mongoTemplate.findOne(query, classOf[MethodVersionInfoContent])) match {
      case Some(info) => info
      case None => new MethodVersionInfoContent(methodSwitchNotify.className, new util.HashMap[java.lang.String, java.lang.String]())
    }
    methodVersionInfo.getMethodVersion.put(methodSwitchNotify.methodName, methodSwitchNotify.methodVersion)
    mongoTemplate.upsert(query, Update.update("methodVersion", methodVersionInfo.getMethodVersion), classOf[MethodVersionInfoContent])
  }

  @EventListener(Array(classOf[ForceNewVersionByShopIdNotifyEvent]))
  def saveForceNewVersionByShopIdNotifyEvent(forceNewVersionByShopIdNotifyEvent: ForceNewVersionByShopIdNotifyEvent): Unit = {
    mongoTemplate.upsert(Query.query(Criteria.where("shopId").is(forceNewVersionByShopIdNotifyEvent.shopId)), Update.update("force", forceNewVersionByShopIdNotifyEvent.force), classOf[ForceNewVersionByShopIdNotifyEvent])
  }

  /**
   * 读取强制使用新版本配置
   *
   * @return
   */
  def readForceVersionChoiceInfo: Array[ForceNewVersionByShopIdNotifyEvent] = mongoTemplate.findAll(classOf[ForceNewVersionByShopIdNotifyEvent]).toArray(Array.empty[ForceNewVersionByShopIdNotifyEvent])

  /**
   * 读取版本设定数据库中所有的配置数据
   */
  def readMethodVersionInfo: Array[MethodVersionInfoContent] = try {
    mongoTemplate.findAll(classOf[MethodVersionInfo]).toArray(Array.empty[MethodVersionInfo]).map(_.getContent)
  }
  catch {
    case ex: Exception => {
      mongoTemplate.findAll(classOf[MethodVersionInfoContent]).toArray(Array.empty[MethodVersionInfoContent])
    }
  }
}
