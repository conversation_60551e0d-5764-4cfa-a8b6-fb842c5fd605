package moonstone.web.core.component.api

import moonstone.order.model.OrderBase
import moonstone.web.core.model.{OutSystemOrder, OutSystemOrderContent}

trait OuterSystemOrderManager {

  /**
   * 聚合订单
   * 将多个内部订单聚合成多个外部订单
   *
   * @param orderArray 被聚合的订单列表
   * @return 外部订单列表
   */
  def getOutSystemOrder(orderArray: Array[_ <: OrderBase]): Array[OutSystemOrder]


  /**
   * 解聚合订单
   *
   * @param outSystemOrderArray 外部订单列表
   * @return 内部订单信息
   */
  def mapOutSystemOrderIntoInside(outSystemOrderArray: Array[OutSystemOrder]): Array[_ <: OrderBase]

  /**
   * 拉取外部订单的内容单
   *
   * @param outSystemOrderId 外部订单Id
   * @return 内容单
   */
  def getOutSystemOrderContent(outSystemOrderId: String): Array[OutSystemOrderContent]
}
