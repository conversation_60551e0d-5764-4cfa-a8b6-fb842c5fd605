package moonstone.web.core.model.dto

import java.util.Date

/**
 * 查询用户的结果
 *
 * @param nickName 用户昵称
 * @param name     名称
 * @param mobile   手机号码
 * @param sex      性别  0:为止  1:男性  2:女性
 * @param orderNum 订单数量
 * @param id       为外部提供的用户Id
 */
case class UserQueryRes(nickName: String
                        , name: String
                        , mobile: String
                        , sex: Int
                        , orderNum: Int
                        , id: String
                        , registeredAt: Date
                        , registerNow : Boolean
                       , firstBoughtAt: Date
                       ) {}
