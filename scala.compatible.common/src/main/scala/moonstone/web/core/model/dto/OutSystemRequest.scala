package moonstone.web.core.model.dto

import com.google.gson.Gson
import moonstone.common.utils.{LogUtil, Translate}
import org.slf4j.LoggerFactory

import java.io.ByteArrayOutputStream
import java.security.MessageDigest
import java.util.Base64
import javax.crypto.spec.{IvParameterSpec, SecretKeySpec}
import javax.crypto.{Cipher, KeyGenerator}


case class OutSystemRequest(requestId: String, key: String, data: String, sign: String) {


  /**
   * 默认使用 SHA-256 32位密钥解密
   *
   * @param secret      密钥
   * @param targetClass 目标类型
   * @tparam T 实体类
   */
  def decode[T](secret: String, targetClass: Class[T]): Either[String, T] = decode(secret, targetClass, "SHA-256")

  /**
   * 使用制定方式解密
   *
   * @param secret          密钥
   * @param targetClass     目标类型
   * @param digestAlgorithm 摘要算法
   */
  def decode[T](secret: String, targetClass: Class[T], digestAlgorithm: String): Either[String, T] = {
    try {
      val outputStream = new ByteArrayOutputStream()
      val digest = MessageDigest.getInstance(digestAlgorithm)

      outputStream.write(Base64.getDecoder.decode(data))
      outputStream.write(secret.getBytes.reverse)
      outputStream.write((System.currentTimeMillis() / OutSystemRequest.divBy % OutSystemRequest.modBy).toString.getBytes)

      val signMatch = Base64.getEncoder.encodeToString(digest.digest(outputStream.toByteArray))

      if (sign == null)
        return Left(new Translate("签名错误").toString)
      if (!sign.equals(signMatch)) {
        outputStream.reset()
        outputStream.write(Base64.getDecoder.decode(data))
        outputStream.write(secret.getBytes.reverse)
        outputStream.write(((System.currentTimeMillis() / OutSystemRequest.divBy - 1) % OutSystemRequest.modBy).toString.getBytes)

        val reMatch = Base64.getEncoder.encodeToString(digest.digest(outputStream.toByteArray))
        if (!sign.equals(reMatch))
          return Left(new Translate("签名错误").toString)
      }

      outputStream.reset()

      val cipher = Cipher.getInstance("AES/CBC/NOPADDING")
      val md5 = if (digest.getAlgorithm.toUpperCase().equals("MD5")) digest else MessageDigest.getInstance("MD5")
      cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(digest.digest(secret.getBytes), "AES"), new IvParameterSpec(md5.digest(secret.reverse.getBytes())))

      outputStream.write(cipher.doFinal(Base64.getDecoder.decode(data)))
      Right(OutSystemRequest.gson.fromJson(outputStream.toString.trim, targetClass))
    }
    catch {
      case ex: Exception => val logger = LoggerFactory.getLogger(classOf[OutSystemRequest])
        logger.error(s"${LogUtil.getClassMethodName} fail to decode the request because [${ex.getMessage}]", ex)
        Left(ex.getMessage)
    }
  }
}

object OutSystemRequest {
  private val gson = new Gson()
  private val modBy = 28657
  private val divBy = 317811

  /**
   * 默认使用SHA-256加密
   *
   * @param requestId 请求头
   * @param key       辨识Id
   * @param secret    密钥
   * @param request   请求
   * @return
   */
  def from(requestId: String, key: String, secret: String, request: Any): OutSystemRequest = OutSystemRequest.from(requestId, key, secret, request, "SHA-256")

  def from(requestId: String, key: String, secret: String, request: Any, algorithmName: String): OutSystemRequest = {
    val byteArrayOutputStream = new ByteArrayOutputStream()
    val json = gson.toJson(request)
    val digest = MessageDigest.getInstance(algorithmName)

    val keyGenerator = KeyGenerator.getInstance("AES")
    keyGenerator.init(256)

    val cipher = Cipher.getInstance("AES/CBC/NOPADDING")
    val md5 = if (digest.getAlgorithm.toUpperCase().equals("MD5")) digest else MessageDigest.getInstance("MD5")
    cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(digest.digest(secret.getBytes()), "AES"), new IvParameterSpec(md5.digest(secret.reverse.getBytes())))
    val wait = if (json.length % 16 == 0) json else {
      val stringBuilder = new StringBuilder(json)
      while (stringBuilder.length() % 16 != 0)
        stringBuilder.append(" ")
      stringBuilder.toString()
    }
    val encrypted = cipher.doFinal(wait.getBytes())
    byteArrayOutputStream.write(encrypted)
    byteArrayOutputStream.write(secret.getBytes.reverse)
    byteArrayOutputStream.write((System.currentTimeMillis() / divBy % modBy).toString.getBytes())
    val sign = digest.digest(byteArrayOutputStream.toByteArray)
    OutSystemRequest(requestId, key, Base64.getEncoder.encodeToString(encrypted), Base64.getEncoder.encodeToString(sign))
  }
}

