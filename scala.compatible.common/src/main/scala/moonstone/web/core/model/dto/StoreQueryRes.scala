package moonstone.web.core.model.dto

/**
 * 门店查询信息
 *
 * @param id                 外部用户Id
 * @param name               店铺名
 * @param mobile             手机号
 * @param address            地址
 * @param todayOrder         近日订单数量
 * @param todayProfit        今日预计利润
 * @param withdrawAbleProfit 可用提现金额
 * @param level              级别
 * @param fanNum             粉丝数量
 * @param honestFanNum       忠粉数量
 * @param belongStore        属于店铺数量
 */
case class StoreQueryRes(id: String, name: String, mobile: String, address: String, todayOrder: Long, todayProfit: Long, withdrawAbleProfit: Long, level: Int, fanNum: Long, honestFanNum: Long, belongStore: Long)
