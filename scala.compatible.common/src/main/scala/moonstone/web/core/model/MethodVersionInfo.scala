package moonstone.web.core.model

import moonstone.common.model.MethodVersionInfoContent

import java.util

/**
 * 方法接口版本数据
 *
 * @param className     类名
 * @param methodVersion 方法版本
 */
case class MethodVersionInfo(className: String, methodVersion: scala.collection.mutable.Map[String, String]){
  /**
   * @return 获取信息本体
   */
  def getContent: MethodVersionInfoContent = {
    val methodVersionInfoContent = new MethodVersionInfoContent()
    methodVersionInfoContent.setClassName(className)
    val map = new util.HashMap[String, String]()
    methodVersion.foreach(entry => map.put(entry._1, entry._2))
    methodVersionInfoContent.setMethodVersion(map)
    methodVersionInfoContent
  }
}
