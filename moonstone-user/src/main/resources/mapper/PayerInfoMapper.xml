<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2019-06-24
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="PayerInfo">

    <resultMap id="PayerInfoMapper" type="moonstone.user.model.PayerInfoRecord">
        <result column="shop_id" property="shopId"/>
        <result column="order_id" property="orderId"/>
        <result column="payer_name" property="payerName"/>
        <result column="payer_no" property="payerNo"/>
        <result column="password" property="password"/>
        <result column="hash" property="hash"/>
    </resultMap>

    <resultMap id="PayerInfoMapper2" type="moonstone.user.model.PayerInfo">
        <result column="shop_id" property="shopId"/>
        <result column="order_id" property="orderId"/>
        <result column="payer_name" property="payerName"/>
        <result column="payer_no" property="payerNo"/>
        <result column="password" property="password"/>
        <result column="hash" property="hash"/>
    </resultMap>

    <sql id="tb">
        payer_info
    </sql>

    <sql id="cols_all">
        shop_id, order_id, payer_name, payer_no, `password`, hash
    </sql>

    <sql id="vals">
        #{shopId}, #{orderId}, #{payerName}, #{payerNo}, #{password}, #{hash}
    </sql>

    <insert id="create" parameterType="map">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_all"/>)
        VALUES
        (<include refid="vals"/>)
        on duplicate key update password = #{password},
        payer_no = #{payerNo}, payer_name = #{payerName}, hash = #{hash}
    </insert>
    <sql id='update-sql'>
        shop_id = shop_id
        <if test='password != null'>
            <![CDATA[, password = #{password}]]>
        </if>
        <if test='payerName != null'>
            <![CDATA[, payer_name = #{payerName}]]>
        </if>
        <if test='shopId != null'>
            <![CDATA[, shop_id = #{shopId}]]>
        </if>
        <if test='payerNo != null'>
            <![CDATA[, payer_no = #{payerNo}]]>
        </if>
        <if test='hash != null'>
            <![CDATA[, hash = #{hash}]]>
        </if>
    </sql>
    <update id="updateByOrderId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            <include refid="update-sql"/>
        </set>
        <where>
            order_id = #{orderId}
        </where>
    </update>
    <select id="findByOrderId" parameterType="map" resultMap="PayerInfoMapper">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            order_id = #{orderId}
        </where>
    </select>

    <select id="findByOrderIds" parameterType="map" resultMap="PayerInfoMapper">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            order_id in
            <foreach collection="orderIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="findByOrderId2" parameterType="map" resultMap="PayerInfoMapper2">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            order_id = #{orderId}
        </where>
    </select>
    <select id="findByOrderId3" parameterType="map" resultType="map">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            order_id = #{orderId}
        </where>
    </select>

</mapper>