<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2016 杭州端点网络科技有限公司
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="StoreProxyInfo">

    <resultMap id="StoreProxyInfoMap" type="StoreProxyInfo">
        <id column="id" property="id"/>
        <result column="proxy_id" property="proxyId"/>
        <result column="city" property="city"/>
        <result column="city_id" property="cityId"/>
        <result column="province_id" property="provinceId"/>
        <result column="province" property="province"/>
        <result column="county_id" property="countyId"/>
        <result column="county" property="county"/>
        <result column="proxy_shop_name" property="proxyShopName"/>
        <result column="tel" property="tel"/>
        <result column="address" property="address"/>
        <result column="proxy_mobile" property="proxyMobile"/>
        <result column="boss_name" property="bossName"/>
        <result column="boss_mobile" property="bossMobile"/>
        <result column="east_longtitude" property="eastLongtitude"/>
        <result column="north_latitude" property="northLatitude"/>
        <result column="status" property="status"/>
        <result column="extra_json" property="extraStr"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>


    <sql id="tb">
        parana_store_proxy_info
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        proxy_id, `status`,city,city_id,
        province,province_id,county,county_id,proxy_shop_name,
        tel,address,proxy_mobile,boss_name,boss_mobile,east_longtitude,north_latitude,extra_json, created_at, updated_at
    </sql>

    <sql id="vals">
        #{proxyId},  #{status},
        #{city},#{cityId},#{province},#{provinceId},#{county},#{countyId},#{proxyShopName} ,
       #{tel} ,#{address} , #{proxyMobile}, #{bossName}, #{bossMobile},#{eastLongtitude},#{northLatitude},
        #{extraStr}, now(), now()
    </sql>

    <sql id="criteria">

        <if test="status != null">AND `status` = #{status}</if>
        <if test="statuss != null">AND status in
            <foreach collection="statuss" item="i" open="(" close=")"
                     separator=",">
                #{i}
            </foreach>
        </if>
        <if test="status == null">AND `status` != -1</if>
    </sql>


    <insert id="create" parameterType="StoreProxyInfo" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="StoreProxyInfo">
        UPDATE
        <include refid="tb"/>
        SET
        <if test="proxyId != null">proxy_id = #{proxyId},</if>
        <if test="status != null">`status` = #{status},</if>
        <if test="city != null">city = #{city},</if>
        <if test="cityId != null">city_id = #{cityId},</if>
        <if test="province != null">province = #{province},</if>
        <if test="provinceId != null">province_id = #{provinceId},</if>
        <if test="county != null">county = #{county},</if>
        <if test="countyId != null">county_id = #{countyId},</if>
        <if test="proxyShopName != null">proxy_shop_name = #{proxyShopName},</if>
        <if test="tel != null">tel = #{tel},</if>
        <if test="address != null">address = #{address},</if>
        <if test="proxyMobile != null">proxy_mobile = #{proxyMobile},</if>
        <if test="bossName != null">boss_name = #{bossName},</if>
        <if test="bossMobile != null">boss_mobile = #{bossMobile},</if>
        <if test="eastLongtitude != null">east_longtitude = #{eastLongtitude},</if>
        <if test="northLatitude != null">north_latitude = #{northLatitude},</if>
        <if test="extraStr != null">extra_json = #{extraStr},</if>
        updated_at = now()
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="long" resultMap="StoreProxyInfoMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE  id = #{id}
    </select>
    <select id="findByProxyId" parameterType="long" resultMap="StoreProxyInfoMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE proxy_id = #{proxyId}
    </select>


    <select id="count" parameterType="map" resultType="long">
        SELECT count(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="StoreProxyInfoMap">
        SELECT id,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="getSubProxyInfo" parameterType="map" resultType="long">
        SELECT count(1)
        FROM
        <include refid="tb"/>
        WHERE
        proxy_id IN
        <foreach item="ids" collection="ids" open="(" separator="," close=")">
            #{ids}
        </foreach>
        and `status`=11
    </select>

    <select id="getCountSubProxyInfo" parameterType="map" resultMap="StoreProxyInfoMap">
        SELECT id,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        WHERE  `status`=#{status} AND
        proxy_id IN
        <foreach item="proxyIds" collection="proxyIds" open="(" separator="," close=")">
            #{proxyIds}
        </foreach>
    </select>

</mapper>