<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2016 杭州端点网络科技有限公司
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="SubSeller">

    <resultMap id="SubSellerMap" type="SubSeller">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="master_user_id" property="masterUserId"/>
        <result column="role_id" property="roleId"/>
        <result column="role_name" property="roleName"/>
        <result column="status" property="status"/>
        <result column="extra_json" property="extraJson"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_user_sub_sellers
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
      user_id, `user_name`, master_user_id, role_id, role_name, status, extra_json, created_at, updated_at
    </sql>

    <sql id="vals">
        #{userId}, #{userName}, #{masterUserId}, #{roleId}, #{roleName}, #{status}, #{extraJson}, now(), now()
    </sql>

    <sql id="criteria">
        status != -1
        <if test="userId != null">AND user_id = #{userId}</if>
        <if test="masterUserId != null">AND master_user_id = #{masterUserId}</if>
        <if test="status != null">AND status = #{status}</if>
    </sql>

    <insert id="create" parameterType="SubSeller" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="SubSeller">
        UPDATE
        <include refid="tb"/>
        SET
        <if test="userId != null">user_id = #{userId},</if>
        <if test="userName != null">user_name = #{userName},</if>
        <if test="masterUserId != null">master_user_id = #{masterUserId},</if>
        <if test="roleId != null">role_id = #{roleId},</if>
        <if test="roleName != null">role_name = #{roleName},</if>
        <if test="status != null">status = #{status},</if>
        <if test="extraJson != null">extra_json = #{extraJson},</if>
        updated_at = now()
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="long" resultMap="SubSellerMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByIds" parameterType="list" resultMap="SubSellerMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT count(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="SubSellerMap">
        SELECT id,
        <include refid="cols_exclude_id"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findByUserId" parameterType="long" resultMap="SubSellerMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `user_id` = #{userId}
    </select>

    <select id="findByMasterUserIdAndRoleIds" parameterType="map" resultMap="SubSellerMap">
        select
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        where master_user_id = #{masterUserId}
          and `status` = 1
          and role_id in
          <foreach collection="roleIds" open="(" item="item" close=")" separator=",">
              #{item}
          </foreach>
    </select>

    <update id="updateAnything" parameterType="map">
        ${sql}
    </update>

    <delete id="deleteAnything" parameterType="map">
        ${sql}
    </delete>

    <insert id="insertAnything" parameterType="map">
        ${sql}
    </insert>

    <update id="ddlAnything" parameterType="map">
        ${sql}
    </update>

    <select id="countByRoleId" parameterType="map" resultType="long">
        SELECT count(*) FROM parana_user_sub_sellers WHERE role_id = #{roleId} and status = 1;
    </select>
</mapper>