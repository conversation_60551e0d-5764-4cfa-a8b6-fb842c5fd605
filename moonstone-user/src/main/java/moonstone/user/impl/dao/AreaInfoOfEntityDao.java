package moonstone.user.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.user.area.model.AreaInfoOfEntity;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AreaInfoOfEntityDao extends MyBatisDao<AreaInfoOfEntity> {
    public List<AreaInfoOfEntity> findByMainIdAndType(Long mainId, Integer type) {
        return sqlSession.selectList(sqlId("findByMainIdAndType"), ImmutableMap.of("mainId", mainId, "type", type));
    }

    public AreaInfoOfEntity findCurrentByMainIdAndType(Long mainId, Integer type) {
        return sqlSession.selectOne(sqlId("findCurrentByMainIdAndType"), ImmutableMap.of("mainId", mainId, "type", type));
    }
}
