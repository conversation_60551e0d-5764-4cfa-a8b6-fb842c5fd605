package moonstone.user.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.user.model.PayerInfo;
import moonstone.user.model.PayerInfoRecord;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class PayerInfoDao extends MyBatisDao<PayerInfo> {

    public List<PayerInfoRecord> findByOrderIds(List<Long> orderIds) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("orderIds", orderIds);

        return getSqlSession().selectList(sqlId("findByOrderIds"), map);
    }
}
