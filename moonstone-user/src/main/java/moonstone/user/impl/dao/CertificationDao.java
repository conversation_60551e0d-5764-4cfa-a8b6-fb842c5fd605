package moonstone.user.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.user.model.Certification;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

/**
 * 功能描述:  用户认证记录持久化操作类
 * 创建时间:  2020/7/1 3:20 下午
 *
 * <AUTHOR>
 */
@Repository
public class CertificationDao extends MyBatisDao<Certification> {


    public Certification findByUserId(Long userId) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("userId", userId);
        return getSqlSession().selectOne(sqlId("findByUserId"), map);
    }


    public Integer updateByUserId(Certification certification) {
        return getSqlSession().update(sqlId("updateByUserId"), certification);
    }

}
