package moonstone.user.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.user.model.UserCertification;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by CaiZhy on 2018/11/13.
 */
@Component
public class UserCertificationDao extends MyBatisDao<UserCertification> {
    public List<UserCertification> findByUserId(Long userId){
        return getSqlSession().selectList(sqlId("findByUserId"), userId);
    }

    public UserCertification findDefaultByUserId(Long userId){
        return getSqlSession().selectOne(sqlId("findDefaultByUserId"), userId);
    }

    public void updateByUserId(UserCertification userCertification){
        getSqlSession().update(sqlId("updateByUserId"), userCertification);
    }

    public Boolean makeDefault(Long id, Long userId){
        return getSqlSession().update(sqlId("makeDefault"), ImmutableMap.of("id", id, "userId", userId)) == 1;
    }

    public Boolean updateStatus(Long id, Integer status){
        return getSqlSession().update(sqlId("updateStatus"), ImmutableMap.of("id", id, "status", status)) == 1;
    }
}
