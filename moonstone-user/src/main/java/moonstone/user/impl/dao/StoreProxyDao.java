package moonstone.user.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import moonstone.user.dto.StoreProxyCriteria;
import moonstone.user.model.StoreProxy;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class StoreProxyDao extends MyBatisDao<StoreProxy> {
    public StoreProxy findByShopIdAndUserId(long shopId, long userId) {
        return getSqlSession().selectOne(sqlId("findByShopIdAndUserId"),
                ImmutableMap.of("shopId", shopId
                        , "userId", userId));
    }

    public List<StoreProxy> findByShopIdAndLevel(long shopId, Integer level) {
        if (level != null) {
            return getSqlSession().selectList(sqlId("findByShopIdAndLevel"),
                    ImmutableMap.of("shopId", shopId
                            , "level", level));
        }
        return getSqlSession().selectList(sqlId("findByShopIdAndLevel"),
                ImmutableMap.of("shopId", shopId));
    }

    public List<StoreProxy> findUserIdAndLevel(long userId, Integer level) {
        if (level != null) {
            return getSqlSession().selectList(sqlId("findByUserIdAndLevel"),
                    ImmutableMap.of("userId", userId, "level", level));
        }
        return getSqlSession().selectList(sqlId("findByUserIdAndLevel"),
                ImmutableMap.of("userId", userId));
    }

    public List<StoreProxy> findBySupperIdAndShopId(long supperId, long shopId) {
        return getSqlSession().selectList(sqlId("findBySupperIdAndShopId"),
                ImmutableMap.of("supperId", supperId
                        , "shopId", shopId
                ));
    }

    public Long count(StoreProxyCriteria storeProxyCriteria) {
        return getSqlSession().selectOne(sqlId("count"), storeProxyCriteria.toMap());
    }

    public long findIdByUserId(long userId, Long shopId) {
        return getSqlSession().selectOne(sqlId("findIdByUserId"), ImmutableMap.of("userId", userId, "shopId", shopId));
    }

    public StoreProxy findBySupperIdUseUserIdAndShopId(Long supperId,long shopId) {

        return getSqlSession().selectOne(sqlId("findBySupperIdUseUserIdAndShopId"), ImmutableMap.of("userId", supperId, "shopId", shopId));
    }

    public StoreProxy findByShopIdAndUserIdAndStatus(Long shopId, Long userId, long status) {
        return getSqlSession().selectOne(sqlId("findByShopIdAndUserIdAndStatus"),
                ImmutableMap.of("shopId", shopId
                        , "userId", userId,"status",status));
    }

    public Long getSubProxy(long userId, long shopId) {
        return  getSqlSession().selectOne(sqlId("getSubProxy"), ImmutableMap.of("userId", userId, "shopId", shopId));
    }

    public Long getSubProxyIsAuth(Long userId, Long shopId) {
        return  getSqlSession().selectOne(sqlId("getSubProxyIsAuth"), ImmutableMap.of("userId", userId, "shopId", shopId));

    }

    public List<StoreProxy> findBySupperIdAndShopIdAndStatus(long userId, Long shopId, long status) {
        return getSqlSession().selectList(sqlId("findBySupperIdAndShopIdAndStatus"),
                ImmutableMap.of("supperId", userId
                        , "shopId", shopId,"status",status
                ));
    }

    public List<StoreProxy> findByProxyShopName(String proxyShopName) {
        return getSqlSession().selectList(sqlId("findByProxyShopName"),
                ImmutableMap.of("proxyShopName", proxyShopName
                ));
    }

    public StoreProxy findByUserId(Long userId) {
        return getSqlSession().selectOne(sqlId("findByUserId"),
                ImmutableMap.of("userId", userId));
    }

    public Long findUserIdByProxyName(String proxyName, Long shopId) {
        return getSqlSession().selectOne(sqlId("findUserIdByProxyName"), ImmutableMap.of("proxyName", proxyName, "shopId", shopId));
    }
}
