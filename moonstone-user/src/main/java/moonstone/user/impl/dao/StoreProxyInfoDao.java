package moonstone.user.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import moonstone.user.model.StoreProxyInfo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/26 14:12
 */
@Repository
@Slf4j
public class StoreProxyInfoDao extends MyBatisDao<StoreProxyInfo> {

    public StoreProxyInfo findByProxyId(Long proxyId) {
        return getSqlSession().selectOne(sqlId("findByProxyId"),
                ImmutableMap.of("proxyId", proxyId));
    }

    public long getSubProxyInfo(List<Long> ids, long status) {
        return getSqlSession().selectOne(sqlId("getSubProxyInfo"), ImmutableMap.of("ids", ids, "status", status));

    }

    public List<StoreProxyInfo> getCountSubProxyInfo(List<Long> proxyIds, Integer status) {
        return getSqlSession().selectList(sqlId("getCountSubProxyInfo"), ImmutableMap.of("proxyIds", proxyIds, "status", status));
    }
}
