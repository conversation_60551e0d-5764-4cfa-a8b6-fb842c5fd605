package moonstone.user.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.user.model.SubSellerRole;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class SubSellerRoleDao extends MyBatisDao<SubSellerRole> {

    public List<SubSellerRole> findByMasterUserIdAndStatus(Long masterUserId, Integer status) {
        return getSqlSession().selectList(sqlId("findByMasterUserIdAndStatus"),
                ImmutableMap.of("masterUserId", masterUserId, "status", status));
    }

    public List<SubSellerRole> findByMasterUserIdAndName(Long masterUserId, String name) {
        return getSqlSession().selectList(sqlId("findByMasterUserIdAndName"),
                ImmutableMap.of("masterUserId", masterUserId, "name", name));
    }

    public List<SubSellerRole> findByMasterUserIdAndNames(Long masterUserId, List<String> nameList) {
        return getSqlSession().selectList(sqlId("findByMasterUserIdAndNames"),
                ImmutableMap.of("masterUserId", masterUserId, "nameList", nameList));
    }

    public SubSellerRole findByUserIdAndType(Long userId, Integer type) {

        return getSqlSession().selectOne(sqlId("findByUserIdAndType"),
                ImmutableMap.of("userId", userId, "type", type));

    }

    public SubSellerRole findByUserId(Long userId) {
        return getSqlSession().selectOne(sqlId("findByUserId"),
                ImmutableMap.of("userId", userId));
    }
}
