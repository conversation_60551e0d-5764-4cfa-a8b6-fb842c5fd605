package moonstone.user.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import moonstone.user.model.SubSeller;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class SubSellerDao extends MyBatisDao<SubSeller> {

    public SubSeller findByUserId(Long userId) {
        return getSqlSession().selectOne(sqlId("findByUserId"), userId);
    }

    public List<SubSeller> findByMasterUserIdAndRoleIds(Long masterUserId, List<Long> roleIds) {
        return getSqlSession().selectList(sqlId("findByMasterUserIdAndRoleIds"),
                ImmutableMap.of("masterUserId", masterUserId, "roleIds", roleIds));
    }

    public int updateAnything(String sql) {
        return getSqlSession().update(sqlId("updateAnything"), ImmutableMap.of("sql", sql));
    }

    public int deleteAnything(String sql) {
        return getSqlSession().delete(sqlId("deleteAnything"), ImmutableMap.of("sql", sql));
    }

    public int insertAnything(String sql) {
        return getSqlSession().insert(sqlId("insertAnything"), ImmutableMap.of("sql", sql));
    }

    public void ddlAnything(String sql) {
        getSqlSession().update(sqlId("ddlAnything"), ImmutableMap.of("sql", sql));
    }

    public Long countByRoleId(Long roleId) {
        return getSqlSession().selectOne(sqlId("countByRoleId"), ImmutableMap.of("roleId",roleId));
    }
}
