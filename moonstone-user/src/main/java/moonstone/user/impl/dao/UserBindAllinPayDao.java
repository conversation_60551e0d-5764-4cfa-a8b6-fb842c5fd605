package moonstone.user.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.user.entity.UserBindAllinPay;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public class UserBindAllinPayDao extends MyBatisDao<UserBindAllinPay> {

    public boolean save(UserBindAllinPay bindAllinPay) {
        return create(bindAllinPay);
    }

    public Boolean updateById(UserBindAllinPay updateObject) {
        return update(updateObject);
    }

    /**
     * 查询个人会员
     * @param shopId
     * @param userId
     * @return
     */
    public UserBindAllinPay getPersonByShopIdAndUserId(Long shopId, Long userId) {
        return getByShopIdAndUserIdAndAllinpayRoleType(shopId, userId, 3L);
    }

    /**
     * 查询企业会员
     * @param shopId
     * @param userId
     * @return
     */
    public UserBindAllinPay getCompanyByShopIdAndUserId(Long shopId, Long userId) {
        return getByShopIdAndUserIdAndAllinpayRoleType(shopId, userId, 2L);
    }

    public UserBindAllinPay getByBizUserId(String bizUserId) {
        return getSqlSession().selectOne("getByBizUserId",
                ImmutableMap.of("bizUserId", bizUserId));
    }

    public List<UserBindAllinPay> getByOpenIdAndAppId(String openId, String appId, Long allinPayRoleType) {
        return getSqlSession().selectList("getByOpenIdAndAppId",
                ImmutableMap.of("openId", openId,
                        "appId", appId,
                        "allinPayRoleType", allinPayRoleType));
    }

    public UserBindAllinPay getByShopIdAndUserIdAndAllinpayRoleType(Long shopId, Long userId, Long allinpayRoleType) {
        return getSqlSession().selectOne("getByShopIdAndUserIdAndAllinUserType",
                ImmutableMap.of("shopId", shopId,
                        "userId", userId,
                        "allinpayRoleType", allinpayRoleType));
    }

    public UserBindAllinPay getActiveByShopIdAndUserId(Long shopId, Long userId) {
        return getSqlSession().selectOne("getActiveByShopIdAndUserId",
                ImmutableMap.of("shopId", shopId,
                        "userId", userId,
                        "isEnabled", true));
    }
}
