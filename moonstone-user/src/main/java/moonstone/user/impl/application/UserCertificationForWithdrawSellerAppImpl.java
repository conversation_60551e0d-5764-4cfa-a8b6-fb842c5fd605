package moonstone.user.impl.application;

import io.terminus.common.model.Paging;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.model.image.EncryptImageConfig;
import moonstone.common.model.image.ImageViewVerify;
import moonstone.common.model.image.slice.ImageDomainSplitter;
import moonstone.common.model.image.slice.ImageViewBackend;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.user.application.UserCertificationForWithdrawSellerApp;
import moonstone.user.cache.UserProfileCacheHolder;
import moonstone.user.criteria.AccountCertificationCriteria;
import moonstone.user.dto.AccountCertificationForSellerVO;
import moonstone.user.model.AccountCertification;
import moonstone.user.model.StoreProxy;
import moonstone.user.service.AccountCertificationReadService;
import moonstone.user.service.AccountCertificationWriteService;
import moonstone.user.service.StoreProxyReadService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class UserCertificationForWithdrawSellerAppImpl implements UserCertificationForWithdrawSellerApp {
    private final AccountCertificationReadService accountCertificationReadService;
    private final AccountCertificationWriteService accountCertificationWriteService;
    private final UserProfileCacheHolder userProfileCacheHolder;
    private final StoreProxyReadService storeProxyReadService;

    @Override
    public Either<Paging<AccountCertificationForSellerVO>> paging(AccountCertificationCriteria criteria, Long operatorId) {
        try {
            if (!ObjectUtils.isEmpty(criteria.getShopName())) {
                criteria.setUserId(storeProxyReadService.findUserIdByProxyName(criteria.getShopName(), criteria.getShopId()).orElse(-1L));
            }
            Either<Paging<AccountCertification>> pagingEither = accountCertificationReadService.paging(criteria);
            if (!pagingEither.isSuccess()) {
                return Either.error(pagingEither.getErrorMsg());
            }
            List<AccountCertificationForSellerVO> voList = new ArrayList<>(pagingEither.take().getData().size());
            for (AccountCertification accountCertification : pagingEither.take().getData()) {
                // insert user view privilege token, not business require but tech(secure) require
                String imageViewVerify;
                String imageViewApiKey;
                if (operatorId != null) {
                    imageViewApiKey = ImageViewVerify.encode(accountCertification.getUserId(), operatorId);
                    imageViewVerify = ImageViewVerify.generateVerify(imageViewApiKey);
                } else {
                    imageViewApiKey = "";
                    imageViewVerify = "";
                }
                // wrap image url
                Function<String, String> imgUrlConvert = img -> Objects.isNull(img) ? null : String.format(img.contains("?") ? "%s&%s=%s&%s=%s" : "%s?%s=%s&%s=%s", img, EncryptImageConfig.VIEW_API_KEY, imageViewApiKey, EncryptImageConfig.VIEW_API_VERIFY, imageViewVerify);
                AccountCertificationForSellerVO vo = new AccountCertificationForSellerVO();
                // bean property copy
                BeanUtils.copyProperties(accountCertification, vo);
                vo.setShopName(storeProxyReadService.findByShopIdAndUserId(accountCertification.getShopId(), accountCertification.getUserId()).orElseGet(Optional::empty).map(StoreProxy::getProxyShopName).orElse(null));
                vo.setApplierNickName(userProfileCacheHolder.getUserProfileByUserId(accountCertification.getUserId()).getRealName());
                // complete the path if require, because layout limit, the paranaConfig's appUrl can't pass here, so pass it another way
                vo.setFrontImageUrl(imgUrlConvert.apply(ImageDomainSplitter.completeDomain(ImageViewBackend.findByShopId(accountCertification.getShopId()).getImageUrlBackend(), accountCertification.getFrontUrl())));
                vo.setBackImageUrl(imgUrlConvert.apply(ImageDomainSplitter.completeDomain(ImageViewBackend.findByShopId(accountCertification.getShopId()).getImageUrlBackend(), accountCertification.getBackUrl())));
                vo.setAuthAble(accountCertification.authStatus());
                voList.add(vo);
            }
            return Either.ok(new Paging<>(pagingEither.take().getTotal(), voList));
        } catch (Exception exception) {
            log.error("{} fail to paging data[{}]", LogUtil.getClassMethodName(), criteria.toString(), exception);
            return Either.error(Translate.of("查询用户信息失败"));
        }
    }

    @Override
    public Either<Boolean> auth(Long id, Boolean accept, String rejectReason) {
        try {
            return accountCertificationWriteService.auth(id, accept, rejectReason);
        } catch (Exception exception) {
            log.error("{} fail to auth[Id=>{}, accept=>{}, reason=>{}]", LogUtil.getClassMethodName(), id, accept, rejectReason, exception);
            return Either.error(Translate.of("审核失败"));
        }
    }
}
