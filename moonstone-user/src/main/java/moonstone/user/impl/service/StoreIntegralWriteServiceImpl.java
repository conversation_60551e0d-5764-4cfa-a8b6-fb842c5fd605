package moonstone.user.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.user.impl.dao.StoreIntegralDao;
import moonstone.user.model.StoreIntegral;
import moonstone.user.service.StoreIntegralWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/24 14:00
 */
@Service
@Slf4j
public class StoreIntegralWriteServiceImpl implements StoreIntegralWriteService {

    @Autowired
    StoreIntegralDao storeIntegralDao;

    @Override
    public Either<Long> addStoreIntegral(StoreIntegral storeIntegral) {
        try {
            if (storeIntegralDao.create(storeIntegral)) {
                return Either.ok(storeIntegral.getId());
            }
            return Either.error("fail.create.storeIntegral");
        } catch (Exception ex) {
            log.error("{} storeIntegral:{}", LogUtil.getClassMethodName(), storeIntegral);
            return Either.error(ex, "fail.create.storeIntegral");
        }
    }

    @Override
    public Either<Boolean> updateStoreIntegralExtra(Long userId, Long shopId, String extraJson) {
        try {
            return Either.ok(storeIntegralDao.updateStoreIntegralExtra(userId, shopId, extraJson));
        } catch (Exception ex) {
            log.error("{} userId:{} shopId:{}", LogUtil.getClassMethodName(), userId, shopId);
            return Either.error(ex, "fail.create.storeIntegral");
        }
    }

    @Override
    public Either<Boolean> updateStoreIntegral(StoreIntegral storeIntegral) {
        try {
            return Either.ok(storeIntegralDao.update(storeIntegral));
        } catch (Exception ex) {
            log.error("{} update for id:{}", LogUtil.getClassMethodName(), storeIntegral.getId());
            return Either.error(ex, "fail.update.storeIntegral");
        }
    }
}
