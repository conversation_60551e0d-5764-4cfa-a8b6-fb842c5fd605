package moonstone.user.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.user.impl.dao.StoreIntegralCodeDao;
import moonstone.user.model.StoreIntegralCode;
import moonstone.user.service.StoreIntegralCodeWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/19 15:07
 */
@Service
@Slf4j
public class StoreIntegralCodeWriteServiceImpl implements StoreIntegralCodeWriteService {
    @Autowired
    StoreIntegralCodeDao storeIntegralCodeDao;

    @Override
    public Either<Boolean> update(StoreIntegralCode storeIntegralCode) {
        try {
            return Either.ok(storeIntegralCodeDao.update(storeIntegralCode));
        } catch (Exception ex) {
            log.error("{} update for id:{}", LogUtil.getClassMethodName(), storeIntegralCode.getId());
            return Either.error(ex, "fail.update.StoreIntegralCode");
        }
    }

    @Override
    public Either<Boolean> create(StoreIntegralCode data) {
        try {
            return Either.ok(storeIntegralCodeDao.create(data));
        } catch (Exception ex) {
            log.error("{} create for id:{}", LogUtil.getClassMethodName(), data.getNum());
            return Either.error(ex, "fail.create.StoreIntegralCode");
        }
    }
}
