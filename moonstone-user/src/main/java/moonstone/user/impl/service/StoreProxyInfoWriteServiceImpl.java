package moonstone.user.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.user.impl.dao.StoreProxyInfoDao;
import moonstone.user.model.StoreProxyInfo;
import moonstone.user.service.StoreProxyInfoWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/26 14:10
 */
@Service
@Slf4j
public class StoreProxyInfoWriteServiceImpl implements StoreProxyInfoWriteService {

    @Autowired
    StoreProxyInfoDao storeProxyInfoDao;

    @Override
    public Either<Long> create(StoreProxyInfo storeProxyInfo) {
        try {
            if (storeProxyInfoDao.create(storeProxyInfo)) {
                return Either.ok(storeProxyInfo.getId());
            }
            return Either.error("fail.create.storeProxy");
        } catch (Exception ex) {
            log.error("{} storeProxy:{}", LogUtil.getClassMethodName(), storeProxyInfo);
            return Either.error(ex, "fail.create.StoreProxyInfo");
        }
    }

    @Override
    public Either<Boolean> update(StoreProxyInfo storeProxyInfo) {
        try {
            return Either.ok(storeProxyInfoDao.update(storeProxyInfo));
        } catch (Exception ex) {
            log.error("{} update for id:{}", LogUtil.getClassMethodName(), storeProxyInfo.getId());
            return Either.error(ex, "fail.update.StoreProxyInfo");
        }
    }
}
