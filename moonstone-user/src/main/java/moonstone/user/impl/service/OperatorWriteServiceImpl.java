package moonstone.user.impl.service;

import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Params;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.UserStatus;
import moonstone.common.utils.EncryptUtil;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.impl.dao.OperatorDao;
import moonstone.user.impl.dao.OperatorRoleDao;
import moonstone.user.impl.dao.UserDao;
import moonstone.user.impl.manager.UserManager;
import moonstone.user.model.Operator;
import moonstone.user.model.OperatorRole;
import moonstone.user.model.User;
import moonstone.user.service.OperatorWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RpcProvider
public class OperatorWriteServiceImpl implements OperatorWriteService {

    private final UserDao userDao;

    private final OperatorDao operatorDao;

    private final OperatorRoleDao operatorRoleDao;

    private final UserManager userManager;

    private final UserTypeBean userTypeBean;

    @Autowired
    public OperatorWriteServiceImpl(UserDao userDao, OperatorDao operatorDao,
            OperatorRoleDao operatorRoleDao, UserManager userManager, UserTypeBean userTypeBean) {
        this.userDao = userDao;
        this.operatorDao = operatorDao;
        this.operatorRoleDao = operatorRoleDao;
        this.userManager = userManager;
        this.userTypeBean = userTypeBean;
    }

    @Override
    public Response<Long> create(Operator operator) {
        try {
            String un = Params.trimToNull(operator.getUserName());
            if (un == null) {
                log.warn("create operator failed, no username specified");
                return Response.fail("operator.create.fail.no.username");
            }
            String pw = Params.trimToNull(operator.getPassword());
            if (pw == null) {
                log.warn("create operator failed, no password specified");
                return Response.fail("operator.create.fail.no.password");
            }

            User exist = userDao.findByName(un);
            if (exist != null) {
                log.warn("create operator failed, username already exist");
                return Response.fail("user.username.already.exist");
            }

            User u = new User();
            u.setName(un);
            u.setPassword(EncryptUtil.encrypt(pw));
            u.setType(userTypeBean.getOperatorType());
            u.setRoles(userTypeBean.getOperatorRole());
            u.setStatus(UserStatus.NORMAL.value());

            operator.setUserName(un);
            operator.setStatus(1);
            Long roleId = operator.getRoleId();
            operator.setRoleName(findRoleName(roleId));
            Long userId = userManager.createOperator(u, operator);
            return Response.ok(userId);
        } catch (Exception e) {
            log.error("create operator failed, operator={}, cause:{}",
                    operator, Throwables.getStackTraceAsString(e));
            return Response.fail("operator.create.fail");
        }
    }

    @Override
    public Response<Boolean> update(Operator operator) {
        try {
            if (operator.getId() == null) {
                return Response.fail("operator.update.fail");
            }
            Long roleId = operator.getRoleId();
            if (roleId == null) {
                Operator exist = operatorDao.findById(operator.getId());
                roleId = exist.getRoleId();
            }
            operator.setRoleName(findRoleName(roleId));
            return Response.ok(operatorDao.update(operator));
        } catch (Exception e) {
            log.error("update operator failed, operator={}, cause:{}",
                    operator, Throwables.getStackTraceAsString(e));
            return Response.fail("operator.update.fail");
        }
    }

    private String findRoleName(Long roleId) {
        if (roleId == null) {
            return "";
        }
        OperatorRole r = operatorRoleDao.findById(roleId);
        if (r == null) {
            log.warn("role not found, id={}", roleId);
            return "";
        }
        return Strings.nullToEmpty(r.getName());
    }
}
