package moonstone.user.impl.service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.user.impl.dao.AccountCertificationDao;
import moonstone.user.model.AccountCertification;
import moonstone.user.service.AccountCertificationWriteService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@Service
public class AccountCertificationWriteServiceImpl implements AccountCertificationWriteService {
    private final AccountCertificationDao accountCertificationDao;

    @Override
    public Either<Long> save(AccountCertification accountCertification) {
        accountCertificationDao.saveAndUpdateWhenDuplicate(accountCertification);
        return Either.ok(accountCertificationDao.findByUserIdAndShopId(accountCertification.getUserId(), accountCertification.getShopId()).getId());
    }

    @Override
    public Either<Boolean> auth(Long id, Boolean accept, String rejectReason) {
        try {
            AccountCertification accountCertification = accountCertificationDao.findById(id);
            if (accept) {
                accountCertification.authStatus().auth();
            } else {
                accountCertification.authStatus().reject();
                accountCertification.setReason(rejectReason);
            }
            return Either.ok(accountCertificationDao.update(accountCertification));
        } catch (Exception exception) {
            log.error("{} fail to auth[Id=>{}, accept=>{}, reason=>{}]", LogUtil.getClassMethodName(), id, accept, rejectReason, exception);
            return Either.error(Translate.of("审核失败"));
        }
    }
}
