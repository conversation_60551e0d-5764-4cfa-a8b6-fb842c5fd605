/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.user.impl.service;

import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.CountryCode;
import moonstone.common.model.Either;
import moonstone.common.utils.B3Util;
import moonstone.common.utils.EncryptUtil;
import moonstone.common.utils.LogUtil;
import moonstone.user.impl.dao.UserDao;
import moonstone.user.model.LoginType;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.User;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.UserReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Stream;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-28
 */
@Service
@Slf4j
@RpcProvider
public class UserReadServiceImpl implements UserReadService<User> {

    private final UserDao userDao;


    private final StoreProxyReadService storeProxyReadService;

    @Autowired
    public UserReadServiceImpl(UserDao userDao, StoreProxyReadService storeProxyReadService) {
        this.userDao = userDao;
        this.storeProxyReadService = storeProxyReadService;
    }

    /**
     * 根据用户id查询用户信息
     *
     * @param id 用户id
     * @return 对应的用户id
     */
    @Override
    public Response<User> findById(Long id) {
        try {
            User user = userDao.findById(id);
            if (user == null) {
                log.error("user(id={}) is not found", id);
                return Response.fail("user.not.found");
            }
            return Response.ok(user);
        } catch (Exception e) {
            log.error("failed to find user(id={}), cause:{}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("user.find.fail");
        }
    }

    @Override
    public Response<User> findByMobile(String mobile){
        try {
            if (mobile == null) {
                return Response.fail("user.not.found");
            }
            User user = userDao.findByMobile(mobile);
            if (user == null) {
                if (!mobile.startsWith(CountryCode.PREFIX_CODE)) {
                    //default old which is impossible but fuck others
                    user = userDao.findByMobile(CountryCode.China.getCode() + mobile);
                } else if (mobile.startsWith(CountryCode.China.getCode())) {
                    //try to find old user that mobile number didnt follow the new rule
                    user = userDao.findByMobile(mobile.substring(CountryCode.PREFIX_CODE_LEN));
                }
            }
            //fuck here!that leave an error that cheat us
            if (user == null) {
                log.error("user(mobile={}) is not found", mobile);
                return Response.fail("user.not.found");
            }
            return Response.ok(user);
        } catch (Exception e) {
            log.error("failed to find user(mobile={}), cause: {}", mobile, Throwables.getStackTraceAsString(e));
            return Response.fail("user.find.fail");
        }
    }

    @Override
    public Response<List<User>> findLikeMobile(String mobile){
        try {
            if (mobile == null || mobile.startsWith(CountryCode.PREFIX_CODE)) {
                List<User> users = userDao.findLikeMobile(mobile);
                return Response.ok(users);
            } else {
                List<User> users = userDao.findLikeMobile(mobile);
                Stream.of(CountryCode.values()).map(CountryCode::getCode)
                        .forEach(code -> users.addAll(userDao.findLikeMobile(code.concat(mobile))));
                return Response.ok(users);
            }
        } catch (Exception e) {
            log.error("failed to find users mobile like {}, cause: {}", mobile, Throwables.getStackTraceAsString(e));
            return Response.fail("user.find.fail");
        }
    }

    @Override
    public Response<List<User>> findByIds(List<Long> ids) {
        try {
            return Response.ok(userDao.findByIds(ids));
        } catch (Exception e) {
            log.error("find users by ids={} failed, cause:{}", ids, Throwables.getStackTraceAsString(e));
            return Response.fail("user.find.fail");
        }
    }

    @Override
    public Response<List<Long>> findIdsByName(String name){
        try{
            return Response.ok(userDao.findIdsLikeName(name));
        } catch (Exception e){
            log.error("fail to find userIds by name={}, cause:{}",name,Throwables.getStackTraceAsString(e));
            return Response.fail("user.ids.find.fail");
        }
    }

    @Override
    public Response<List<Long>> findIdsLikeMobile(String mobile){
        try{
            List<Long> ids = userDao.findIdsLikeMobile(mobile);
            if (mobile == null || mobile.startsWith("00")) {
                return Response.ok(ids);
            } else {
                Stream.of(CountryCode.values()).map(CountryCode::getCode).forEach(code -> userDao.findLikeMobile(code.concat(mobile)));
                return Response.ok(ids);
            }
        } catch (Exception e){
            log.error("fail to find userIds by mobile={}, cause:{}",mobile,Throwables.getStackTraceAsString(e));
            return Response.fail("user.ids.find.fail");
        }
    }

    @Override
    public Response<List<Long>> findIdsLikeMobileInIds(String mobile, List<Long> ids){
        try{
            return Response.ok(userDao.findIdsLikeMobileInIds(mobile, ids));
        } catch (Exception e){
            log.error("fail to find userIds like mobile={} in ids={}, cause:{}",mobile,ids,Throwables.getStackTraceAsString(e));
            return Response.fail("user.ids.find.fail");
        }
    }

    /**
     * 根据用户标识查询客户
     *
     * @param loginId   用户标识
     * @param loginType 用户标志类型
     * @return 对应的用户
     */
    @Override
    public Response<User> findBy(String loginId, LoginType loginType) {
        try {
            log.debug("[op:login] loginId={}, loginType={}", loginId, loginType.toString());
            User user;
            switch (loginType) {
                case NAME:
                    user = userDao.findByName(loginId);
                    break;
                case EMAIL:
                    user = userDao.findByEmail(loginId);
                    break;
                case MOBILE:
                    user = userDao.findByMobile(loginId);
                    if (user == null && loginId.startsWith(CountryCode.China.getCode())) {
                        user = userDao.findByMobile(loginId.substring(CountryCode.PREFIX_CODE_LEN));
                    }
                    break;
                default:
                    return Response.fail("user.not.found");
            }
            if (user == null) {
                log.error("user(loginId={}, loginType={}) not found", loginId, loginType);
                return Response.fail("user.not.found");
            }
            return Response.ok(user);
        } catch (Exception e) {
            log.error("failed to find user(loginId={}, loginType={}), cause:{}",
                    loginId, loginType, Throwables.getStackTraceAsString(e));
            return Response.fail("user.find.fail");
        }
    }

    /**
     * 根据用户名分页模糊查找用户列表
     * @param userName 用户名
     * @param pageNo 页码
     * @param pageSize 页内消息数量
     * @return  用户列表
     */
    @Override
    public Response<List<User>> searchUsersByName(String userName,Integer pageNo,Integer pageSize) {
        try {
            PageInfo page = new PageInfo(pageNo, pageSize);
            List<User> list = userDao.searchUsersByName(userName,page.getLimit(),page.getOffset());
            return Response.ok(list);
        } catch (Exception e) {
            log.error("find users by userName={} failed, cause:{}", userName, Throwables.getStackTraceAsString(e));
            return Response.fail("user.find.fail");
        }
    }
    /**
     * 模糊查找记录总数
     * @param userName 姓名
     * @return 记录总数
     */
    @Override
    public Response<Long> searchUsersCountByName(String userName) {
        try {
            return Response.ok(userDao.searchUsersCountByName(userName));
        }catch (Exception e){
            log.error("faild to count users by name = {}", userName);
            return Response.fail("user.find.fail");
        }
    }

    @Override
    public Response<Boolean> checkExist(String loginId, LoginType loginType) {
        try {
            User user;
            switch (loginType) {
                case NAME:
                    user = userDao.findByName(loginId);
                    break;
                case EMAIL:
                    user = userDao.findByEmail(loginId);
                    break;
                case MOBILE:
                    user = userDao.findByMobile(loginId);
                    if (loginId != null && user == null && loginId.startsWith(CountryCode.China.getCode())) {
                        user = userDao.findByMobile(loginId.substring(CountryCode.PREFIX_CODE_LEN));
                    }
                    break;
                default:
                    return Response.fail("user.unknown.login.type");
            }
            return Response.ok(user != null);
        } catch (Exception e) {
            log.error("check user existing failed, loginId={}, loginType={}, cause:{}",
                    loginId, loginType, Throwables.getStackTraceAsString(e));
            return Response.fail("user.find.fail");
        }
    }

    /**
     * 用户登录, 首先检查用户是否存在, 其次检查状态是否正常, 最后检查密码是否匹配
     *
     * @param loginId   登录id
     * @param password  登录密码
     * @param loginType 登录类型
     * @return 如果登录成功, 则返回对应的用户
     */
    @Override
    public Response<User> login(String loginId, String password, LoginType loginType) {
        log.debug("[op:login] loginId={}, password=[MD5:{}], loginType={}", loginId, B3Util.b3sumStr(password.getBytes(StandardCharsets.UTF_8)), loginType.toString());
        Response<User> rUser = findBy(loginId, loginType);
        if (!rUser.isSuccess()) {
            return rUser;
        }
        User user = rUser.getResult();
        if (user.getStatus() < 0) {
            log.error("user(loginId={}, loginType={})'s status is {}, login forbidden ",
                    loginId, loginType, user.getStatus() );
            return switch (user.getStatus()) {
                case -1 -> Response.fail("user.status.locked");
                case -2 -> Response.fail("user.status.frozen");
                case -3 -> Response.fail("user.status.deleted");
                default -> Response.fail("user.status.abnormal");
            };
        }
        //处理经销商和门店 由于刚刚注册没有密码因此用手机号后六位来当登录密码
        Either<Optional<StoreProxy>> optionalResult = storeProxyReadService.findByUserId(user.getId());
        if (optionalResult.isSuccess() && optionalResult.take().isPresent()
                && !ObjectUtils.isEmpty(optionalResult.take()) && Objects.equals(optionalResult.take().get().getStatus(), 3)) {
            rUser.getResult().setName(optionalResult.take().get().getProxyShopName());
            if (ObjectUtils.isEmpty(rUser.getResult().getRoles())) {
                rUser.getResult().setRoles(Collections.singletonList("SUB_SELLER"));
            }
            if (ObjectUtils.isEmpty(user.getPassword())
                    && Objects.equals(password, user.getMobile().substring(user.getMobile().length() - 6))) {
                return rUser;
            }
            if (ObjectUtils.isEmpty(user.getPassword()) && !Objects.equals(password, user.getMobile().substring(user.getMobile().length() - 6))) {
                return Response.fail("账户或密码错误");
            }
        }

        if(!EncryptUtil.match(password, user.getPassword())){
            log.error("user(loginId={}, loginType={})'s password mismatch, login failed",loginId, loginType );
            return Response.fail("账户或密码错误");
        }
        // if the password is the old, update it
        if (user.getPassword().contains("@") && !user.getPassword().contains("#")){
            // update the password
            String b3Password = EncryptUtil.encrypt(password);
            Long userId = user.getId();
            User updatePassword = new User();
            updatePassword.setId(userId);
            updatePassword.setPassword(b3Password);
            userDao.update(updatePassword);
            rUser.getResult().setPassword(b3Password);
        }
        return rUser;
    }

    /**
     * 分页查找用户列表
     *
     * @param id 用户的id
     * @param username 用户名
     * @param email 用户邮箱
     * @param mobile 用户手机号
     * @param status 用户状态
     * @param type 用户类型
     * @param createdFrom 创建时间左区间(闭)
     * @param createdTo 创建时间右区间(开), 若只精确到天, 传入时自行+1天
     * @param pageNo 当前页码
     * @param pageSize 每页大小
     * @return 分页显示的用户列表
     */
    @Override
    public Response<Paging<User>> paging(Long id,
                                         String username, String email, String mobile,
                                         Integer status, Integer type,
                                         Date createdFrom, Date createdTo, Integer pageNo, Integer pageSize) {
        return paging(id, username, email, mobile, status, type,
                createdFrom, createdTo, null, null, pageNo, pageSize);
    }

    /**
     * 分页查找用户列表
     *
     * @param id 用户的id
     * @param username 用户名
     * @param email 用户邮箱
     * @param mobile 用户手机号
     * @param status 用户状态
     * @param type 用户类型
     * @param createdFrom 创建时间左区间(闭)
     * @param createdTo 创建时间右区间(开), 若只精确到天, 传入时自行+1天
     * @param sortBy 排序条件
     * @param sortType 排序规则
     * @param pageNo 当前页码
     * @param pageSize 每页大小
     * @return 分页显示的用户列表
     */
    @Override
    public Response<Paging<User>> paging(Long id,
                                         String username, String email, String mobile,
                                         Integer status, Integer type, Date createdFrom, Date createdTo,
                                         String sortBy, Integer sortType, Integer pageNo, Integer pageSize) {
        Map<String, Object> params = Maps.newHashMap();
        try {
            if (id != null) {
                params.put("id", id);
            }
            if (status != null) {
                params.put("status", status);
            }
            if (type != null) {
                params.put("type", type);
            }
            if (StringUtils.hasText(username)) {
                params.put("name", username);
            }
            if (StringUtils.hasText(email)) {
                params.put("email", email);
            }
            if (StringUtils.hasText(mobile)) {
                params.put("mobile", mobile);
            }
            if (createdFrom != null) {
                params.put("createdFrom", createdFrom);
            }
            if (createdTo != null) {
                params.put("createdTo", createdTo);
            }
            if (sortBy != null) {
                params.put("sortBy", sortBy);
            }
            if (sortType != null) {
                params.put("sortType", sortType);
            }

            PageInfo page = PageInfo.of(pageNo, pageSize);

            Paging<User> userPage = userDao.paging(page.getOffset(), page.getLimit(), params);
            return Response.ok(userPage);
        } catch (Exception e) {
            log.error("fail to page user by params:{}, cause:{}", params, Throwables.getStackTraceAsString(e));
            return Response.fail("user.find.fail");
        }
    }

    public Response<List<Long>> findLikeMobileAndName(String mobile, String name) {
        try {
            if (mobile == null || mobile.startsWith(CountryCode.PREFIX_CODE)) {
                return Response.ok(userDao.findLikeMobileAndName(mobile, name));
            } else {
                List<Long> ids = userDao.findLikeMobileAndName(mobile, name);
                Stream.of(CountryCode.values()).map(CountryCode::getCode)
                        .forEach(code -> ids.addAll(userDao.findLikeMobileAndName(code.concat(mobile), name)));
                return Response.ok(ids);
            }
        } catch (Exception ex) {
            log.error("[UserReadService](findLikeMobileAndName) fail by mobile:{} name:{}", mobile, name);
            ex.printStackTrace();
            return Response.fail("user.find.fail");
        }
    }

    @Override
    public Either<Long> countAll() {
        try {
            return Either.ok(userDao.countAll());
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{}", LogUtil.getClassMethodName());
            return Either.error(ex);
        }
    }


}
