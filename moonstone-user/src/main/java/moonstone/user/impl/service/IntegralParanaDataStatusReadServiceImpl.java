package moonstone.user.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.user.impl.dao.IntegralParanaDataStatusDao;
import moonstone.user.model.IntegralParanaDataStatus;
import moonstone.user.service.IntegralParanaDataStatusReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/11/4 16:19
 */
@Slf4j
@Service
@RpcProvider
public class IntegralParanaDataStatusReadServiceImpl implements IntegralParanaDataStatusReadService {

    @Autowired
    IntegralParanaDataStatusDao integralParanaDataStatusDao;

    @Override
    public Response<List<IntegralParanaDataStatus>>findIntegralParanaDataStatusByBatch(String batch,Long shopId) {
        try {
            return Response.ok(integralParanaDataStatusDao.findIntegralParanaDataStatusByBatch(batch,shopId));
        } catch (Exception e) {
            log.error("find IntegralParanaDataStatus by batch={} failed, cause:{}",
                    batch, Throwables.getStackTraceAsString(e));
            return Response.fail("operator.role.find.fail");
        }
    }

    @Override
    public Either<Optional<IntegralParanaDataStatus>> findIntegralParanaDataStatusById(Long id) {
        try {
            return Either.ok(Optional.ofNullable(integralParanaDataStatusDao.findIntegralParanaDataStatusById(id)));
        } catch (Exception e) {
            log.error("find IntegralParanaDataStatus by id={} failed, cause:{}",
                    id, Throwables.getStackTraceAsString(e));
            return Either.fail();
        }
    }
}
