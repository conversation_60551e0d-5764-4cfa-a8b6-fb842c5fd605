package moonstone.user.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.user.impl.dao.StoreProxyInfoDao;
import moonstone.user.model.StoreProxyInfo;
import moonstone.user.service.StoreProxyInfoReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/26 14:10
 */
@Service
@Slf4j
public class StoreProxyInfoReadServiceImpl implements StoreProxyInfoReadService {
    @Autowired
    StoreProxyInfoDao storeProxyInfoDao;

    @Override
    public Either<Optional<StoreProxyInfo>> findByProxyId(Long proxyId) {
        try {
            return Either.ok(Optional.ofNullable(storeProxyInfoDao.findByProxyId(proxyId)));
        } catch (Exception ex) {
            log.error("{} id:{}", LogUtil.getClassMethodName(), proxyId);
            ex.printStackTrace();
            return Either.error(ex, "fail");
        }
    }

    @Override
    public Either<Long> getSubProxyInfo(List<Long> ids, long status) {
        try {
            return Either.ok(Optional.ofNullable(storeProxyInfoDao.getSubProxyInfo(ids, status)).orElse(0L));
        } catch (Exception ex) {
            log.error("{} shopId:{} userId:{}", LogUtil.getClassMethodName(), ids, status);
            ex.printStackTrace();
            return Either.error(ex, "fail");
        }
    }

    @Override
    public Either<List<StoreProxyInfo>> getCountSubProxyInfo(List<Long> proxyIds, Integer status) {
        return Either.ok(storeProxyInfoDao.getCountSubProxyInfo(proxyIds, status));
    }
}
