<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="WeShopSku">

    <resultMap id="WeShopSkuMap" type="WeShopSku">
        <id property="id" column="id"/>
        <result column="we_shop_id" property="weShopId"/>
        <result column="shop_id" property="shopId"/>
        <result column="sku_id" property="skuId"/>
        <result column="item_id" property="itemId"/>
        <result column="we_shop_item_id" property="weShopItemId"/>
        <result column="diff_price" property="diffPrice"/>
        <result column="price" property="price"/>
        <result column="tax" property="tax"/>
        <result column="profit" property="profit"/>
        <result column="tax_seller_bear" property="taxSellerBear"/>
        <result column="extra_str" property="extraStr"/>
        <result column="tags_str" property="tagsStr"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        we_shop_skus
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `we_shop_id`, `shop_id`, `sku_id`, `item_id`, `diff_price`, `price`, `profit`, `tax`, we_shop_item_id,
        `tax_seller_bear`, `extra_str`, `tags_str`, `status`, `created_at`, `updated_at`
    </sql>

    <sql id="vals">
        #{weShopId}, #{shopId}, #{skuId}, #{itemId}, #{diffPrice}, #{price}, #{profit}, #{tax}, #{weShopItemId},
        #{taxSellerBear}, #{extraStr}, #{tagStr}, #{status}, now(), now()
    </sql>

    <sql id="criteria">
        <if test="ids != null">AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="weShopId != null">
            we_shop_id = #{weShopId}
        </if>
        <if test="shopId != null">
            shop_id = #{shopId}
        </if>
        <if test="itemId != null">
            item_id = #{itemId}
        </if>
        <if test="diffPrice != null">
            diff_price = #{diffPrice}
        </if>
        <if test="price != null">
            price = #{price}
        </if>
        <if test="statuses == null and status == null">
            AND `status` != -99
        </if>
        <if test="status != null">AND status = #{status}</if>
        <if test="statuses != null">
            and status in
            <foreach collection="statuses" open="(" close=")" separator="," item="s">
                #{s}
            </foreach>
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'updatedAt'">ORDER BY updated_at
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'createdAt'">ORDER BY created_at
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <insert id="create" parameterType="WeShop" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.weShopId}, #{i.shopId}, #{i.skuId}, #{i.itemId}, #{i.diffPrice}, #{i.price}, #{i.profit}, #{i.tax},
            #{i.weShopItemId},
            #{i.extraStr}, #{i.tagsStr}, #{i.status}, now(), now()
            )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="WeShopSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="WeShopSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findWeShopIdAndSkuId" parameterType="map" resultMap="WeShopSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE we_shop_id = #{weShopId} and sku_id = #{skuId} and `status` != #{notStatus} order by id desc limit 1
    </select>

    <select id="findByWeShopId" parameterType="long" resultMap="WeShopSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE we_shop_id = #{weShopId} and status != -99
    </select>
    <select id="findByWeShopIdAndItemId" parameterType="map" resultMap="WeShopSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE we_shop_id = #{weShopId} and item_id = #{itemId} and status != -99 limit 1
    </select>

    <select id="findListByWeShopIdAndItemId" parameterType="map" resultMap="WeShopSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE we_shop_id = #{weShopId} and item_id = #{itemId}
    </select>
    <select id="findByWeShopItemId" parameterType="long" resultMap="WeShopSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE we_shop_item_id = #{weShopItemId} and status != -99
    </select>

    <update id="update" parameterType="WeShop">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="weShopId != null">we_shop_Id = #{weShopId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="skuId != null">sku_id = #{skuId},</if>
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="weShopItemId != null">we_shop_item_id = #{weShopItemId},</if>
            <if test="taxSellerBear!= null">tax_seller_bear = #{taxSellerBear},</if>
            <if test="diffPrice != null">diff_price = #{diffPrice}, price = null,</if>
            <if test="price != null">price = #{price}, diff_price = null,</if>
            <if test="tax != null">tax = #{tax},</if>
            <if test="profit != null">`profit` = #{profit},</if>
            <if test="tagStr != null">tag_str = #{tagStr},</if>
            <if test="extraStr != null">extra_str = #{extraStr},</if>
            <if test="status != null">`status` = #{status},</if>
            `updated_at` = now()
        </set>
        WHERE id = #{id} and status != -99
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id = #{id}
    </update>

    <update id="batchUpdateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id IN
        <foreach item="id" collection="ids"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <update id="deletes" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET status=-1
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="WeShopSkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>
    <update id="updateStatusByWeShopIdAndItemId" parameterType="map">
        update
        <include refid="tb"/>
        <set>
            `status` = #{status}
        </set>
        <where>
            we_shop_id = #{weShopId}
            and item_id = #{itemId}
        </where>
    </update>

    <delete id="deleteByWeShopIdAndWeShopItemId" parameterType="map">
        update
        <include refid="tb"/>
        <set>
            `status` = -1
        </set>
        <where>
            we_shop_id = #{weShopId}
            and we_shop_item_id = #{weShopItemId}
        </where>
    </delete>

    <select id="maxId" resultType="long">
        SELECT MAX(id) FROM
        <include refid="tb"/>
    </select>

    <insert id="createOrUpdate" parameterType="WeShopSku" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
        ON DUPLICATE KEY UPDATE
        diff_price = #{diffPrice},
        price = #{price},
        tax = #{tax},
        profit = #{profit},
        tax_seller_bear = #{taxSellerBear},
        status = #{status},
        extra_str = #{extraStr},
        sku_id = #{skuId},
        <if test="weShopItemId!= null">we_shop_item_id = #{weShopItemId},</if>
        <if test="tagStr != null">tag_str = #{tagStr},</if>
        updated_at = #{updatedAt}
    </insert>

</mapper>
