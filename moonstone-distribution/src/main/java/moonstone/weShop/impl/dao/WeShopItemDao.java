package moonstone.weShop.impl.dao;

import com.google.common.base.MoreObjects;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.common.model.PagingCriteria;
import moonstone.weShop.model.WeShopItem;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by CaiZhy on 2018/12/10.
 */
@Repository
public class WeShopItemDao extends MyBatisDao<WeShopItem> {
    public List<WeShopItem> findByWeShopId(Long weShopId) {
        return getSqlSession().selectList(sqlId("findByWeShopId"), weShopId);
    }

    public void modifySellQuantityById(Long id, Long quantity) {
        getSqlSession().update(sqlId("modifySellQuantityById"), ImmutableMap.of("id", id, "quantity", quantity));
    }

    public List<WeShopItem> findByWeShopIdAndShopId(Long weShopId, Long shopId) {
        return getSqlSession().selectList(sqlId("findByWeShopIdAndShopId"), ImmutableMap.of("weShopId", weShopId, "shopId", shopId));
    }

    public List<WeShopItem> pagingOnSellItemByWeShopIds(List<Long> weShopIds, PagingCriteria pagingCriteria) {
        return getSqlSession().selectList(sqlId("pagingOnSellItemByWeShopIds"), ImmutableMap.of("weShopIds", weShopIds, "offset", pagingCriteria.getOffset(), "limit", pagingCriteria.getLimit()));
    }

    public List<WeShopItem> pagingByWeShopIds(List<Long> weShopIds, PagingCriteria pagingCriteria) {
        return getSqlSession().selectList(sqlId("pagingByWeShopIds"), ImmutableMap.of("weShopIds", weShopIds, "offset", pagingCriteria.getOffset(), "limit", pagingCriteria.getLimit()));
    }

    public List<WeShopItem> findByWeShopIds(List<Long> weShopIds) {
        return getSqlSession().selectList(sqlId("findByWeShopIds"), weShopIds);
    }

    public List<WeShopItem> findByItemIds(List<Long> itemIds) {
        if (itemIds.size() == 0)
            return new ArrayList<>();
        return getSqlSession().selectList(sqlId("findByItemIds"), itemIds);
    }

    public List<WeShopItem> findByItemId(Long itemId) {
        return getSqlSession().selectList(sqlId("findByItemId"), itemId);
    }

    public List<WeShopItem> findListByWeShopIdAndItemId(Long weShopIds, Long itemId) {
        return getSqlSession().selectList(sqlId("findListByWeShopIdAndItemId"),
                ImmutableMap.of("weShopId", weShopIds, "itemId", itemId));
    }

    public WeShopItem findByWeShopIdAndItemId(Long weShopId, Long itemId) {
        return getSqlSession().selectOne(sqlId("findByWeShopIdAndItemId"),
                ImmutableMap.of("weShopId", weShopId, "itemId", itemId));
    }

    public Boolean updateStatus(Long id, Integer status) {
        return getSqlSession().update(sqlId("updateStatus"), ImmutableMap.of("id", id, "status", status)) == 1;
    }

    public Boolean updateStatusByShopId(Long shopId, Integer status) {
        return getSqlSession().update(sqlId("updateStatusByShopId"), ImmutableMap.of("shopId", shopId, "status", status)) > 1;
    }

    /**
     * 获取某微分销店铺首页显示的最大排序
     * @param weShopId 微分销店铺id
     * @return 最大排序
     */
    public Long maxSortIndexByWeShopIdIndexDisplay(long weShopId)
    {
        return getSqlSession().selectOne(sqlId("maxSortIndexByWeShopIdIndexDisplay"), weShopId);
    }

    /**
     * 获取某微分销店铺已上架的数量
     * @param weShopId 微分销店铺id
     * @return 数量
     */
    public Long countOnSellItemByWeShopId(long weShopId)
    {
        return getSqlSession().selectOne(sqlId("countOnSellItemByWeShopId"), weShopId);
    }

    /**
     * 获取某微分销店铺首页显示的数量
     * @param weShopId 微分销店铺id
     * @return 数量
     */
    public Long countIndexDisplayByWeShopId(long weShopId)
    {
        return getSqlSession().selectOne(sqlId("countIndexDisplayByWeShopId"), weShopId);
    }

    /**
     * 微分销商品当前最大的id, 这个是dump搜素引擎用的
     *
     * @return 当前最大的id
     */
    public Long maxId() {
        Long id = getSqlSession().selectOne(sqlId("maxId"));
        return MoreObjects.firstNonNull(id, 0L);
    }

    /**
     * 指定微分销店铺内微分销商品当前最大的id, 这个是dump搜素引擎用的
     *
     * @param weShopId 微分销店铺id
     * @return 店铺内当前最大的商品id
     */
    public Long maxIdByShopId(Long weShopId) {
        Long id = getSqlSession().selectOne(sqlId("maxIdByWeShopId"), weShopId);
        return MoreObjects.firstNonNull(id, 0L);
    }

    /**
     * 查询id小于lastId内且更新时间大于since的limit个微分销商品, 这个是dump搜素引擎用的
     *
     * @param lastId lastId 最大的微分销店铺id
     * @param since  起始更新时间
     * @param limit  微分销商品个数
     * @return id小于lastId内且更新时间大于since的limit个微分销店铺
     */
    public List<WeShopItem> listSince(Long lastId, String since, int limit) {
        return getSqlSession().selectList(sqlId("listSince"),
                ImmutableMap.of("lastId", lastId, "limit", limit, "since", since));
    }

    /**
     * 根据微分销店铺id查询id小于lastId内的limit个微分销商品, 这个是dump搜素引擎用的
     *
     * @param weShopId 微分销店铺id
     * @param lastId   lastId 最大的微分销商品id
     * @param limit    微分销商品个数
     * @return id小于lastId内的limit个微分销店铺
     */
    public List<WeShopItem> listByShopId(Long weShopId, Long lastId, int limit) {
        return getSqlSession().selectList(sqlId("listByWeShopId"),
                ImmutableMap.of("weShopId", weShopId, "lastId", lastId, "limit", limit));
    }

    /**
     * 更新商品状态
     *
     * @param status 状态
     * @param itemId 商品Id
     * @return 状态
     */
    public Boolean updateStatusByItemId(int status, Long itemId) {
        return getSqlSession().update(sqlId("updateStatusByItemId"), ImmutableMap.of("itemId", itemId, "status", status)) > 0;
    }

    public Long countByWeShopIdAndStatus(long weShopId, Integer status) {
        return getSqlSession().selectOne(sqlId("countByWeShopIdAndStatus"), ImmutableMap.of("weShopId", weShopId, "status", status == null ? -99 : status));
    }

    public List<Long> findByWeShopIdAndStatus(Long weShopId, int itemStatus) {
        return getSqlSession().selectList(sqlId("findByWeShopIdAndStatus"), ImmutableMap.of("weShopId", weShopId, "itemStatus", itemStatus));
    }

    public Boolean increaseSellQuantity(Integer quantity, Long itemId, long weShopId) {
        return getSqlSession().update(sqlId("increaseSellQuantity"), ImmutableMap.of("sellQuantity", quantity, "itemId", itemId, "weShopId", weShopId)) > 0;

    }
}
