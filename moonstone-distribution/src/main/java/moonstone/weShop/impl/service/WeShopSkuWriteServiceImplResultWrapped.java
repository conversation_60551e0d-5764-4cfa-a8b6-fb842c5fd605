package moonstone.weShop.impl.service;

import moonstone.common.model.Either;
import moonstone.weShop.impl.dao.WeShopSkuDao;
import moonstone.weShop.model.WeShopSku;
import moonstone.weShop.service.WeShopSkuWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class WeShopSkuWriteServiceImplResultWrapped implements WeShopSkuWriteService {
    @Autowired
    private WeShopSkuDao weShopSkuDao;

    @Override
    public Either<Long> create(WeShopSku weShopSku) {
        try {
            weShopSku.setId(null);
            weShopSkuDao.create(weShopSku);
        } catch (Exception e) {
            return Either.error(e);
        }
        return Either.ok(weShopSku.getId());
    }

    @Override
    public Either<Long> update(WeShopSku weShopSku) {
        if (weShopSku.getId() == null) {
            return Either.error("require.id.notNull");
        }
        weShopSkuDao.update(weShopSku);
        return Either.ok(weShopSku.getId());
    }

    @Override
    public Either<Boolean> updateStatusByWeShopIdAndItemId(Long weShopId, Long itemId, int status) {
        weShopSkuDao.updateStatusByWeShopIdAndItemId(weShopId, itemId, status);
        return Either.ok(true);
    }

    @Override
    public Either<Boolean> deleteWeShopSkuByWeShopItemId(Long weShopId, Long weShopItemId) {
        weShopSkuDao.deleteByWeShopIdAndWeShopItemId(weShopId, weShopItemId);
        return Either.ok(true);
    }

    @Override
    public Either<Long> createOrUpdate(WeShopSku weShopSku) {
        try {
            return Either.ok(weShopSkuDao.createOrUpdate(weShopSku));
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    @Override
    public Either<Boolean> updateStatusByWeShopItemId(Long weShopItemId, int status) {
        try {
            for (WeShopSku weShopSku : weShopSkuDao.findByWeShopItemId(weShopItemId)) {
                WeShopSku updateStatus = new WeShopSku();
                updateStatus.setId(weShopSku.getId());
                updateStatus.setStatus(status);
                weShopSkuDao.update(updateStatus);
            }
            return Either.ok(true);
        } catch (Exception e) {
            return Either.error(e);
        }
    }
}
