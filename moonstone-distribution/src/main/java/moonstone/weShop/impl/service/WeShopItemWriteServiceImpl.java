package moonstone.weShop.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.weShop.impl.dao.WeShopItemDao;
import moonstone.weShop.impl.dao.WeShopSkuDao;
import moonstone.weShop.impl.manager.WeShopItemManager;
import moonstone.weShop.model.WeShopItem;
import moonstone.weShop.model.WeShopSku;
import moonstone.weShop.service.WeShopItemWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @date 2018/12/10
 */
@Slf4j
@Service
@RpcProvider
public class WeShopItemWriteServiceImpl implements WeShopItemWriteService {
    @Autowired
    private WeShopItemDao weShopItemDao;
    @Autowired
    private WeShopItemManager weShopItemManager;
    @Autowired
    private WeShopSkuDao weShopSkuDao;

    @Override
    public Either<Boolean> modifySellQuantityById(Long id, Long quantity) {
        try {
            if (id == null || quantity == null) {
                return Either.error(Translate.exceptionOf("Id => %s Quantity => %s 不得为null"));
            }
            weShopItemDao.modifySellQuantityById(id, quantity);
            return Either.ok(true);
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    @Override
    public Response<Long> create(WeShopItem weShopItem) {
        try {
            weShopItemDao.create(weShopItem);
            return Response.ok(weShopItem.getId());
        } catch (Exception e) {
            log.error("fail to create weShop item({}), cause: {}", weShopItem, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopItem.create.fail");
        }
    }

    @Override
    public Response<Boolean> update(WeShopItem weShopItem) {
        try {
            Boolean result = weShopItemDao.update(weShopItem);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("fail to update weShop item({}), cause: {}", weShopItem, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopItem.update.fail");
        }
    }

    @Override
    public Response<Boolean> batchUpdate(List<WeShopItem> weShopItems) {
        try {
            weShopItemManager.batchUpdate(weShopItems);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to batch update weShop items({}), cause: {}", weShopItems, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopItem.update.fail");
        }
    }

    @Override
    public Response<Boolean> updateStatus(Long weShopItemId, Integer status) {
        try {
            Boolean result = weShopItemDao.updateStatus(weShopItemId, status);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("fail to update weShop item status by id={}, status={}, cause: {}", weShopItemId, status, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopItem.update.fail");
        }
    }

    @Override
    public Response<Boolean> updateStatusByShopId(Long shopId, Integer status) {
        try {
            Boolean result = weShopItemDao.updateStatusByShopId(shopId, status);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("fail to update weShop item status by shopId={}, status={}, cause: {}", shopId, status, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopItem.update.fail");
        }
    }

    @Override
    public Response<Boolean> delete(Long weShopId) {
        try {
            Boolean result = weShopItemDao.delete(weShopId);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("fail to delete weShop item by id={}, cause: {}", weShopId, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopItem.delete.fail");
        }
    }

    @Override
    public Response<Boolean> updateStatusByItemId(Long itemId, int status) {
        try {
            return Response.ok(weShopItemDao.updateStatusByItemId(status, itemId));
        } catch (Exception ex) {
            log.error("{} update status[{}] by item[{}] fail", LogUtil.getClassMethodName(), status, itemId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> increaseSellQuantity(Integer quantity, Long itemId, long weShopId) {
        try {
            return Response.ok(weShopItemDao.increaseSellQuantity(quantity, itemId, weShopId));
        } catch (Exception exception) {
            log.error("{} fail to increase sellQuantity[quantity=>{}, item=>{}, weShopId=>{}]", LogUtil.getClassMethodName(), quantity, itemId, weShopId);
            return Response.fail(Translate.of("增加销量失败"));
        }
    }

    @Override
    public Either<Long> initStatusForCreateWeShopItem(Long weShopId, Long shopId, Long itemId, Long skuId) {
        WeShopItem weShopItem;
        weShopItem = new WeShopItem();
        weShopItem.setShopId(shopId);
        weShopItem.setWeShopId(weShopId);
        weShopItem.setItemId(itemId);
        weShopItem.setIndexDisplay(1);
        weShopItem.setSortIndex(1);
        weShopItem.setStatus(0);
        WeShopSku weShopSku = weShopSkuDao.findWeShopIdAndSkuId(weShopId, skuId, -99);
        if (weShopSku != null && weShopSku.getWeShopItemId() != null) {
            if (1 == weShopSku.getStatus()) {
                log.debug("{} WeShopItem[{}] already at on sell", LogUtil.getClassMethodName(), weShopItem.getId());
                return Either.error(Translate.of("该商品已经处于上架状态, 请勿重复操作"));
            }
            weShopItem = Optional.ofNullable(weShopItemDao.findById(weShopSku.getWeShopItemId())).orElseGet(() -> weShopItemDao.findByWeShopIdAndItemId(weShopId, itemId));
            if (weShopItem == null) {
                weShopItem = new WeShopItem();
                weShopItem.setShopId(shopId);
                weShopItem.setWeShopId(weShopId);
                weShopItem.setItemId(itemId);
                weShopItem.setIndexDisplay(1);
                weShopItem.setSortIndex(1);
                weShopItem.setStatus(0);
                weShopItemDao.create(weShopItem);
                return Either.ok(weShopItem.getId());
            }
            if (Objects.equals(weShopItem.getStatus(), -1)) {
                // 修改任何一个下架的商品 都是加入清单中
                weShopItemDao.updateStatus(weShopItem.getId(), 0);
            }
            if (weShopSku.getWeShopItemId() == null) {
                weShopItemDao.create(weShopItem);
                return Either.ok(weShopItem.getId());
            }
            return Either.ok(weShopSku.getWeShopItemId());
        }
        weShopItemDao.create(weShopItem);
        return Either.ok(weShopItem.getId());
    }
}