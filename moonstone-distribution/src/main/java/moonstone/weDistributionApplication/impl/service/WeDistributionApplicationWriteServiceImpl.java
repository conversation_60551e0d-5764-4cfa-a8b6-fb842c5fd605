package moonstone.weDistributionApplication.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.weDistributionApplication.impl.dao.WeDistributionApplicationDao;
import moonstone.weDistributionApplication.model.WeDistributionApplication;
import moonstone.weDistributionApplication.service.WeDistributionApplicationWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by CaiZhy on 2018/12/3.
 */
@Slf4j
@Service
@RpcProvider
public class WeDistributionApplicationWriteServiceImpl implements WeDistributionApplicationWriteService {
    @Autowired
    private WeDistributionApplicationDao weDistributionApplicationDao;

    @Override
    public Response<Long> create(WeDistributionApplication weDistributionApplication) {
        try {
            weDistributionApplicationDao.create(weDistributionApplication);
            return Response.ok(weDistributionApplication.getId());
        } catch (Exception e) {
            log.error("fail to create weDistribution application({}), cause: {}", weDistributionApplication, Throwables.getStackTraceAsString(e));
            return Response.fail("weDistributionApplication.create.fail");
        }
    }

    @Override
    public Response<Boolean> update(WeDistributionApplication weDistributionApplication) {
        try {
            Boolean result = weDistributionApplicationDao.update(weDistributionApplication);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("fail to update weDistribution application({}), cause: {}", weDistributionApplication, Throwables.getStackTraceAsString(e));
            return Response.fail("weDistributionApplication.update.fail");
        }
    }

    @Override
    public Response<Boolean> updateStatus(Long weDistributionApplicationId, Integer status) {
        try {
            Boolean result = weDistributionApplicationDao.updateStatus(weDistributionApplicationId, status);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("fail to update weDistribution application status by id={}, status={}, cause: {}",
                    weDistributionApplicationId, status, Throwables.getStackTraceAsString(e));
            return Response.fail("weDistributionApplication.update.fail");
        }
    }

    @Override
    public Response<Boolean> delete(Long weDistributionApplicationId) {
        try {
            Boolean result = weDistributionApplicationDao.delete(weDistributionApplicationId);
            return Response.ok(result);
        } catch (Exception e) {
            log.error("fail to delete weDistribution application by id={}, cause: {}", weDistributionApplicationId, Throwables.getStackTraceAsString(e));
            return Response.fail("weDistributionApplication.delete.fail");
        }
    }
}
