package moonstone.search.weShopItem.impl;

import io.terminus.search.core.ESClient;
import lombok.extern.slf4j.Slf4j;
import moonstone.search.BaseESInitiator;
import moonstone.search.weShopItem.SearchWeShopItemProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/12/24.
 */
@Slf4j
@Component
public class ESWeShopItemInitiator extends BaseESInitiator {

    private final SearchWeShopItemProperties searchWeShopItemProperties;

    @Autowired
    public ESWeShopItemInitiator(ESClient esClient, SearchWeShopItemProperties searchWeShopItemProperties) {
        super(esClient);
        this.searchWeShopItemProperties = searchWeShopItemProperties;
    }

//    @StartEventTag(cache = false, desc = "微分销-删除该事件")
//    @EventListener(ContextRefreshedEvent.class)
//    public void init() throws Exception {
//        init(searchWeShopItemProperties.getIndexName(), searchWeShopItemProperties.getIndexType(), searchWeShopItemProperties.getMappingPath());
//    }
}
