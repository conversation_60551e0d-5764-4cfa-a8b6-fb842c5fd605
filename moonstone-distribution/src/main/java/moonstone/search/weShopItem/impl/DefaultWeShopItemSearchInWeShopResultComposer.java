package moonstone.search.weShopItem.impl;

import com.google.common.base.Objects;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.common.model.Paging;
import io.terminus.common.utils.Splitters;
import io.terminus.search.api.model.WithAggregations;
import io.terminus.search.model.Bucket;
import lombok.extern.slf4j.Slf4j;
import moonstone.brand.model.Brand;
import moonstone.cache.BackCategoryCacher;
import moonstone.cache.BrandCacher;
import moonstone.cache.ShopCategoryCacher;
import moonstone.category.model.BackCategory;
import moonstone.category.model.FrontCategory;
import moonstone.category.model.ShopCategory;
import moonstone.search.dto.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by CaiZhy on 2018/12/24.
 */
@Slf4j
public class DefaultWeShopItemSearchInWeShopResultComposer extends BaseWeShopItemSearchResultComposer
        implements WeShopItemSearchInWeShopResultComposer<SearchedWeShopItemInWeShopWithAggs> {

    private final ShopCategoryCacher shopCategoryCacher;

    private final BackCategoryCacher backCategoryCacher;

    private final BrandCacher brandCacher;

    private static final String SHOP_CATEGORY_AGGS = "shop_category_aggs";

    public DefaultWeShopItemSearchInWeShopResultComposer(BackCategoryCacher backCategoryCacher,
                                                         BrandCacher brandCacher,
                                                         ShopCategoryCacher shopCategoryCacher) {
        this.backCategoryCacher = backCategoryCacher;
        this.brandCacher = brandCacher;
        this.shopCategoryCacher = shopCategoryCacher;
    }

    public SearchedWeShopItemInWeShopWithAggs compose(WithAggregations withAggs,
                                                      Map<String, String> params,
                                                      Map<String, Object> context) {
        SearchedWeShopItemInWeShopWithAggs searchWithAggs = new SearchedWeShopItemInWeShopWithAggs();

        //处理搜索结果
        Paging entities = new Paging<>(withAggs.getTotal(), withAggs.getData());
        searchWithAggs.setEntities(entities);

        //处理聚合
        Map<String, List<Bucket>> aggregations = withAggs.getAggregations();

        //处理类目聚合
        List<Bucket> catAggs = aggregations.get(CAT_AGGS);

        String chosenCats = params.get("bcids");

        Set<String> chosenCategories = Sets.newHashSet();
        if (StringUtils.hasText(chosenCats)) {
            for (String chosenCat : Splitters.UNDERSCORE.split(chosenCats)) {
                chosenCategories.add(chosenCat);
            }
        }

        List<AggNav> catNavs = makeCategoryNavs(catAggs, chosenCategories);
        searchWithAggs.setBackCategories(catNavs);

        //处理品牌聚合
        List<Bucket> brandAggs = aggregations.get(BRAND_AGGS);
        List<AggNav> brandNavs = makeBrandNavs(brandAggs);
        searchWithAggs.setBrands(brandNavs);


        //处理属性聚合
        String chosenAttrs = params.get("attrs");
        List<Bucket> attrAggs = aggregations.get(ATTR_AGGS);
        List<GroupedAggNav> attrNavs = makeGroupedAttrNavs(attrAggs, chosenAttrs);
        searchWithAggs.setAttributes(attrNavs);

        //处理店铺内类目聚合
        String chosenShopCatId = params.get("shopCatId");
        List<Bucket> shopCategoryAggs = aggregations.get(SHOP_CATEGORY_AGGS);
        List<AggNav> shopCategoryNavs = makeShopCategoryNavs(shopCategoryAggs, chosenShopCatId);
        searchWithAggs.setShopCategories(shopCategoryNavs);

        //处理面包屑
        Long chosenShopCategoryId = 0L;
        if (StringUtils.hasText(chosenShopCatId)) {
            chosenShopCategoryId = Long.valueOf(chosenShopCatId);
        } else if (shopCategoryNavs != null && shopCategoryNavs.size() == 1) {
            chosenShopCategoryId = (Long) shopCategoryNavs.get(0).getKey();
        }
        List<IdAndName> breadCrumbs = makeBreadCrumbsOfShopCategory(chosenShopCategoryId);
        searchWithAggs.setBreadCrumbs(breadCrumbs);

        //处理已选择的属性
        List<Chosen> chosens = Lists.newArrayList();
        addChosenAttributes(chosenAttrs, chosens);

        //处理已选择的品牌
        addChosenBrands(params.get("bids"), chosens);

        //处理已选择的前台类目
        if (context.get("frontCategory") != null) {
            addChosenFrontCategory((FrontCategory) context.get("frontCategory"), chosens);
        }
        searchWithAggs.setChosen(chosens);
        return searchWithAggs;
    }

    private void addChosenFrontCategory(FrontCategory frontCategory, List<Chosen> chosens) {
        if (frontCategory != null) {
            chosens.add(new Chosen(3, frontCategory.getId(), frontCategory.getName()));
        }
    }

    private void addChosenBrands(String brandIds, List<Chosen> chosens) {
        if (StringUtils.hasText(brandIds)) {
            for (String brandId : Splitters.UNDERSCORE.split(brandIds)) {
                Brand brand = brandCacher.findBrandById(Long.valueOf(brandId));
                chosens.add(new Chosen(1, brandId, brand.getName()));
            }
        }
    }

    /**
     * 品牌类目聚合
     *
     * @param brandAggs
     * @return
     */
    private List<AggNav> makeBrandNavs(List<Bucket> brandAggs) {
        if (CollectionUtils.isEmpty(brandAggs)) {
            return null;
        }
        List<AggNav> brandNavs = Lists.newArrayListWithCapacity(brandAggs.size());
        for (Bucket brandAgg : brandAggs) {
            try {
                Long brandId = Long.valueOf(brandAgg.getKey());
                Brand brand = brandCacher.findBrandById(brandId);
                AggNav aggNav = new AggNav(brandId, brand.getName(), brandAgg.getDoc_count());

                // 暂时先在品牌额外信息中加入品牌 logo
                Map<String, String> extra = Collections.singletonMap("img", brand.getLogo_());
                aggNav.setExtra(extra);
                brandNavs.add(aggNav);
            } catch (Exception e) {
                log.error("failed to build brand navs for bucket(key={}),cause:{}",
                        brandAgg.getKey(), Throwables.getStackTraceAsString(e));
            }
        }
        return brandNavs;
    }

    /**
     * 处理类目聚合, 移除掉已经选择的类目
     *
     * @param catAggs          类目聚合
     * @param chosenCategories 已选择的类目
     * @return 类目聚合
     */
    private List<AggNav> makeCategoryNavs(List<Bucket> catAggs, Set<String> chosenCategories) {

        if (CollectionUtils.isEmpty(catAggs)) {
            return null;
        }

        List<AggNav> catNavs = Lists.newArrayListWithCapacity(catAggs.size());
        for (Bucket catAgg : catAggs) {
            final String key = catAgg.getKey();
            if (chosenCategories.contains(key)) { //忽略已选择的类目
                continue;
            }
            try {
                Long categoryId = Long.valueOf(key);
                BackCategory backCategory = backCategoryCacher.findBackCategoryById(categoryId);
                if (backCategory.getHasChildren()) {  //忽略非叶子类目
                    continue;
                }
                AggNav aggNav = new AggNav(categoryId, backCategory.getName(), catAgg.getDoc_count());
                catNavs.add(aggNav);
            } catch (Exception e) {
                log.error("failed to build cat navs for bucket(key={}),cause:{}",
                        key, Throwables.getStackTraceAsString(e));
            }
        }

        return catNavs;
    }

    /**
     * 构造店铺类目面包屑
     *
     * @param currentShopCategoryId 当前店铺类目id
     * @return 面包屑
     */
    private List<IdAndName> makeBreadCrumbsOfShopCategory(Long currentShopCategoryId) {
        List<IdAndName> idAndNames = Lists.newArrayList();
        while (currentShopCategoryId > 0) {
            ShopCategory shopCategory = shopCategoryCacher.findById(currentShopCategoryId);
            if (shopCategory == null || shopCategory.getId() <= 0L) {
                break;
            }
            idAndNames.add(new IdAndName(shopCategory.getId(), shopCategory.getName()));
            currentShopCategoryId = shopCategory.getPid();
        }
        List<IdAndName> breadCrumbs = Lists.newArrayListWithCapacity(idAndNames.size() + 1);
        breadCrumbs.add(new IdAndName(0L, "所有分类"));
        breadCrumbs.addAll(Lists.reverse(idAndNames));
        return breadCrumbs;
    }

    private List<AggNav> makeShopCategoryNavs(List<Bucket> shopCategoryAggs, String chosenShopCatId) {
        if (CollectionUtils.isEmpty(shopCategoryAggs)) {
            return null;
        }

        List<AggNav> shopCategoryNavs = Lists.newArrayListWithCapacity(shopCategoryAggs.size());
        if (StringUtils.hasText(chosenShopCatId)) {
            //选择了店铺内类目,若是选择未分类则不需要聚合了,否则返回下一级类目聚合
            Long chosenShopCategoryId = Long.valueOf(chosenShopCatId);
            if (Objects.equal(chosenShopCategoryId, ShopCategory.ID_UNKNOWN)) {
                return null;
            }

            for (Bucket shopCategoryAgg : shopCategoryAggs) {
                Long shopCategoryId = Long.valueOf(shopCategoryAgg.getKey());

                if (Objects.equal(shopCategoryId, chosenShopCategoryId)) {
                    continue;
                }

                ShopCategory shopCategory = shopCategoryCacher.findById(shopCategoryId);
                if (shopCategory == null || shopCategory.getId() <= 0) {
                    break;
                }
                if (Objects.equal(shopCategory.getPid(), chosenShopCategoryId)) {
                    AggNav aggNavOfNextLevel = new AggNav(shopCategoryId, shopCategory.getName(), shopCategoryAgg.getDoc_count());
                    shopCategoryNavs.add(aggNavOfNextLevel);
                }
            }

        } else {
            //未选择店铺内类目,则只返回第一级类目聚合和未分类聚合
            for (Bucket shopCategoryAgg : shopCategoryAggs) {
                Long shopCategoryId = Long.valueOf(shopCategoryAgg.getKey());

                if (Objects.equal(shopCategoryId, ShopCategory.ID_UNKNOWN)) {
                    AggNav aggNavOfUnknown = new AggNav(ShopCategory.ID_UNKNOWN, "未分类", shopCategoryAgg.getDoc_count());
                    shopCategoryNavs.add(aggNavOfUnknown);
                } else {
                    ShopCategory shopCategory = shopCategoryCacher.findById(shopCategoryId);
                    if (shopCategory == null || shopCategory.getId() <= 0) {
                        break;
                    }
                    if (Objects.equal(shopCategory.getPid(), ShopCategory.ID_ROOT)) {
                        AggNav aggNavOfFirstLevel = new AggNav(shopCategoryId, shopCategory.getName(), shopCategoryAgg.getDoc_count());
                        shopCategoryNavs.add(aggNavOfFirstLevel);
                    }
                }
            }
        }
        return shopCategoryNavs;
    }
}
