package moonstone.search.weShopItem.impl;

import io.terminus.search.api.model.WithAggregations;
import moonstone.search.dto.SearchedWeShopItemInWeShopWithAggs;

import java.util.Map;

/**
 * 店铺内搜索结果组装器
 * Created by CaiZhy on 2018/12/24.
 */
public interface WeShopItemSearchInWeShopResultComposer<T extends SearchedWeShopItemInWeShopWithAggs> {

    /**
     * 组装店铺内搜索结果
     *
     * @param withAggs 原始的搜索结果
     * @param params   参数
     * @param context  上下文
     * @return 组装好的微分销商品信息
     */
    T compose(WithAggregations withAggs, Map<String, String> params, Map<String, Object> context);
}
