/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.util;

import com.google.common.base.Strings;
import moonstone.common.model.CommonUser;
import moonstone.user.model.User;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Optional;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-31
 */
public abstract class ParanaUserMaker {
    /**
     * 复制基础信息进入到Parana用户中, 需要手动添加店铺信息
     *
     * @param user 用户
     * @return 具有基础信息的用户实体
     */
    public static CommonUser from(User user) {
        CommonUser commonUser = new CommonUser();
        BeanUtils.copyProperties(user, commonUser);
        commonUser.setName(getUserName(user));
        commonUser.setHasPassword(!ObjectUtils.isEmpty(user.getPassword()));
        commonUser.setHasMobile(!ObjectUtils.isEmpty(user.getMobile()));
        commonUser.setRoles(Optional.ofNullable(user.getRoles()).orElseGet(ArrayList::new));
        commonUser.setTags(Optional.ofNullable(user.getTags()).orElseGet(HashMap::new));

        return commonUser;
    }

    public static String getUserName(User user) {
        if (!Strings.isNullOrEmpty(user.getName())) {
            return user.getName();
        }
        if (!Strings.isNullOrEmpty(user.getEmail())) {
            return user.getEmail();
        }
        if (!Strings.isNullOrEmpty(user.getMobile())) {
            return user.getMobile();
        }
        return "";
    }
}
