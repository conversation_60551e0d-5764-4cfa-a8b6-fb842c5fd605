package moonstone.web.core.user.application;

import com.alibaba.fastjson.JSONObject;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.model.AuthAble;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.UserUtil;
import moonstone.shop.model.Shop;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.user.criteria.UserRelationEntityCriteria;
import moonstone.user.model.StoreProxyInfo;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.model.view.UserLevelView;
import moonstone.user.service.StoreProxyInfoReadService;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.model.ServiceProvider;
import moonstone.web.core.user.view.Auth;
import moonstone.web.core.util.StoreProxyAuthView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/identity")
public class IdentitySpecifyController {
    @Resource
    StoreProxyReadService storeProxyReadService;
    @Autowired
    SubStoreReadService subStoreReadService;
    @Autowired
    SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;
    @Autowired
    StoreProxyInfoReadService storeProxyInfoReadService;
    @Autowired
    ShopCacheHolder shopCacheHolder;
    @Autowired
    UserRelationEntityReadService userRelationEntityReadService;

    @Resource
    private ServiceProviderCache serviceProviderCache;

    @GetMapping("/getIdentity")
    public IdentityView getIdentity(long shopId, @RequestParam(defaultValue = "true") Boolean hide) {
        long userId = Optional.ofNullable(UserUtil.getUserId()).orElseThrow(() -> new JsonResponseException("user.not.login"));
        IdentityView identityView = new IdentityView();
        if (shopCacheHolder.findShopById(shopId).getUserId() == userId) {
            identityView.setShopOwner(true);
            identityView.setPlatform(Auth.Pass);
        }
        checkIfStoreProxy(shopId, userId, identityView);
        checkIfServiceProvider(shopId, userId, identityView);
        checkIfSubStore(shopId, userId, identityView);
        checkIfGuider(shopId, userId, identityView, hide);

        return identityView;
    }

    private void checkIfGuider(long shopId, long userId, IdentityView identityView, boolean hide) {
        subStoreTStoreGuiderReadService.findByStoreGuiderIdAndShopIdWithStatusBit(userId, shopId, null)
                .orElse(Collections.emptyList())
                .stream().findFirst().ifPresent(identityView::setGuider);
        if (identityView.getGuider() != null && hide) {
            log.warn("{} guider:{} cover the identity of subStore:{}", LogUtil.getClassMethodName(), identityView.getGuider(), identityView.getSubStoreOwner());
            identityView.setSubStoreOwner(null);
        }
    }

    private void checkIfSubStore(long shopId, long userId, IdentityView identityView) {
        val rSubStore = subStoreReadService.findUserIdAndShopId(userId, shopId);
        if (rSubStore.isSuccess()) {
            identityView.setSubStoreOwner(rSubStore.getResult());
        }
    }

    private void checkIfServiceProvider(long shopId, long userId, IdentityView identityView) {
        Shop shop = shopCacheHolder.findShopById(shopId);
        UserRelationEntityCriteria criteria = new UserRelationEntityCriteria();
        criteria.setUserId(userId);
        criteria.setRelationId(shop.getUserId());
        criteria.setType(UserRelationEntity.UserRelationType.SUPER.getType());
        List<UserLevelView> viewList = Optional.ofNullable(userRelationEntityReadService.paging(criteria).getResult())
                .map(Paging::getData).orElse(Collections.emptyList()).stream().map(UserLevelView::new).collect(Collectors.toList());
        Optional<UserLevelView> userLevelView = viewList.stream()
                .filter(view -> Objects.equals(view.getShopId(), shopId))
                .findFirst();
        userLevelView.ifPresent(view -> {
            identityView.setServiceProvider(Auth.Pass);
            identityView.setServiceProviderInfo(serviceProviderCache.findByMongo(userId, shopId));
        });
    }

    private void checkIfStoreProxy(long shopId, long userId, IdentityView identityView) {
        val rEntityProxy = storeProxyReadService.findByShopIdAndUserId(shopId, userId);
        if (rEntityProxy.isSuccess() && rEntityProxy.take().isPresent()) {
            identityView.setLadderDistribution(StoreProxyAuthView.build(rEntityProxy.take().get()));
            //add 2019-08-29
            Either<Optional<StoreProxyInfo>> storeProxyInfo = storeProxyInfoReadService.findByProxyId(rEntityProxy.take().get().getId());
            if (storeProxyInfo.isSuccess() && storeProxyInfo.take().isPresent()) {
                JSONObject jsonObject = new JSONObject();
                if (!ObjectUtils.isEmpty(storeProxyInfo.take().get().getExtra()) && !storeProxyInfo.take().get().getExtra().isEmpty()) {
                    jsonObject.put("auditingRemark", storeProxyInfo.take().get().getExtra().get("auditingRemark"));
                    jsonObject.put("jxRemark", storeProxyInfo.take().get().getExtra().get("jxRemark"));
                } else {
                    jsonObject.put("auditingRemark", "");
                    jsonObject.put("jxRemark", "");
                }
                jsonObject.put("status", storeProxyInfo.take().get().getStatus());
                identityView.setInfos(jsonObject);
            }
        }
    }

    @Data
    public static class IdentityView {
        AuthAble ladderDistribution;
        AuthAble guider;
        AuthAble subStoreOwner;
        AuthAble serviceProvider;
        AuthAble platform;
        boolean shopOwner;
        JSONObject infos;//身份变更审核

        ServiceProvider serviceProviderInfo;
    }
}
