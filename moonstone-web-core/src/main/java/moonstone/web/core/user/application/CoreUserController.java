/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.user.application;

import cn.hutool.json.JSONUtil;
import com.google.common.base.Objects;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.api.Result;
import moonstone.common.enums.CountryCode;
import moonstone.common.enums.UserRole;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.EncryptUtil;
import moonstone.common.utils.RespHelper;
import moonstone.common.utils.UserUtil;
import moonstone.user.auth.Role;
import moonstone.user.auth.RoleContent;
import moonstone.user.auth.UserRoleLoader;
import moonstone.user.model.LoginType;
import moonstone.user.model.User;
import moonstone.user.model.view.RoleMenuView;
import moonstone.user.model.view.ValidatePasswordRequest;
import moonstone.user.service.RoleMenuManager;
import moonstone.user.service.UserReadService;
import moonstone.user.service.UserWriteService;
import moonstone.web.core.component.captcha.CaptchaGenerator;
import moonstone.web.core.component.user.UserSubShopPackComponent;
import moonstone.web.core.user.IdentityAvailableResponse;
import moonstone.web.core.user.application.utils.PasswordValidator;
import moonstone.web.core.util.ParanaUserMaker;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;

import static moonstone.web.core.user.IdentityAvailableResponse.*;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-30
 */
@RestController
@Slf4j
@ApiModel(value = "用户")
@RequestMapping("/api/user")
@AllArgsConstructor
public class CoreUserController {
    private final UserWriteService<User> userWriteService;
    private final UserReadService<User> userReadService;
    private final UserRoleLoader userRoleLoader;
    private final CaptchaGenerator captchaGenerator;
    private final UserSubShopPackComponent userSubShopPackComponent;
    private final RoleMenuManager roleMenuManager;


    @RequestMapping(value = "/{userId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<CommonUser> findUserById(@PathVariable Long userId, @RequestParam(required = false) Long shopId) {
        val userResp = userReadService.findById(userId);
        if (!userResp.isSuccess()) {
            log.warn("find user by id={} failed, error={}", userId, userResp.getError());
            return Response.fail(userResp.getError());
        }
        CommonUser commonUser = ParanaUserMaker.from(userResp.getResult());
        userSubShopPackComponent.wrap(commonUser, shopId);
        return Response.ok(commonUser);
    }

    @RequestMapping(value = "/{userId}/roles", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<RoleContent> getUserRolesByUserId(@PathVariable Long userId) {
        // find the role content first
        RoleContent roleContent = userRoleLoader.hardLoadRoles(userId).getResult();
        log.info("默认角色菜单内容 {}", JSONUtil.toJsonStr(roleContent));
        for (Role dynamicRole : roleContent.getDynamicRoles()) {
            List<String> notFulfilledLeaf = new LinkedList<>();
            Set<String> leafSet = new HashSet<>(dynamicRole.getTreeNodeSelection());
            for (RoleMenuView.Children child : roleMenuManager.findByRole(UserRole.SELLER.name()).take().getChildren()) {
                checkIfFulfillLeaf(leafSet, child, notFulfilledLeaf);
            }
            log.info("{} 不需要展示的菜单路由 {}",JSONUtil.toJsonStr(dynamicRole),JSONUtil.toJsonStr(notFulfilledLeaf));
            dynamicRole.getTreeNodeSelection().removeAll(notFulfilledLeaf);
        }
        return Response.ok(roleContent);
    }

    /**
     * check if the tree contain all the leaf
     *
     * @param treeNodeSelection node
     * @param children          leafs
     * @param notFulfilledLeaf  parent leaf
     */
    private void checkIfFulfillLeaf(Set<String> treeNodeSelection, RoleMenuView.Children children, List<String> notFulfilledLeaf) {
        if (children == null) {
            return;
        }

        // 如果是叶子节点
        if (children.getChildren() == null || children.getChildren().isEmpty()) {
            if (!treeNodeSelection.contains(children.getKey())) {
                notFulfilledLeaf.add(children.getKey());
            }
            return;
        }

        // 检查所有子节点是否都被选中
        boolean allChildrenSelected = true;
        for (RoleMenuView.Children child : children.getChildren()) {
            if (!treeNodeSelection.contains(child.getKey())) {
                allChildrenSelected = false;
                break;
            }
        }

        // 如果子节点未全部选中，则当前节点未满足
        if (!allChildrenSelected) {
            notFulfilledLeaf.add(children.getKey());
        }

        // 递归检查子节点
        for (RoleMenuView.Children child : children.getChildren()) {
            checkIfFulfillLeaf(treeNodeSelection, child, notFulfilledLeaf);
        }
    }


    @GetMapping("/{userId}/role-names")
    public List<String> getRoleNamesOfUserId(@PathVariable Long userId) {
        RoleContent content = RespHelper.or500(userRoleLoader.hardLoadRoles(userId));
        List<String> result = new ArrayList<>();
        if (content != null) {
            for (Role role : content.getRoles()) {
                result.add(role.getBase());
            }
            for (Role role : content.getDynamicRoles()) {
                result.addAll(role.getNames());
            }
        }
        return result;
    }

    @RequestMapping(value = "/captcha", method = RequestMethod.GET)
    public ResponseEntity<byte[]> getCaptcha(HttpServletRequest request) {
        byte[] imgCache;
        HttpSession session = request.getSession();
        imgCache = captchaGenerator.captcha(session);
        final HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_JPEG);
        return new ResponseEntity<>(imgCache, headers, HttpStatus.CREATED);
    }

    @RequestMapping(value = "/mobile-available", method = RequestMethod.GET)
    public IdentityAvailableResponse checkMobileAvailable(@RequestParam(defaultValue = "0086") String countryCode, @RequestParam String mobile,
                                                          @RequestParam(defaultValue = "auto") String anonymous) {
        mobile = countryCode.trim().replaceAll("^\\+", CountryCode.PREFIX_CODE) + mobile;
        return checkAvailable(mobile, LoginType.MOBILE, anonymous);
    }

    @RequestMapping(value = "/email-available", method = RequestMethod.GET)
    public IdentityAvailableResponse checkEmailAvailable(@RequestParam String email,
                                                         @RequestParam(defaultValue = "auto") String anonymous) {
        return checkAvailable(email, LoginType.EMAIL, anonymous);
    }

    @RequestMapping(value = "/username-available", method = RequestMethod.GET)
    public IdentityAvailableResponse checkUsernameAvailable(@RequestParam String username,
                                                            @RequestParam(defaultValue = "auto") String anonymous) {
        return checkAvailable(username, LoginType.NAME, anonymous);
    }

    /**
     * data = true：校验通过（输入的密码正确）
     * @param request
     * @return
     */
    @ApiOperation(value = "校验密码", notes = "校验密码")
    @PostMapping("/validate-password")
    public Result<Boolean> validatePassword(@RequestBody ValidatePasswordRequest request) {
        Long userId = UserUtil.getUserId();
        if(StringUtils.isEmpty(request.getPassword())){
            return Result.fail("密码不能为空");
        }

        log.debug("user {} want to change password at {}", userId, new Date());
        User user = RespHelper.or500(userReadService.findById(userId));
        if (EncryptUtil.match(request.getPassword(), user.getPassword())) {
            log.warn("user old password {} is matched.", request.getPassword());
            return Result.data(true);
        }
        return Result.data(false);
    }

    public static void main(String[] args) {
        String db = "2426#736362BCEF193FCA5BE6";
        String arg = "Abc123";
        System.out.println(EncryptUtil.match(arg, db));
    }
    
    @RequestMapping(value = "/change_password", method = RequestMethod.POST)
    public Result<Boolean> changePassword(String oldPassword, String newPassword) {
        Long userId = UserUtil.getUserId();
        if (userId == null) {
            throw new JsonResponseException(401, "user.not.login");
        }
//        if (!newPassword.matches("[\\s\\S]{6,16}")) {
//            log.warn("password syntax error");
//            throw new JsonResponseException(500, "user.password.6to16");
//        }
        if(StringUtils.isNotEmpty(oldPassword) && oldPassword.equals(newPassword)){
            return Result.fail("新密码不能与旧密码相同");
        }
        if(!PasswordValidator.validatePassword(newPassword)){
            log.warn("password syntax error {}", newPassword);
//            throw new JsonResponseException(500, "新密码长度为8-20位，密码必须大写字母、小写字母、数字和英文符号至少包含3种");
            return Result.fail("新密码长度为8-20位，密码必须大写字母、小写字母、数字和英文符号至少包含3种");
        }
        log.debug("user {} want to change password at {}", userId, new Date());
        User user = RespHelper.or500(userReadService.findById(userId));
        if (!EncryptUtil.match(oldPassword, user.getPassword())) {
            log.warn("user old password{} isn't matched.", oldPassword);
            throw new JsonResponseException("user.old.password.not.match");
        }
        User toUpdate = new User();
        toUpdate.setId(userId);
        toUpdate.setPassword(EncryptUtil.encrypt(newPassword));
        // 更新为不进行密码检查
        toUpdate.setCheckCount(-1);
        Response<Boolean> result = userWriteService.update(toUpdate);
        if (result.isSuccess()) {
            return Result.data(true);
        } else {
            log.warn("failed to change password for user id={},error code:{}", userId, result.getError());
//            throw new JsonResponseException(500, result.getError());
            return Result.fail(result.getError());
        }
    }

    private IdentityAvailableResponse checkAvailable(String identity, LoginType type, String anonymous) {
        val r = userReadService.checkExist(identity, type);
        if (!r.isSuccess()) {
            log.warn("checking {} available failed, identity={}, error={}", type, identity, r.getError());
            throw new JsonResponseException(r.getError());
        }
        boolean isExist = r.getResult();
        if (!isExist) {
            // 用户不存在, 直接判定可用
            return available();
        }
        Long userId = UserUtil.getUserId();
        boolean isAnonymous;
        if (isAuto(anonymous)) {
            // 自动判定是否匿名调用
            isAnonymous = userId == null;
        } else {
            // 通过 anonymous 参数 true or false 判定是否匿名调用
            isAnonymous = !isNotAnonymous(anonymous);
        }
        if (isAnonymous) {
            return notAvailable();
        }
        if (userId == null) {
            throw new JsonResponseException(401, "user.not.login");
        }
        val rUser = userReadService.findBy(identity, type);
        if (!rUser.isSuccess()) {
            log.warn("find user by identity={} failed, type={}, error={}", identity, type, rUser.getError());
            throw new JsonResponseException(rUser.getError());
        }
        User user = rUser.getResult();
        if (Objects.equal(userId, user.getId())) {
            return itsYou();
        }
        return notAvailable();
    }

    private boolean isAuto(String anonymous) {
        return "auto".equalsIgnoreCase(anonymous);
    }

    private boolean isNotAnonymous(String anonymous) {
        return "false".equalsIgnoreCase(anonymous) || "0".equalsIgnoreCase(anonymous);
    }
}
