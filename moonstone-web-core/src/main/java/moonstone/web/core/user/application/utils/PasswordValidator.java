package moonstone.web.core.user.application.utils;

import java.util.regex.*;

public class PasswordValidator {

    public static boolean validatePassword(String password) {
        // 检查密码长度
        if (password.length() < 8 || password.length() > 20) {
            System.out.println("密码长度必须在8到20位之间。");
            return false;
        }

        // 检查密码是否包含至少3种类型的字符
        int count = 0;
        if (password.matches(".*[A-Z].*")) count++;
        if (password.matches(".*[a-z].*")) count++;
        if (password.matches(".*\\d.*")) count++;
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) count++;

        if (count < 3) {
//            System.out.println("密码必须包含大写字母、小写字母、数字和英文符号中的至少3种。");
            return false;
        }

        // 检查密码是否只包含这四种类型的字符
        if (!password.matches("[A-Za-z\\d!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]+")) {
//            System.out.println("密码只能包含大写字母、小写字母、数字和英文符号。");
            return false;
        }

        return true;
    }

    public static void main(String[] args) {
        String password = "Abc12345";
        if (validatePassword(password)) {
            System.out.println("密码符合要求。");
        } else {
            System.out.println("密码不符合要求。");
        }
    }
}