package moonstone.web.core.user.application;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.web.core.component.pay.allinpay.user.AllinPaySignatureService;
import moonstone.web.core.component.pay.allinpay.user.req.signature.AllinSignatureUserRegisterReq;
import moonstone.web.core.component.pay.allinpay.user.res.signature.AllinSignatureStatusVo;
import moonstone.web.core.component.pay.allinpay.user.signature.GetDownloadLinkReq;
import moonstone.web.core.component.pay.allinpay.user.signature.SignConfirmationReq;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("allinPay/signature")
@ApiModel(value = "通联-万鉴通")
public class AllinPaySignatureController {

	@Resource
	private AllinPaySignatureService allinPaySignatureService;

	/**
	 * 1、注册账户
	 * 2、生成模板合同副本
	 * 3、如果已经签署则返回签署后的预览地址
	 * 4、如果未签署则返回默认的预览地址
	 * @param dto
	 * @return
	 */
	@PostMapping("userRegister")
	@ApiOperation(value = "查询签约状态", notes = "查询签约状态")
	public Result<AllinSignatureStatusVo> userRegister(@RequestBody AllinSignatureUserRegisterReq dto){
		return allinPaySignatureService.userRegister(dto);
	}

	/**
	 * 1、签署合同
	 * 2、返回签署后的合同预览地址
	 * 3、发布签署成功的消息：消费逻辑：下载文件、上传oss
	 * @param req
	 * @return
	 */
	@PostMapping("signConfirmation")
	@ApiOperation(value = "去签约", notes = "去签约")
	public Result<AllinSignatureStatusVo> signConfirmation(@RequestBody @Valid SignConfirmationReq req){
		return allinPaySignatureService.signConfirmation(req);
	}

}
