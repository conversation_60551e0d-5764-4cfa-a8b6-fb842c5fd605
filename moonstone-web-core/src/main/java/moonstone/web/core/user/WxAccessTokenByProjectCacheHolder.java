package moonstone.web.core.user;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.kevinsawicki.http.HttpRequest;
import io.terminus.common.exception.JsonResponseException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EmptyUtils;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.web.core.model.AccessToken;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * @apiNote WeChat AccessToken
 */
@Slf4j
@Component
public class WxAccessTokenByProjectCacheHolder {
    @Autowired
    private ShopWxaReadService shopWxaReadService;

    @Autowired
    private ShopWxaProjectReadService shopWxaProjectReadeService;

    private LoadingCache<Long, AccessToken> accessTokenCache;

    @PostConstruct
    public void init() {
        accessTokenCache = Caffeine.newBuilder()
                .expireAfterWrite(120, TimeUnit.SECONDS)
                .maximumSize(10000)
                .build(this::getAccessToken);
    }

    /**
     * 直接微信获取AccessToken
     *
     * @param projectId
     * @return
     */
    public AccessToken getAccessToken(Long projectId) {
        ShopWxa shopWxa = shopWxaReadService.findById(shopWxaProjectReadeService.findById(projectId).getResult().getShopWxaId()).getResult();
        Map<String, Object> pMap = new HashMap<String, Object>();
        pMap.put("appid", shopWxa.getAppId());
        pMap.put("secret", shopWxa.getAppSecret());
        pMap.put("grant_type", "client_credential");
        String url = "https://api.weixin.qq.com/cgi-bin/token";
        HttpRequest request = HttpRequest.get(url, pMap, false);
        if (request.ok()) {
            String result = request.body();
            log.debug("[op:auth.getAccessToken] pMap={}, result={}", JSON.toJSONString(pMap), result);
            AccessToken accessToken = JSONObject.parseObject(result, AccessToken.class);
            accessToken.setAppId(shopWxa.getAppId());
            accessToken.setAppSecret(shopWxa.getAppSecret());
            accessToken.setAppId(shopWxa.getAppId());
            accessToken.setAppSecret(shopWxa.getAppSecret());
            accessToken.setCreateTime(new Date());
            return accessToken;
        } else {
            log.error("[op:auth.getAccessToken] HttpRequest failed");
            throw new JsonResponseException(500, "auth.getAccessToken.http.request.fail");
        }
    }

    /**
     * 外部获取(带缓存) AccessToken
     *
     * @param projectId
     * @return
     * @throws ExecutionException
     */
    public AccessToken findAccessToken(Long projectId) {
        try {

            AccessToken accessToken = accessTokenCache.get(projectId);
            if (accessToken == null || EmptyUtils.isEmpty(accessToken.getAccessToken())) {
                accessTokenCache.invalidate(projectId);
                accessToken = accessTokenCache.get(projectId);
            }
            if (accessToken.getCreateTime().before(new DateTime(Date.from(Instant.now())).minusMinutes(60).toDate())) {
                accessTokenCache.invalidate(projectId);
                accessToken = accessTokenCache.get(projectId);
            }
            ShopWxa shopWxa = shopWxaReadService.findById(shopWxaProjectReadeService.findById(projectId).getResult().getShopWxaId()).getResult();
            if (shopWxa != null
                    && Objects.equals(shopWxa.getAppId(), accessToken.getAppId())
                    && Objects.equals(shopWxa.getAppSecret(), accessToken.getAppSecret())) {
                return accessToken;
            } else {
                accessTokenCache.invalidate(projectId);
                accessToken = accessTokenCache.get(projectId);
            }
            return accessToken;
        } catch (Exception e) {
            log.error("findAccessToken is error {}", projectId);
            throw new JsonResponseException("微信获取服务异常");
        }
    }

}
