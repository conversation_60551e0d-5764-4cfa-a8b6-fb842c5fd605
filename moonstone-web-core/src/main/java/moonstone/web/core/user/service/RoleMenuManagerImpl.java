package moonstone.web.core.user.service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.user.model.RoleMenu;
import moonstone.user.model.UserRoleMenu;
import moonstone.user.model.view.RoleMenuView;
import moonstone.user.service.RoleMenuManager;
import moonstone.web.core.util.MongoUtil;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@AllArgsConstructor
@Component
@Slf4j
public class RoleMenuManagerImpl implements RoleMenuManager {
    MongoTemplate mongoTemplate;

    @Override
    public Either<Boolean> createUserRoleMenu(Long userId, String role, List<String> userRoleMenu) {
        mongoTemplate.upsert(Query.query(Criteria.where("userId").is(userId))
                        .addCriteria(Criteria.where("roleName").is(role))
                , Update.update("allowMenu", userRoleMenu)
                        .set("defaultMenu", false)
                , UserRoleMenu.class
        );
        return Either.ok(true);
    }

    @Override
    public Either<Boolean> createDefaultRoleMenu(String role, List<String> userRoleMenu) {
        mongoTemplate.upsert(Query.query(Criteria.where("defaultMenu").is(true))
                        .addCriteria(Criteria.where("roleName").is(role))
                , Update.update("allowMenu", userRoleMenu)
                , UserRoleMenu.class
        );
        return Either.ok(true);
    }

    @Override
    public Either<List<String>> defaultAllowMenuForRole(String role, Long userId) {
        UserRoleMenu userRoleMenu = mongoTemplate.findOne(Query.query(Criteria.where("roleName").is(role))
                        .addCriteria(Criteria.where("userId").is(userId))
                        .addCriteria(Criteria.where("defaultMenu").is(false))
                , UserRoleMenu.class
        );
        if (userRoleMenu == null) {
            userRoleMenu = mongoTemplate.findOne(Query.query(Criteria.where("roleName").is(role))
                            .addCriteria(Criteria.where("defaultMenu").is(true))
                    , UserRoleMenu.class
            );
        }
        return Either.ok(userRoleMenu).map(UserRoleMenu::getAllowMenu);
    }

    @Override
    public Either<List<RoleMenuView>> all() {
        Map<String, List<RoleMenu>> roleMenuMap = new HashMap<>();
        for (RoleMenu roleMenu : mongoTemplate.findAll(RoleMenu.class)) {
            roleMenuMap.computeIfAbsent(roleMenu.getRole(), k -> new LinkedList<>());
            roleMenuMap.get(roleMenu.getRole()).add(roleMenu);
        }
        return Either.ok(roleMenuMap.entrySet().stream().map(roleList -> buildView(roleList.getKey(), roleList.getValue()))
                .collect(Collectors.toList()));
    }

    @Override
    public Either<RoleMenuView> findByRole(String role) {
        return Either.ok(buildView(role, mongoTemplate.find(Query.query(Criteria.where("role").is(role)), RoleMenu.class)));
    }

    private RoleMenuView buildView(String role, List<RoleMenu> roleMenus) {
        if (roleMenus == null || roleMenus.isEmpty()) {
            return null;
        }
        RoleMenuView roleMenuView = new RoleMenuView();
        roleMenuView.setBaseRole(role);
        Map<String, List<RoleMenuView.Children>> leafMap = new HashMap<>(8);
        Map<String, String> fatherTree = new HashMap<>();
        for (RoleMenu roleMenu : roleMenus) {
            RoleMenuView.Children children = new RoleMenuView.Children();
            children.setId(roleMenu.getId());
            children.setKey(roleMenu.getCode());
            children.setName(roleMenu.getName());
            leafMap.computeIfAbsent(roleMenu.getParentId(), k -> new LinkedList<>());
            leafMap.get(roleMenu.getParentId()).add(children);
            fatherTree.put(roleMenu.getId(), roleMenu.getParentId());
        }
        leafMap.forEach((parentId, list) -> list.forEach(children -> children.setChildren(leafMap.get(children.getId()))));
        String rootId = fatherTree.values().stream().findFirst().orElse(null);
        while (rootId != null && fatherTree.get(rootId) != null) {
            rootId = fatherTree.get(rootId);
        }
        roleMenuView.setChildren(leafMap.get(rootId));
        return roleMenuView;
    }

    @Override
    public Either<RoleMenu> findRoleMenuById(Long id) {
        return Either.ok(mongoTemplate.findOne(Query.query(Criteria.where("id").is(id)), RoleMenu.class));
    }

    @Override
    public Either<String> createRoleMenu(RoleMenu roleMenu) {
        return Either.ok(mongoTemplate.insert(roleMenu).getId());
    }

    @Override
    public Either<Boolean> updateRoleMenu(RoleMenu roleMenu) {
        mongoTemplate.findAndModify(
                Query.query(Criteria.where("id").is(roleMenu.getId()))
                , MongoUtil.generateUpdate(roleMenu, new HashSet<>(Arrays.asList("id", "createdAt")), false)
                , RoleMenu.class
        );
        return Either.ok(true);
    }

    @Override
    public Either<Boolean> createByView(RoleMenuView roleMenuView) {
        for (RoleMenuView.Children child : roleMenuView.getChildren()) {
            createRoleMenu(roleMenuView.getBaseRole(), "ROOT", child);
        }
        return Either.ok(true);
    }

    private void createRoleMenu(String role, String parentId, RoleMenuView.Children children) {
        String id;
        if (mongoTemplate.exists(Query.query(Criteria.where("code").is(children.getKey())).addCriteria(Criteria.where("role").is(role)), RoleMenu.class)) {
            id = mongoTemplate.findOne(Query.query(Criteria.where("code").is(children.getKey())).addCriteria(Criteria.where("role").is(role)), RoleMenu.class).getId();
            mongoTemplate.findAndModify(Query.query(Criteria.where("code").is(children.getKey())).addCriteria(Criteria.where("role").is(role)), Update.update("parentId", parentId), RoleMenu.class);
        } else {
            RoleMenu roleMenu = new RoleMenu();
            roleMenu.setRole(role);
            roleMenu.setName(children.getName());
            roleMenu.setCode(children.getKey());
            roleMenu.setParentId(parentId);
            roleMenu.setCreatedAt(new Date());
            id = mongoTemplate.insert(roleMenu).getId();
        }
        if (children.getChildren() == null) {
            return;
        }
        for (RoleMenuView.Children child : children.getChildren()) {
            createRoleMenu(role, id, child);
        }
    }
}
