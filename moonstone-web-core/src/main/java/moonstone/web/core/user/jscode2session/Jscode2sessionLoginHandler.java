package moonstone.web.core.user.jscode2session;

import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.model.WxCommonUser;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.user.dto.WxSpBean;
import moonstone.user.model.User;
import moonstone.user.model.view.UserTagView;
import moonstone.user.service.UserReadService;
import moonstone.web.core.constants.ParanaConfig;
import moonstone.web.core.decoration.api.WxUserDomainFactory;
import moonstone.web.core.decoration.model.WxUserDomain;
import moonstone.web.core.user.api.LoginService;
import moonstone.web.core.util.AlipayOpenAPIUtil;
import moonstone.web.core.util.ParanaUserMaker;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@Slf4j
@Component
public class Jscode2sessionLoginHandler {

    @Autowired
    ParanaConfig paranaConfig;

    @Resource
    ShopWxaProjectReadService shopWxaProjectReadeService;

    @Resource
    ShopWxaReadService shopWxaReadService;

    @Resource
    private WxUserDomainFactory wxUserDomainFactory;

    @Resource
    UserReadService<User> userReadService;

    @Resource
    private LoginService loginService;

    @Resource
    private AlipayOpenAPIUtil alipayOpenAPIUtil;

    public String login(Long wxaProjectId, String wxCode){
        ShopWxa shopWxa = findShopWxa(wxaProjectId);
        String appSecret = shopWxa.getAppSecret();
        var appType = AppTypeEnum.from(shopWxa.getAppType());
        String appId = shopWxa.getAppId();

        String openId = "";
        if (java.util.Objects.isNull(appSecret)) {
            // https://developers.weixin.qq.com/doc/oplatform/Third-party_Platforms/2.0/product/wxcloudrun_dev_opendata.html
            log.info("无appSecret 微信云服务模式");
            // use open platform login
            WxUserDomain domain = wxUserDomainFactory.create(wxCode, wxaProjectId);
            Long userId = domain.login().take();
            openId = domain.getUserWx().getOpenId();
        } else if (appType == null) {
            // 这应该是兼容之前的逻辑
            log.info("微信普通模式 appType兼容");
            WxSpBean sp = loginService.code2Session(wxCode, appId, appSecret);
            openId = sp.getOpenid();
        } else {

            // old login style
            switch (appType) {
                case WECHAT:
                    // 普通模式 https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-login/code2Session.html
                    log.info("微信普通模式");
                    WxSpBean sp = loginService.code2Session(wxCode, appId, appSecret);
                    openId = sp.getOpenid();
                    break;
                case ALIPAY:
                    log.info("支付宝模式");
                    AlipaySystemOauthTokenResponse alipaySystemOauthTokenResponse
                            = alipayAuthTokenRequest(wxCode, appId, shopWxa.getAppSecret(), shopWxa.getAppPublicSecret(), null);
                    openId = alipaySystemOauthTokenResponse.getOpenId();
                    break;
            };
        }
        if(StringUtils.isEmpty(openId)){
            log.error("授权码 为空");
        }else{
            log.info("授权码 {}", openId);
        }
        return openId;
    }

    /**
     * 根据项目Id 查找shopWxa 信息
     * 如果projectId =0 或者 -1 则使用默认信息
     *
     * @param wxaProjectId 项目Id
     * @return shopWxa
     */
    public ShopWxa findShopWxa(Long wxaProjectId) {
        ShopWxa shopWxa = new ShopWxa();
        if (wxaProjectId.equals(0L)) {
            shopWxa.setAppId(paranaConfig.getParanaWxaAppId());
            shopWxa.setAppSecret(paranaConfig.getParanaWxaAppSecret());
        } else if (wxaProjectId.equals(-1L)) {
            shopWxa.setAppId(paranaConfig.getParanaWeBuyerAppId());
            shopWxa.setAppSecret(paranaConfig.getParanaWeBuyerAppSecret());
        } else {
            Response<ShopWxaProject> shopWxaProjectRes = shopWxaProjectReadeService.findById(wxaProjectId);
            if (!shopWxaProjectRes.isSuccess()) {
                log.error("[op:wxaProjectLogin] failed to find shopWxaProject by id={}, error code: {}",
                        wxaProjectId, shopWxaProjectRes.getError());
                throw new JsonResponseException(shopWxaProjectRes.getError());
            }
            ShopWxaProject shopWxaProject = shopWxaProjectRes.getResult();
            Response<ShopWxa> shopWxaResponse = shopWxaReadService.findById(shopWxaProject.getShopWxaId());
            if (!shopWxaResponse.isSuccess()) {
                log.error("[op:wxaProjectLogin] failed to find shopWxa by id={}, error code: {}",
                        shopWxaProject.getShopWxaId(), shopWxaResponse.getError());
                throw new JsonResponseException(shopWxaResponse.getError());
            }
            return shopWxaResponse.getResult();
        }
        return shopWxa;
    }


    private AlipaySystemOauthTokenResponse alipayAuthTokenRequest(String authCode, String appId, String privateSecret, String publicSecret,
                                                                  String appAuthToken) {
        // 基础校验
        checkForAlipay(authCode, appId, privateSecret, publicSecret);

        // 调用 alipay.system.oauth.token， 换取授权访问令牌
        AlipaySystemOauthTokenResponse tokenResponse = alipayOpenAPIUtil.getAlipayAuthToken(authCode, null, appId, privateSecret,
                publicSecret, appAuthToken);
        if (tokenResponse == null || !tokenResponse.isSuccess()) {
            throw new RuntimeException("换取授权访问令牌失败");
        }

        return tokenResponse;
    }

    private void checkForAlipay(String authCode, String appId, String privateSecret, String publicSecret) {
        if (!StringUtils.hasText(authCode)) {
            throw new RuntimeException("授权码authCode为空");
        }
        if (!StringUtils.hasText(appId)) {
            throw new RuntimeException("appId为空");
        }
        if (!StringUtils.hasText(privateSecret)) {
            throw new RuntimeException("私钥为空");
        }
        if (!StringUtils.hasText(publicSecret)) {
            throw new RuntimeException("公钥为空");
        }
    }

}
