package moonstone.web.core.express.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serial;

/**
 * Author:cp
 * Created on 5/30/16.
 */

@ToString(callSuper = true)
public class OrderExpressTrack extends ExpressTrack {

    @Serial
    private static final long serialVersionUID = 7873649570912333499L;

    /**
     * 发货单id
     */
    @Getter
    @Setter
    private Long shipmentId;

    /**
     * 发货单号
     */
    @Getter
    @Setter
    private String shipmentSerialNo;

    /**
     * 物流公司名称
     */
    @Getter
    @Setter
    private String shipmentCorpName;

    /**
     * 订单id
     */
    @Getter
    @Setter
    private Long orderId;
}
