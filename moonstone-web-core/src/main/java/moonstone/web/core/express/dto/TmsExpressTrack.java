package moonstone.web.core.express.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TmsExpressTrack extends OrderExpressTrack implements Serializable {

    @Serial
    private static final long serialVersionUID = -4706445059113772560L;

    /**
     * 面单id
     */
    private String waybillNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 快递
     */
    private String tmsExpressCode;

    /**
     * 完整的物流轨迹
     */
    private List<TmsTrackItem> trackList;

    @Data
    public static class TmsTrackItem implements Serializable {

        @Serial
        private static final long serialVersionUID = 3856305216345502078L;

        /**
         * 轨迹正文
         */
        private String content;

        /**
         * 轨迹产生时间
         */
        private Date time;

        /**
         * 扫描网点地址
         */
        private String scanSite;

        /**
         * 轨迹类型
         */
        private String tmsScanType;

        /**
         * 轨迹简单描述、轨迹状态
         */
        private String tmsScanDesc;
    }
}
