package moonstone.web.core.order.app;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.common.net.Response;
import com.danding.common.utils.MD5Util;
import com.danding.mercury.pay.sdk.MercuryPayAccountType;
import com.danding.mercury.pay.sdk.MercuryPayException;
import com.danding.mercury.pay.sdk.MercuryPayRequestParametersHolder;
import com.danding.mercury.pay.sdk.MercuryPayResult;
import com.danding.mercury.pay.sdk.utils.MapUtils;
import com.danding.mercury.pay.sdk.utils.MercuryPayWebUtils;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.order.dto.ccs.CCSRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
public record CCSClient(String gateway, String appCode, String accountType, String accountCode) {
	/**
	 * 执行入口
	 */
	public <T extends MercuryPayResult> Response<T> execute(CCSRequest request, Class<T> tClass) {
		MercuryPayRequestParametersHolder parametersHolder = null;
		try {
			// 从request和client构造params
			parametersHolder = getParametersHolder(request);
			Map<String, Object> params = getParamMap(parametersHolder);
			HttpRequest httpRequest = HttpRequest.post(this.gateway).form(params);
			if (httpRequest.ok()) {
				JSONObject jsonObject = JSON.parseObject(httpRequest.body());
				if (Objects.equals(true, jsonObject.getBoolean("success"))) {
					T result = jsonObject.getJSONObject("result").toJavaObject(tClass);
					return Response.ok(result);
				} else {
					return Response.fail(jsonObject.getString("error"), jsonObject.getString("errorMessage"));
				}
			} else {
				return Response.fail("mercury.pay.connect.fail", "支付系统连接异常");
			}
		} catch (MercuryPayException e) {
			log.error("Fail to the request param -> {}, gateway -> {}", parametersHolder, gateway, e);
			return Response.fail("local.system.error", e.getMessage());
		}
	}

	/**
	 * 解析异步回传信息的方法
	 * clazz未做限定，但实际要填的是目标结果类的类型
	 */
	public <T extends MercuryPayResult> Response<T> receive(HttpServletRequest request, Class<T> clazz) {
		try {
			JSONObject res = JSON.parseObject(JSON.toJSONString(MapUtils.getPostParameterMap(request)));
			if (Objects.equals(true, res.getBoolean("success"))) {
				T result = res.getJSONObject("result").toJavaObject(clazz);
				return Response.ok(result);
			} else {
				return Response.fail(res.getString("error"), res.getString("errorMessage"));
			}
		} catch (Exception e) {
			log.error("Fail to receive msg", e);
			return Response.fail("local.system.error", e.getMessage());
		}
	}

	/**
	 * 获取JSON字符串格式通知原始参数
	 */
	public String receiveBody(HttpServletRequest request) {
		return JSON.toJSONString(MapUtils.getPostParameterMap(request));
	}

	/**
	 * 获取参数持有者
	 */
	private MercuryPayRequestParametersHolder getParametersHolder(CCSRequest request) {
		MercuryPayRequestParametersHolder parametersHolder = new MercuryPayRequestParametersHolder();
		Map<String, Object> mustParams = new HashMap<>();
		mustParams.put("appCode", this.appCode);
		mustParams.put("accountType", this.accountType);
		mustParams.put("accountCode", this.accountCode);
		String salt = this.appCode + request.getMethod() + request.getVersion(); // 加盐MD5签名
		mustParams.put("sign", MD5Util.MD5(request.getBizContent() + salt));
		parametersHolder.setMustParams(mustParams);
		JSONObject optParams = JSON.parseObject(JSON.toJSONString(request));
		parametersHolder.setOptParams(optParams);
		return parametersHolder;
	}

	/**
	 * 获取全部参数请求Url
	 */
	private String getRequestUrl(MercuryPayRequestParametersHolder requestHolder)
			throws MercuryPayException {
		StringBuilder urlSb = new StringBuilder(this.gateway);
		try {
			String sysMustQuery = MercuryPayWebUtils.buildQuery(requestHolder.getMustParams(),
					"UTF-8");
			String sysOptQuery = MercuryPayWebUtils.buildQuery(requestHolder.getOptParams(), "UTF-8");

			urlSb.append("?");
			urlSb.append(sysMustQuery);
			if (sysOptQuery != null & sysOptQuery.length() > 0) {
				urlSb.append("&");
				urlSb.append(sysOptQuery);
			}
		} catch (IOException e) {
			throw new MercuryPayException(e);
		}

		return urlSb.toString();
	}

	/**
	 * 获取公共参数在内的请求Url
	 */
	public String getMustRequest(MercuryPayRequestParametersHolder requestHolder)
			throws MercuryPayException {
		StringBuilder urlSb = new StringBuilder(this.gateway);
		try {
			String sysMustQuery = MercuryPayWebUtils.buildQuery(requestHolder.getMustParams(),
					"UTF-8");
			urlSb.append("?");
			urlSb.append(sysMustQuery);
		} catch (IOException e) {
			throw new MercuryPayException(e);
		}

		return urlSb.toString();
	}

	/**
	 * 获取全部参数请求参数键值对格式
	 */
	private String getParamStr(MercuryPayRequestParametersHolder requestHolder)
			throws MercuryPayException {
		StringBuilder urlSb = new StringBuilder();
		try {
			String sysMustQuery = MercuryPayWebUtils.buildQuery(requestHolder.getMustParams(),
					"UTF-8");
			String sysOptQuery = MercuryPayWebUtils.buildQuery(requestHolder.getOptParams(), "UTF-8");

			urlSb.append(sysMustQuery);
			if (sysOptQuery != null & sysOptQuery.length() > 0) {
				urlSb.append("&");
				urlSb.append(sysOptQuery);
			}
		} catch (IOException e) {
			throw new MercuryPayException(e);
		}

		return urlSb.toString();
	}

	/**
	 * 获取全部参数请求参数键Map格式
	 */
	private Map<String, Object> getParamMap(MercuryPayRequestParametersHolder requestHolder)
			throws MercuryPayException {
		Map<String, Object> paramMap = new HashMap<>();
		try {
			paramMap.putAll(requestHolder.getMustParams());
			paramMap.putAll(requestHolder.getOptParams());
		} catch (Exception e) {
			throw new MercuryPayException(e);
		}
		return paramMap;
	}

}
