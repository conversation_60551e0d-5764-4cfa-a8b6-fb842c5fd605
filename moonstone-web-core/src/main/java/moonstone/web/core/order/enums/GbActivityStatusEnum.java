package moonstone.web.core.order.enums;

import lombok.Getter;

/**
 * 开团状态: 1-新建完成 2-进行中 3- 已到期  4-已失效 9-已清结")
 */
@Getter
public enum GbActivityStatusEnum {

    NEW(1, "未开始",true),
    IN_PROGRESS(2, "进行中",true),
    EXPIRED(3, "已结束",true),
    INVALID(4, "已作废",true),
    CLEARED(9, "已清结",false);

    private final Integer code;
    private final String description;
    private final Boolean isView;

    GbActivityStatusEnum(Integer code, String description, Boolean isView) {
        this.code = code;
        this.description = description;
        this.isView = isView;
    }

    public static GbActivityStatusEnum fromCode(Integer code) {
        for (GbActivityStatusEnum status : GbActivityStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }

    public static String findDescByCode(Integer activityStatus) {
        if(activityStatus == null){
            return "";
        }
        return fromCode(activityStatus).getDescription();
    }
}
