package moonstone.web.core.refund.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import moonstone.order.dto.fsm.OrderOperation;
import moonstone.order.model.Refund;
import moonstone.order.model.RefundExpressInfo;
import moonstone.order.model.SkuOrder;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * dulicate the view for Refund for farther design
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefundView {

    /**
     * copy Refund from source into view
     *
     * @param source source
     * @return view
     */
    public static RefundView copyOf(Object source) {
        RefundView refundView = new RefundView();
        BeanUtils.copyProperties(source, refundView);
        return refundView;
    }

    private Refund refund;

    private List<SkuOrder> skuOrders;

    private Set<OrderOperation> operations;

    private List<RefundExpressInfo> refundExpressInfos;
}
