package moonstone.web.core.refund.service;

import com.mongodb.client.result.UpdateResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ExpressCompanyCacher;
import moonstone.common.model.Either;
import moonstone.common.utils.Translate;
import moonstone.express.model.ExpressCompany;
import moonstone.order.api.RefundExpressService;
import moonstone.order.model.RefundExpressInfo;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
@Slf4j
@AllArgsConstructor
public class RefundExpressServiceImpl implements RefundExpressService {
    MongoTemplate mongoTemplate;
    ExpressCompanyCacher expressCompanyCacher;

    /**
     * link the refund express
     * not support multi-express info for refund now
     *
     * @param refundId    refundId
     * @param expressNo   expressNo
     * @param companyCode company name of express
     * @return Either.ok(1L)
     */
    @Override
    public Either<Long> linkRefundExpress(Long refundId, String expressNo, String companyCode) {
        Query query = Query.query(Criteria.where("refundId").is(refundId))
                .addCriteria(Criteria.where("disable").is(false));
        String companyName = expressCompanyCacher.listAllActiveExpressCompanies().stream().filter(company -> company.getCode().equals(companyCode))
                .map(ExpressCompany::getName).findFirst().orElseThrow(() -> Translate.exceptionOf("快递公司不存在, 请稍后再试"));
        UpdateResult result = mongoTemplate.upsert(query,
                Update.update("companyCode", companyCode)
                        .set("expressNo", expressNo)
                        .set("companyName", companyName)
                        .set("shipAt", new Date())
                , RefundExpressInfo.class);
        if (result.getUpsertedId() != null) {
            mongoTemplate.updateFirst(Query.query(Criteria.where("refundId").is(refundId))
                    , Update.update("createdAt", new Date())
                    , RefundExpressInfo.class
            );
        }
        return Either.ok(1L);
    }

    @Override
    public Either<Boolean> setReturnExpressInfo(Long refundId, String address, String receiverName, String receiverMobile) {
        Query query = Query.query(Criteria.where("refundId").is(refundId))
                .addCriteria(Criteria.where("disable").is(false));
        UpdateResult result = mongoTemplate.upsert(query,
                Update.update("address", address)
                        .set("receiverName", receiverName)
                        .set("receiverMobile", receiverMobile)
                , RefundExpressInfo.class);
        if (result.getUpsertedId() != null) {
            mongoTemplate.updateFirst(Query.query(Criteria.where("refundId").is(refundId))
                    , Update.update("createdAt", new Date())
                    , RefundExpressInfo.class
            );
        }
        return Either.ok(true);
    }

    @Override
    public Either<RefundExpressInfo> queryRefundExpress(Long refundId) {
        Query query = Query.query(Criteria.where("refundId").is(refundId))
                .addCriteria(Criteria.where("disable").is(false));

        return Either.ok(mongoTemplate.findOne(query, RefundExpressInfo.class));
    }

    @Override
    public Either<List<RefundExpressInfo>> findByRefundIds(List<Long> refundIds) {
        Query query = Query.query(Criteria.where("refundId").in(refundIds))
                .addCriteria(Criteria.where("disable").is(false));

        return Either.ok(mongoTemplate.find(query, RefundExpressInfo.class));
    }
}
