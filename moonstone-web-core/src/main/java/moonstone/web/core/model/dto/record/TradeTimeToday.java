package moonstone.web.core.model.dto.record;

import moonstone.common.api.ARecordInsert;
import moonstone.common.api.IRecordInsert;
import moonstone.common.enums.OrderOutFrom;

/**
 * 用于记录今日交易数量统计
 * UserId为店铺拥有人
 * key为店铺类型 + shopId
 */
public abstract class TradeTimeToday extends ARecordInsert {
    Long userId;
    OrderOutFrom shopType;
    Long shopId;

    protected TradeTimeToday(Long userId, OrderOutFrom shopType, Long shopId) {
        this.userId = userId;
        this.shopType = shopType;
        this.shopId = shopId;
    }

    public static IRecordInsert build(Long userId, OrderOutFrom shopType, Long shopId) {
        return new TradeTimeToday(userId, shopType, shopId) {
        };
    }

    public String getKey() {
        return String.format("%s_%sST%s", userId, shopType.Id(), shopId);
    }
}
