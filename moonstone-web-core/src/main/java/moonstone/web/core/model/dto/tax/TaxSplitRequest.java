package moonstone.web.core.model.dto.tax;

import lombok.Data;

import java.util.List;

@Data
public class TaxSplitRequest {
    /**
     * skuId that is required now
     */
    Long skuId;
    Long shopId;
    /**
     * 外部商品的Id
     * {@see Sku.outerSkuId}
     */
    String outerSkuId;
    /**
     * 第三方Id
     * {@see Sku.tag["pushSystem"]}
     */
    Integer thirdPartySystem;
    /**
     * 最终价格列表
     */
    List<Long> price;
}
