package moonstone.web.core;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.StartEventTag;
import moonstone.common.utils.EncryptHelper;
import moonstone.common.utils.LogUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;

import javax.annotation.PostConstruct;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyStore;
import java.util.Objects;
import java.util.concurrent.ForkJoinPool;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnClass(EncryptHelper.class)
public class KeyLoaderConfig {
    @Autowired
    private Environment environment;

    @Value("${key.store.property:secret}")
    private String secretProperty;
    @Value("${key.store.pathProperty:STORE_PATH}")
    private String storePathProperty;

    @PostConstruct
    public void loadKey() throws Exception {
        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        String pathFromProperty = environment.getProperty(storePathProperty, Paths.get(System.getenv("HOME") + "/.keystore").toString());
        Path path = Paths.get(pathFromProperty);
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream(String.format("%s.keystore", environment.getActiveProfiles()[0]));
        if (!path.toFile().exists() && Objects.isNull(inputStream)) {
            log.error("{} no keystore is found", LogUtil.getClassMethodName());
            return;
        }
        String key = environment.getProperty(secretProperty, environment.getActiveProfiles()[0] + "-secret");
        if (key.contains("integration")) {
            key = "test-secret";
        }
        if (key.contains("stag")) {
            // stag环境使用线上的证书与秘钥
            key = "online-secret";
        }
		log.info("证书秘钥 {}", key);
        if (path.toFile().exists()) {
            inputStream = new FileInputStream(path.toFile());
        }
        keyStore.load(inputStream, key.toCharArray());
        EncryptHelper.instance.keyStore(keyStore)
                .secret(key);
    }

    @StartEventTag(cache = false, desc = "[已确认] jvm证书加载")
    @EventListener(ContextRefreshedEvent.class)
    public void contextRefreshed() {
        new ForkJoinPool().execute(() -> EncryptHelper.instance.encrypt(EncryptHelper.KeyEnu.CommonKey, "load".getBytes()));
    }
}
