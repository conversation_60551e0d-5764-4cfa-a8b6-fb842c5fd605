package moonstone.web.core.events.trade.listener;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.event.OrderCancelEvent;
import moonstone.event.OrderCreatedEvent;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.web.core.component.promotion.UserPromotionUsage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;

import java.util.List;

/**
 * 监听订单创建事件,冻结用户营销
 * Author:cp
 * Created on 7/4/16.
 */
@Slf4j
public class OrderPromotionFreezeListener {

    @RpcConsumer
    private ShopOrderReadService shopOrderReadService;

    @RpcConsumer
    private SkuOrderReadService skuOrderReadService;

    @Autowired
    private UserPromotionUsage promotionUsage;

    @EventListener(OrderCreatedEvent.class)
    public void onOrderCreated(OrderCreatedEvent orderCreatedEvent) {
        Long shopOrderId = orderCreatedEvent.getOrderId();
        ShopOrder shopOrder = findShopOrder(shopOrderId);
        List<SkuOrder> skuOrders = findSkuOrders(shopOrderId);

        //冻结用户营销(优惠券之类)
        frozenUserPromotionForShopOrder(shopOrder);
        frozenUserPromotionForSkuOrders(skuOrders);
    }

    @EventListener(OrderCancelEvent.class)
    public void onOrderCanceled(OrderCancelEvent orderCancelEvent) {
        Long shopOrderId = orderCancelEvent.getOrderLevel() == OrderLevel.SHOP.getValue() ? orderCancelEvent.getOrderId() : skuOrderReadService.findById(orderCancelEvent.getOrderId()).getResult().getOrderId();
        ShopOrder shopOrder = findShopOrder(shopOrderId);
        List<SkuOrder> skuOrders = findSkuOrders(shopOrderId);

        //冻结用户营销(优惠券之类)
        unfrozenUserPromotionForShopOrder(shopOrder);
        unfrozenUserPromotionForSkuOrders(skuOrders);
    }

    private void unfrozenUserPromotionForSkuOrders(List<SkuOrder> skuOrders) {
        for (SkuOrder skuOrder : skuOrders) {
            final Long promotionIdOfSkuOrder = skuOrder.getPromotionId();
            if (promotionIdOfSkuOrder != null) {
                if (promotionUsage.isUserPromotion(promotionIdOfSkuOrder)) {
                    boolean success = promotionUsage.unfrozen(skuOrder.getBuyerId(), promotionIdOfSkuOrder, 1);
                    if (!success) {
                        log.warn("fail to unfrozen promotion for skuOrder(id={})", skuOrder.getId());
                    }
                }
            }
        }
    }

    private void unfrozenUserPromotionForShopOrder(ShopOrder shopOrder) {
        doUnfrozenUserPromotionForShopOrder(shopOrder, shopOrder.getPromotionId());
        doUnfrozenUserPromotionForShopOrder(shopOrder, shopOrder.getShipmentPromotionId());
    }

    private void doUnfrozenUserPromotionForShopOrder(ShopOrder shopOrder, Long promotionId) {
        if (promotionId != null) {
            if (promotionUsage.isUserPromotion(promotionId)) {
                boolean success = promotionUsage.unfrozen(shopOrder.getBuyerId(), promotionId, 1);
                if (!success) {
                    log.warn("fail to unfrozen promotion(id={}) for shopOrder(id={})", promotionId, shopOrder.getId());
                }
            }
        }
    }

    private ShopOrder findShopOrder(Long shopOrderId) {
        Response<ShopOrder> shopOrderResp = shopOrderReadService.findById(shopOrderId);
        if (!shopOrderResp.isSuccess()) {
            log.error("fail to find shop order by id:{},cause:{}", shopOrderId, shopOrderResp.getError());
            throw new JsonResponseException(shopOrderResp.getError());
        }
        return shopOrderResp.getResult();
    }

    private List<SkuOrder> findSkuOrders(Long shopOrderId) {
        Response<List<SkuOrder>> skuOrdersResp = skuOrderReadService.findByShopOrderId(shopOrderId);
        if (!skuOrdersResp.isSuccess()) {
            log.error("fail to find sku order by shop order id:{} when create trade snapshot,cause:{}", shopOrderId, skuOrdersResp.getError());
            throw new JsonResponseException(skuOrdersResp.getError());
        }
        return skuOrdersResp.getResult();
    }

    private void frozenUserPromotionForShopOrder(ShopOrder shopOrder) {
        doFrozenUserPromotionForShopOrder(shopOrder, shopOrder.getPromotionId());
        doFrozenUserPromotionForShopOrder(shopOrder, shopOrder.getShipmentPromotionId());
    }

    private void doFrozenUserPromotionForShopOrder(ShopOrder shopOrder, Long promotionId) {
        if (promotionId != null) {
            if (promotionUsage.isUserPromotion(promotionId)) {
                boolean success = promotionUsage.frozen(shopOrder.getBuyerId(), promotionId, 1);
                if (!success) {
                    log.warn("fail to frozen promotion(id={}) for shopOrder(id={})", promotionId, shopOrder.getId());
                }
            }
        }
    }

    private void frozenUserPromotionForSkuOrders(List<SkuOrder> skuOrders) {
        for (SkuOrder skuOrder : skuOrders) {
            final Long promotionIdOfSkuOrder = skuOrder.getPromotionId();
            if (promotionIdOfSkuOrder != null) {
                if (promotionUsage.isUserPromotion(promotionIdOfSkuOrder)) {
                    boolean success = promotionUsage.frozen(skuOrder.getBuyerId(), promotionIdOfSkuOrder, 1);
                    if (!success) {
                        log.warn("fail to frozen promotion for skuOrder(id={})", skuOrder.getId());
                    }
                }
            }
        }
    }

}
