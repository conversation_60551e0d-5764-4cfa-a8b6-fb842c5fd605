package moonstone.web.core.events.Integral.listener;

import moonstone.common.utils.UUID;
import org.springframework.util.ObjectUtils;

import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.Random;

public class IntegralRandomUtils {
    /**
     * code(2位)+years(年份2位)+14(随机数)+随机数隐码+最后一位隐码
     *
     * @param code
     * @return
     */
    private static final String genCode(String code, String randomNum) {
        String years = new SimpleDateFormat("yy").format(new Date());
        String thirdCode = nextNum(1);
        int lens = Integer.valueOf(thirdCode);
        return code + years + randomNum.substring(0, lens) + thirdCode + randomNum.substring(lens) + thirdCode;
    }

    /**
     * 梅森旋转算法生成14位随机数
     *
     * @return
     */
    private static final String randomMTNum() {
        MTRandomUtils mtRandom = new MTRandomUtils();
        int num = mtRandom.next(32);
        if (num < 0) {
            num = -num;
        }
        String firstcode = String.valueOf(num);
        if (firstcode.length() < 10) {
            firstcode = firstcode + nextNum(10 - firstcode.length());
        } else {
            firstcode = firstcode.substring(0, 10);
        }
        String secondCode = nextNum(4);
        return firstcode + secondCode;
    }

    /**
     * 生成指定长度纯数字的字符串
     *
     * @param num
     * @return
     */
    private static final String nextNum(int num) {
        String numbers = "";
        for (int i = 0; i < num; i++) {
            int random = (int) (Math.random() * 9 + 1);
            numbers += random + "";
        }
        return numbers;
    }

    //生成14位纯数字hash值
    private static final String getUUIDHash() {
        //随机生成一位整数
//        int random = (int) (Math.random() * 9 + 1);
        Random rd = new SecureRandom();
        int random = rd.nextInt(9) + 1;
        String valueOf = String.valueOf(random);
        //生成uuid的hashCode值
        int hashCode = UUID.randomUUID().toString().hashCode();
        //可能为负数
        if (hashCode < 0) {
            hashCode = -hashCode;
        }
        return valueOf + String.format("%013d", hashCode);
    }

    /**
     * 通过UUID生成14位数字
     *
     * @return
     */
    private static final String UUIDRandom() {
        String uid = UUID.randomUUID().toString().replaceAll("[^\\d]+", "");
        if (uid.length() > 13) {
            return uid.substring(0, 14);
        } else {
            return uid + nextNum(14 - uid.length());
        }
    }

    /**
     * 生成数字随机积分码
     *
     * @param code
     */
    public static String RandomGenCode(String code) {
        Random rd = new SecureRandom();
//        int random = rd.nextInt(9) + 1;
        int randomNum = rd.nextInt(2);//((int) (10 * Math.random())) % 2;
        String genCode = "";
        switch (randomNum) {
            case 0:
                genCode = genCode(code, UUIDRandom());
                break;
            case 1:
                genCode = genCode(code, randomMTNum());
                break;
            default:
                break;
        }
        return genCode;
    }

    public static final Boolean Checking(String code) {
        return !ObjectUtils.isEmpty(code) && Objects.equals(code.substring(4 + Integer.valueOf(code.substring(19)), 5 + Integer.valueOf(code.substring(19))), code.substring(19));
    }

    /**
     * 生成16位不重复的随机数，含数字
     *
     * @return
     */
    private static String getGUID() {
        StringBuilder uid = new StringBuilder();
        //产生16位的强随机数
        Random rd = new SecureRandom();
        for (int i = 0; i < 14; i++) {
            //产生0-2的3位随机数
            int type = rd.nextInt(9);
            uid.append(type);
        }
        return uid.toString();
    }

}
