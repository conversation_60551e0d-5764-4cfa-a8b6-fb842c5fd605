package moonstone.web.core.events.customs;

import lombok.Data;

/**
 * 海关支付单申报失败事件
 */
@Data
public class CustomsDeclareFailEvent {

    /**
     * 支付单ID
     */
    private Long paymentId;

    /**
     * 店铺级别ID
     */
    private Long shopOrderId;

    /**
     * 身份证错误标志
     */
    private Boolean identityError;

    /**
     * 失败信息
     */
    private String failMsg;

    public CustomsDeclareFailEvent(Long paymentId, Long shopOrderId, Boolean identityError,
        String failMsg) {
        this.paymentId = paymentId;
        this.shopOrderId = shopOrderId;
        this.identityError = identityError;
        this.failMsg = failMsg;
    }
}
