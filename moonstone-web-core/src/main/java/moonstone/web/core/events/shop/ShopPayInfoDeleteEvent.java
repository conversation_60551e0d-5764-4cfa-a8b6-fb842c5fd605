package moonstone.web.core.events.shop;

import lombok.Data;
import moonstone.showcase.mq.Topic;
import moonstone.showcase.mq.event.MQOrderlyEvent;

@Data
public class ShopPayInfoDeleteEvent implements MQOrderlyEvent {
    public static final String TOPIC = Topic.SHOP_PAY_INFO_DELETE_EVENT;

    private Long shopId;
    private String channel;
    private Integer tradeType;

    public ShopPayInfoDeleteEvent(){}

    public ShopPayInfoDeleteEvent(Long shopId, String channel, Integer tradeType) {
        this.shopId = shopId;
        this.channel = channel;
        this.tradeType = tradeType;
    }

    @Override
    public Object toMQEvent() {
        return this;
    }

    @Override
    public String topic() {
        return TOPIC;
    }

    @Override
    public String hashKey() {
        // shopId 有序
        return String.valueOf(shopId);
    }
}