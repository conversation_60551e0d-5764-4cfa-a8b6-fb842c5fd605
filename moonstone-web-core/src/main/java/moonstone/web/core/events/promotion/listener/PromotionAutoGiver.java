package moonstone.web.core.events.promotion.listener;

import io.terminus.common.model.Paging;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.service.ShopOrderReadService;
import moonstone.promotion.component.PromotionUserUsageRecordManager;
import moonstone.promotion.constants.PromotionConditionKey;
import moonstone.promotion.constants.PromotionExtraKeys;
import moonstone.promotion.enums.PromotionStatus;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.model.UserPromotionRecord;
import moonstone.promotion.service.PromotionReadService;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.user.criteria.UserRelationEntityCriteria;
import moonstone.user.model.User;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.UserReadService;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.user.service.UserWxReadService;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.component.promotion.CouponOutlet;
import moonstone.web.core.events.promotion.PromotionPublishEvent;
import moonstone.web.core.events.promotion.PromotionTrackChangeEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class PromotionAutoGiver {

    @Resource
    private PromotionUserUsageRecordManager promotionUserUsageRecordManager;

    @Autowired
    private UserReadService<User> userReadService;
    // 优惠卷操作器
    @Autowired
    private CouponOutlet couponOutlet;

    // 用于寻找用户
    @Autowired
    private UserRelationEntityReadService userRelationEntityReadService;

    // 用于寻找数据
    @Autowired
    private PromotionReadService promotionReadService;
    @Autowired
    private UserWxReadService userWxReadService;
    @Autowired
    private ShopWxaCacheHolder shopWxaCacheHolder;
    @Autowired
    private ShopOrderReadService shopOrderReadService;

    /**
     * 当优惠卷被发布或者被激活的的时候自动分发优惠卷
     *
     * @param promotionPublishEvent 优惠卷事件
     */
    @EventListener(PromotionPublishEvent.class)
    public void onPublic(PromotionPublishEvent promotionPublishEvent) {
        log.debug("{} promotionId:{} is published:{}", LogUtil.getClassMethodName(), promotionPublishEvent.getPromotionId(), promotionPublishEvent.getTargetStatus());
        HashSet<Long> notCycle = new HashSet<>();
        // 初始化为null
        Promotion promotion = null;
        int retry = 0;
        while (retry < 5 && promotion == null) {
            try {
                promotion = promotionReadService.findById(promotionPublishEvent.getPromotionId()).getResult();
                switch (PromotionStatus.fromInt(promotion.getStatus())) {
                    case PUBLISHED, ONGOING -> retry = 100;
                    default -> {
                    }
                }
                if (retry < 5) {
                    Thread.sleep(50);
                    retry++;
                }
            } catch (Exception ex) {
                log.error("{} fail to find promotion by (id:{})", LogUtil.getClassMethodName(), promotionPublishEvent.getPromotionId(), ex);
            }
        }

        if (PromotionType.SUB_STORE_ONLY.getValue() == promotion.getType()) {
            return;
        }
        // 发布或者生效则自动分发
        if (promotion.getStatus() == null || Stream.of(PromotionStatus.PUBLISHED, PromotionStatus.ONGOING).map(PromotionStatus::getValue).noneMatch(promotion.getStatus()::equals)) {
            return;
        }
        if (promotion.getExtra() == null || !promotion.getExtra().getOrDefault(PromotionExtraKeys.AUTO_GIVE, "").equals("true")) {
            log.debug("{} skip promotionId:{}", LogUtil.getClassMethodName(), promotion.getId());
            return;
        }
        log.debug("{} now we are going to give for promotionId:{}", LogUtil.getClassMethodName(), promotion.getId());
        Long shopId = promotion.getShopId();
        // 当搜索完毕时(没有用户需要查找了)将searchTurn置-1
        int searchTurn = 1;
        OrderCriteria orderCriteria = new OrderCriteria();
        UserRelationEntityCriteria criteria = new UserRelationEntityCriteria();
        criteria.setRelationId(shopId);
        criteria.setPageSize(200);
        criteria.setType(UserRelationEntity.UserRelationType.Member.getType());
        while (searchTurn > 0) {
            Set<Long> userIds = null;
            if (shopId == 0L) {
                Paging<User> userPaging = userReadService.paging(null, null, null, null, 1, null, null, null, searchTurn, 200).getResult();
                if (userPaging == null || userPaging.isEmpty()) {
                    searchTurn = -1;
                } else {
                    userIds = userPaging.getData().stream().map(User::getId).collect(Collectors.toSet());
                }
            } else {
                criteria.setPageNo(searchTurn);
                Paging<UserRelationEntity> userRelationEntityPaging = userRelationEntityReadService.paging(criteria).getResult();
                if (userRelationEntityPaging == null || userRelationEntityPaging.isEmpty()) {
                    searchTurn = -1;
                } else {
                    userIds = userRelationEntityPaging.getData().stream().map(UserRelationEntity::getUserId).collect(Collectors.toSet());
                }
            }
            if (CollectionUtils.isEmpty(userIds)) {
                log.info("{} no more user for searchTurn[{}] promotion[{}]", LogUtil.getClassMethodName(), searchTurn, promotion.getId());
                return;
            }
            if (searchTurn == 1)
                userIds.addAll(Optional.ofNullable(shopWxaCacheHolder.findReleaseOneForShopId(shopId))
                        .map(ShopWxa::getAppId)
                        .map(userWxReadService::findUserIdByAppId).orElse(Collections.emptyList()));
            searchTurn++;
            var condition = Optional.ofNullable(promotion.getConditionParams()).orElse(new HashMap<>());
            var requireNoBuy = condition.getOrDefault(PromotionConditionKey.FIRST_BOUGHT, "false").equals("true");
            orderCriteria.setStatus(OrderStatus.getValidStatusForFirstBuyPromotion().stream().map(OrderStatus::getValue).collect(Collectors.toList()));
            orderCriteria.setShopId(shopId);

            for (Long receiver : userIds) {
                if (notCycle.contains(receiver)) {
                    log.error("{} dead cycle here:{} turn:{} promotionId:{} criteria:{}", LogUtil.getClassMethodName(), receiver, searchTurn, promotion.getId(), criteria);
                    return;
                }
                notCycle.add(receiver);
                log.debug("{} try to give promotion[{}] to user[{}] set[{}]", LogUtil.getClassMethodName(), promotion.getId(), receiver, notCycle.size());
                // 如果已经拿过这个卷了则不给
                if (promotionUserUsageRecordManager.hasUserGainPromotionEver(receiver, shopId, promotion.getId()).orElse(true)) {
                    continue;
                }
                if (requireNoBuy) {
                    orderCriteria.setBuyerId(receiver);
                    if (shopOrderReadService.count(orderCriteria).getResult() != 0) {
                        continue;
                    }
                }
                try {
                    couponOutlet.receive(receiver, promotionPublishEvent.getPromotionId());
                    EventSender.sendApplicationEvent(new PromotionTrackChangeEvent(promotionPublishEvent.getPromotionId(), 1, PromotionTrackChangeEvent.ChangeType.RECEIVED_QUANTITY));
                    increasePromotionNumWithExpect(receiver, shopId, promotion.getId(), 1L);
                } catch (Exception ex) {
                    promotionUserUsageRecordManager.removeOnePromotion(receiver, shopId, promotion.getId());
                    log.warn("{} fail to send promotion[{}] to user[{}] msg:{} method:{} line:{}", LogUtil.getClassMethodName(), promotionPublishEvent.getPromotionId(), receiver, ex.getMessage(), ex.getStackTrace()[0].getMethodName(), ex.getStackTrace()[0].getLineNumber());
                    if (ex.getMessage().equals("coupon.send.over")) {
                        log.info("{} promotion[{}] coupon send over", LogUtil.getClassMethodName(), promotionPublishEvent.getPromotionId());
                        return;
                    }
                    if (ex.getMessage().equals("coupon.is.expire")) {
                        log.error("{} promotion[{}] coupon is expired", LogUtil.getClassMethodName(), promotion.getId());
                        return;
                    }
                }
            }
        }
    }

    /**
     * 试图给予一个用户一张卷,且永远只有一张
     *
     * @param userId      用户Id
     * @param shopId      店铺Id
     * @param promotionId 优惠卷Id
     */
    public void tryToGiveOnePromotionOnlyOnce(Long userId, Long shopId, Long promotionId) {
        if (increasePromotionNumWithExpect(userId, shopId, promotionId, 1L)) {
            couponOutlet.receive(userId, promotionId);
            EventSender.sendApplicationEvent(new PromotionTrackChangeEvent(promotionId, 1, PromotionTrackChangeEvent.ChangeType.RECEIVED_QUANTITY));
        }
    }

    /**
     * 先给予一个卷,然后如果是预计的数量则返回true 不然则返回false 给予为原子操作 并发安全
     *
     * @param userId      用户Id
     * @param shopId      店铺Id
     * @param promotionId 优惠卷Id
     * @param expect      期望值
     * @return 是否可以继续给予操作
     */
    private boolean increasePromotionNumWithExpect(Long userId, Long shopId, Long promotionId, Long expect) {
        Either<UserPromotionRecord> promotionRecord = promotionUserUsageRecordManager.userGainPromotion(userId, shopId, promotionId);
        if (!promotionRecord.isSuccess()) {
            log.error("{} failed  to increase the number for userId:{} shopId:{} promotionId:{}", LogUtil.getClassMethodName(), userId, shopId, promotionId, promotionRecord.getError());
            return false;
        }
        if (Objects.equals(Optional.ofNullable(promotionRecord.take().getGainNumber()).orElse(0L), expect - 1)) {
            return true;
        } else {
            promotionUserUsageRecordManager.removeOnePromotion(userId, shopId, promotionId).logErrorStr(log::error);
        }
        return false;
    }
}
