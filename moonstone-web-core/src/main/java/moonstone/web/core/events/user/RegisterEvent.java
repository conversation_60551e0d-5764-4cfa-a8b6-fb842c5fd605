/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.events.user;


import moonstone.common.model.CommonUser;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-31
 */
public class RegisterEvent extends UserEvent {

    public RegisterEvent(HttpServletRequest request, HttpServletResponse response, CommonUser user) {
        super(request, response, user);
    }

    public RegisterEvent(HttpServletRequest request, HttpServletResponse response) {
        super(request, response);
    }
}
