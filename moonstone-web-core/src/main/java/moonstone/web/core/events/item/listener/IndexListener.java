///*
// * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
// */
//
//package moonstone.web.core.events.item.listener;
//
//import com.alibaba.fastjson.JSONObject;
//import io.terminus.boot.rpc.common.annotation.RpcConsumer;
//import io.terminus.common.model.Response;
//import io.vertx.core.AbstractVerticle;
//import lombok.extern.slf4j.Slf4j;
//import moonstone.cache.TaxCacher;
//import moonstone.common.VertxEventTag;
//import moonstone.common.utils.LogUtil;
//import moonstone.item.model.Sku;
//import moonstone.item.service.SkuReadService;
//import moonstone.search.item.ItemSearchWriteService;
//import moonstone.search.weShopItem.WeShopItemSearchWriteService;
//import moonstone.showcase.mq.config.anno.MQEventConsumerMethod;
//import moonstone.web.core.component.cache.SkuStockCacheAtRedisManager;
//import moonstone.web.core.component.vertx.VertxEventBusListener;
//import moonstone.web.core.events.item.DistributionPriceSetEvent;
//import moonstone.web.core.events.item.ItemDeletedEvent;
//import moonstone.web.core.events.item.ItemUpdateEvent;
//import org.apache.rocketmq.spring.annotation.MessageModel;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.event.EventListener;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * 商品搜索引擎更新
// *
// * @ConditionalOnProperty(value = "search.enabled", havingValue = "true")
// *      只有当配置中存在 search.enabled 属性且其值明确等于 "true" 时，条件才成立
// *      如果配置中完全不存在 search.enabled 属性，条件不成立（Bean不会创建）
// */
//@Slf4j
//@Component
////@ConditionalOnProperty(value = "search.enabled", havingValue = "true")
//public class IndexListener extends AbstractVerticle {
//
//    @RpcConsumer
//    private ItemSearchWriteService itemSearchWriteService;
//
//    @RpcConsumer
//    private WeShopItemSearchWriteService weShopItemSearchWriteService;
//
//    @RpcConsumer
//    private SkuReadService skuReadService;
//
//
//    @Autowired
//    private SkuStockCacheAtRedisManager skuStockCacheAtRedisManager;
//
//    @Autowired
//    private TaxCacher taxCacher;
//
//    @VertxEventTag(cache = true, desc = "缓存失活", model = MessageModel.BROADCASTING)
////    @VertxEventBusListener(ItemUpdateEvent.class)
//    @MQEventConsumerMethod(ItemUpdateEvent.class)
//    public void onItemUpdate(ItemUpdateEvent itemUpdateEvent) {
//        log.info("ItemUpdateEvent LOG : onItemUpdate {}", itemUpdateEvent.getItemId());
//        taxCacher.invalidateAll();
//
//        updateStockCache(itemUpdateEvent.getItemId());
//    }
//
//    /**
//     * 更新ES（集群消费模式）
//     * @param itemUpdateEvent
//     */
//    @MQEventConsumerMethod(value = ItemUpdateEvent.class)
//    public void onItemUpdateES(ItemUpdateEvent itemUpdateEvent) {
//        log.info("ItemUpdateEvent LOG : onItemUpdateES {}", itemUpdateEvent.getItemId());
//        Long itemId = itemUpdateEvent.getItemId();
//        Response<Boolean> response = itemSearchWriteService.update(itemId);
//        if (!response.isSuccess()) {
//            log.error("failed to update item(id={}) index, error code:{}",
//                    itemId, response.getError());
//        }
//        Response<Boolean> weShopItemResponse = weShopItemSearchWriteService.updateByItem(itemId);
//        if (!weShopItemResponse.isSuccess()) {
//            log.error("failed to update weShopItem index by itemId={}, error code: {}", itemId, weShopItemResponse.getError());
//        }
//    }
//
//    /**
//     * 清楚商品在redis中的缓存迫使其从数据库中刷新（可能会导致超卖 但是由于前者的数据更新 可能性大幅度下降)
//     *
//     * @param itemId 商品Id
//     */
//    private void updateStockCache(Long itemId) {
//        Response<List<Sku>> rSkus = skuReadService.findSkusByItemId(itemId);
//        if (!rSkus.isSuccess()) {
//            log.error("{} failed to update sku stock in redis may cause item(id:{}) oversell or unsellable", LogUtil.getClassMethodName(), itemId);
//            return;
//        }
//        skuStockCacheAtRedisManager.removeCache(rSkus.getResult().stream().map(Sku::getId).collect(Collectors.toList()));
//    }
//
//    @VertxEventTag(cache = true, desc = "缓存失活", model = MessageModel.BROADCASTING)
////    @VertxEventBusListener(ItemDeletedEvent.class)
//    public void onItemDeleted(ItemDeletedEvent itemDeletedEvent) {
//        log.info("IndexListener#onItemDeleted {}", JSONObject.toJSONString(itemDeletedEvent));
//
//        taxCacher.invalidateAll();
//    }
//
//    @VertxEventTag(cache = false, desc = "更新ES-转MQ", model = MessageModel.CLUSTERING)
//    @VertxEventBusListener(ItemDeletedEvent.class)
//    public void onItemDeletedES(ItemDeletedEvent itemDeletedEvent) {
//        log.info("IndexListener#onItemDeletedES {}", JSONObject.toJSONString(itemDeletedEvent));
//
//        Long itemId = itemDeletedEvent.getItemId();
//        Response<Boolean> response = itemSearchWriteService.delete(itemId);
//        if (!response.isSuccess()) {
//            log.error("failed to delete item(id={}) index, error code:{}",
//                    itemId, response.getError());
//        }
//        Response<Boolean> weShopItemResponse = weShopItemSearchWriteService.deleteByItem(itemId);
//        if (!weShopItemResponse.isSuccess()) {
//            log.error("failed to delete weShopItem index by itemId={}, error code: {}", itemId, weShopItemResponse.getError());
//        }
//    }
//
//    @EventListener(DistributionPriceSetEvent.class)
//    public void onDistributionPriceSet(DistributionPriceSetEvent distributionPriceSetEvent) {
//        taxCacher.invalidateAll();
//        Long itemId = distributionPriceSetEvent.getItemId();
//        Response<Boolean> weShopItemResponse = weShopItemSearchWriteService.updateByItem(itemId);
//        if (!weShopItemResponse.isSuccess()) {
//            log.error("failed to update weShopItem index by itemId={}, error code: {}", itemId, weShopItemResponse.getError());
//        }
//    }
//}
