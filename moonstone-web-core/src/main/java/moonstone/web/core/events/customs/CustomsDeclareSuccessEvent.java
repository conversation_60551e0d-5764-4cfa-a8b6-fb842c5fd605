package moonstone.web.core.events.customs;

import lombok.Data;
import moonstone.showcase.mq.Topic;
import moonstone.showcase.mq.event.MQEvent;

/**
 * 海关支付单申报失败事件
 */
@Data
public class CustomsDeclareSuccessEvent implements MQEvent {

    public static final String TOPIC = Topic.CUSTOMS_DECLARE_SUCCESS_EVENT;

    /**
     * 支付单ID
     */
    private Long paymentId;

    /**
     * 店铺级别ID
     */
    private Long shopOrderId;

    /**
     * 身份证错误标志
     */
    private Boolean identityError;

    /**
     * 失败信息
     */
    private String successMsg;

    public CustomsDeclareSuccessEvent(){}

    public CustomsDeclareSuccessEvent(Long paymentId, Long shopOrderId, Boolean identityError,
                                      String successMsg) {
        this.paymentId = paymentId;
        this.shopOrderId = shopOrderId;
        this.identityError = identityError;
        this.successMsg = successMsg;
    }

    @Override
    public CustomsDeclareSuccessEvent toMQEvent() {
        return this;
    }

    @Override
    public String topic() {
        return TOPIC;
    }
}
