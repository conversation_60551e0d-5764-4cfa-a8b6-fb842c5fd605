package moonstone.web.core.events.shop;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import moonstone.shop.model.ShopPayInfo;
import moonstone.showcase.mq.Topic;
import moonstone.showcase.mq.event.MQOrderlyEvent;

@Getter
@AllArgsConstructor
@EqualsAndHashCode
@ToString
public final class ShopPayInfoChangeEvent implements MQOrderlyEvent {
    public static final String TOPIC = Topic.SHOP_PAY_INFO_CHANGE_EVENT;

    private ShopPayInfo shopPayInfo;

    public ShopPayInfoChangeEvent(){}

    @Override
    public Object toMQEvent() {
        return this;
    }

    @Override
    public String topic() {
        return TOPIC;
    }

    @Override
    public String hashKey() {
        if(shopPayInfo == null){
            return "";
        }
        // shopId 有序
        return String.valueOf(shopPayInfo.getShopId());
    }
}