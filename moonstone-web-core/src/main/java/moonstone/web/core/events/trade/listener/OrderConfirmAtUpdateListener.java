package moonstone.web.core.events.trade.listener;

import lombok.extern.slf4j.Slf4j;
import moonstone.event.OrderConfirmEvent;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.Shipment;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.ShipmentReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.ShopOrderWriteService;
import moonstone.order.service.SkuOrderReadService;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 订单确认收货时，更新 shopOrder 上的确认收货时间信息
 */
@Slf4j
@Component
public class OrderConfirmAtUpdateListener {

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Resource
    private ShipmentReadService shipmentReadService;

    @Resource
    private ShopOrderWriteService shopOrderWriteService;

    /**
     * 订单确认收货时，更新 shopOrder 上的确认收货时间信息
     *
     * @param event
     */
    @EventListener(OrderConfirmEvent.class)
    public void onOrderConfirm(OrderConfirmEvent event) {
        Long orderId = event.orderId();
        Integer orderType = event.getOrderType();
        log.info("OrderConfirmAtUpdateListener.onOrderConfirm, receive event, orderId={}, orderType={}", orderId, orderType);

        // 更新shopOrder上的确认收货时间信息
        updateConfirmAt(orderId, orderType);
    }

    /**
     * 更新shopOrder上的确认收货时间信息
     *
     * @param orderId
     * @param orderType
     */
    private void updateConfirmAt(Long orderId, Integer orderType) {
        if (orderType == null || orderId == null) {
            return;
        }

        try {
            // 主订单id
            Long shopOrderId = findShopOrderId(orderId, orderType);
            if (shopOrderId == null) {
                log.error("OrderConfirmAtUpdateListener.updateConfirmAt, orderId={}, orderType={}, fail to determine shopOrderId",
                        orderId, orderType);
                return;
            }

            // 找出最早和最晚的确认收货时间
            var confirmTimeInfo = findFirstAndLastConfirmAt(shopOrderId);
            if (confirmTimeInfo == null) {
                log.error("OrderConfirmAtUpdateListener.updateConfirmAt, orderId={}, orderType={}, confirmTimeInfo is null.",
                        orderId, orderType);
                return;
            }

            // 更新
            updateFirstAndLastConfirmAt(shopOrderId, confirmTimeInfo.getLeft(), confirmTimeInfo.getRight());
        } catch (Exception ex) {
            log.error("OrderConfirmAtUpdateListener.updateConfirmAt error, orderId={}, orderType={} ", orderId, orderType, ex);
        }
    }

    private void updateFirstAndLastConfirmAt(Long shopOrderId, Date first, Date last) {
        if (shopOrderId == null || (first == null && last == null)) {
            return;
        }

        var updateObject = new ShopOrder();
        updateObject.setId(shopOrderId);
        updateObject.setFirstConfirmAt(first);
        updateObject.setLastConfirmAt(last);

        shopOrderWriteService.update(updateObject);
    }

    /**
     * 找出最早和最晚的确认收货时间
     *
     * @param shopOrder
     * @return left = 最早确认收货时间， right = 最晚确认收货时间
     */
    public Pair<Date, Date> findFirstAndLastConfirmAt(ShopOrder shopOrder) {
        if (shopOrder == null) {
            log.error("The target shopOrder is null!");
            return null;
        }

        // 关联的发货信息
        List<Shipment> shipmentList = findShipments(shopOrder.getId());
        if (CollectionUtils.isEmpty(shipmentList)) {
            log.warn("OrderConfirmAtUpdateListener.updateConfirmAt, shopOrderId={}, no shipments found.",
                    shopOrder.getId());
            return null;
        }

        Date first = shopOrder.getFirstConfirmAt(), last = shopOrder.getLastConfirmAt();
        for (var current : shipmentList) {
            if (current.getConfirmAt() == null) {
                continue;
            }

            if (first == null || current.getConfirmAt().before(first)) {
                first = current.getConfirmAt();
            }
            if (last == null || current.getConfirmAt().after(last)) {
                last = current.getConfirmAt();
            }
        }

        return Pair.of(first, last);
    }

    /**
     * 找出最早和最晚的确认收货时间
     *
     * @param shopOrderId
     * @return left = 最早确认收货时间， right = 最晚确认收货时间
     */
    private Pair<Date, Date> findFirstAndLastConfirmAt(Long shopOrderId) {
        return findFirstAndLastConfirmAt(shopOrderReadService.findById(shopOrderId).getResult());
    }

    private List<Shipment> findShipments(Long shopOrderId) {
        var list = shipmentReadService.findByOrderIdAndOrderLevel(shopOrderId, OrderLevel.SHOP).getResult();
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }

        var skuOrders = skuOrderReadService.findByShopOrderId(shopOrderId).getResult();
        if (CollectionUtils.isEmpty(skuOrders)) {
            return Collections.emptyList();
        }

        return shipmentReadService.findByOrderIdsAndOrderLevel(skuOrders.stream().map(SkuOrder::getId).toList(), OrderLevel.SKU).getResult();
    }

    private Long findShopOrderId(Long orderId, Integer orderType) {
        Long shopOrderId = null;

        if (OrderLevel.SHOP.getValue() == orderType) {
            shopOrderId = orderId;
        } else if (OrderLevel.SKU.getValue() == orderType) {
            shopOrderId = skuOrderReadService.findById(orderId).getResult().getOrderId();
        }

        return shopOrderId;
    }
}
