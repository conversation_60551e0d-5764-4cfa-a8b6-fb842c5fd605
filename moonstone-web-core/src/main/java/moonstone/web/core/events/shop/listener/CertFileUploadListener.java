package moonstone.web.core.events.shop.listener;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import lombok.extern.slf4j.Slf4j;
import moonstone.shop.CertFileStatus;
import moonstone.shop.model.ShopPayInfo;
import moonstone.shop.service.ShopPayInfoWriteService;
import moonstone.web.core.events.shop.CertFileUploadEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Created by CaiZhy on 2018/9/26.
 */
@Slf4j
@Component
public class CertFileUploadListener {

    @RpcConsumer
    private ShopPayInfoWriteService shopPayInfoWriteService;

    @EventListener(CertFileUploadEvent.class)
    public void onUpload(CertFileUploadEvent event) {
        ShopPayInfo shopPayInfo = new ShopPayInfo();
        shopPayInfo.setId(event.getShopPayInfoId());
        shopPayInfo.setWxCertFileStatus(CertFileStatus.UPLOADED.getValue());
        shopPayInfoWriteService.update(shopPayInfo);
        log.info("upload certFile for shopPayInfo( id={})", shopPayInfo.getId());
    }
}
