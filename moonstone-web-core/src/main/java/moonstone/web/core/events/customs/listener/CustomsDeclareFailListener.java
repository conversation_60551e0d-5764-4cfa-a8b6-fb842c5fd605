package moonstone.web.core.events.customs.listener;

import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.enums.CountryCode;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.model.OrderPayment;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.events.customs.CustomsDeclareFailEvent;
import moonstone.web.core.events.msg.MsgSendRequestEvent;
import moonstone.web.core.msg.model.WxMsgData;
import moonstone.web.core.msg.service.MsgRequestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CustomsDeclareFailListener {
    @Resource
    ShopOrderReadService shopOrderReadService;
    @Resource
    UserReadService<User> userUserReadService;
    @Autowired
    ShopWxaCacheHolder shopWxaCacheHolder;
    @Resource
    ShopWxaProjectReadService shopWxaProjectReadService;
    @Autowired
    MsgRequestService msgRequestService;
    @Autowired
    EnvironmentConfig environmentConfig;
    @Resource
    PaymentReadService paymentReadService;


    @EventListener(CustomsDeclareFailEvent.class)
    public void execute(CustomsDeclareFailEvent customsDeclareFailEvent) {
        log.error("{} shopOrder id:{},payment id:{} custom declare fail because:{}", LogUtil.getClassMethodName(), customsDeclareFailEvent.getShopOrderId(), customsDeclareFailEvent.getPaymentId(), customsDeclareFailEvent.getFailMsg());
        val rShopOrder = shopOrderReadService.findById(customsDeclareFailEvent.getShopOrderId());
        if (!rShopOrder.isSuccess()) {
            log.error("shop order find fail,cause:{}", rShopOrder.getError());
            return;
        }
        ShopOrder shopOrder = rShopOrder.getResult();
        if (customsDeclareFailEvent.getFailMsg().contains("身份证")) {
            sendIdentityErrorSms(shopOrder);
            sendIdentityErrorWechatMsg(shopOrder, customsDeclareFailEvent);
        }
    }

    private void sendIdentityErrorWechatMsg(ShopOrder shopOrder, CustomsDeclareFailEvent customsDeclareFailEvent) {
        try {
            // 由于所有关联订单单为同一个支付单，一个失败则全部失败
            Long orderId = shopOrder.getId();
            Long userId = shopOrder.getBuyerId();

            List<OrderPayment> orderPayments = paymentReadService.findOrderIdsByPaymentId(customsDeclareFailEvent.getPaymentId()).getResult();
            if (orderPayments == null) {
                log.error("{} order-by-payment(id:{}) query failed", LogUtil.getClassMethodName(), customsDeclareFailEvent.getPaymentId());
                return;
            }
            Long shopId = shopOrder.getShopId();
            ShopWxa shopWxa = shopWxaCacheHolder.findReleaseOneForShopId(shopId);
            Long shopWxaId = shopWxa.getId();
            Long projectId = shopWxaProjectReadService.findByShopWxaId(shopWxaId).getResult().stream()
                    .filter(project -> project.getStatus() > 0).findFirst().map(ShopWxaProject::getId).orElseThrow(() -> Translate.exceptionOf("无法查找到订单[%s]对应的项目Id", shopOrder.getId()));
            String page = "/pages/my/payer-modify?orderId=" + orderId + "&eventId=" + System.currentTimeMillis();
            String notify = customsDeclareFailEvent.getFailMsg().length() > 20 ? customsDeclareFailEvent.getFailMsg().substring(0, 20) : customsDeclareFailEvent.getFailMsg();
            msgRequestService.send(projectId, userId, "certification",
                    (request, sender) -> {
                        WxMsgData wxMsgData = WxMsgData.builder()
                                .object(ImmutableMap.of("thing1.DATA", "海关订单实名审核",
                                        "phrase2.DATA", "审核失败",
                                        "time3.DATA", LocalDateTime.now().format(DateTimeFormatter.ofPattern("y-M-d H:m:s")),
                                        "thing4.DATA", notify
                                ))
                                .page(page)
                                .build();
                        log.debug("[Wechat Subscribe Msg] generate Data -> {}", wxMsgData);
                        sender.accept(wxMsgData);
                    }).take();
        } catch (Exception ex) {
            log.error("CustomsDeclareFailListener.sendIdentityErrorWechatMsg error, shopOrderId={}", shopOrder.getId(), ex);
        }
    }

    private void sendIdentityErrorSms(ShopOrder shopOrder) {
        try {
            val rUser = userUserReadService.findById(shopOrder.getBuyerId());
            if (!rUser.isSuccess()) {
                log.error("user find by id:{}", shopOrder.getBuyerId());
                return;
            }
            String mobile = rUser.getResult().getFullMobile();
            if (mobile.startsWith(CountryCode.PREFIX_CODE)) {
                if (mobile.startsWith(CountryCode.China.getCode())) {
                    mobile = mobile.substring(CountryCode.China.getCode().length());
                }
            }
            MsgSendRequestEvent msgSendRequestEvent = new MsgSendRequestEvent(mobile,
                    "订单实名验证失败", ImmutableMap.of("name", shopOrder.getBuyerName(), "orderSn", shopOrder.getId()));
            EventSender.sendApplicationEvent(msgSendRequestEvent);
        } catch (Exception ex) {
            log.error("CustomsDeclareFailListener.sendIdentityErrorSms error, shopOrderId={}", shopOrder.getId(), ex);
        }
    }
}
