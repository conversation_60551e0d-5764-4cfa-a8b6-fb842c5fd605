package moonstone.web.core.events.msg;

import lombok.Getter;
import lombok.Setter;
import moonstone.showcase.mq.Topic;
import moonstone.showcase.mq.event.MQEvent;

import java.io.Serializable;
import java.util.Map;

@Getter
@Setter
public class MsgSendRequestEvent implements Serializable, MQEvent {

    public static final String TOPIC = Topic.MSG_SEND_REQUEST_EVENT;

    public String toes;
    public String template;
    public Map<String, Serializable> context;

    public MsgSendRequestEvent(){}

    public MsgSendRequestEvent(String toes, String template, Map<String, Serializable> context) {
        this.toes = toes;
        this.template = template;
        this.context = context;
    }

    @Override
    public MsgSendRequestEvent toMQEvent() {
        return this;
    }

    @Override
    public String topic() {
        return TOPIC;
    }

    public String toes() {
        return toes;
    }

    public String template() {
        return template;
    }

    public Map<String, Serializable> context() {
        return context;
    }
}