package moonstone.web.core.events.item.listener;

import cn.hutool.json.JSONUtil;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.json.JsonArray;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.constants.RocketMQConstant;
import moonstone.common.utils.LogUtil;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.OrderRefund;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.RefundWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.storage.service.StorageService;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.api.RefreshItemStockByTimePeriod;
import moonstone.web.core.events.item.ReturnStockEvent;
import moonstone.web.core.events.item.handler.OrderInventoryValidateUtil;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReturnStockListener extends AbstractVerticle {
    @Autowired
    SkuCacheHolder skuCacheHolder;
    @Autowired
    private RefundReadService refundReadService;
    @Autowired
    private RefundWriteService refundWriteService;
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private ShopOrderReadService shopOrderReadService;
    @Autowired
    private StorageService storageService;

    @Resource
    private ItemReadService itemReadService;
    @Resource
    private OrderInventoryValidateUtil orderInventoryValidateUtil;

    @Resource
    private RocketMQProducer rocketMQProducer;

    @Transactional(rollbackFor = RuntimeException.class)
    @EventListener
    @VertxEventBusListener(ReturnStockEvent.class)
    public void returnStock(ReturnStockEvent returnStockEvent) {
        log.info("接受到回退库存的消息 {}", JSONUtil.toJsonStr(returnStockEvent));
        try {
            Set<Long> itemSet = new HashSet<>();
            //根据退款单id列表查询绑定关系
            Response<List<OrderRefund>> rOrderRefunds = refundReadService.findOrderIdsByRefundId(returnStockEvent.getRefundId());
            if (!rOrderRefunds.isSuccess()) {
                log.error("fail to find orderRefunds by refundId={}, cause:{}", returnStockEvent.getRefundId(), rOrderRefunds.getError());
                throw new JsonResponseException("fail to find orderRefunds");
            }

            for (OrderRefund orderRefund : rOrderRefunds.getResult()) {
                if (orderRefund.getStatus() == OrderStatus.REFUND.getValue()) {
                    log.debug("{} order[{} at [{}] refundId=>{}] return stock done", LogUtil.getClassMethodName(), orderRefund.getOrderId(), orderRefund.getUpdatedAt(), orderRefund.getRefundId());
                    continue;
                }
                ShopOrder shopOrder = shopOrderReadService.findById(orderRefund.getOrderId()).getResult();
                switch (orderRefund.getOrderLevel()) {
                    case SHOP:
                        Response<List<SkuOrder>> rSkuOrderList = skuOrderReadService.findByShopOrderId(orderRefund.getOrderId());
                        if (!rSkuOrderList.isSuccess()) {
                            log.error("fail to find skuOrderList by shopOrderId={}, cause:{}", orderRefund.getOrderId(), rSkuOrderList.getError());
                            throw new JsonResponseException("fail to find skuOrderList");
                        }
                        List<Long> skuIdList = new ArrayList<>();
                        for (SkuOrder skuOrder : rSkuOrderList.getResult()) {

                            // 拥有独立库存的订单 库存不返还到老库存上
                            if(orderInventoryValidateUtil.isIndependentStockAvailable(shopOrder, skuOrder)){
                                log.info("SHOP订单");
                                continue;
                            }

                            log.debug("{} return stock [{}] for orderId[orderId => {}, id => {}]", LogUtil.getClassMethodName(),
                                    skuOrder.getQuantity(), skuOrder.getOrderId(), skuOrder.getId());
                            Item item = itemReadService.findById(skuOrder.getItemId()).getResult();
                            Response<Boolean> deltaR = storageService.increaseBy(skuOrder.getSkuId(), null, null, skuOrder.getQuantity(),item.getType());
                            if (!deltaR.isSuccess()) {
                                log.error("fail to increase sku(id={})'s storage, delta={}, error code:{}",
                                        skuOrder.getSkuId(), skuOrder.getQuantity(), deltaR.getError());
                            }
                            skuIdList.add(skuOrder.getSkuId());
                            itemSet.add(skuCacheHolder.findSkuById(skuOrder.getSkuId()).getItemId());
                        }
                        rocketMQProducer.sendMessage(RocketMQConstant.APP_ADMIN_TOPIC, RocketMQConstant.SKU_STOCK_CHANGE_TAG, JSONUtil.toJsonStr(skuIdList));
                        log.info("退款回退库存 Shop 订单 发送商品库存变化的消息 {}", JSONUtil.toJsonStr(skuIdList));
                        break;
                    case SKU:
                        Response<SkuOrder> rSkuOrder = skuOrderReadService.findById(orderRefund.getOrderId());
                        if (!rSkuOrder.isSuccess()) {
                            log.error("fail to find skuOrder by id={}, cause:{}", orderRefund.getOrderId(), rSkuOrder.getError());
                            throw new JsonResponseException("fail to find skuOrder");
                        }
                        SkuOrder skuOrder = rSkuOrder.getResult();

                        // 拥有独立库存的订单 库存不返还到老库存上
                        if(orderInventoryValidateUtil.isIndependentStockAvailable(shopOrder, skuOrder)){
                            log.info("SKU订单");
                            continue;
                        }

                        log.debug("{} return stock [{}] for orderId[orderId => {}, id => {}]", LogUtil.getClassMethodName(),
                                skuOrder.getQuantity(), skuOrder.getOrderId(), skuOrder.getId());
                        Item item = itemReadService.findById(skuOrder.getItemId()).getResult();
                        Response<Boolean> deltaR = storageService.increaseBy(skuOrder.getSkuId(), null, null, skuOrder.getQuantity(),item.getType());
                        if (!deltaR.isSuccess()) {
                            log.error("fail to increase sku(id={})'s storage, delta={}, error code:{}",
                                    skuOrder.getSkuId(), skuOrder.getQuantity(), deltaR.getError());
                        }
                        itemSet.add(skuCacheHolder.findSkuById(skuOrder.getSkuId()).getItemId());
                        rocketMQProducer.sendMessage(RocketMQConstant.APP_ADMIN_TOPIC, RocketMQConstant.SKU_STOCK_CHANGE_TAG, JSONUtil.toJsonStr(List.of(skuOrder.getSkuId())));
                        log.info("退款回退库存 Sku订单 发送商品库存变化的消息 {}", skuOrder.getSkuId());
                        break;
                    default:
                }
                JsonArray itemArray = new JsonArray();
                itemSet.forEach(itemArray::add);
                vertx.eventBus().request(RefreshItemStockByTimePeriod.ADDRESS, itemArray);
                refundWriteService.updateOrderRefundStatus(orderRefund.getRefundId(), OrderStatus.REFUND.getValue()).take();
            }
        } catch (Exception e) {
            log.error("fail to return stock by refundId={}, cause:{}", returnStockEvent.getRefundId(), e.getMessage());
            throw new RuntimeException(e);
        }
    }

}
