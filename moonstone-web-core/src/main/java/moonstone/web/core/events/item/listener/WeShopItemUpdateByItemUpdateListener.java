package moonstone.web.core.events.item.listener;

import io.vertx.core.AbstractVerticle;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.showcase.mq.config.anno.MQEventConsumerMethod;
import moonstone.weShop.model.WeShopItem;
import moonstone.weShop.service.WeShopItemReadService;
import moonstone.weShop.service.WeShopItemWriteService;
import moonstone.web.core.component.qrCode.WeShopItemShareImageCacheHolder;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.item.ItemDeletedEvent;
import moonstone.web.core.events.item.ItemEvent;
import moonstone.web.core.events.item.ItemUpdateEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@SuppressWarnings("UnstableApiUsage")
@Slf4j
@Component
public class WeShopItemUpdateByItemUpdateListener extends AbstractVerticle {

    @Autowired
    private ItemReadService itemReadService;
    @Autowired
    private WeShopItemWriteService weShopItemWriteService;
    @Autowired
    private WeShopItemReadService weShopItemReadService;
    @Autowired
    private WeShopItemShareImageCacheHolder weShopItemShareImageCacheHolder;

    // 微分销
//    @VertxEventBusListener(ItemUpdateEvent.class)
//    @MQEventConsumerMethod(ItemUpdateEvent.class)
//    public void dumpWeShopItemWhenItemUpdate(ItemUpdateEvent itemUpdateEvent) {
//        log.info("ItemUpdateEvent LOG : WeShopItemUpdateByItemUpdateListener {}", itemUpdateEvent.getItemId());
//        dumpWeshopItem(itemUpdateEvent);
//    }
//
//    @VertxEventBusListener(ItemDeletedEvent.class)
//    public void dumpWeShopItemWhenItemDelete(ItemDeletedEvent itemDeletedEvent) {
//        dumpWeshopItem(itemDeletedEvent);
//    }
//
//    public void dumpWeshopItem(ItemEvent itemEvent) {
//        if (itemEvent instanceof ItemDeletedEvent || itemEvent instanceof ItemUpdateEvent) {
//            Item item = itemReadService.findById(itemEvent.getItemId()).getResult();
//            //  下架
//            if (item.getStatus() < 0) {
//                weShopItemWriteService.updateStatusByItemId(item.getId(), -1);
//            }
//            for (WeShopItem weShopItem : weShopItemReadService.findByItemId(item.getId()).getResult()) {
//                weShopItemShareImageCacheHolder.invalidateForWeShopItemId(weShopItem.getId());
//            }
//        }
//    }


}
