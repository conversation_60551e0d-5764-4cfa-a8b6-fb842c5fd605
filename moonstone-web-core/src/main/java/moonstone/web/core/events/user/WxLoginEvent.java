package moonstone.web.core.events.user;

import lombok.Getter;
import moonstone.common.model.WxCommonUser;

import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;

/**
 * Created by <PERSON>ai<PERSON><PERSON> on 2018/10/12.
 */
public class WxLoginEvent implements Serializable{
    private static final long serialVersionUID = -5269968564510671693L;

    @Getter
    private final HttpServletResponse response;

    @Getter
    private final WxCommonUser user;

    public WxLoginEvent(HttpServletResponse response) {
        this(response, null);
    }

    public WxLoginEvent(WxCommonUser user) {
        this(null, user);
    }

    public WxLoginEvent(HttpServletResponse response, WxCommonUser user) {
        this.response = response;
        this.user = user;
    }
}
