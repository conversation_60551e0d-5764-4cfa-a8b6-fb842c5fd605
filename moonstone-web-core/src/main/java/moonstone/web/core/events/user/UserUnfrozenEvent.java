/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.events.user;

import lombok.Getter;

import java.io.Serializable;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-31
 */
public class UserUnfrozenEvent implements Serializable {
    private static final long serialVersionUID = 8800453857299602788L;
    @Getter
    private final Long userId;

    @Getter
    private final Long operatorId;

    public UserUnfrozenEvent(Long userId, Long operatorId) {
        this.userId = userId;
        this.operatorId = operatorId;
    }
}
