package moonstone.web.core.events.user;

import lombok.Getter;

import java.io.Serializable;

/**
 * Author:cp
 * Created on 09/11/2016.
 */
public class LoginUserCacheInvalidateEvent implements Serializable {

    private static final long serialVersionUID = 3355281396084367180L;

    @Getter
    private final Long userId;

    public LoginUserCacheInvalidateEvent(Long userId) {
        this.userId = userId;
    }
}
