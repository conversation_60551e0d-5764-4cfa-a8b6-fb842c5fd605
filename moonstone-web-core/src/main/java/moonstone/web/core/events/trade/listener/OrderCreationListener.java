package moonstone.web.core.events.trade.listener;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import moonstone.component.item.component.ItemSnapshotFactory;
import moonstone.event.OrderCreatedEvent;
import moonstone.order.api.Y800OrderIdGenerator;
import moonstone.order.model.OrderBase;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.web.core.model.UserRegisterForShopEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;

import java.util.List;

/**
 * Author:cp
 * Created on 5/31/16.
 */
@Slf4j
public class OrderCreationListener {

    @Autowired
    private SkuOrderReadService skuOrderReadService;

    @Autowired
    private OrderWriteService orderWriteService;

    @Autowired
    private ItemSnapshotFactory itemSnapshotFactory;

    @Autowired
    private Y800OrderIdGenerator y800OrderIdGenerator;

    @Autowired
    private Vertx vertx;

    @EventListener(OrderCreatedEvent.class)
    public void onCreated(OrderCreatedEvent orderCreatedEvent) {
        vertx.createSharedWorkerExecutor("OrderCreated").executeBlocking(p -> {
            try {
                Long shopOrderId = orderCreatedEvent.getOrderId();
                List<SkuOrder> skuOrders = findSkuOrders(shopOrderId);
                //创建交易快照
                createTradeSnapshot(skuOrders);
                //发送注册用户事件
                sendUserRegisterEventForShop(skuOrders);
                //获取申报订单号
                p.complete(y800OrderIdGenerator.getDeclareId(shopOrderId));
            } finally {
                // create order cache
            }
        });
    }

    private void sendUserRegisterEventForShop(List<SkuOrder> skuOrders) {
        skuOrders.stream().map(OrderBase::getShopId)
                .distinct()
                .forEach(shopId -> EventSender.sendApplicationEvent(new UserRegisterForShopEvent(skuOrders.get(0).getBuyerId(), shopId)));
    }

    private List<SkuOrder> findSkuOrders(Long shopOrderId) {
        Response<List<SkuOrder>> skuOrdersResp = skuOrderReadService.findByShopOrderId(shopOrderId);
        if (!skuOrdersResp.isSuccess()) {
            log.error("fail to find sku order by shop order id:{} when create trade snapshot,cause:{}", shopOrderId, skuOrdersResp.getError());
            throw new JsonResponseException(skuOrdersResp.getError());
        }
        return skuOrdersResp.getResult();
    }

    private void createTradeSnapshot(List<SkuOrder> skuOrders) {
        for (SkuOrder skuOrder : skuOrders) {
            Response<Long> getSnapshotIdResp = itemSnapshotFactory.getItemSnapshotId(skuOrder.getItemId());
            if (!getSnapshotIdResp.isSuccess()) {
                log.error("fail to get item(id={}) snapshot id,cause:{}", skuOrder.getItemId(), getSnapshotIdResp.getError());
                continue;
            }
            Long itemSnapshotId = getSnapshotIdResp.getResult();

            Response<Boolean> setResp = orderWriteService.setItemSnapshot(skuOrder.getId(), itemSnapshotId);
            if (!setResp.isSuccess()) {
                log.error("fail to set item snapshot(id={}) for sku order(id={}),cause:{}",
                        itemSnapshotId, skuOrder.getId(), setResp.getError());
            }
        }
    }
}
