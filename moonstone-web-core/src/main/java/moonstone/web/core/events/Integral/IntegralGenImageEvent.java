package moonstone.web.core.events.Integral;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/21 11:07
 */
@NoArgsConstructor
@Data
public class IntegralGenImageEvent implements Serializable {
    private static final long serialVersionUID = 162565869607640482L;

    private Long shopId;
    private long num;
    private long faceValue;
    private long thirdId;

    public IntegralGenImageEvent(Long shopId, long num,long faceValue,long thirdId){
        this.shopId = shopId;
        this.num = num;
        this.faceValue = faceValue;
        this.thirdId = thirdId;
    }
}
