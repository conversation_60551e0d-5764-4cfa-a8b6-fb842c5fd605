package moonstone.web.core.events.thirdParty;

import moonstone.common.enums.ThirdPartySystem;
import moonstone.thirdParty.model.ThirdPartyUserShop;

public record ThirdPartySynchronizeEvent(
        ThirdPartySystem thirdPartySystem, ThirdPartyUserShop thirdPartyUserShop) {

    public ThirdPartySystem getThirdPartySystem() {
        return thirdPartySystem;
    }


    public ThirdPartyUserShop getThirdPartyUserShop() {
        return thirdPartyUserShop;
    }
}
