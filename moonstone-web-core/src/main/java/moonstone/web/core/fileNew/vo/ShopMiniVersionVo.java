package moonstone.web.core.fileNew.vo;

import lombok.Data;

import java.util.Date;

/**
 * author：书生
 */
@Data
public class ShopMiniVersionVo {

    /**
     * id
     */
    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺小程序id
     */
    private Long shopWxaId;

    /**
     * 版本号
     */
    private String version;

    /**
     * 版本类型（0-微信小程序，1-支付宝小程序）
     */
    private Integer versionType;

    /**
     * 模板版本
     */
    private String templateVersion;

    /**
     * 模板版本描述
     */
    private String templateVersionDesc;

    /**
     * 构建开始时间
     */
    private Date buildStartTime;

    /**
     * 构建结束时间
     */
    private Date buildEndTime;

    /**
     * 上架时间
     */
    private Date onlineTime;

    /**
     * 下架时间
     */
    private Date offlineTime;

    /**
     * 版本描述
     */
    private String versionDesc;

    /**
     * 是否为体验版
     */
    private Boolean whetherExperience;

    /**
     * 体验版地址
     */
    private String experienceUrl;

    /**
     * 是否为线上版本
     */
    private Boolean whetherOnline;

    /**
     * 版本状态（0-构建中，1-构建完成，2-构建失败，3-审核中，4-审核通过，5-审核驳回，99-下架，100-上架）
     */
    private Integer versionState;

    /**
     * 版本状态描述
     */
    private String versionStateDesc;

    /**
     * 失败原因
     */
    private String failedReason;

}
