package moonstone.web.core.fileNew.export;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.model.*;
import moonstone.order.model.result.OrderPaymentInfoDO;
import moonstone.order.model.result.OrderShipmentInfoDO;
import moonstone.order.service.*;
import moonstone.user.enums.DataExportTaskTypeEnum;
import moonstone.user.model.PayerInfoRecord;
import moonstone.user.service.PayerInfoReadService;
import moonstone.web.core.fileNew.view.AdminOrderExportView;
import moonstone.web.core.util.BaseExporter;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * author：书生
 */
@Component
public class AdminOrderExporter extends BaseExporter<AdminOrderExportView, AdminOrderExporter.OrderExportParameter> {

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Resource
    private PayerInfoReadService payerInfoReadService;

    @Resource
    private OrderReceiverInfoService orderReceiverInfoService;

    @Resource
    private PaymentReadService paymentReadService;

    @Resource
    private ShipmentReadService shipmentReadService;


    @Override

    protected DataExportTaskTypeEnum getCurrentTaskType() {
        return DataExportTaskTypeEnum.ORDER_MANAGEMENT;
    }

    @Override
    protected List<AdminOrderExportView> findPageDataList(OrderExportParameter queryParameter, int pageNo, int pageSize) {
        if (queryParameter == null || CollectionUtils.isEmpty(queryParameter.shopOrderIdList())) {
            return Collections.emptyList();
        }

        int start = (pageNo - 1) * pageSize;
        int end = Math.min(start + pageSize, queryParameter.shopOrderIdList().size());
        if (start >= queryParameter.shopOrderIdList().size()) {
            return Collections.emptyList();
        }

        return batchFindExportData(queryParameter.shopOrderIdList().subList(start, end));
    }


    public List<AdminOrderExportView> batchFindExportData(List<Long> orderIdList) {
        Map<Long, ShopOrder> shopOrderMap = shopOrderReadService.getShopOrderMap(orderIdList);
        if (CollUtil.isEmpty(shopOrderMap)) {
            return Collections.emptyList();
        }

        Map<Long, OrderPaymentInfoDO> paymentInfoMap = paymentReadService.findMapByOrderIds(orderIdList, OrderLevel.SHOP);
        Map<Long, OrderShipmentInfoDO> shipmentMap = shipmentReadService.getShipmentMap(orderIdList, OrderLevel.SHOP);
        List<PayerInfoRecord> payerInfoList = payerInfoReadService.findByOrderIds(orderIdList).getResult();
        Map<Long, PayerInfoRecord> payerInfoMap = payerInfoList.stream().collect(Collectors.toMap(PayerInfoRecord::orderId, Function.identity()));

        List<AdminOrderExportView> resultList = buildFromSkuOrder(orderIdList);
        for (AdminOrderExportView entity : resultList) {
            Long orderId = entity.getOrderId();
            ShopOrder shopOrder = shopOrderMap.get(orderId);
            // 填充买家信息
            appendBuyerInfo(entity, shopOrder);
            // 填充支付信息
            appendPaymentInfo(entity, paymentInfoMap.get(orderId));
            // 填充快递信息
            appendShipmentInfo(entity, shipmentMap.get(orderId));
            // 填充收货信息
            appendReceiverInfo(entity);
            // 填充支付人信息
            appendPayerInfo(entity, payerInfoMap.get(orderId));
        }
        return resultList;
    }

    private void appendShipmentInfo(AdminOrderExportView entity, OrderShipmentInfoDO shipmentInfo) {
        if (shipmentInfo == null) {
            return;
        }
        entity.setSendTime(DateUtil.formatDateTime(shipmentInfo.getCreatedAt()));
        entity.setExpressCompany(shipmentInfo.getShipmentCorpName());
        entity.setExpressCode(shipmentInfo.getShipmentSerialNo());
    }

    private void appendReceiverInfo(AdminOrderExportView entity) {
        OrderReceiverInfo orderReceiverInfo = orderReceiverInfoService.findByOrderIdAndOrderType(entity.getOrderId(), entity.getOrderType());
        if (orderReceiverInfo != null) {
            ReceiverInfo receiverInfo = orderReceiverInfo.getReceiverInfo();
            if (receiverInfo != null) {
                receiverInfo.desensitization();
                entity.setReceiveUserName(receiverInfo.getReceiveUserName());
                entity.setReceiveUserPhone(receiverInfo.getMobile());
                String builder = receiverInfo.getProvince() +
                        "-" +
                        receiverInfo.getCity() +
                        "-" +
                        receiverInfo.getRegion() +
                        " " +
                        receiverInfo.getDetail();
                entity.setAddress(builder);
            }
        }
    }

    private void appendPayerInfo(AdminOrderExportView entity, PayerInfoRecord payerInfoRecord) {
        //            PayerInfoRecord payerInfo = payerInfoMap.get(orderId);
//            if (payerInfo != null) {
//                PayerInfo.Helper.Info info = PayerInfo.Helper.decodeWithoutCache(EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey),
//                        payerInfo.info());
//                if (StrUtil.isNotBlank(info.getName())) {
//                    entity.setPayerName(info.getName().replaceAll(RegexConstant.NAME, RegexConstant.NAME_REPLACE));
//                }
//                if (StrUtil.isNotBlank(info.getNo())) {
//                    entity.setPayerIdCard(info.getNo().replaceAll(RegexConstant.IDCARD, RegexConstant.IDCARD_REPLACE));
//                }
//            }
    }

    private void appendPaymentInfo(AdminOrderExportView entity, OrderPaymentInfoDO payment) {
        if (payment == null) {
            return;
        }
        entity.setPaySerialNo(payment.getPaySerialNo());
        entity.setPayAt(DateUtil.formatDateTime(payment.getPaidAt()));
    }

    private void appendBuyerInfo(AdminOrderExportView entity, ShopOrder shopOrder) {
        entity.setOrderType(shopOrder.getType());
        entity.setBuyerId(shopOrder.getBuyerId());
        entity.setBuyerName(shopOrder.getBuyerName());
        entity.setShopId(shopOrder.getShopId());
        entity.setShopName(shopOrder.getShopName());
        entity.setStatus(shopOrder.getStatus());
        entity.setStatusDesc(OrderStatus.fromInt(shopOrder.getStatus()).getDesc());
        entity.setCreateAt(DateUtil.formatDateTime(shopOrder.getCreatedAt()));
        entity.setTotalAmount(toPrice(shopOrder.getFee()));
        entity.setSendTime(DateUtil.formatDateTime(shopOrder.getLastShipmentAt()));
        entity.setConfirmReceiveTime(DateUtil.formatDateTime(shopOrder.getLastConfirmAt()));
    }

    private List<AdminOrderExportView> buildFromSkuOrder(List<Long> orderIdList) {
        if (CollUtil.isEmpty(orderIdList)) {
            return Collections.emptyList();
        }
        List<SkuOrder> skuOrderList = skuOrderReadService.findByShopOrderIds(orderIdList).getResult();
        return skuOrderList.stream().map(skuOrder -> {
            AdminOrderExportView entity = new AdminOrderExportView();
            entity.setOrderId(skuOrder.getOrderId());
            entity.setOuterSkuId(skuOrder.getOuterSkuId());
            Integer pushStatus = skuOrder.getPushStatus();
            if (pushStatus != null) {
                entity.setPushStatus(skuOrder.getPushStatus());
                entity.setPushStatusDesc(SkuOrderPushStatus.fromInt(pushStatus).desc());
            }
            Long itemId = skuOrder.getItemId();
            entity.setItemId(itemId);
            entity.setItemName(skuOrder.getItemName());
            entity.setItemPrice(toPrice(skuOrder.getFee() / skuOrder.getQuantity()));
            entity.setQuantity(skuOrder.getQuantity());
            entity.setTax(toPrice(skuOrder.getTax()));
            entity.setShipFee(toPrice(skuOrder.getShipFee()));
            entity.setDiscount(toPrice(skuOrder.getDiscount()));
            return entity;
        }).toList();
    }

    /**
     * 价格字段格式化
     *
     * @param fee
     * @return
     */
    public String toPrice(Long fee) {
        if (fee == null) {
            return null;
        }
        return BigDecimal.valueOf(fee).divide(new BigDecimal(100), 2, RoundingMode.DOWN).toString();
    }


    public record OrderExportParameter(List<Long> shopOrderIdList) {

    }
}
