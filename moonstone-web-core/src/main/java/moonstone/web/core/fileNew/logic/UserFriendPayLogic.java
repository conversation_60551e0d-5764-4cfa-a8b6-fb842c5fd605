package moonstone.web.core.fileNew.logic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import io.terminus.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.api.ResultCode;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.user.acct.IAllinpayUserHandler;
import moonstone.user.model.UserFriendPay;
import moonstone.user.service.UserFriendPayService;
import moonstone.web.core.fileNew.dto.UserFriendPayDto;
import moonstone.web.core.fileNew.dto.UserFriendPaySubmitDto;
import moonstone.web.core.fileNew.vo.UserFriendPayVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserFriendPayLogic {

	@Resource
	private UserFriendPayService userFriendPayService;

	@Resource
	private IAllinpayUserHandler allinpayUserHandler;

	@Resource
	private UserFriendPayLogic userFriendPayLogic;


	public UserFriendPayVo getEnableCurrent(Long shopId) {
		CommonUser currentUser = UserUtil.getCurrentUser();
		if (currentUser == null) {
			throw new ApiException(ResultCode.UN_AUTHORIZED);
		}
		log.info("登录人信息 {}", JSONUtil.toJsonStr(currentUser));
		Map<String, Object> query = new HashMap<>();
		query.put("shopId", shopId);
		query.put("userId", currentUser.getId());
		query.put("enable", true);
		UserFriendPay userFriendPay = userFriendPayService.getOne(query);
		if (userFriendPay == null) {
			return new UserFriendPayVo();
		}
		return BeanUtil.copyProperties(userFriendPay, UserFriendPayVo.class);
	}

	public Boolean submit(UserFriendPaySubmitDto reqDto) {
		CommonUser currentUser = UserUtil.getCurrentUser();
		if (currentUser == null) {
			throw new ApiException(ResultCode.UN_AUTHORIZED);
		}

		// 生成bizUserId
		String bizUserId = allinpayUserHandler.createBizUserId(currentUser.getId(), reqDto.getPaperNo());
		Long shopId = reqDto.getShopId();
		// 到通联注册该bizUserId
		Result<String> result = allinpayUserHandler.registerBizUserId(shopId, bizUserId);
		if(!result.isSuccess()){
			throw new ServiceException(result.getErrorMsg());
		}

		// 查询并更新好友代付信息
		UserFriendPayDto userFriendPayDto = BeanUtil.copyProperties(reqDto, UserFriendPayDto.class);
		userFriendPayDto.setUserId(currentUser.getId());
		userFriendPayDto.setFriendName(reqDto.getPaperName());
		userFriendPayDto.setFriendIdentityCard(reqDto.getPaperNo());
		userFriendPayDto.setFriendBizUserId(bizUserId);
		return userFriendPayLogic.findAndUpdate(userFriendPayDto);
	}

	@Transactional(rollbackFor = Exception.class)
	public Boolean findAndUpdate(UserFriendPayDto userFriendPayDto) {
		Map<String, Object> query = new HashMap<>();
		query.put("shopId", userFriendPayDto.getShopId());
		query.put("userId", userFriendPayDto.getUserId());
		query.put("friendName", userFriendPayDto.getFriendName());
		query.put("friendIdentityCard", userFriendPayDto.getFriendIdentityCard());
		query.put("enable", true);
		UserFriendPay userFriendPay = userFriendPayService.getOne(query);
		if(ObjUtil.isNotEmpty(userFriendPay)) {
			log.info("提交的好友支付人信息已存在，不需要修改 对应的信息 {}", JSONUtil.toJsonStr(userFriendPay));
			return true;
		}
		// 先将用户下的为不可用
		userFriendPayService.invalid(userFriendPayDto.getUserId(), userFriendPayDto.getShopId());
		// 新增
		UserFriendPay entity = BeanUtil.copyProperties(userFriendPayDto, UserFriendPay.class);
		entity.setEnable(true);
		return userFriendPayService.save(entity);
	}

	public UserFriendPay getOne(Map<String, Object> query) {
		return userFriendPayService.getOne(query);
	}
}
