package moonstone.web.core.fileNew.dto;

import lombok.Data;
import moonstone.order.model.Payment;
import moonstone.order.model.Refund;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;

/**
 * <AUTHOR>
 */
@Data
public class RefundApplyDto {
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单类型 （1: 店铺订单, 2: 子订单）
     */
    private Integer orderType;

    /**
     * 退款类型（1：仅退款，3：退货退款）
     */
    private Integer refundType;

    /**
     * 图片json
     */
    private String imagesJson;

    /**
     * 退款原因
     */
    private Integer reasonType;

    /**
     * 买家备注
     */
    private String buyerNote;

    /**
     * 系统备注
     */
    private String systemNote;

    /**
     * 买家是否收到货
     */
    private Boolean buyerReceivedStatus;


    /**
     * 支付单信息
     */
    private Payment payment;

    /**
     * 主订单
     */
    private ShopOrder shopOrder;

    /**
     * 子订单
     */
    private SkuOrder skuOrder;

    /**
     * 退款单号
     */
    private Refund refund;


}


