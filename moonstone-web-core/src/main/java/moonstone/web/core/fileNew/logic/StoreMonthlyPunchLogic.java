package moonstone.web.core.fileNew.logic;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.imm20200930.models.CreateFileCompressionTaskRequest;
import com.aliyun.oss.model.OSSObjectSummary;
import io.terminus.common.model.BaseUser;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.CommonSqlConstant;
import moonstone.common.constants.OssDirectoryConstant;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.shop.model.StoreMonthlyPunch;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.StoreMonthlyPunchService;
import moonstone.user.enums.DataExportTaskStatusEnum;
import moonstone.user.enums.DataExportTaskTypeEnum;
import moonstone.user.model.DataExportTask;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.dto.StoreMonthlyPunchCreateDto;
import moonstone.web.core.fileNew.dto.StoreMonthlyPunchExportDto;
import moonstone.web.core.fileNew.export.StoreMonthlyPunchExport;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.core.fileNew.provider.AliYunImmProvider;
import moonstone.web.core.files.config.OSSConfig;
import moonstone.web.core.files.service.OSSClientService;
import moonstone.web.core.shop.model.ServiceProvider;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * author：书生
 */
@Service
@Slf4j
public class StoreMonthlyPunchLogic {

    @Resource
    private StoreMonthlyPunchService storeMonthlyPunchService;

    @Resource
    private OSSConfig ossConfig;

    @Resource
    private OSSClientService ossClientService;

    @Resource
    private EnvironmentConfig environmentConfig;

    @Resource
    private SubStoreLogic subStoreLogic;

    @Resource
    private StoreMonthlyPunchExport storeMonthlyPunchExport;

    @Resource
    private RocketMQProducer rocketMQProducer;

    @Resource
    private DataExportTaskLogic dataExportTaskLogic;

    @Resource
    private AliYunImmProvider aliYunImmProvider;


    /**
     * 上传门店打卡照片
     *
     * @param file       照片
     * @param subStoreId 门店id
     * @param type       1：门头照  2：陈列照
     * @return 打卡照片url
     */
    public String uploadPicture(MultipartFile file, Long subStoreId, Integer type) {
        String originalFilename = file.getOriginalFilename();
        if (StrUtil.isBlank(originalFilename)) {
            throw new ApiException("文件名为空");
        }
        SubStore subStore = subStoreLogic.findById(subStoreId);
        if (subStore == null) {
            throw new ApiException("未找到对应的门店信息");
        }
        String env = environmentConfig.getEnv();
        String name = "";
        String time = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_FORMAT);
        ServiceProvider serviceProvider = subStoreLogic.getServiceProviderOfSubStore(subStoreId);
        if (type == 1) {
            name = serviceProvider.getName() + "-" + subStore.getName() + "-" + "门头照" + "-" + time;
        } else if (type == 2) {
            name = serviceProvider.getName() + "-" + subStore.getName() + "-" + "陈列照" + "-" + time;
        } else {
            name = IdUtil.fastSimpleUUID();
        }
        // 文件夹名称   环境/门店打卡（固定值）/店铺id/年/月/日/服务商名称/门店名称/服务商名称-门店名称-门头照|陈列照-打卡时间.后缀
        String fileName = env + OssDirectoryConstant.DIAGONAL_FILE
                + OssDirectoryConstant.STORE_MONTHLY_PUNCH + OssDirectoryConstant.DIAGONAL_FILE
                + subStore.getShopId() + OssDirectoryConstant.DIAGONAL_FILE
                + DateUtil.format(new Date(), "yyyy/MM/dd") + OssDirectoryConstant.DIAGONAL_FILE
                + serviceProvider.getName() + OssDirectoryConstant.DIAGONAL_FILE
                + subStore.getName() + OssDirectoryConstant.DIAGONAL_FILE
                + name + originalFilename.substring(originalFilename.lastIndexOf("."));
        log.info("文件名称 {}", fileName);
        try {
            return ossClientService.fileUpload(fileName, file.getInputStream());
        } catch (IOException e) {
            log.error("上传失败 失败原因 {}", e.getMessage(), e);
            throw new ApiException("上传失败");
        }
    }

    /**
     * 保存门店打卡记录
     *
     * @param req 请求参数
     * @return 是否保存成功  ture：成功 false：失败
     */
    public Boolean save(StoreMonthlyPunchCreateDto req) {
        Long subStoreId = req.getSubStoreId();
        SubStore subStore = subStoreLogic.findById(subStoreId);
        if (subStore == null) {
            throw new ApiException("门店信息未找到");
        }
        if (!subStore.getPunchMark()) {
            subStore.setPunchMark(true);
            subStoreLogic.update(subStore);
        }
        StoreMonthlyPunch storeMonthlyPunch = new StoreMonthlyPunch();
        storeMonthlyPunch.setShopId(subStore.getShopId());
        storeMonthlyPunch.setSubStoreId(subStore.getId());
        storeMonthlyPunch.setSubStoreName(subStore.getName());
        ServiceProvider serviceProvider = subStoreLogic.getServiceProviderOfSubStore(subStoreId);
        storeMonthlyPunch.setServiceProviderProvince(serviceProvider.getProvince());
        storeMonthlyPunch.setServiceProviderCity(serviceProvider.getCity());
        storeMonthlyPunch.setServiceProviderName(serviceProvider.getName());
        storeMonthlyPunch.setServiceProviderMobile(serviceProvider.getMobile());
        storeMonthlyPunch.setServiceProviderUserId(serviceProvider.getUserId());
        storeMonthlyPunch.setSubStoreProvince(subStore.getProvince());
        storeMonthlyPunch.setSubStoreCity(subStore.getCity());
        storeMonthlyPunch.setSubStoreMobile(subStore.getMobile());
        storeMonthlyPunch.setSubStoreUserId(subStore.getUserId());
        BaseUser currentUser = UserUtil.getCurrentUser();
        if (currentUser != null) {
            storeMonthlyPunch.setUploaderId(currentUser.getId());
        }
        storeMonthlyPunch.setDoorPictureUrl(addWatermark(subStore.getName(), req.getDoorPictureUrl()));
        storeMonthlyPunch.setDisplayPictureUrl(addWatermark(subStore.getName(), req.getDisplayPictureUrl()));
        storeMonthlyPunchService.save(storeMonthlyPunch);
        return true;
    }

    /**
     * 添加一下水印参数
     *
     * @param subStoreName 门店名称
     * @param url          图片地址
     * @return 添加水印后的图片地址
     */
    public String addWatermark(String subStoreName, String url) {
        Base64.Encoder encoder = Base64.getUrlEncoder();
        String subStoreNameEncode = encoder.encodeToString(subStoreName.getBytes(StandardCharsets.UTF_8));
        subStoreNameEncode = subStoreNameEncode.replaceAll("/", "_");
        subStoreNameEncode = subStoreNameEncode.replaceAll("=", "");
        subStoreNameEncode = subStoreNameEncode.replaceAll("\\+", "");
        // 水印： 两行  左下角   第一行 门店名称  第二行 日期
        String time = DateUtil.formatDate(new Date());
        String timeEncode = encoder.encodeToString(time.getBytes(StandardCharsets.UTF_8));
        timeEncode = timeEncode.replaceAll("/", "_");
        timeEncode = timeEncode.replaceAll("=", "");
        timeEncode = timeEncode.replaceAll("\\+", "");
        String addWaterMarkUrl = url + "?x-oss-process=image/watermark,text_" + subStoreNameEncode + ",g_sw,color_FFFFFF,t_70,y_60" + "/watermark,text_" + timeEncode + ",g_sw,color_FFFFFF,t_70";
        HttpRequest httpRequest = HttpRequest.get(addWaterMarkUrl);
        try (HttpResponse response = httpRequest.execute()) {
            String fileName = url.substring(url.indexOf(environmentConfig.getEnv() + OssDirectoryConstant.DIAGONAL_FILE));
            ossClientService.fileUpload(fileName, response.bodyStream());
        } catch (Exception e) {
            log.error("获取加水印的图片失败 失败原因 {}", e.getMessage(), e);
            throw new ApiException("获取加水印的图片失败");
        }
        return url;
    }


    /**
     * 异步导出门店打卡明细
     *
     * @param req 请求参数
     * @return 任务id
     */
    public Long export(StoreMonthlyPunchExportDto req) {
        //  异步导出门店打卡明细
        setUploadTimeWithoutParam(req);
        if (req.getShopId() == null) {
            throw new ApiException("店铺id不能为空");
        }
        Map<String, Object> query = new HashMap<>();
        query.put("shopId", req.getShopId());
        query.put("uploadStart", new Date(req.getUploadStart()));
        query.put("uploadEnd", new Date(req.getUploadEnd()));
        query.put(CommonSqlConstant.SORT_BY, "createdAt");
        query.put(CommonSqlConstant.SORT_TYPE, CommonSqlConstant.SORT_DESC);
        if (StrUtil.isNotBlank(req.getSubStoreMobile())) {
            query.put("subStoreMobile", req.getSubStoreMobile());
        }
        if (StrUtil.isNotBlank(req.getSubStoreName())) {
            query.put("subStoreName", req.getSubStoreName());
        }
        if (StrUtil.isNotBlank(req.getServiceProviderName())) {
            query.put("serviceProviderName", req.getServiceProviderName());
        }
        if (StrUtil.isNotBlank(req.getServiceProviderMobile())) {
            query.put("serviceProviderMobile", req.getServiceProviderMobile());
        }
        List<StoreMonthlyPunch> storeMonthlyPunches = storeMonthlyPunchService.list(query);
        if (CollUtil.isEmpty(storeMonthlyPunches)) {
            throw new ApiException("没有需要导出的数据");
        }
        List<Long> idList = storeMonthlyPunches.stream().map(StoreMonthlyPunch::getId).toList();
        log.info("需要导出的id列表 {}", JSONUtil.toJsonStr(idList));
        try {
            //导出文件名 陈列打卡明细表-下载时间
            String fileName = DataExportTaskTypeEnum.STORE_MONTHLY_PUNCH.getDescription() + "-" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN)
                    + ".xlsx";
            //查询参数
            StoreMonthlyPunchExport.StoreMonthlyPunchExportParameter parameter = new StoreMonthlyPunchExport.StoreMonthlyPunchExportParameter(idList);
            CommonUser currentUser = UserUtil.getCurrentUser();
            //执行导出
            DataExportTask task = storeMonthlyPunchExport.asyncExport(currentUser, fileName, parameter);
            return task.getId();
        } catch (Exception e) {
            log.error("导出门店打卡明细失败 失败原因 {}", e.getMessage(), e);
            throw new ApiException("导出门店打卡明细失败");
        }
    }

    /**
     * 导出门店打卡照片
     *
     * @param req 请求参数
     * @return 任务id
     */
    public Long exportPicture(StoreMonthlyPunchExportDto req) {
        //  异步导出门店
        setUploadTimeWithoutParam(req);
        if (req.getShopId() == null) {
            throw new ApiException("店铺id不能为空");
        }
        List<SubStore> subStoreList = subStoreLogic.listByShopId(req.getShopId());
        if (CollUtil.isEmpty(subStoreList)) {
            throw new ApiException("未查询到对应的门店信息");
        }
        Map<String, Object> query = new HashMap<>();
        query.put("shopId", req.getShopId());
        query.put("uploadStart", new Date(req.getUploadStart()));
        query.put("uploadEnd", new Date(req.getUploadEnd()));
        query.put(CommonSqlConstant.SORT_BY, "createdAt");
        query.put(CommonSqlConstant.SORT_TYPE, CommonSqlConstant.SORT_DESC);
        List<StoreMonthlyPunch> storeMonthlyPunches = storeMonthlyPunchService.list(query);
        if (CollUtil.isEmpty(storeMonthlyPunches)) {
            throw new ApiException("没有需要导出的数据");
        }
        JSONObject msg = new JSONObject();
        msg.put("uploadStart", req.getUploadStart());
        msg.put("uploadEnd", req.getUploadEnd());
        msg.put("shopId", req.getShopId());
        DataExportTask dataExportTask = new DataExportTask();
        dataExportTask.setType(DataExportTaskTypeEnum.STORE_MONTHLY_PUNCH_PICTURE.getCode());
        //  注释放开 本地测的时候 注释
        try {
            CommonUser currentUser = UserUtil.getCurrentUser();
            dataExportTask.setUserId(currentUser.getId());
//            dataExportTask.setUserId(43538L);
            dataExportTask.setShopId(req.getShopId());
            String fileName = "门店陈列图片压缩包" + "-" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + ".zip";
            dataExportTask.setFileName(fileName);
            dataExportTaskLogic.save(dataExportTask);
            msg.put("taskId", dataExportTask.getId());
            log.info("导出门店打卡照片 发送的消息 {}", msg);
            rocketMQProducer.sendMessage(RocketMQConstant.APP_MERCHANT_TOPIC, RocketMQConstant.MERCHANT_PACKING_STORE_MONTHLY_PUNCH_PICTURE_TAG, msg.toString());
            return dataExportTask.getId();
        } catch (Exception e) {
            log.error("异步导出门店月度打卡照片失败 失败原因 {}", e.getMessage(), e);
            throw new ApiException("导出门店月度打卡照片失败");
        }
    }

    private void setUploadTimeWithoutParam(StoreMonthlyPunchExportDto req) {
        if (req.getUploadStart() == null || req.getUploadEnd() == null) {
            Date today = new Date();
            DateTime uploadStart = DateUtil.beginOfMonth(today);
            DateTime uploadEnd = DateUtil.endOfDay(today);
            req.setUploadStart(uploadStart.getTime());
            req.setUploadEnd(uploadEnd.getTime());
        }
        if (req.getShopId() == null) {
            CommonUser currentUser = UserUtil.getCurrentUser();
            req.setShopId(currentUser.getShopId());
        }
    }

    public void packingPicture(String msg) {
        JSONObject msgJson = JSONUtil.parseObj(msg);
        Long taskId = msgJson.get("taskId", Long.class);
        DataExportTask dataExportTask = dataExportTaskLogic.getById(taskId);
        if (dataExportTask == null) {
            log.error("获取打包照片任务失败 任务id为 {} 是一个不存在的任务", taskId);
            return;
        }
        try {
            List<CreateFileCompressionTaskRequest.CreateFileCompressionTaskRequestSources> sources = buildSources(msgJson);
            String fileUrl = aliYunImmProvider.createFileCompressionTask(sources, dataExportTask.getFileName());
            // 去查询对应文件是否生成成功
            boolean isWhile = true;
            while (isWhile) {
                String keyPrefix = environmentConfig.getEnv() + OssDirectoryConstant.DIAGONAL_FILE +
                        OssDirectoryConstant.COMPRESS + OssDirectoryConstant.DIAGONAL_FILE +
                        OssDirectoryConstant.ZIP + OssDirectoryConstant.DIAGONAL_FILE +
                        DateUtil.format(new Date(), "yyyy/MM/dd") + OssDirectoryConstant.DIAGONAL_FILE;
                List<OSSObjectSummary> sums = ossClientService.listObjects(ossConfig.getBucketName(), keyPrefix);
                for (OSSObjectSummary s : sums) {
                    boolean contains = s.getKey().contains(dataExportTask.getFileName());
                    if (contains) {
                        isWhile = false;
                    }
                }
                ThreadUtil.safeSleep(1000);
            }
            dataExportTask.setFileUrl(fileUrl);
            dataExportTask.setStatus(DataExportTaskStatusEnum.FINISHED.getCode());
        } catch (Exception e) {
            log.error("打包照片失败 失败原因 {}", e.getMessage(), e);
            dataExportTask.setStatus(DataExportTaskStatusEnum.FAILED.getCode());
            dataExportTask.setExceptionMessage(e.getMessage());
        }
        dataExportTask.setEndTime(new Date());
        dataExportTaskLogic.update(dataExportTask);
    }

    /**
     * 构建需要压缩的文件夹目录列表
     *
     * @param msgJson 消息参数
     * @return 需要压缩的文件夹目录列表
     */
    private List<CreateFileCompressionTaskRequest.CreateFileCompressionTaskRequestSources> buildSources(JSONObject msgJson) {
        Long shopId = msgJson.get("shopId", Long.class);
        Date uploadStart = msgJson.get("uploadStart", Date.class);
        Date uploadEnd = msgJson.get("uploadEnd", Date.class);
        // 循环生成日期列表
        List<CreateFileCompressionTaskRequest.CreateFileCompressionTaskRequestSources> sources = new ArrayList<>();
        while (!uploadStart.after(uploadEnd)) {
            String date = DateUtil.format(uploadStart, "yyyy/MM/dd");
            String alias = OssDirectoryConstant.DIAGONAL_FILE + DateUtil.formatDate(uploadStart) + OssDirectoryConstant.DIAGONAL_FILE;
            CreateFileCompressionTaskRequest.CreateFileCompressionTaskRequestSources sources0 = new CreateFileCompressionTaskRequest.CreateFileCompressionTaskRequestSources();
            String uri = "oss://" + ossConfig.getBucketName() + OssDirectoryConstant.DIAGONAL_FILE +
                    environmentConfig.getEnv() + OssDirectoryConstant.DIAGONAL_FILE +
                    OssDirectoryConstant.STORE_MONTHLY_PUNCH + OssDirectoryConstant.DIAGONAL_FILE +
                    shopId + OssDirectoryConstant.DIAGONAL_FILE +
                    date + OssDirectoryConstant.DIAGONAL_FILE;
            log.info("uri {}", uri);
            sources0.setURI(uri);
            sources0.setAlias(alias);
            sources0.setMode("prefix");
            uploadStart = DateUtil.offsetDay(uploadStart, 1);
            sources.add(sources0);
        }
        return sources;
    }

    /**
     * 获取最近一次的打卡时间
     *
     * @param subStoreId 门店id
     * @return 最近一次的打卡时间
     */
    public String latestRecord(Long subStoreId) {
        Map<String, Object> query = new HashMap<>();
        query.put("subStoreId", subStoreId);
        query.put(CommonSqlConstant.SORT_BY, "createdAt");
        query.put(CommonSqlConstant.SORT_TYPE, CommonSqlConstant.SORT_DESC);
        List<StoreMonthlyPunch> storeMonthlyPunches = storeMonthlyPunchService.list(query);
        if (CollUtil.isEmpty(storeMonthlyPunches)) {
            return "";
        }
        Date uploadTime = storeMonthlyPunches.get(0).getCreatedAt();
        return DateUtil.formatDateTime(uploadTime);
    }
}
