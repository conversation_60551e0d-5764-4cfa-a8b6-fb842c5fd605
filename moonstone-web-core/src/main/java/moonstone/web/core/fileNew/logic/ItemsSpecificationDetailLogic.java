package moonstone.web.core.fileNew.logic;

import lombok.extern.slf4j.Slf4j;
import moonstone.item.model.ItemsSpecificationDetail;
import moonstone.item.service.ItemsSpecificationDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ItemsSpecificationDetailLogic {
	@Resource
	private ItemsSpecificationDetailService itemsSpecificationDetailService;


	/**
	 * 根据商品id获取商品规格详情列表
	 * @param itemId 商品id
	 * @return 商品规格详情列表
	 */
	public List<ItemsSpecificationDetail> getItemSpecListByItemId(Long itemId) {
		return itemsSpecificationDetailService.getItemSpecListByItemId(itemId);
	}

}
