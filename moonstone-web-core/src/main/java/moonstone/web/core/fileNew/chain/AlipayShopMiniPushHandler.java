package moonstone.web.core.fileNew.chain;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alipay.api.domain.AppVersionInfo;
import com.alipay.api.response.*;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.enums.ShopMiniVersionStateEnum;
import moonstone.common.exception.ApiException;
import moonstone.shopMini.model.ShopMiniVersion;
import moonstone.shopMini.service.ShopMiniVersionService;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.shopWxa.service.ShopWxaWriteService;
import moonstone.web.core.fileNew.constant.AlipayMessageNotifyConstant;
import moonstone.common.constants.RedisKeyConstant;
import moonstone.common.constants.RocketMQConstant;
import moonstone.web.core.fileNew.dto.ShopAuthInvitationLinkDto;
import moonstone.web.core.fileNew.dto.ShopWxaCreateDto;
import moonstone.web.core.fileNew.producer.RocketMQProducer;
import moonstone.web.core.fileNew.vo.MiniVersionVo;
import moonstone.web.core.util.AlipayOpenAPIUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AlipayShopMiniPushHandler implements ShopMiniPublishHandler {


    private static final String ALIPAY_PC_BASE = "https://b.alipay.com/page/message/tasksDetail";

    @Value("${parana.alipayOpen.authNotifyUrl}")
    private String alipayAuthNotifyUrl;

    @Value("${parana.alipayOpen.serviceAppId}")
    private String serviceAppId;

    @Resource
    private ShopWxaWriteService shopWxaWriteService;

    @Resource
    private ShopWxaReadService shopWxaReadService;

    @Resource
    private ShopMiniVersionService shopMiniVersionService;

    @Resource
    private AlipayOpenAPIUtil alipayOpenAPIUtil;

    @Resource
    private RocketMQProducer rocketMQProducer;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    @Override
    public boolean isSupply(int appType) {
        return appType == AppTypeEnum.ALIPAY.getCode();
    }

    @Override
    public ShopAuthInvitationLinkDto getInvitationLink(ShopWxaCreateDto req) {
        // 构建请求参数
        Map<String, Object> bizData = buildBizData(req);
        log.debug("getInvitationLink buildBizData result：{}", bizData);
        String bizDataString = URLEncoder.encode(JSONUtil.toJsonStr(bizData), StandardCharsets.UTF_8);
        String pcInviteUrl = ALIPAY_PC_BASE + "?bizData=" + bizDataString;
        String appInviteUrl = "alipays://platformapi/startapp?appId=2021003130652097&page=pages%2Fauthorize%2Findex%3FbizData%3D" + bizDataString;
        ShopAuthInvitationLinkDto shopAuthInvitationLinkDto = new ShopAuthInvitationLinkDto();
        shopAuthInvitationLinkDto.setAppInviteUrl(appInviteUrl);
        shopAuthInvitationLinkDto.setPcInviteUrl(pcInviteUrl);
        return shopAuthInvitationLinkDto;
    }

    private Map<String, Object> buildBizData(ShopWxaCreateDto req) {
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("platformCode", "O");
        bizData.put("taskType", "INTERFACE_AUTH");
        Map<String, Object> agentOpParam = new HashMap<>();
        agentOpParam.put("redirectUri", alipayAuthNotifyUrl);
        agentOpParam.put("appTypes", List.of("TINYAPP", "WEBAPP", "PUBLICAPP", "MOBILEAPP"));
        agentOpParam.put("isvAppId", serviceAppId);
        JSONObject state = new JSONObject();
        state.put("shopId", req.getShopId());
        state.put("shopWxaId", req.getShopWxaId());
        agentOpParam.put("state", Base64.getEncoder().encodeToString(state.toString().getBytes(StandardCharsets.UTF_8)));
        bizData.put("agentOpParam", agentOpParam);
        return bizData;
    }

    @Override
    public String authorizeCallbackUpdates(JSONObject param) {
        // 店铺id
        Long shopId = param.get("shopId", Long.class);
        Long shopWxaId = param.get("shopWxaId", Long.class);
        Map<String, Object> query = new HashMap<>();
        query.put("id", shopWxaId);
        query.put("appType", AppTypeEnum.ALIPAY.getCode());
        ShopWxa shopWxa = shopWxaReadService.getOne(query);
        if (shopWxa == null) {
            log.error("认证回调查询店铺小程序信息失败，未找到id为{}的店铺小程序信息", shopWxaId);
            return "认证回调查询店铺小程序信息失败，未找到id为" + shopWxaId + "的店铺小程序信息";
        }
        log.info("根据店铺小程序id：{}查询到的店铺小程序信息：{}", shopWxaId, JSONUtil.toJsonStr(shopWxa));
        // 店铺第三方小程序id
        String appId = param.get("shopAppId", String.class);
        // 店铺小程序名称
        String appName = param.get("appName", String.class);
        // 店铺小程序名称
        String appLogo = param.get("appLogo", String.class);
        String appAuthToken = param.get("appAuthToken", String.class);
        String appRefreshToken = param.get("appRefreshToken", String.class);
        Integer authorizationState = param.get("authorizationState", Integer.class);
        query = new HashMap<>();
        query.put("appId", appId);
        query.put("appType", AppTypeEnum.ALIPAY.getCode());
        ShopWxa shopMiniByAppId = shopWxaReadService.getOne(query);
        log.info("根据appid查询到店铺小程序信息：{},查询条件：{}", JSONUtil.toJsonStr(shopMiniByAppId), JSONUtil.toJsonStr(query));
        if (shopMiniByAppId == null) {
            shopWxa.setAppId(appId);
            shopWxa.setName(appName);
            shopWxa.setAppLogo(appLogo);
            shopWxa.setIsAuthorized(1);
            shopWxa.setAuthorizationState(authorizationState);
            shopWxa.setAuthorizerAccessToken(appAuthToken);
            shopWxa.setAuthorizerRefreshToken(appRefreshToken);
            shopWxaWriteService.updateInfo(shopWxa);
        } else {
            // 授权的appid已经授权了情况下
            if (!shopMiniByAppId.getId().equals(shopWxa.getId())) {
                // 授权应用 和 数据库中店铺小程序不同时
                shopMiniByAppId.setAuthorizerAccessToken(appAuthToken);
                shopMiniByAppId.setAuthorizerRefreshToken(appRefreshToken);
                shopWxaWriteService.updateInfo(shopMiniByAppId);
                return "选中的授权小程序已经授权给店铺名：" + shopMiniByAppId.getShopName() + "过了，无需重复授权，请确认需要授权的小程序应用";
            }
        }
        return "授权成功";
    }

    /**
     * 查询小程序版本列表
     *
     * @param param 查询条件
     * @return 小程序版本列表
     */
    @Override
    public List<MiniVersionVo> queryVersionList(JSONObject param) {
        AlipayOpenMiniVersionListQueryResponse response = alipayOpenAPIUtil.queryVersionList(param);
        List<AppVersionInfo> appVersionInfos = response.getAppVersionInfos();
        if (appVersionInfos == null || appVersionInfos.isEmpty()) {
            return Collections.emptyList();
        }
        List<MiniVersionVo> result = new ArrayList<>();
        for (int i = 0; i < appVersionInfos.size(); i++) {
            AppVersionInfo appVersionInfo = appVersionInfos.get(i);
            MiniVersionVo miniVersionVo = new MiniVersionVo();
            miniVersionVo.setSort(i + 1);
            miniVersionVo.setAppVersion(appVersionInfo.getAppVersion());
            miniVersionVo.setVersionDescription(appVersionInfo.getVersionDescription());
            result.add(miniVersionVo);
        }
        return result;
    }

    /**
     * 小程序版本
     *
     * @param param 传入的参数
     * @return 发起构建后的响应
     */
    @Override
    public void uploadMiniApp(JSONObject param) {
        AlipayOpenMiniVersionUploadResponse response = alipayOpenAPIUtil.miniVersionUpload(param);
        if (StrUtil.isNotBlank(response.getSubMsg())) {
            throw new ApiException(response.getSubMsg());
        }
    }

    @Override
    public void buildQuery(JSONObject param) {
        Integer appType = param.get("appType", Integer.class);
        Long shopWxaId = param.get("shopWxaId", Long.class);
        String version = param.get("appVersion", String.class);
        Map<String, Object> query = new HashMap<>();
        query.put("id", shopWxaId);
        ShopWxa shopWxa = shopWxaReadService.getOne(query);
        if (shopWxa == null) {
            log.error("查询构建状态错误，原因：传入的店铺小程序id未找到对应的店铺小程序信息 id：{}", shopWxaId);
            return;
        }
        query = new HashMap<>();
        query.put("shopWxaId", shopWxaId);
        query.put("templateId", shopWxa.getWxaTemplatesId());
        query.put("versionState", ShopMiniVersionStateEnum.IN_BUILD.getCode());
        query.put("version", version);
        query.put("versionType", shopWxa.getAppType());
        ShopMiniVersion shopMiniVersion = shopMiniVersionService.getOne(query);
        if (shopMiniVersion == null) {
            log.error("查询构建状态错误，原因：未找到对应的店铺小程序迭代信息 请求参数 {}", param);
            return;
        }
        try {
            if (!appType.equals(shopWxa.getAppType())) {
                throw new ApiException("查询构建状态错误，原因：传入的店铺小程序类型与数据库保存的小程序类型不匹配 appType：" + appType);
            }

            JSONObject queryBuildVersionParam = new JSONObject();
            queryBuildVersionParam.put("token", shopWxa.getAuthorizerAccessToken());
            queryBuildVersionParam.put("appVersion", shopMiniVersion.getVersion());
            AlipayOpenMiniVersionBuildQueryResponse response = alipayOpenAPIUtil.queryBuildVersion(queryBuildVersionParam);
            if (!response.isSuccess()) {
                throw new ApiException("查询构建状态错误，原因：查询支付宝小程序版本构建状态失败 失败原因：" + response.getSubMsg());
            }
            //0：构建排队中； 1：正在构建； 2：构建成功； 3：构建失败； 5：构建超时； 6：版本创建成功。
            switch (response.getCreateStatus()) {
                case "3":
                    // 构建失败
                    shopMiniVersion.setVersionState(ShopMiniVersionStateEnum.BUILD_FAILED.getCode());
                    shopMiniVersionService.update(shopMiniVersion);
                    throw new ApiException("查询构建状态错误，原因：支付宝小程序版本构建失败");
                case "5":
                    // 构建超时
                    shopMiniVersion.setVersionState(ShopMiniVersionStateEnum.BUILD_FAILED.getCode());
                    shopMiniVersionService.update(shopMiniVersion);
                    throw new ApiException("查询构建状态错误，原因：支付宝小程序版本构建超时");
                case "6":
                    // 构建完成 修改构建状态并生成体验版本、
                    shopMiniVersion.setFailedReason("");
                    shopMiniVersion.setBuildEndTime(new Date());
                    shopMiniVersion.setVersionState(ShopMiniVersionStateEnum.BUILD_COMPLETED.getCode());
                    shopMiniVersionService.update(shopMiniVersion);
                    break;
                default:
                    // 还在构建当中 继续发送延迟消息 轮训查询小程序构建状态
                    rocketMQProducer.sendDelayMessage(
                            RocketMQConstant.APP_ADMIN_TOPIC,
                            RocketMQConstant.ADMIN_BUILD_QUERY_TAG,
                            JSONUtil.toJsonStr(param),
                            2
                    );
            }
        } catch (Exception e) {
            // 异常信息是保存到迭代信息里面
            String errorMsg = e.getMessage();
            shopMiniVersion.setVersionState(ShopMiniVersionStateEnum.BUILD_FAILED.getCode());
            shopMiniVersion.setFailedReason(errorMsg);
            shopMiniVersionService.update(shopMiniVersion);
        }
    }

    @Override
    public void createExperience(ShopMiniVersion shopMiniVersion) {
        Long shopWxaId = shopMiniVersion.getShopWxaId();
        Map<String, Object> query = new HashMap<>();
        query.put("id", shopWxaId);
        ShopWxa shopWxa = shopWxaReadService.getOne(query);
        try {
            JSONObject createExperienceParam = new JSONObject();
            createExperienceParam.put("token", shopWxa.getAuthorizerAccessToken());
            createExperienceParam.put("appVersion", shopMiniVersion.getVersion());
            AlipayOpenMiniExperienceCreateResponse response = alipayOpenAPIUtil.createExperience(createExperienceParam);
            if (!response.isSuccess()) {
                throw new ApiException("生成体验版小程序错误 失败原因：" + response.getSubMsg());
            }
            shopMiniVersion.setWhetherExperience(true);
            shopMiniVersionService.update(shopMiniVersion);
            // 查询小程序体验版状态
            rocketMQProducer.sendDelayMessage(
                    RocketMQConstant.APP_ADMIN_TOPIC,
                    RocketMQConstant.ADMIN_QUERY_EXPERIENCE_TAG,
                    JSONUtil.toJsonStr(shopMiniVersion),
                    1
            );
        } catch (Exception e) {
            // 异常信息是保存到版本信息里面
            String errorMsg = e.getMessage();
            shopMiniVersion.setFailedReason(errorMsg);
            shopMiniVersionService.update(shopMiniVersion);
        }
    }

    @Override
    public void queryExperience(ShopMiniVersion shopMiniVersion) {
        Long shopWxaId = shopMiniVersion.getShopWxaId();
        Map<String, Object> query = new HashMap<>();
        query.put("id", shopWxaId);
        ShopWxa shopWxa = shopWxaReadService.getOne(query);
        JSONObject queryExperienceParam = new JSONObject();
        queryExperienceParam.put("token", shopWxa.getAuthorizerAccessToken());
        queryExperienceParam.put("appVersion", shopMiniVersion.getVersion());
        try {
            AlipayOpenMiniExperienceQueryResponse response = alipayOpenAPIUtil.queryExperience(queryExperienceParam);
            if (!response.isSuccess()) {
                throw new ApiException("查询体验版状态失败 失败原因：" + response.getSubMsg());
            }
            // 体验版打包成功
            if (response.getStatus().equals("expVersionPackged")) {
                shopMiniVersion.setExperienceUrl(response.getExpQrCodeUrl());
                shopMiniVersion.setWhetherExperience(true);
                shopMiniVersion.setFailedReason("");
                shopMiniVersionService.update(shopMiniVersion);
            }
            // 体验版打包中
            if (response.getStatus().equals("expVersionPackaging")) {
                // 查询小程序体验版状态
                rocketMQProducer.sendDelayMessage(
                        RocketMQConstant.APP_ADMIN_TOPIC,
                        RocketMQConstant.ADMIN_QUERY_EXPERIENCE_TAG,
                        JSONUtil.toJsonStr(shopMiniVersion),
                        2
                );
            }
        } catch (Exception e) {
            // 异常信息是保存到版本信息里面
            String errorMsg = e.getMessage();
            shopMiniVersion.setFailedReason(errorMsg);
            shopMiniVersionService.update(shopMiniVersion);
        }
    }

    @Override
    public void submitAudit(ShopMiniVersion shopMiniVersion) {
        Long shopWxaId = shopMiniVersion.getShopWxaId();
        Map<String, Object> query = new HashMap<>();
        query.put("id", shopWxaId);
        ShopWxa shopWxa = shopWxaReadService.getOne(query);
        JSONObject submitAuditParam = new JSONObject();
        submitAuditParam.put("versionDesc", shopMiniVersion.getVersionDesc());
        submitAuditParam.put("app_version", shopMiniVersion.getVersion());
        submitAuditParam.put("token", shopWxa.getAuthorizerAccessToken());
        submitAuditParam.put("auto_online", "true");
        try {
            //   提交审核
            AlipayOpenMiniVersionAuditApplyResponse response = alipayOpenAPIUtil.submitAudit(submitAuditParam);
            if (!response.isSuccess()) {
                throw new ApiException("支付宝 店铺小程序提交审核执行失败 原因：" + response.getSubMsg());
            }
            // 提交审核成功后 删除用做 幂等 校验的key
            List<String> keys = getAuditKeys(shopMiniVersion);
            redisTemplate.delete(keys);
            shopMiniVersion.setVersionState(ShopMiniVersionStateEnum.IN_AUDIT.getCode());
            shopMiniVersion.setFailedReason("");
            shopMiniVersionService.update(shopMiniVersion);
        } catch (Exception e) {
            // 异常信息是保存到版本信息里面
            String errorMsg = e.getMessage();
            shopMiniVersion.setFailedReason(errorMsg);
            shopMiniVersionService.update(shopMiniVersion);
            throw new ApiException(errorMsg);
        }
    }

    /**
     * 获取审核响应需要用的key
     *
     * @param shopMiniVersion 店铺小程序迭代版本信息
     * @return 审核响应需要用的key
     */
    private List<String> getAuditKeys(ShopMiniVersion shopMiniVersion) {
        List<String> keys = new ArrayList<>();
        String redisKey = RedisKeyConstant.SHOP_MINI_VERSION_AUDIT_MESSAGE_PREFIX + shopMiniVersion.getShopWxaId() + ":" + shopMiniVersion.getVersion() + ":" + AlipayMessageNotifyConstant.MINI_VERSION_AUDIT_PASSED;
        keys.add(redisKey);
        redisKey = RedisKeyConstant.SHOP_MINI_VERSION_AUDIT_MESSAGE_PREFIX + shopMiniVersion.getShopWxaId() + ":" + shopMiniVersion.getVersion() + ":" + AlipayMessageNotifyConstant.MINI_VERSION_AUDIT_REJECTED;
        keys.add(redisKey);
        redisKey = RedisKeyConstant.SHOP_MINI_VERSION_AUDIT_MESSAGE_PREFIX + shopMiniVersion.getShopWxaId() + ":" + shopMiniVersion.getVersion() + ":" + AlipayMessageNotifyConstant.MINI_VERSION_BASE_AUDIT_PASSED;
        keys.add(redisKey);
        return keys;
    }

    /**
     * 支付宝消息回调
     *
     * @param param 请求参数
     * @return 是否已经成功处理消息
     */
    @Override
    public String messageCallbackNotify(JSONObject param) {
        String bodyStr = param.get("body", String.class);
        Map<String, String> map = alipayOpenAPIUtil.convertMap(bodyStr);
        String msgMethod = map.get("msg_method");
        if (msgMethod.startsWith("alipay.open.mini.version")) {
            // 小程序审核通过通知  小程序审核驳回通知   小程序审核不可营销通知
            return miniAuditCallBack(map);
        }
        return "success";
    }

    @Override
    public void offline(ShopWxa shopWxa) {
        JSONObject offlineParam = new JSONObject();
        offlineParam.put("token", shopWxa.getAuthorizerAccessToken());
        offlineParam.put("appVersion", shopWxa.getOnlineVersion());
        AlipayOpenMiniVersionOfflineResponse response = alipayOpenAPIUtil.offline(offlineParam);
        if (!response.isSuccess()) {
            throw new ApiException("小程序下架失败 失败原因 " + response.getSubMsg());
        }
    }

    @Override
    public void rollback(ShopWxa shopWxa) {
        JSONObject offlineParam = new JSONObject();
        offlineParam.put("token", shopWxa.getAuthorizerAccessToken());
        offlineParam.put("appVersion", shopWxa.getOnlineVersion());
        AlipayOpenMiniVersionRollbackResponse response = alipayOpenAPIUtil.rollback(offlineParam);
        if (!response.isSuccess()) {
            throw new ApiException("小程序回滚失败 失败原因 " + response.getSubMsg());
        }
    }

    @Override
    public String bindQrCode(JSONObject param) {
        AlipayOpenMiniQrcodeBindResponse response = alipayOpenAPIUtil.bindQrCode(param);
        if (!response.isSuccess()) {
            throw new ApiException("支付宝 店铺小程序关联普通二维码失败 原因 " + response.getSubMsg());
        }
        return response.getRouteGroup();
    }

    @Override
    public Boolean cancelVersion(ShopMiniVersion shopMiniVersion) {
        Long shopWxaId = shopMiniVersion.getShopWxaId();
        Map<String, Object> query = new HashMap<>();
        query.put("id", shopWxaId);
        ShopWxa shopWxa = shopWxaReadService.getOne(query);
        JSONObject versionCancelParam = new JSONObject();
        versionCancelParam.put("appVersion", shopMiniVersion.getVersion());
        versionCancelParam.put("token", shopWxa.getAuthorizerAccessToken());
        AlipayOpenMiniVersionAuditedCancelResponse response = alipayOpenAPIUtil.versionCancel(versionCancelParam);
        if (!response.isSuccess()) {
            throw new ApiException("回退开发失败 失败原因 " + response.getSubMsg());
        }
        return true;
    }

    /**
     * 处理  小程序审核通过通知  小程序审核驳回通知   小程序审核不可营销通知
     *
     * @param param 请求参数
     * @return 是否已经成功处理消息
     */
    public String miniAuditCallBack(Map<String, String> param) {
        String bizContentStr = param.get("biz_content");
        JSONObject bizContent = JSONUtil.parseObj(bizContentStr);
        String appId = bizContent.get("mini_app_id", String.class);
        String version = bizContent.get("mini_app_version", String.class);
        Map<String, Object> query = new HashMap<>();
        query.put("appId", appId);
        query.put("appType", AppTypeEnum.ALIPAY.getCode());
        ShopWxa shopWxa = shopWxaReadService.getOne(query);
        query = new HashMap<>();
        query.put("shopWxaId", shopWxa.getId());
        query.put("templateId", shopWxa.getWxaTemplatesId());
        query.put("version", version);
        ShopMiniVersion shopMiniVersion = shopMiniVersionService.getOne(query);
        String msgMethod = param.get("msg_method");
        try {
            boolean flag = alipayOpenAPIUtil.checkSign(param);
            if (!flag) {
                log.error("支付宝 验签失败，参数：{}", param);
                throw new ApiException("支付宝 验签失败");
            }
            log.info("验签通过");
            // 幂等性处理
            String redisKey = RedisKeyConstant.SHOP_MINI_VERSION_AUDIT_MESSAGE_PREFIX + shopWxa.getId() + ":" + version + ":" + msgMethod;
            Boolean isProcess = redisTemplate.hasKey(redisKey);
            if (Boolean.TRUE.equals(isProcess)) {
                log.warn("审核回调 该店铺小程序id为 {} 版本号为{}的审核回调为{}已经处理过，无需重复处理", shopWxa.getId(), version, msgMethod);
                return "success";
            }
            if (msgMethod.equals(AlipayMessageNotifyConstant.MINI_VERSION_AUDIT_PASSED)) {
                // 小程序审核通过通知
                auditPassed(shopWxa, shopMiniVersion, redisKey);
            } else if (msgMethod.equals(AlipayMessageNotifyConstant.MINI_VERSION_AUDIT_REJECTED)) {
                // 小程序审核驳回通知
                String auditReason = bizContent.get("audit_reason", String.class);
                auditRejected(shopMiniVersion, shopWxa, auditReason, redisKey);
            } else {
                // 小程序审核不可营销通知
                String auditReason = bizContent.getByPath("promote_audit_reason.memos[0].memo", String.class);
                auditPartPassed(shopWxa, shopMiniVersion, auditReason, redisKey);
            }
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            if (errorMsg.length() > 255) {
                errorMsg = errorMsg.substring(0, 254);
            }
            shopMiniVersion.setFailedReason(errorMsg);
            shopMiniVersion.setVersionState(ShopMiniVersionStateEnum.AUDIT_REJECTED.getCode());
            shopMiniVersionService.update(shopMiniVersion);
        }
        return "success";
    }


    /**
     * 审核部分通过
     *
     * @param shopWxa         店铺小程序信息
     * @param shopMiniVersion 店铺小程序迭代版本信息
     * @param auditReason     异常信息
     */
    private void auditPartPassed(ShopWxa shopWxa, ShopMiniVersion shopMiniVersion, String auditReason, String redisKey) {
        // 商家存在线上且是双重审核通过的版本 不予上架
        Boolean whetherOnline = shopWxa.getWhetherOnline();
        if (whetherOnline) {
            // 商家小程序已上架  此时存放的上架版本为上一个版本的
            String lastVersion = shopWxa.getOnlineVersion();
            Map<String, Object> query = new HashMap<>();
            query.put("shopWxaId", shopWxa.getId());
            query.put("templateId", shopWxa.getWxaTemplatesId());
            query.put("appVersion", shopWxa.getAppType());
            query.put("version", lastVersion);
            ShopMiniVersion lastShopMiniVersion = shopMiniVersionService.getOne(query);
            if (lastShopMiniVersion.getAuditPassedState() == 1) {
                // 上一次的版本为双重审核通过的版本
                log.info("店铺小程序：{}，上一次版本是：{}，审核状态为双重审核通过,则直接回退", shopWxa.getName(), lastVersion);
                JSONObject versionCancelParam = new JSONObject();
                versionCancelParam.put("appVersion", shopMiniVersion.getVersion());
                versionCancelParam.put("token", shopWxa.getAuthorizerAccessToken());
                AlipayOpenMiniVersionAuditedCancelResponse response = alipayOpenAPIUtil.versionCancel(versionCancelParam);
                if (!response.isSuccess()) {
                    throw new ApiException("回退开发失败 失败原因 " + response.getSubMsg());
                }
                shopMiniVersion.setFailedReason(auditReason);
                shopMiniVersion.setAuditPassedState(2);
                shopMiniVersion.setVersionState(ShopMiniVersionStateEnum.AUDIT_REJECTED.getCode());
                shopMiniVersionService.update(shopMiniVersion);
                // 由于回调总时长为25h 这里给的过期时间稍微大于回调总时长即可
                redisTemplate.opsForValue().set(redisKey, "process", 30, TimeUnit.HOURS);
                return;
            }
            // 上一次的版本为部分审核通过的版本
            // 将上个版本状态改为下架
            lastShopMiniVersion.setFailedReason("");
            lastShopMiniVersion.setVersionState(ShopMiniVersionStateEnum.OFFLINE.getCode());
            lastShopMiniVersion.setOfflineTime(new Date());
            lastShopMiniVersion.setWhetherOnline(false);
            shopMiniVersionService.update(lastShopMiniVersion);
            shopMiniVersion.setLastVersion(lastVersion);
        }
        // 店铺小程序未上架 || 上一次的版本为部分审核通过的版本
        shopWxa.setWhetherOnline(true);
        shopWxa.setOnlineVersion(shopMiniVersion.getVersion());
        shopWxaWriteService.updateInfo(shopWxa);
        shopMiniVersion.setWhetherOnline(true);
        shopMiniVersion.setOnlineTime(new Date());
        shopMiniVersion.setFailedReason(auditReason);
        shopMiniVersion.setWhetherExperience(false);
        shopMiniVersion.setAuditPassedState(2);
        shopMiniVersion.setVersionState(ShopMiniVersionStateEnum.ONLINE.getCode());
        shopMiniVersionService.update(shopMiniVersion);
        // 由于回调总时长为25h 这里给的过期时间稍微大于回调总时长即可
        redisTemplate.opsForValue().set(redisKey, "process", 30, TimeUnit.HOURS);
    }

    /**
     * 审核通过
     *
     * @param shopWxa         店铺小程序信息
     * @param shopMiniVersion 店铺小程序迭代版本信息
     * @param redisKey
     */
    private void auditPassed(ShopWxa shopWxa, ShopMiniVersion shopMiniVersion, String redisKey) {
        String lastVersion = shopWxa.getOnlineVersion();
        if (StrUtil.isNotBlank(lastVersion)) {
            // 下架上一个版本
            offlineLastVersion(shopWxa);
            shopMiniVersion.setLastVersion(lastVersion);
        }
        shopWxa.setWhetherOnline(true);
        shopWxa.setOnlineVersion(shopMiniVersion.getVersion());
        shopWxaWriteService.updateInfo(shopWxa);
        shopMiniVersion.setFailedReason("");
        shopMiniVersion.setWhetherOnline(true);
        // 上架后 体验版取消了
        shopMiniVersion.setWhetherExperience(false);
        // 由于审核通过后直接回自动上架
        shopMiniVersion.setVersionState(ShopMiniVersionStateEnum.ONLINE.getCode());
        shopMiniVersion.setOnlineTime(new Date());
        shopMiniVersion.setAuditPassedState(1);
        shopMiniVersionService.update(shopMiniVersion);
        // 由于回调总时长为25h 这里给的过期时间稍微大于回调总时长即可
        redisTemplate.opsForValue().set(redisKey, "process", 30, TimeUnit.HOURS);
    }

    /**
     * 下架上一个版本
     *
     * @param shopWxa         店铺小程序信息
     */
    private void offlineLastVersion(ShopWxa shopWxa) {
        Map<String, Object> query = new HashMap<>();
        query.put("shopWxaId", shopWxa.getId());
        query.put("templateId", shopWxa.getWxaTemplatesId());
        query.put("version", shopWxa.getOnlineVersion());
        query.put("appVersion", shopWxa.getAppType());
        ShopMiniVersion lastShopMiniVersion = shopMiniVersionService.getOne(query);
        if (lastShopMiniVersion != null) {
            // 存在现有上架的版本
            lastShopMiniVersion.setVersionState(ShopMiniVersionStateEnum.OFFLINE.getCode());
            lastShopMiniVersion.setWhetherOnline(false);
            lastShopMiniVersion.setOfflineTime(new Date());
            shopMiniVersionService.update(lastShopMiniVersion);
        }
    }

    private void auditRejected(ShopMiniVersion shopMiniVersion, ShopWxa shopWxa, String auditReason, String redisKey) {
        shopMiniVersion.setVersionState(ShopMiniVersionStateEnum.AUDIT_REJECTED.getCode());
        shopMiniVersion.setFailedReason(auditReason);
        shopMiniVersionService.update(shopMiniVersion);
        log.info("回退开发 店铺小程序：{}，版本是：{}，则直接回退", shopWxa.getName(), shopMiniVersion.getVersion());
        JSONObject versionCancelParam = new JSONObject();
        versionCancelParam.put("appVersion", shopMiniVersion.getVersion());
        versionCancelParam.put("token", shopWxa.getAuthorizerAccessToken());
        AlipayOpenMiniVersionAuditedCancelResponse response = alipayOpenAPIUtil.versionCancel(versionCancelParam);
        if (!response.isSuccess()) {
            throw new ApiException("回退开发失败 失败原因 " + response.getSubMsg());
        }
        // 由于回调总时长为25h 这里给的过期时间稍微大于回调总时长即可
        redisTemplate.opsForValue().set(redisKey, "process", 30, TimeUnit.HOURS);
    }


}
