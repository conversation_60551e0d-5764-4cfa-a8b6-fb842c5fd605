package moonstone.web.core.fileNew.y800.client;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.gson.Gson;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.ApiV3Resp;
import moonstone.common.model.Y800OpenRequestV3;

/**
 * @Author: yousx
 * @Date: 2024/12/20
 * @Description:
 */

@Slf4j
@Component
public class Y800V3Client {

    @Value("${y800.gateApi}")
    private String apiV3;

    public ApiV3Resp execute(Y800OpenRequestV3 y800OpenRequestV3) {
        Map<String, String> reqParams = y800OpenRequestV3.toMap();
        log.info("请求apiV3参数：{}", reqParams);
        StringBuilder requestBuf = new StringBuilder();
        for (String key : reqParams.keySet()) {
            requestBuf.append(key).append("=").append(URLEncoder.encode(reqParams.get(key), StandardCharsets.UTF_8)).append("&");
        }
        String sendStr = requestBuf.toString();
        HttpRequest httpRequest = HttpRequest.post(apiV3)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .send(sendStr);
        if (httpRequest.ok()) {
            String body = httpRequest.body();
            log.info("请求apiV3返回结果：{}", body);
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                return objectMapper.readValue(body, ApiV3Resp.class);
            } catch (JsonProcessingException e) {
                log.error("请求apiV3失败，结果转换异常", e);
                throw new RuntimeException("请求apiV3失败，结果转换异常");
            }
        } else {
            throw new RuntimeException("请求apiV3失败，网络异常，网络错误码为：" + httpRequest.code());
        }
    }
}
