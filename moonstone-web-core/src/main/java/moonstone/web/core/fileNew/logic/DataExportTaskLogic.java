package moonstone.web.core.fileNew.logic;

import lombok.extern.slf4j.Slf4j;
import moonstone.user.enums.DataExportTaskStatusEnum;
import moonstone.user.model.DataExportTask;
import moonstone.user.service.DataExportTaskReadService;
import moonstone.user.service.DataExportTaskWriteService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * author：书生
 */
@Service
@Slf4j
public class DataExportTaskLogic {

    @Resource
    private DataExportTaskReadService dataExportTaskReadService;

    @Resource
    private DataExportTaskWriteService dataExportTaskWriteService;


    /**
     * 保存导出任务
     *
     * @param dataExportTask 保存导出任务
     * @return 导出任务
     */
    public DataExportTask save(DataExportTask dataExportTask) {
        dataExportTask.setStartTime(new Date());
        dataExportTask.setStatus(DataExportTaskStatusEnum.PROCESSING.getCode());
        dataExportTaskWriteService.create(dataExportTask);
        return dataExportTask;
    }


    /**
     * 根据任务id查询导出任务信息
     *
     * @param taskId 任务id
     * @return 导出任务信息
     */
    public DataExportTask getById(Long taskId) {
        return dataExportTaskReadService.findById(taskId).getResult();
    }

    /**
     * 更新导出任务信息
     * @param dataExportTask 导出任务信息
     */
    public void update(DataExportTask dataExportTask) {
        dataExportTaskWriteService.update(dataExportTask);
    }
}
