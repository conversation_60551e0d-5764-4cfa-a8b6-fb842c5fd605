package moonstone.web.core.fileNew.strategy.api;

import moonstone.web.core.fileNew.dto.*;

/**
 * <AUTHOR>
 */
public interface RefundStrategy {

    /**
     * 根据发货仓类型 获取不同发货仓类型的退款策略
     * @param shippingWarehouseType 发货仓类型
     * @return 不同发货仓类型对应的退款策略
     */
    boolean isSupport(int shippingWarehouseType);

    /**
     * 申请退款
     * @param refundApplyDto 请求参数
     * @return 退款单id
     */
    Long apply(RefundApplyDto refundApplyDto);


    /**
     * 同意退款申请
     * @param refundApplyAgreeDto 请求参数
     * @return 是否成功
     */
    Boolean applyAgree(RefundApplyAgreeDto refundApplyAgreeDto);

    /**
     * 拒绝退货申请
     * @param refundApplyRejectDto 请求参数
     * @return 是否成功
     */
    Boolean applyReject(RefundApplyRejectDto refundApplyRejectDto);


    /**
     * 上传快递单号
     * @param refundUploadExpressInfoDto 请求参数
     * @return 是否成功
     */
    Boolean uploadExpressInfo(RefundUploadExpressInfoDto refundUploadExpressInfoDto);

    /**
     * 退款申请与三方联调
     * @param refundThirdPartyDto 请求参数
     */
    void refundApplyThirdPartyInteraction(RefundThirdPartyDto refundThirdPartyDto);

    /**
     * 退款处理
     * @param refundHandleDto 请求参数
     * @return 是否成功
     */
    Boolean refundHandle(RefundHandleDto refundHandleDto);

    /**
     * 退款申请与三方联调
     * @param refundThirdPartyDto 请求参数
     */
    void refundRejectThirdPartyInteraction(RefundThirdPartyDto refundThirdPartyDto);

    /**
     * 上传退货物流单号与三方联调
     * @param refundThirdPartyDto 请求参数
     */
    void refundAfterSaleLogisticsUploadThirdPartyInteraction(RefundThirdPartyDto refundThirdPartyDto);
}
