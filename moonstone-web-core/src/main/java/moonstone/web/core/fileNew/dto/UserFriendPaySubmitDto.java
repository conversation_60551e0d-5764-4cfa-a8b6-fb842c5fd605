package moonstone.web.core.fileNew.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class UserFriendPaySubmitDto {


	/**
	 * 店铺id
	 */
	@NotNull(message = "店铺id不能为空")
	private Long shopId;

	/**
	 * 好友名称
	 */
	@NotBlank(message = "好友名称不能为空")
	private String paperName;

	/**
	 * 好友身份证
	 */
	@NotBlank(message = "好友身份证不能为空")
	private String paperNo;


}
