package moonstone.web.core;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.security.KeyStore;

/**
 * <AUTHOR>
 */
@Profile({"dev", "test", "stag", "integration"})
@Component
public class AppUnionPayKeyStoreDevConfig {

    @Bean
    public KeyStore pcUnionpayKeyStore() throws Exception {
        return KeyStore.getInstance(KeyStore.getDefaultType());
    }

    @Bean
    public KeyStore appUnionpayKeyStore() throws Exception {
        return KeyStore.getInstance(KeyStore.getDefaultType());
    }

    @Bean
    public KeyStore wapUnionpayKeyStore() throws Exception {
        return KeyStore.getInstance(KeyStore.getDefaultType());
    }

}
