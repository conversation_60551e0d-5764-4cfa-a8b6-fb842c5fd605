package moonstone.web.core.component.pay.allinpay.handler;

import com.alibaba.fastjson.JSONObject;
import io.terminus.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.PaymentWayEnum;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.result.ShopOrderWithPaymentDO;
import moonstone.order.service.ShopOrderReadService;
import moonstone.user.acct.IAllinpayUserHandler;
import moonstone.user.bo.UserFriendPayBO;
import moonstone.user.service.UserBindAllinPayService;
import moonstone.web.core.fileNew.logic.PaymentV2Logic;
import moonstone.web.core.fileNew.logic.ShopOrderLogic;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class PayerBizUserIdHandler {

    @Resource
    private IAllinpayUserHandler allinpayUserHandler;

    @Resource
    private UserBindAllinPayService userBindAllinPayService;

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private PaymentV2Logic paymentV2Logic;

    @Resource
    private ShopOrderLogic shopOrderLogic;

    /**
     * 支付单发起支付时：获取支付人信息（兼容原通联会员bizUserId获取方式）
     *
     * @param shopId
     * @param userId
     * @param shopOrder
     * @return
     */
    public PayerBizUserInfo getPayerBizUserInfo(Long shopId, Long userId, ShopOrder shopOrder) {
        PayerBizUserInfo userInfo = new PayerBizUserInfo();

        if(!PaymentWayEnum.FRIEND_PAY.getCode().equals(shopOrder.getPaymentWay())){
            var buyerAllInPay = userBindAllinPayService.getActiveByShopIdAndUserId(shopId, userId);
            if (buyerAllInPay == null || StringUtils.isBlank(buyerAllInPay.getBizUserId())) {
                throw new RuntimeException("当前用户尚未注册通商云");
            }
            userInfo.setFriendName("");
            userInfo.setFriendIdentityCard("");
            userInfo.setBizUserId(buyerAllInPay.getBizUserId());
            userInfo.setFriendIdentityCard("");
            return userInfo;
        }
        // 代付逻辑
        return getPayerBizUserInfoByShopOrder(shopOrder);
    }

    /**
     * 仅退款时用（因为提示原因）
     * @param paymentId
     * @return
     */
    public PayerBizUserInfo getPayerBizUserInfoByPaymentId(Long paymentId){
        // 查询支付单关联的订单信息
        List<Long> shopOrderList = paymentV2Logic.listOrderIdByPaymentId(paymentId);
        if(CollectionUtils.isEmpty(shopOrderList)){
            throw new ServiceException("支付单关联的订单不能为空");
        }

        ShopOrder shopOrder = shopOrderLogic.getById(shopOrderList.get(0));
        Long shopId = shopOrder.getShopId();
        Long userId = shopOrder.getBuyerId();

        PayerBizUserInfo userInfo = new PayerBizUserInfo();

        if(!PaymentWayEnum.FRIEND_PAY.getCode().equals(shopOrder.getPaymentWay())){
            var buyerAllInPay = userBindAllinPayService.getActiveByShopIdAndUserId(shopId, userId);
            if (buyerAllInPay == null || StringUtils.isBlank(buyerAllInPay.getBizUserId())) {
                throw new RuntimeException("买家尚未注册云商通");
            }
            userInfo.setFriendName("");
            userInfo.setFriendIdentityCard("");
            userInfo.setBizUserId(buyerAllInPay.getBizUserId());
            userInfo.setFriendIdentityCard("");
            return userInfo;
        }
        // 代付逻辑
        return getPayerBizUserInfoByShopOrder(shopOrder);
    }

    /**
     * 通过shopOrder拓展字段查询支付人的bizUserId
     * @param shopOrder
     * @return
     */
    private PayerBizUserInfo getPayerBizUserInfoByShopOrder(ShopOrder shopOrder) {
        PayerBizUserInfo userInfo = new PayerBizUserInfo();

        Map<String, String> shopOrderExtra = shopOrder.getExtra();
        String payerBizUserId = shopOrderExtra.get(ShopOrderExtra.payerBizUserId.name());
        String friendId = shopOrderExtra.get(ShopOrderExtra.friendId.name());
        Long _friendId = Long.valueOf(friendId);


        UserFriendPayBO userFriendPayBO = allinpayUserHandler.findById(_friendId);
        if(userFriendPayBO == null){
            log.error("好友代付时 代付人信息不存在 {}", friendId);
            throw new RuntimeException("好友代付时 代付人信息不能为空");
        }else {
            // 使用代付人的信息
            userInfo.setFriendName(userFriendPayBO.getFriendName());
            userInfo.setFriendIdentityCard(userFriendPayBO.getFriendIdentityCard());
            // 取订单上的值：理论上代付人信息不回变更，取代付人的也行
            userInfo.setBizUserId(payerBizUserId);
            userInfo.setFriendId(userFriendPayBO.getFriendId());
        }
        log.info("代付人信息 {}", JSONObject.toJSONString(userInfo));
        return userInfo;
    }
//    /**
//     * 设置支付人bizUserId
//     * @param bizParameter
//     * @param buyerAllInPay
//     * @param userFriendPayBO
//     */
//    private void setPayerId(BizParameter bizParameter, UserBindAllinPay buyerAllInPay, UserFriendPayBO userFriendPayBO) {
//        if(userFriendPayBO == null){
//            bizParameter.put("payerId", buyerAllInPay.getBizUserId());
//        }else {
//            // 使用代付人的信息
//            bizParameter.put("payerId", userFriendPayBO.getFriendBizUserId());
//        }
//    }

}
