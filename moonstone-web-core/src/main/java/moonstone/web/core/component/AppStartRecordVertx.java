package moonstone.web.core.component;

import io.vertx.core.Vertx;
import moonstone.common.StartEventTag;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.InetAddress;

@Component
public class AppStartRecordVertx{

    @Resource
    private AppStartUUID appStartUUID;

    @Resource
    private Vertx vertx;

    /**
     * 这个类是一个 Spring 组件，用于在应用启动时记录当前实例的 UUID 和 IP 地址，并存储到 Vert.x 的共享数据 AsyncMap 中，以便后续分布式环境下的节点识别和管理。
     */
    @StartEventTag(cache = false, desc = "[已确认] 更新本机地址到共享内存")
    @EventListener(ContextRefreshedEvent.class)
    public void refresh(){
        try {
            String address = InetAddress.getLocalHost().getHostAddress();
            vertx.runOnContext(v -> {
                vertx.sharedData().getAsyncMap("UUID")
                        .onSuccess(map -> map.put(address, appStartUUID.password()));
            });
        }catch (Exception ignored){

        }
    }
}
