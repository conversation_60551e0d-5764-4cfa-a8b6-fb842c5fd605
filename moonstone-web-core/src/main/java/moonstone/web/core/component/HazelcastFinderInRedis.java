package moonstone.web.core.component;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.XxlJobDesc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.exceptions.JedisDataException;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.InetAddress;
import java.util.*;

@Slf4j
@Component
public class HazelcastFinderInRedis {
    private final Set<String> addressSet = new HashSet<>();
    @Value("${hzl.broadcast:hazelcastCenter}")
    public String broadcast;

    @Value("${hzl.broadcast:hazelcastAddressLive-}")
    public String addressLive;

    @Autowired
    JedisPool jedisPool;

    public List<String> getAddress() {
        try (Jedis jedis = jedisPool.getResource()) {
            for (String address : jedis.smembers(broadcast)) {
                if (Objects.nonNull(jedis.get(addressLive + address))) {
                    addressSet.add(address);
                } else {
                    jedis.srem(broadcast, address);
                }
            }
        } catch (JedisDataException jedisDataException) {
            log.error("[{}] data acquire[key=>{}] error, deleting the broadcast now", LogUtil.getClassMethodName()
                    , broadcast, jedisDataException);
            try (Jedis jedis = jedisPool.getResource()) {
                jedis.del(broadcast);
            }
        } catch (Exception ex) {
            log.debug("{} fail to declare self", LogUtil.getClassMethodName(), ex);
        }
        return new ArrayList<>(addressSet);
    }

    @PostConstruct
    public void declareSelf2() {
        declareSelf("");
    }

    @XxlJobDesc(desc = "HazelcastFinderInRedis#declareSelf", core = "0 0/1 * * * ?")
    @XxlJob("HazelcastFinderInRedis#declareSelf")
//    @Scheduled(cron = "0 0/1 * * * ?")
    public ReturnT<String> declareSelf(String param) {
        try (Jedis jedis = jedisPool.getResource()) {
            String address = InetAddress.getLocalHost().getHostAddress();
            jedis.sadd(broadcast, address);
            jedis.set(addressLive + address, "true");
            jedis.expire(addressLive + address, 61);
            return ReturnT.SUCCESS;
        } catch (JedisDataException jedisDataException) {
            log.error("[{}] data acquire[key=>{}] error, deleting the broadcast now", LogUtil.getClassMethodName()
                    , broadcast, jedisDataException);
            try (Jedis jedis = jedisPool.getResource()) {
                jedis.del(broadcast);
                String address = InetAddress.getLocalHost().getHostAddress();
                jedis.sadd(broadcast, address);
                jedis.set(addressLive + address, "true");
                jedis.expire(addressLive + address, 61);
            } catch (Exception exception) {
                log.error("{} fail to get address", LogUtil.getClassMethodName(), exception);
            }
        } catch (Exception ex) {
            log.debug("{} fail to declare self", LogUtil.getClassMethodName(), ex);
        }
        return ReturnT.FAIL;
    }

    @PreDestroy
    public void clearSelf() {
        try (Jedis jedis = jedisPool.getResource()) {
            String address = InetAddress.getLocalHost().getHostAddress();
            jedis.srem(broadcast, address);
        } catch (Exception ex) {
            log.debug("{} fail to clear self", LogUtil.getClassMethodName(), ex);
        }
    }
}
