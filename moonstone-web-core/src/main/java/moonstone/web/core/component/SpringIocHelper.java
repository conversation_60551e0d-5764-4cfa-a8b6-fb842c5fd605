package moonstone.web.core.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.StartEventTag;
import org.springframework.context.ApplicationContext;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Spring Context Helper
 * For Vertx to reuse the Spring component
 */
@Slf4j
@Component
public class SpringIocHelper {
    private static final CompletableFuture<ApplicationContext> CONTEXT = new CompletableFuture<>();

    @StartEventTag(cache = true, desc = "[已确认] 获取spring上下文")
    @EventListener(ContextRefreshedEvent.class)
    public void loadContext(ContextRefreshedEvent refreshedEvent) {
        CONTEXT.complete(refreshedEvent.getApplicationContext());
    }

    public static <T> CompletableFuture<T> getBean(Class<T> clazz) {
        return CONTEXT.thenApply(context -> context.getBean(clazz));
    }

    public static <T> CompletableFuture<T> getBean(Class<T> clazz, String name) {
        return CONTEXT.thenApply(context -> context.getBean(name, clazz));
    }

    public static <T> CompletableFuture<Map<String, T>> getListOfType(Class<T> type) {
        return CONTEXT.thenApply(context -> context.getBeansOfType(type));
    }
}
