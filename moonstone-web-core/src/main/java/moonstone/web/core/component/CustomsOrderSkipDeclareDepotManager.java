package moonstone.web.core.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.StartEventTag;
import moonstone.common.utils.EventSender;
import moonstone.web.core.events.customs.SkipDeclareDepotCodeRegisterEvent;
import moonstone.web.core.model.SkipDeclareDepotCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class CustomsOrderSkipDeclareDepotManager {
    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 注意：
     *  1、初始化时刷新内存缓存没问题，但是通过接口发布的新时间都是内存事件（不会刷新其他节点，需要其他节点重启后生效）
     *  2、其他方法没有实际入口
     */
    @StartEventTag(cache = true, desc = "[已确认] 通过spring事件刷新内存缓存")
    @EventListener(ContextRefreshedEvent.class)
    public void autoRegisterAfterContextReady() {
        Optional.ofNullable(list()).ifPresent(list -> list.stream().map(SkipDeclareDepotCode::getDepotCode)
                .map(SkipDeclareDepotCodeRegisterEvent::new)
                .forEach(EventSender::publish));
    }

    public List<SkipDeclareDepotCode> list() {
        return mongoTemplate.findAll(SkipDeclareDepotCode.class);
    }

    public void delSkipDeclareDepotCode(String depotCode) {
        mongoTemplate.remove(Query.query(Criteria.where("depotCode").is(depotCode)), SkipDeclareDepotCode.class);
        EventSender.publish(new SkipDeclareDepotCodeRegisterEvent(depotCode, false));
    }

    public void addSkipDeclareDepotCode(String name, String depotCode) {
        mongoTemplate.insert(new SkipDeclareDepotCode(name, depotCode));
        EventSender.publish(new SkipDeclareDepotCodeRegisterEvent(depotCode, true));
    }
}
