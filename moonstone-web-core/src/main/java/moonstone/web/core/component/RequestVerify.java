package moonstone.web.core.component;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.UUID;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import redis.clients.jedis.Jedis;
import redis.clients.util.Pool;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
public class RequestVerify implements Filter {

    static final String ADDRESS = "/api/token/request";
    @Autowired(required = false)
    VerifyOn on;
    Set<String> LET_IT_GO = new HashSet<>(Arrays.asList(
            "/api/order/pay",
            "/api/k8s/living",
            "/api/user/current",
            "/api/user/newWxUserBind",
            "/api/Y800/shipmentCallBack",
            "/api/callback/Y800Storage",
            "/api/wechat/notify/receive_ticket",
            "/api/customs/payment/declare/notify",
            "/api/user/wxaProjectLogin",
            "/api/search-in-shop",
            "/api/news/search-in-shop",
            "/api/order/paid",
            "/api/order/paid/userRegister",
            "/api/userCertification/realNameCertificationCheck",
            "/api/shop/get-shop-fwb",
            "/api/shopAppAuth/alipay/authNotify",
            "/api/alipay/shop/mini/message/notify",
            "/api/user/files/upload",
            "/api/file/upload",
            "/api/external/order/pages",
            "/api/external/order/by/declaredId",
            "/api/external/subStore/pageExternalSubStore"
    ));
    Set<String> PATTERN_LET_IT_GO = new HashSet<>(Arrays.asList("/api/user/\\d*/roles"
            , "/api/user/\\d*"
            , "/api/item/search/blacklist/.*"
            , "/api/pages/.*"
            , "/api/\\d*/design/page/.*"
            , "/api/order/paid/.*/account/.*"
            , "/api/order/paid/userRegister/.*/account/.*"
            , "/api/refund/notify/.*/account/.*"
            , "/api/v1/mirror/.*/.*"
            , "/api/.*/upload/library/fileList-page"
            , "/api/.*/upload/library/uploadFile"
            , "/api/wechat/notify/.*/callback"
            , "/api/withdrawPayment/.*/payCallback/.*"
            , "/api/withdrawPayment/.*/payCallbackPost/.*"
            , "/api/agentPayPayment/.*/payCallback/.*"
            , "/api/fundsTransferPayment/.*/payCallback/.*"
            , "/api/enterpriseWithdraw/.*/payCallback/.*"
            , "/allinPay/.*"
            , "/api/scan/.*"
    ));
    @Autowired
    @Lazy
    Pool<Jedis> jedisPool;
    LoadingCache<String, Boolean> clientLoadingCache =
            Caffeine.newBuilder()
                    .maximumSize(500)
                    .expireAfterWrite(3, TimeUnit.MINUTES)
                    .build(this::fetchClientFromRedis);
    LoadingCache<String, String> tokenLoadingCache =
            Caffeine.newBuilder()
                    .maximumSize(1000)
                    .expireAfterWrite(15, TimeUnit.SECONDS)
                    .build(this::fetchTokenFromRedis);

    private boolean fetchClientFromRedis(@NonNull String client) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.exists(client);
        }
    }

    private String fetchTokenFromRedis(@NonNull String client) {
        try (Jedis jedis = jedisPool.getResource()) {
            return jedis.get("AC:" + client);
        }
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        if (on == null) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        if (!(servletRequest instanceof HttpServletRequest)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        // check if the request come from wechat
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        String msid = httpServletRequest.getHeader("msid");
        log.info("msid -> {}", msid);
        String userAgent = Optional.ofNullable(httpServletRequest.getHeader("User-Agent")).orElse("");
        String fromSource = Optional.ofNullable(httpServletRequest.getHeader("FromSource")).orElse("");
        if (userAgent.contains("Security")) {
            if (System.currentTimeMillis() % 10 == 0) {
                filterChain.doFilter(servletRequest, servletResponse);
            } else {
                servletResponse.getWriter().write(new JsonObject().put("code", -1)
                        .put("errorMsg", "too frequency").toString());
            }
            return;
        }
        if ((userAgent.contains("MicroMessenger") || userAgent.contains("wechat") || fromSource.contains("wechat"))) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        // check if the request to token
        String path = httpServletRequest.getRequestURI();
        if (ADDRESS.equals(path) ||  LET_IT_GO.contains(path)
                || PATTERN_LET_IT_GO.stream().anyMatch(path::matches)) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        // verify the token
        if (!verifyToken(httpServletRequest)) {
            servletResponse.setContentType("application/json");
            servletResponse.getWriter()
                    .write(new JsonObject()
                            .put("success", "false")
                            .put("errorMsg", "Unauthorized Request")
                            .put("code", 401)
                            .toString()
                    );
            return;
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    private boolean verifyToken(HttpServletRequest servletRequest) {
        if (Optional.ofNullable(System.getenv("SKIP_VERIFY"))
                .map(String::toUpperCase).filter("TRUE"::equals).isPresent()) {
            return true;
        }
        try {
            String client = servletRequest.getHeader("client");
            String token = servletRequest.getHeader("token");
            log.info("client -> {}, token -> {}", client, token);
            if (client == null) {
                log.error("Fail to fetch client, Url -> {}, Agent -> {}"
                        , servletRequest.getRequestURI()
                        , servletRequest.getHeader("User-Agent"));
                return false;
            }
            String realToken = fetchToken(client);
            if (realToken == null) {
                log.error("Fail to fetch Token");
                return false;
            }
            byte[] rom = Base64.getDecoder().decode(token);
            byte[] realTokenByte = realToken.getBytes(StandardCharsets.UTF_8);
            for (int i = 0; i < rom.length; i++) {
                rom[i] ^= realTokenByte[i];
            }
            String time = new String(rom);
            return Long.parseLong(time) > (System.currentTimeMillis() - 10000);
        } catch (Exception e) {
            log.error("Fail to Verify Request", e);
        }
        return false;
    }

    @GetMapping(ADDRESS)
    public String token(HttpServletRequest request, HttpServletResponse response) {
        if (on == null) {
            return "";
        }
        String client = request.getHeader("client");
        if (client == null || !verifyClient(client)) {
            client = generateClient();
            response.setHeader("client", client);
        }
        String token = fetchToken(client);
        if (token == null) {
            token = writeToken(client);
        }
        return client + "|" + token;
    }

    private String writeToken(String client) {
        String token = UUID.randomUUID().toString().replaceAll("-", "").subSequence(0, 13).toString();
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.setex("AC:" + client, 60, token);
            tokenLoadingCache.invalidate(client);
        }
        return token;
    }

    private String fetchToken(String client) {
        return tokenLoadingCache.get(client);
    }

    private boolean verifyClient(String client) {
        return Objects.nonNull(clientLoadingCache.get(client));
    }

    private String generateClient() {
        String client = UUID.randomUUID().toString().replaceAll("-", "");
        // client should be update every 15 min
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.setex(client, 15 * 60, "1");
            clientLoadingCache.invalidate(client);
        }
        return client;
    }

    public static class VerifyOn {

    }
}
