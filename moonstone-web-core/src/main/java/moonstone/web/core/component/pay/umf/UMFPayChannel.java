package moonstone.web.core.component.pay.umf;

import com.google.common.eventbus.EventBus;
import io.terminus.pay.api.ChannelRegistry;
import io.terminus.pay.api.TokenProvider;
import io.terminus.pay.enums.TradeType;
import io.terminus.pay.event.PaymentRequestEvent;
import io.terminus.pay.exception.PayException;
import io.terminus.pay.model.*;
import io.terminus.pay.service.PayChannel;
import io.vertx.core.Future;
import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.eventbus.Message;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EncryptHelper;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.order.api.Y800OrderIdGenerator;
import moonstone.order.enu.ShopOrderExtra;
import moonstone.order.enu.ShopOrderIdentityErrorEnum;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.user.cache.UserCacheHolder;
import moonstone.user.model.User;
import moonstone.user.model.UserCertification;
import moonstone.user.service.UserCertificationReadService;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.component.user.UserAppInfoComponent;
import moonstone.web.core.registers.shop.TokenShopPayInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.time.Instant;
import java.util.*;
import java.util.stream.Stream;

/**
 * 底层依赖相关的 clojure 代码已被删除，后续有商家需要用到再重新对接
 */
@Deprecated
@Slf4j
@Component
public class UMFPayChannel implements PayChannel {
    @Autowired
    ChannelRegistry channelRegistry;
    @Autowired
    EventBus eventBus;
    @Resource
    ShopOrderReadService shopOrderReadService;
    @Resource
    SkuOrderReadService skuOrderReadService;
    @Resource
    PaymentReadService paymentReadService;
    @Resource
    UserCacheHolder userCacheHolder;
    @Resource
    ReceiverInfoReadService receiverInfoReadService;
    @Resource
    UserCertificationReadService userCertificationReadService;
    @Autowired
    TokenProvider<UMFToken> umfTokenTokenProvider;

    @Autowired
    ShopWxaCacheHolder shopWxaCacheHolder;
    @Resource
    RefundReadService refundReadService;
    @Autowired
    Y800OrderIdGenerator y800OrderIdGenerator;
    @Resource
    OrderWriteService orderWriteService;

    @Resource
    private UserAppInfoComponent userAppInfoComponent;

    @Lazy
    @Autowired
    Vertx vertx;

    @Value("${order.auto.cancel.in.minutes}")
    private Integer expireMinutes;

    @Resource
    private TokenShopPayInfo tokenShopPayInfo;

    @PostConstruct
    public void reg() {
        channelRegistry.register("umf", this);
    }

    @Override
    public TradeResult customDeclare(CustomDeclareParams customDeclareParams) throws PayException {
        throw Translate.exceptionOf("NOT SUPPORT");
    }

    @Override
    public TradeResult customDeclareRest(CustomDeclareParams customDeclareParams) throws PayException {
        throw Translate.exceptionOf("NOT SUPPORT");
    }

    @Override
    public TradeResult paymentStats(PaymentQueryParams params) {
        throw Translate.exceptionOf("NOT SUPPORT");
    }

    @Override
    public TradeResult refundStats(RefundQueryParams params) {
        JsonObject request = new JsonObject();
        Refund refund = refundReadService.findByOutId(params.getRefundNo()).getResult();
        Payment payment = paymentReadService.findByPaySerialNoAndChannel(refund.getPaySerialNo(), "umf").getResult();
        Long shopId = refund.getShopId();

        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        UMFToken umfToken = umfTokenTokenProvider.findToken(shopId + "");
        request.put("clientId", umfToken.getClient());
        request.put("secret", umfToken.getSecret());
        request.put("key", umfToken.getKey());
        request.put("paymentId", payment.getPaySerialNo());
        var data = vertx.eventBus()
                .<JsonObject>request("umf#query-refund", request, new DeliveryOptions().setLocalOnly(true))
                .toCompletionStage()
                .toCompletableFuture()
                .join()
                .body();
        log.info("REFUND -> {}", data);
        return data.mapTo(TradeResult.class);
    }

    @Override
    public TradeResult customStats(RefundQueryParams params) {
        throw Translate.exceptionOf("NOT SUPPORT");
    }

    @Override
    public void verify(HttpServletRequest request) throws PayException {
        // read data into attr
        try {
            Buffer buffer = Buffer.buffer();
            InputStream inputStream = request.getInputStream();
            for (int c = inputStream.read(); c > -1; c = inputStream.read()) {
                buffer.appendByte((byte) c);
            }
            request.setAttribute("requestBody", buffer.toJsonObject());
            // verify
            String header = request.getHeader("sign");
            // TODO next time
        } catch (Exception e) {
            log.error("fail to verify", e);
        }
    }

    @Override
    public TradeResult businessPay(BusinessPayParams businessPayParams) {
        throw Translate.exceptionOf("NOT SUPPORT");
        //return null;
    }

    @Override
    public TradeResult paymentClose(PaymentCloseParams paymentCloseParams) {
        // return null;
        throw Translate.exceptionOf("NOT SUPPORT");
    }

    Future<TradeRequest> buildReq(Message<JsonObject> message) {
        TradeRequest request = TradeRequest.ok(new Redirect("umf", false, null, message.body().toString(), message.body().toString()));
        return Future.succeededFuture(request);
    }

    @Override
    public TradeRequest paymentRequest(PaymentParams paymentParams) {
        Payment payment = paymentReadService.findByOutId(paymentParams.getTradeNo()).getResult();
        List<OrderPayment> ids = paymentReadService.findOrderIdsByPaymentId(payment.getId()).getResult();
        var shopOrder = shopOrderReadService.findById(ids.get(0).getOrderId()).getResult();
        Date date = shopOrder.getCreatedAt();
        User user = findPayerUser(shopOrder).orElseThrow(() -> Translate.exceptionOf("请不要代替购买"));
        ReceiverInfo receiverInfo = receiverInfoReadService.findByOrderId(ids.get(0).getOrderId(), OrderLevel.SHOP)
                .getResult().get(0);
        StringBuilder stringBuilder = new StringBuilder();

        Stream.of(receiverInfo.getProvince(), "-", receiverInfo.getCity(), "-", receiverInfo.getRegion(), "-",
                        receiverInfo.getStreet(), " ", receiverInfo.getDetail())
                .filter(Objects::nonNull)
                .forEach(stringBuilder::append);
        String address = stringBuilder.toString().replace("\n", " ");
        JsonObject request = new JsonObject();

        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        UMFToken umfToken = umfTokenTokenProvider.findToken(paymentParams.getSellerNo());
        if (umfToken.getKey() == null) {
            // reload key
            log.info("Key lost decode again [{}]", umfToken.getKeyStr());
            umfToken = new UMFToken(umfToken.getClient(), umfToken.getSecret(), Buffer.buffer(EncryptHelper.instance.decrypt(EncryptHelper.KeyEnu.CommonKey,
                    Base64.getDecoder().decode(umfToken.getKeyStr())).take().toByteArray()), umfToken.getKeyStr());
        }
        UserCertification userCertification = userCertificationReadService.findDefaultByUserId(user.getId()).getResult().get();
        String appId = userAppInfoComponent.findAppId(paymentParams.getSellerNo(), paymentParams.getOpenId(), user.getId());

        request.put("clientId", umfToken.getClient());
        request.put("secret", umfToken.secret);
        request.put("key", umfToken.getKey());
        request.put("content", new JsonObject()
                .put("ret_url", umfToken.getRefundNotifyUrl() + "/umf/account/" + paymentParams.getSellerNo())
                .put("notify_url", umfToken.getNotifyUrl() + "/umf/account/" + paymentParams.getSellerNo())
                .put("reg_day",
                        "" + (Instant.now().getEpochSecond() - user.getCreatedAt().toInstant().getEpochSecond()) / (24 * 60 * 60))
                .put("address", address)
                .put("id_num", userCertification.getPaperNo())
                .put("mobile", user.getMobile())
                .put("real_name", userCertification.getPaperName())
                .put("orders", buildOrder(ids))
                .put("payment_no", paymentParams.getTradeNo())
                .put("order_at", date.toInstant().getEpochSecond())
                // 写死
                .put("expire_time", expireMinutes == null ? "120" : expireMinutes.toString())
                .put("fee", payment.getFee())
                .put("open_id", paymentParams.getOpenId())
                .put("app_id", appId)
        );

        log.info("UMFPayChannel.paymentRequest, 支付单id={}, 开始调用umf#pay", payment.getId());
        return vertx.eventBus().<JsonObject>request("umf#pay", request, new DeliveryOptions().setLocalOnly(true))
                .flatMap(umfResult -> {
                    log.info("UMFPayChannel.paymentRequest, 支付单id={}, 完成调用umf#pay", payment.getId());
                    return buildReq(umfResult);
                })
                .onSuccess(r -> updateOrderStatusIfPayFail(r, ids))
                .onSuccess(r -> this.eventBus.post(new PaymentRequestEvent(paymentParams, r)))
                .toCompletionStage().toCompletableFuture().join();
    }

    private Optional<User> findPayerUser(ShopOrder shopOrder) {
        Long userId = UserUtil.getUserId();
        if (userId == null) {
            userId = shopOrder.getBuyerId();
        }
        return userCacheHolder.findByUserId(userId);
    }

    private void updateOrderStatusIfPayFail(TradeRequest r, List<OrderPayment> ids) {
        if (new JsonObject(r.getResult().getRequestOriginalUrl())
                .getString("code", "").equals("ID-ERROR")) {
            // update the order extra
            vertx.executeBlocking(p -> {
                for (OrderPayment op : ids) {
                    Map<String, String> orderExtra = Optional.ofNullable(shopOrderReadService.findById(op.getOrderId()).getResult().getExtra())
                            .orElseGet(HashMap::new);
                    orderExtra.put(ShopOrderExtra.identityError.name(), ShopOrderIdentityErrorEnum.TRUE.getCode());
                    orderWriteService.updateOrderExtra(op.getOrderId(), OrderLevel.SHOP, orderExtra);
                }
            });
        }
    }

    private JsonArray buildOrder(List<? extends OrderRelation> ids) {
        JsonArray jsonObjects = new JsonArray();
        for (OrderRelation id : ids) {
            JsonObject jsonObject = new JsonObject();
            ShopOrder shopOrder = shopOrderReadService.findById(id.getOrderId()).getResult();
            List<SkuOrder> skuOrders = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
            jsonObject.put("fee", shopOrder.getFee())
                    .put("orderId", y800OrderIdGenerator.getDeclareId(shopOrder))
                    .put("is_customs", skuOrders.get(0).getIsBonded())
                    .put("items", buildItem(shopOrder, skuOrders));
            jsonObjects.add(jsonObject);
        }
        return jsonObjects;

    }

    private JsonArray buildItem(ShopOrder shopOrder, List<SkuOrder> skuOrders) {
        JsonArray jsonArray = new JsonArray();
        for (SkuOrder skuOrder : skuOrders) {
            JsonObject jsonObject = new JsonObject();
            jsonObject.put("fee", skuOrder.getFee())
                    .put("name", skuOrder.getItemName())
                    .put("quantity", skuOrder.getQuantity())
                    .put("type", 2)
                    .put("sku_id", skuOrder.getSkuId());
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    @Override
    public TradeResult paymentCallback(HttpServletRequest httpServletRequest) {
        // read all
        Map<String, String[]> param = httpServletRequest.getParameterMap();
        JsonObject jsonObject = (JsonObject) httpServletRequest.getAttribute("requestBody");
        log.info("Pay Callback -> {}, param -> {}", jsonObject, param);
        if (!"TRADE_SUCCESS".equals(jsonObject.getJsonObject("payment", new JsonObject()).getString("state"))) {
            return TradeResult.fail(TradeType.PAYMENT, "umf", jsonObject.toString());
        }
        TradeResult result = new TradeResult();
        result.setChannel("umf");
        try {
            if (jsonObject.containsKey("settle_date")) {
                result.setTradeAt(Date.from(jsonObject.getInstant("settle_date")));
            } else {
                result.setTradeAt(new Date());
            }
        } catch (Exception e) {
            result.setTradeAt(new Date());
            log.error("ERROR to get date", e);
        }
        JsonObject payment = jsonObject.getJsonObject("payment");
        result.setMerchantSerialNo(payment.getJsonObject("order").getString("mer_reference_id"));
        result.setGatewaySerialNo(payment.getString("id"));
        result.setPaySuccess(true);
        return result;
    }

    @Override
    public TradeRequest refundRequest(RefundParams refundParams) {
        //Integer tradeType = tokenShopPayInfo.getShopOrderTradeType(null); //TODO 多支付,需要订单id
        UMFToken umfToken = umfTokenTokenProvider.findToken(refundParams.getSellerNo());
        Long refundId = refundReadService.findByTradeNo(refundParams.getTradeNo())
                .getResult().stream().filter(r -> Objects.equals(r.getOutId(), refundParams.getRefundNo()))
                .map(Refund::getId).findFirst().get();

        List<OrderRefund> o = refundReadService.findOrderIdsByRefundId(refundId).getResult();

        Payment payment = paymentReadService.findByOutId(refundParams.getTradeNo()).getResult();

        JsonObject request = new JsonObject();
        request.put("clientId", umfToken.getClient());
        request.put("secret", umfToken.secret);
        request.put("key", umfToken.getKey());
        request.put("id", payment.getPaySerialNo());
        request.put("refundNo", refundParams.getRefundNo());
        request.put("fee", refundParams.getRefundAmount());
        request.put("orders", buildOrder(o));
        request.put("callback", umfToken.getRefundNotifyUrl() + "/umf/account/" + refundParams.getSellerNo());
        JsonObject res = vertx.eventBus()
                .<JsonObject>request("umf#refund", request, new DeliveryOptions().setLocalOnly(true))
                .toCompletionStage()
                .toCompletableFuture()
                .join()
                .body();
        log.info("UMFPayChannel.refundRequest,refundId={}, umf#refund.result={}", refundId, res);

        TradeRequest result = new TradeRequest();
        JsonObject raw = res.getJsonObject("raw");
        if (raw.getString("state", "REFUND_FAIL").equals("REFUND_FAIL")) {
            result.setError(raw.getJsonObject("meta").getString("ret_msg"));
        }
        result.setResult(new Redirect("umf", false, "", res.toString()));
        return result;
    }

    @Override
    public TradeResult refundCallback(HttpServletRequest httpServletRequest) {
        Map<String, String[]> param = httpServletRequest.getParameterMap();
        JsonObject jsonObject = (JsonObject) httpServletRequest.getAttribute("body");
        log.info("Refund Callback -> {}, param -> {}", jsonObject, param);
        if (!"REFUND_SUCCESS".equals(jsonObject.getJsonObject("payment", new JsonObject())
                .getJsonObject("refund")
                .getString("state"))) {
            return TradeResult.fail(TradeType.PAYMENT, "umf", jsonObject.toString());
        }
        TradeResult result = new TradeResult();
        result.setChannel("umf");
        try {
            result.setTradeAt(Date.from(jsonObject.getInstant("settle_date")));
        } catch (Exception e) {
            result.setTradeAt(new Date());
            log.error("ERROR to get date", e);
        }
        result.setMerchantSerialNo(jsonObject.getJsonObject("order").getString("mer_reference_id"));
        result.setGatewaySerialNo(jsonObject.getString("id"));
        result.setPaySuccess(true);
        return result;
        //  return null;
    }
}
