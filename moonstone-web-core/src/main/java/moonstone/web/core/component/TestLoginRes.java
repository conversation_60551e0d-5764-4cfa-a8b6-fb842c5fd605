package moonstone.web.core.component;

import moonstone.cache.ShopCacheHolder;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.AppResponse;
import moonstone.common.utils.EventSender;
import moonstone.shop.model.Shop;
import moonstone.user.dto.UserUpdateEvent;
import moonstone.web.core.AppConstants;
import org.joda.time.DateTime;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Objects;

//@Profile({"dev", "test","pre"})
@RestController
@RequestMapping("/api/user/test")
public record TestLoginRes(ParanaUserWrapper paranaUserWrapper, ShopCacheHolder shopCacheHolder){


    /**
     * 模拟登录
     *
     * @param request 请求头
     * @param userId  用户Id
     * @param shopId  店铺Id
     * @return 用户实体
     */
    @GetMapping("/login")
    public CommonUser mockLogin(HttpServletRequest request, Long userId, @RequestParam(required = false) Long shopId) {
        CommonUser user = paranaUserWrapper.wrap(userId, shopId);
        Shop shop = shopCacheHolder.findShopByUserId(userId);
        if (shop!=null){
            user.setShopId(shop.getId());
        }
        request.getSession().setAttribute(AppConstants.SESSION_USER_ID, user.getId());
        if (Objects.nonNull(user.getShopId())) {
            request.getSession().setAttribute(AppConstants.SESSION_SHOP_ID, user.getShopId());
        }
        EventSender.publish(new UserUpdateEvent(userId));
        return user;
    }

    @GetMapping("/app/login")
    public AppResponse appLogin(HttpServletRequest request, Long userId) {
        return AppResponse.ok(mockLogin(request, userId, null));
    }

    @GetMapping("/time")
    public Date currentDate() {
        return new Date();
    }

    @GetMapping("/inactive")
    public Date getInactive(HttpServletRequest request) {
        return new DateTime().plus(request.getSession().getMaxInactiveInterval()).toDate();
    }
}