package moonstone.web.core.component.weShop;

import com.alibaba.fastjson.JSON;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopItemCacher;
import moonstone.common.utils.LogUtil;
import moonstone.search.weShopItem.WeShopItemDumpService;
import moonstone.showcase.mq.config.anno.MQEventConsumerMethod;
import moonstone.weShop.model.WeShopItem;
import moonstone.weShop.service.WeShopItemReadService;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.item.ItemUpdateEvent;
import moonstone.web.core.events.weShopItem.WeShopItemUpdatedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.Objects;

@Component
@Slf4j
public class WeShopSkuProfitUpdateListener extends AbstractVerticle {
    @Autowired
    private WeShopItemReadService weShopItemReadService;
    @Autowired
    private WeShopItemCacher weShopItemCacher;
    @Autowired
    private WeShopItemDumpService weShopItemDumpService;

//    @VertxEventBusListener(ItemUpdateEvent.class)
//    @MQEventConsumerMethod(ItemUpdateEvent.class)
//    public void itemUpdate(ItemUpdateEvent itemUpdateEvent) {
//        log.info("ItemUpdateEvent LOG : WeShopSkuProfitUpdateListener {}", itemUpdateEvent.getItemId());
//        updateWeShopSkuProfit(itemUpdateEvent, null);
//    }
//
    // 微分销
//    @VertxEventBusListener(WeShopItemUpdatedEvent.class)
//    public void weShopItemUpdate(Message<Buffer> weShopUpdateMessage) {
//        WeShopItemUpdatedEvent weShopItemUpdatedEvent = JSON.parseObject(weShopUpdateMessage.body().getBytes(), WeShopItemUpdatedEvent.class);
//        updateWeShopSkuProfit(weShopItemUpdatedEvent, weShopUpdateMessage);
//    }
//
//    public void updateWeShopSkuProfit(Object event, Message<?> message) {
//        Long itemId = null;
//        Long weShopItemId = null;
//        if (event instanceof ItemUpdateEvent) {
//            itemId = ((ItemUpdateEvent) event).getItemId();
//        }
//        if (event instanceof WeShopItemUpdatedEvent) {
//            weShopItemId = ((WeShopItemUpdatedEvent) event).getWeShopItemId();
//        }
//        Long itemIdI = itemId;
//        Long weShopItemIdI = weShopItemId;
//        vertx.executeBlocking(p -> {
//            if (itemIdI != null) {
//                for (WeShopItem weShopItem : weShopItemReadService.findByItemId(itemIdI).getResult()) {
//                    updateWeShopSkuProfitAndDumpIt(weShopItem);
//                }
//                p.complete();
//                return;
//            }
//            if (weShopItemIdI != null) {
//                WeShopItem weShopItem = weShopItemCacher.findWeShopItemById(weShopItemIdI);
//                updateWeShopSkuProfitAndDumpIt(weShopItem);
//                p.complete();
//                return;
//            }
//            log.warn("{} unmatched event[{}]", LogUtil.getClassMethodName(), event);
//            p.complete();
//        }, r -> {
//            if (Objects.nonNull(message)) {
//                message.reply(true);
//            }
//        });
//    }
//
//    /**
//     * 更新WeShopItemSku的利润
//     * 同时会清除一部分缓存
//     *
//     * @param weShopItem weShopItem
//     */
//    public void updateWeShopSkuProfitAndDumpIt(@NotNull WeShopItem weShopItem) {
//        if (weShopItem == null) {
//            throw new IllegalArgumentException("weShopItem require not null");
//        }
//        weShopItemCacher.invalidateWeShopItemById(weShopItem.getId());
//        weShopItemCacher.invalidateWeShopItemsByWeShopId(weShopItem.getWeShopId());
//        weShopItemDumpService.dump(weShopItem);
//    }
}
