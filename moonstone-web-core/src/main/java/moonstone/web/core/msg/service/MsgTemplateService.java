package moonstone.web.core.msg.service;

import io.terminus.common.model.Paging;
import moonstone.common.model.Either;
import moonstone.web.core.msg.model.MsgTemplate;

import java.util.List;

/**
 * Msg App Template的简单存储服务
 * 用于存储Template
 */
public interface MsgTemplateService {

    /**
     * 为特定项目设置模板
     *
     * @param templateType 这个模板的类型 往往标记为其用途
     * @param templateId   模板的Id
     * @param projectId    项目的Id
     * @return 模板Id
     */
    Either<String> setTemplate(String templateType, String templateId, Long projectId);

    /**
     * 更新模板的数据
     *
     * @param id         模板Id
     * @param templateId 模板对应的外部Id
     * @return 更新结果
     */
    Either<Boolean> updateTemplate(String id, String templateId);

    /**
     * 删除模板
     *
     * @param id 模板Id
     * @return 更新结果
     */
    Either<Boolean> deleteTemplate(String id);

    /**
     * 根据项目Id 和 类型进行查询
     *
     * @param projectId 项目Id
     * @param type      类型
     * @return 列表
     */
    Either<List<MsgTemplate>> queryListByProjectIdAndType(Long projectId, String type);

    /**
     * 用于修改列表时的分页搜索
     *
     * @param projectId 项目Id 必传
     * @param type      类型
     * @param page      第几页
     * @param limit     页面大小
     * @return 数据列表
     */
    Either<Paging<MsgTemplate>> paging(Long projectId, String type, int page, int limit);
}
