package moonstone.web.core.msg.model;

import java.util.Date;
import java.util.List;
import java.util.Map;

public record CommonMsg(String uuid, Long shopId, String template, Map<String, String> content, Date sentAt,
                        List<String> relatedId,
                        String hash, Integer level) {

    public record CommonMsgTemplate(String uuid, String title, String template, Date createdAt, Integer level) {

    }
}
