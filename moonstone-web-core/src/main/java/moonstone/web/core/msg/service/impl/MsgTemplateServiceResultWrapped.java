package moonstone.web.core.msg.service.impl;

import io.terminus.common.model.Paging;
import lombok.AllArgsConstructor;
import moonstone.common.model.Either;
import moonstone.web.core.msg.model.MsgTemplate;
import moonstone.web.core.msg.service.MsgTemplateService;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;

@Component
@AllArgsConstructor
public class MsgTemplateServiceResultWrapped implements MsgTemplateService {
    MongoTemplate mongoTemplate;

    @Override
    public Either<String> setTemplate(String templateType, String templateId, Long projectId) {
        mongoTemplate.findAllAndRemove(Query.query(Criteria.where("type").is(templateType)).addCriteria(Criteria.where("projectId").is(projectId)), MsgTemplate.class);
        return Either.ok(mongoTemplate.insert(MsgTemplate.builder().type(templateType)
                .templateId(templateId)
                .projectId(projectId)
                .createdAt(new Date())
                .build()).getId());
    }

    @Override
    public Either<Boolean> updateTemplate(String id, String templateId) {
        return Either.ok(mongoTemplate.updateFirst(Query.query(Criteria.where("id").is(id)), Update.update("templateId", templateId), MsgTemplate.class).getModifiedCount() > 0);
    }

    @Override
    public Either<Boolean> deleteTemplate(String id) {
        return Either.ok(mongoTemplate.remove(Query.query(Criteria.where("id").is(id)), MsgTemplate.class).getDeletedCount() > 0);
    }

    @Override
    public Either<List<MsgTemplate>> queryListByProjectIdAndType(Long projectId, String type) {
        return Either.ok(mongoTemplate.find(Query.query(Criteria.where("projectId").is(projectId)).addCriteria(Criteria.where("type").is(type)), MsgTemplate.class));
    }

    @Override
    public Either<Paging<MsgTemplate>> paging(Long projectId, String type, int page, int limit) {
        Query pagingQuery = Query.query(Criteria.where("projectId").is(projectId));
        if (!ObjectUtils.isEmpty(type)) {
            pagingQuery.addCriteria(Criteria.where("type").is(type));
        }
        long count = mongoTemplate.count(pagingQuery, MsgTemplate.class);
        pagingQuery.limit(limit).skip(Math.max(1, page) * limit - limit);
        List<MsgTemplate> fetchResult = mongoTemplate.find(pagingQuery, MsgTemplate.class);
        return Either.ok(new Paging<>(count, fetchResult));
    }
}