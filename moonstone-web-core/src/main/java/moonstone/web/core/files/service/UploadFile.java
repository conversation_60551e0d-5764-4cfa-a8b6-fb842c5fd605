package moonstone.web.core.files.service;

import io.vertx.core.Vertx;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.Message;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.UUID;
import moonstone.web.core.constants.EnvironmentConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Component
@Slf4j
public class UploadFile {
	@Autowired
	Vertx vertx;
	@Autowired
	OSSClientService ossClientService;
	@Autowired
	EnvironmentConfig environmentConfig;

	@PostConstruct
	public void onLoad() {
		vertx.runOnContext(v -> vertx.eventBus().<Buffer>localConsumer("upload-file-by-path").handler(this::uploadByFile));
	}

	public void uploadByFile(Message<Buffer> path) {
		vertx.executeBlocking(p -> {
			try {
				String url = ossClientService.upload(environmentConfig.getEnv(),
						LocalDateTime.now().format(DateTimeFormatter.ofPattern("[yyyy-MM-dd]-HH:mm:ss-"))
								+ UUID.randomUUID()
								+ "订单导出.xls"
						, new File(path.body().toString()));
				int fileAt = url.lastIndexOf("/");
				url = url.substring(0, fileAt) + URLEncoder.encode(url.substring(fileAt), Charset.defaultCharset());
				p.complete(url);
			} catch (Exception e) {
				log.error("Fail to upload the exported file {}", path.body(), e);
				p.fail(e);
			}
		}, false).onSuccess(path::reply)
				.onFailure(path::reply);

	}

}
