package moonstone.web.core.exports.common;

import lombok.Data;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

/**
 * DATE: 16/11/21 下午3:45 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Data
@XmlRootElement(name = "item")
public class ExportTable implements Serializable {
    private static final long serialVersionUID = -3289658533196621989L;

    @XmlAttribute(name = "className")
    private String name;
    @XmlAttribute(name = "display")
    private String display;
    @XmlElement(name = "column", type = ExportColumn.class)
    private List<ExportColumn> columns;
}
