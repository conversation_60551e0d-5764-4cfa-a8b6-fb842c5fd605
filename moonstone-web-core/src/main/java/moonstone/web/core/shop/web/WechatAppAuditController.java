package moonstone.web.core.shop.web;

import blue.sea.moonstone.bridge.app.HttpServletVerticle;
import blue.sea.moonstone.bridge.model.Request;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.mongo.MongoClient;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.component.Mongo;
import moonstone.web.core.shop.events.WechatAuditApplyEvent;
import moonstone.web.core.shop.model.WechatAuditRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;
import java.util.Scanner;

@RestController
@Slf4j
public class WechatAppAuditController extends HttpServletVerticle {
    MongoClient MONGO_CLIENT = null;
    @Autowired
    MongoTemplate mongoTemplate;

    @Override
    public void start() throws Exception {
        serve("monitor.wechat-app.list", this::listAllAudit);
        serve("wechat.app.submit.mock", this::mock);
    }

    private void mock(Request request) {
        EventSender.sendApplicationEvent(GSON().fromJson(request.reader(), WechatAuditApplyEvent.class));
        request.json(APIResp.ok(true));
    }

    private MongoClient mongoClient() {
        // config the mongodb, create client here for no construct command use
        if (MONGO_CLIENT == null) {
            synchronized (this) {
                if (MONGO_CLIENT == null) {
                    MONGO_CLIENT = Mongo.Pool.getMongo(vertx);
                }
            }
        }
        return MONGO_CLIENT;
    }

    private void listAllAudit(Request request) {
        vertx.executeBlocking(promise -> {
                    try {
                        JsonObject query = Optional.of(new Scanner(request.data()))
                                .filter(Scanner::hasNext)
                                .map(Scanner::next)
                                .map(JsonObject::new)
                                .orElseGet(JsonObject::new);
                        if (query.containsKey("projectId")) {
                            request.json(mongoTemplate.find(Query.query(Criteria.where("projectId").is(query.getLong("projectId"))), WechatAuditRecord.class));
                        } else {
                            request.json(mongoTemplate.findAll(WechatAuditRecord.class));
                        }
                        mongoClient().find(WechatAuditRecord.class.getSimpleName(), query)
                                .onComplete(res -> {
                                    if (!res.succeeded()) {
                                        log.error("{} fail to query from mongo[{}, Query => {}]", LogUtil.getClassMethodName()
                                                , WechatAuditRecord.class.getSimpleName()
                                                , query
                                                , res.cause());
                                    } else {
                                        log.debug("{} query[{}, Query => {}] Result => {}", LogUtil.getClassMethodName()
                                                , WechatAuditRecord.class.getSimpleName()
                                                , query
                                                , res.result());
                                    }
                                });
                        promise.complete();
                    }
                    catch (Exception e){
                        promise.fail(e);
                    }
                },
                res -> {
                    if (!res.succeeded()) {
                        request.fail(res.cause());
                    }
                });
    }
}
