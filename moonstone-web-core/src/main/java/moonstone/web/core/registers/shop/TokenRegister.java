package moonstone.web.core.registers.shop;

import com.alibaba.fastjson.JSON;
import com.github.kevinsawicki.http.HttpRequest;
import io.terminus.common.model.Response;
import io.terminus.pay.alipay.app.component.AppAlipayTokenProvider;
import io.terminus.pay.alipay.common.model.token.AppAlipayToken;
import io.terminus.pay.alipay.common.model.token.PcAlipayToken;
import io.terminus.pay.alipay.common.model.token.WapAlipayToken;
import io.terminus.pay.alipay.pc.component.PcAlipayTokenProvider;
import io.terminus.pay.alipay.wap.component.WapAlipayTokenProvider;
import io.terminus.pay.model.Token;
import io.terminus.pay.wechatpay.app.component.AppWechatpayTokenProvider;
import io.terminus.pay.wechatpay.common.model.token.AppWxToken;
import io.terminus.pay.wechatpay.common.model.token.JsapiWxToken;
import io.terminus.pay.wechatpay.common.model.token.QrWxToken;
import io.terminus.pay.wechatpay.jsapi.component.JsapiWechatpayTokenProvider;
import io.terminus.pay.wechatpay.qr.component.QrWechatpayTokenProvider;
import io.vertx.core.buffer.Buffer;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.model.Either;
import moonstone.common.utils.CopyUtil;
import moonstone.common.utils.EncryptHelper;
import moonstone.common.utils.LogUtil;
import moonstone.shop.enums.ShopPayInfoExtraIndexEnum;
import moonstone.shop.enums.ShopPayInfoPayChannelEnum;
import moonstone.shop.enums.ShopPayInfoStatusEnum;
import moonstone.shop.model.PayChannelRelation;
import moonstone.shop.model.Shop;
import moonstone.shop.model.ShopPayInfo;
import moonstone.shop.service.PayChannelRelationService;
import moonstone.shop.service.ShopPayInfoReadService;
import moonstone.shop.service.ShopPayInfoWriteService;
import moonstone.shop.service.ShopReadService;
import moonstone.shopWxa.enums.ShopWxaStatus;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.web.core.PayTokenReloadEvent;
import moonstone.web.core.component.BeanProtectorManager;
import moonstone.web.core.component.pay.allinpay.AllInPayToken;
import moonstone.web.core.component.pay.allinpay.AllInPayTokenProvider;
import moonstone.web.core.component.pay.allinpayyst.AllInPayYSTToken;
import moonstone.web.core.component.pay.allinpayyst.AllInPayYSTTokenProvider;
import moonstone.web.core.component.pay.bhecard.app.EasyPayTokenProvider;
import moonstone.web.core.component.pay.bhecard.domain.EasyPayToken;
import moonstone.web.core.component.pay.umf.UMFToken;
import moonstone.web.core.component.pay.umf.UMFTokenProvider;
import moonstone.web.core.constants.ParanaConfig;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class TokenRegister {

    @Resource
    private ShopPayInfoReadService shopPayInfoReadService;
    @Resource
    private ShopPayInfoWriteService shopPayInfoWriteService;

    @Resource
    private ShopWxaReadService shopWxaReadService;

    @Autowired
    private ParanaConfig paranaConfig;

    @Autowired
    private PcAlipayTokenProvider pcAlipayTokenProvider;
    @Autowired
    private AppAlipayTokenProvider appAlipayTokenProvider;
    @Autowired
    private WapAlipayTokenProvider wapAlipayTokenProvider;

    @Autowired
    private AppWechatpayTokenProvider appWechatpayTokenProvider;
    @Autowired
    private JsapiWechatpayTokenProvider jsapiWechatpayTokenProvider;
    @Autowired
    private QrWechatpayTokenProvider qrWechatpayTokenProvider;
    @Autowired
    private EasyPayTokenProvider easyPayTokenProvider;
    @Autowired
    UMFTokenProvider umfTokenProvider;
    @Resource
    private AllInPayTokenProvider allInPayTokenProvider;
    @Resource
    private AllInPayYSTTokenProvider allInPayYSTTokenProvider;

    @Autowired
    private BeanProtectorManager beanProtectorManager;

    @Resource
    private PayChannelRelationService payChannelRelationService;

    @Resource
    private ShopReadService shopReadService;

    @Value("${pay.notifyUrl}")
    private String notifyUrl;

    @Value("${pay.refundNotifyUrl}")
    private String refundNotifyUrl;

    @Value("${pay.returnUrl}")
    private String returnUrl;

    @Value("${pay.h5ReturnUrl}")
    private String h5ReturnUrl;

    @Value("${pay.certFilePath}")
    private String certFilePath;

    @Value("${wechat.applet.appId}")
    private String wechatAppId;

    @Value("${wechat.applet.secret}")
    private String wechatSecret;

    @Value("${wechat.applet.mchId}")
    private String wechatMchId;

    @Value("${pay.easy-pay.gateway:https://newpay.bhecard.com/api_gateway.do}")
    private String easyPayGateWay;

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    /**
     * 根据特定顺序初始化加载支付Token
     */
    @PostConstruct
    @EventListener({PayTokenReloadEvent.class})
    public void initProvider() {
        initShopPayInfo();//初始化历史数据
        List<ShopPayInfo> shopPayInfoList = buildPayInfoList();
        if (CollectionUtils.isEmpty(shopPayInfoList)) {
            log.error("{} failed to init payInfo token", LogUtil.getClassMethodName());
            return;
        }
        List<ShopPayInfo> alipayInfoList = new ArrayList<>();
        List<ShopPayInfo> wechatpayInfoList = new ArrayList<>();
        List<ShopPayInfo> wechatpayJsapiInfoList = new ArrayList<>();
        List<ShopPayInfo> restPayInfoList = new ArrayList<>();
        for (ShopPayInfo shopPayInfo : shopPayInfoList) {
            var payChannel = ShopPayInfoPayChannelEnum.parse(shopPayInfo.getPayChannel());
            if (payChannel == null) {
                continue;
            }

            switch (payChannel) {
                case WECHATPAY: {
                    wechatpayInfoList.add(shopPayInfo);
                    break;
                }
                case ALIPAY: {
                    alipayInfoList.add(shopPayInfo);
                    break;
                }
                case UMF: {
                    registerUMF(shopPayInfo);
                    break;
                }
                case ALLINPAY: {
                    registerAllInPay(shopPayInfo);
                    break;
                }
                case ALLINPAY_YST: {
                    registerAllInPayYST(shopPayInfo);
                    break;
                }
                default:
                    restPayInfoList.add(shopPayInfo);
            }
        }
        shopPayInfoList.clear();
        shopPayInfoList.addAll(alipayInfoList);
        shopPayInfoList.addAll(wechatpayInfoList);
        // JsAPI支付需要重载之前的wechatPay初始化的支付token
        shopPayInfoList.addAll(wechatpayJsapiInfoList);

        // 支付注册
        for (ShopPayInfo shopPayInfo : shopPayInfoList) {
            regPayTokenFromShopPayInfo(shopPayInfo);
        }
        restPayInfoList.forEach(this::regPayTokenFromShopPayInfo);
    }

    private List<ShopPayInfo> buildPayInfoList() {
        List<PayChannelRelation> relationList = payChannelRelationService.findAll().getResult();
        if (CollectionUtils.isEmpty(relationList)) {
            log.error("{} failed to init payInfo-relation token", LogUtil.getClassMethodName());
            return new ArrayList<>();
        }
        List<Long> idList = relationList.stream().map(PayChannelRelation::getProFileId).collect(Collectors.toList());
        List<ShopPayInfo> shopPayInfoList = shopPayInfoReadService.findByIds(idList).getResult();
        if (CollectionUtils.isEmpty(shopPayInfoList)) {
            log.error("{} failed to init payInfo token", LogUtil.getClassMethodName());
            return new ArrayList<>();
        }
        Map<Long, ShopPayInfo> payInfoMap = shopPayInfoList.stream().collect(Collectors.toMap(ShopPayInfo::getId, Function.identity()));

        List<ShopPayInfo> payInfoList = new ArrayList<>();
        for (PayChannelRelation relation : relationList) {
            ShopPayInfo shopPayInfo = payInfoMap.get(relation.getProFileId());
            if (Objects.isNull(shopPayInfo)) {
                continue;
            }
            ShopPayInfo payInfo = CopyUtil.copy(shopPayInfo, ShopPayInfo.class);
            payInfo.setTradeType(relation.getTradeType());
            payInfoList.add(payInfo);
        }
        return payInfoList;
    }

    /**
     * 初始化历史数据
     */
    private void initShopPayInfo() {
        try {
            //判断是否存在配置关系,如果存在,说明已经初始化过,直接返回
            List<PayChannelRelation> relationList = payChannelRelationService.findAll().getResult();
            if (!CollectionUtils.isEmpty(relationList)) {
                return;
            }
            List<ShopPayInfo> shopPayInfoList = shopPayInfoReadService.findAll().getResult();
            if (!CollectionUtils.isEmpty(shopPayInfoList)) {
                for (ShopPayInfo payInfo : shopPayInfoList) {
                    try {
                        Integer status = payInfo.getStatus();
                        Shop shop = shopReadService.findById(payInfo.getShopId()).getResult();
                        log.info("查询店铺为空shopId={},shop={}", payInfo.getShopId(), JSON.toJSONString(shop));
                        if (Objects.isNull(shop)) {
                            return;
                        }
                        if (!ShopPayInfoStatusEnum.validStatus().contains(status)) {
                            continue;
                        }
                        //把不是启用状态时间,改成启用状态时间
                        payInfo.setStatusTime(0L);
                        payInfo.setStatus(ShopPayInfoStatusEnum.ACTIVE.getCode());
                        shopPayInfoWriteService.update(payInfo);

                        if (Objects.equals(2, shop.getType())) { //2 混合类型
                            buildRelation(payInfo, 0, status);
                            buildRelation(payInfo, 1, status);
                        } else {
                            buildRelation(payInfo, shop.getType(), status);
                        }
                    } catch (DuplicateKeyException ignored) {
                        log.error("重复状态={}", JSON.toJSONString(payInfo));
                    }
                }
            }
        } catch (Exception e) {
            log.error("初始化历史数据异常e=", e);
        }
    }

    private void buildRelation(ShopPayInfo payInfo, Integer tradeType, Integer status) {
        //新增关系
        PayChannelRelation channelRelation = new PayChannelRelation();
        channelRelation.setShopId(payInfo.getShopId());
        channelRelation.setPayChannel(payInfo.getPayChannel());
        channelRelation.setProFileId(payInfo.getId());
        channelRelation.setTradeType(tradeType);
        channelRelation.setRelationType(Objects.equals(ShopPayInfoStatusEnum.GRAY_RELEASE.getCode(), status) ? 1 : 2);//类型: 1 灰度, 2 全量
        channelRelation.setCreatedBy("");
        channelRelation.setUpdatedBy("");
        channelRelation.setStatus(1);
        channelRelation.setVersion(1L);
        payChannelRelationService.saveRelation(channelRelation);
    }

    private void registerAllInPayYST(ShopPayInfo shopPayInfo) {
        try {
            var token = new AllInPayYSTToken();

            var extra = shopPayInfo.getExtra();
            if (CollectionUtils.isEmpty(extra)) {
                extra = new HashMap<>();
            }

            token.setAccountSetNo(extra.get(ShopPayInfoExtraIndexEnum.ACCOUNT_SET_NO.getCode()));
            token.setAppId(shopPayInfo.getAccountNo());
            token.setBizUserId(shopPayInfo.getMchId());
            token.setBizUserPercent(Long.parseLong(extra.get(ShopPayInfoExtraIndexEnum.BIZ_USER_PERCENT.getCode())));

            token.setCertPassword(extra.get(ShopPayInfoExtraIndexEnum.CERT_PASSWORD.getCode()));
            token.setCertPath(extra.get(ShopPayInfoExtraIndexEnum.CERT_PATH.getCode()));
            token.setSecretKey(shopPayInfo.getSecretKey());

            token.setTlCertPath(extra.get(ShopPayInfoExtraIndexEnum.TL_CERT_PATH.getCode()));
            token.setTokenLoadTime(System.currentTimeMillis());
            token.setVspAppId(extra.get(ShopPayInfoExtraIndexEnum.VSP_APP_ID.getCode()));
            token.setVspCusid(extra.get(ShopPayInfoExtraIndexEnum.VSP_CUSID.getCode()));

            token.setVspMerchantid(extra.get(ShopPayInfoExtraIndexEnum.VSP_MERCHANTID.getCode()));
            token.setVspTermid(extra.get(ShopPayInfoExtraIndexEnum.VSP_TERMID.getCode()));
            token.setEnterpriseWithdrawAccount(extra.get(ShopPayInfoExtraIndexEnum.ENTERPRISE_WITHDRAW_ACCOUNT.getCode()));

            token.setNotifyUrl(notifyUrl);
            token.setRefundNotifyUrl(refundNotifyUrl);

            allInPayYSTTokenProvider.register(shopPayInfo.getShopId().toString(), token);
            if (Objects.nonNull(shopPayInfo.getTradeType())) {
                allInPayYSTTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), token);
            }
        } catch (Exception ex) {
            log.error("TokenRegister.registerAllInPayYST error, shopPayInfo={}", JSON.toJSONString(shopPayInfo), ex);
        }
    }

    private void registerAllInPay(ShopPayInfo shopPayInfo) {
        var token = new AllInPayToken();

        token.setAppid(shopPayInfo.getAccountNo());
        token.setCusid(shopPayInfo.getMchId());
        token.setPrivateKey(shopPayInfo.getSecretKey());
        token.setPublicKey(shopPayInfo.getPublicKey());
        token.setNotifyUrl(notifyUrl);

        var extra = shopPayInfo.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        token.setSignType(extra.get(ShopPayInfoExtraIndexEnum.SIGN_TYPE.getCode()));

        allInPayTokenProvider.register(shopPayInfo.getShopId().toString(), token);
        if (Objects.nonNull(shopPayInfo.getTradeType())) {
            allInPayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), token);
        }
    }

    private void registerUMF(ShopPayInfo shopPayInfo) {
        new Thread(() -> {
            HttpRequest request = HttpRequest.get(shopPayInfo.getSecretKey());
            log.info("Shop [{}] receive key from [{}]", shopPayInfo.getShopId(), shopPayInfo.getSecretKey());
            if (!request.ok()) {
                log.error("Fail to load key {}, {}", shopPayInfo.getShopId(), shopPayInfo.getSecretKey());
                return;
            }
            ByteArrayOutputStream b = new ByteArrayOutputStream();
            request.receive(b);
            String based = "";
            try {
                based = b.toString();
                if (based.endsWith("\n")) {
                    based = based.substring(0, based.length() - 1);
                }

                byte[] key = Base64.getDecoder().decode(based.getBytes(StandardCharsets.UTF_8));
                Either<ByteArrayOutputStream> buff = EncryptHelper.instance.decrypt(EncryptHelper.KeyEnu.CommonKey, key);
                UMFToken umfToken = new UMFToken(shopPayInfo.getAccountNo(), shopPayInfo.getMchId(), Buffer.buffer(buff.take().toByteArray()), based);
                umfToken.setNotifyUrl(notifyUrl);
                umfToken.setRefundNotifyUrl(refundNotifyUrl);
                umfToken.setReturnUrl(h5ReturnUrl);
                umfTokenProvider.register(shopPayInfo.getShopId() + "", umfToken);
                if (Objects.nonNull(shopPayInfo.getTradeType())) {
                    umfTokenProvider.register(shopPayInfo.getShopId() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType() + "", umfToken);
                }
            } catch (Exception e) {
                log.error("Fail to decrypt {}", shopPayInfo.getSecretKey(), e);
                UMFToken umfToken = new UMFToken(shopPayInfo.getAccountNo(), shopPayInfo.getMchId(), Buffer.buffer(), based);
                umfToken.setNotifyUrl(notifyUrl);
                umfToken.setRefundNotifyUrl(refundNotifyUrl);
                umfToken.setReturnUrl(h5ReturnUrl);
                umfTokenProvider.register(shopPayInfo.getShopId() + "", umfToken);
                if (Objects.nonNull(shopPayInfo.getTradeType())) {
                    umfTokenProvider.register(shopPayInfo.getShopId() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType() + "", umfToken);
                }
            }
        }).start();

    }

    /**
     * 保护Token不被篡改
     * {@link moonstone.web.core.events.shop.listener.ShopPayInfoChangeListener} 注意Token被保护了
     *
     * @param token 支付token
     * @return 被保护的Token
     */
    private <T extends Token> T protectToken(T token) {
        return beanProtectorManager.newProtector(token);
    }

    /**
     * 注册支付信息
     * <p>  被以下依赖
     * {@link moonstone.web.core.events.shop.listener.ShopWxaUpdateStatusListener}
     * {@link moonstone.web.core.events.shop.listener.ShopPayInfoChangeListener}
     *
     * @param shopPayInfo 支付信息
     */
    public void regPayTokenFromShopPayInfo(ShopPayInfo shopPayInfo) {
        log.info("注册支付信息入参={}", JSON.toJSONString(shopPayInfo));
        if (shopPayInfo == null) {
            return;
        }
        if (shopPayInfo.getPayChannel() == null || !Objects.equals(ShopPayInfoStatusEnum.ACTIVE.getCode(), shopPayInfo.getStatus())) {
            log.error("TokenRegister.regPayTokenFromShopPayInfo, not qualified shopPayInfo={}", JSON.toJSONString(shopPayInfo));
            return;
        }
        var payChannel = ShopPayInfoPayChannelEnum.parse(shopPayInfo.getPayChannel());
        if (payChannel == null) {
            log.error("TokenRegister.regPayTokenFromShopPayInfo, shopPayInfoId={}, unknown payChannel={}",
                    shopPayInfo.getId(), shopPayInfo.getPayChannel());
            return;
        }

        switch (payChannel) {
            case ALIPAY: {
                registerAlipayToken(shopPayInfo);
                break;
            }
            case WECHATPAY: {
                registerAppWechatPayToken(shopPayInfo);
                break;
            }
            case WECHATPAY_JSAPI: {
                JsapiWxToken jsapiWxToken = new JsapiWxToken();
                jsapiWxToken.setMchId(shopPayInfo.getMchId());
                jsapiWxToken.setAppId(shopPayInfo.getAccountNo());
                log.info("商家 {} appId {}", shopPayInfo.getShopId().toString(), shopPayInfo.getAccountNo());
                jsapiWxToken.setPartnerKey(shopPayInfo.getSecretKey());
                jsapiWxToken.setNotifyUrl(notifyUrl);
                jsapiWxToken.setRefundNotifyUrl(refundNotifyUrl);
                jsapiWxToken.setReturnUrl(h5ReturnUrl);
                jsapiWxToken.setCertFilePath(certFilePath + "/" + shopPayInfo.getShopId() + "/apiclient_cert.p12");
                jsapiWechatpayTokenProvider.register(shopPayInfo.getShopId().toString(), protectToken(jsapiWxToken));
                if (Objects.nonNull(shopPayInfo.getTradeType())) {
                    jsapiWechatpayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), protectToken(jsapiWxToken));
                }
                break;
            }
            case HBECARD: {
                EasyPayToken easyPayToken = new EasyPayToken();
                easyPayToken.setMerchantId(shopPayInfo.getMchId());
                easyPayToken.setPartnerId(shopPayInfo.getAccountNo());
                easyPayToken.setSecretKey(shopPayInfo.getSecretKey());
                easyPayToken.setGateway(easyPayGateWay);
                easyPayToken.setNotifyUrl(notifyUrl);
                easyPayTokenProvider.register(shopPayInfo.getShopId().toString(), easyPayToken);
                if (Objects.nonNull(shopPayInfo.getTradeType())) {
                    easyPayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), easyPayToken);
                }
                break;
            }
            case UMF: {
                registerUMF(shopPayInfo);
                break;
            }
            case ALLINPAY: {
                registerAllInPay(shopPayInfo);
                break;
            }
            case ALLINPAY_YST: {
                registerAllInPayYST(shopPayInfo);
                break;
            }
            default: {
                log.warn("{} not compatible shopPay:{}", LogUtil.getClassMethodName(), shopPayInfo);
            }
        }
    }

    private void registerAppWechatPayToken(ShopPayInfo shopPayInfo) {
                    /*
                      APP方式注册
                     */
        AppWxToken appWxToken = new AppWxToken();
        appWxToken.setMchId(shopPayInfo.getMchId());
        appWxToken.setAppId(shopPayInfo.getAccountNo());
        appWxToken.setPartnerKey(shopPayInfo.getSecretKey());
        appWxToken.setNotifyUrl(notifyUrl);
        appWxToken.setRefundNotifyUrl(refundNotifyUrl);
        appWxToken.setReturnUrl(returnUrl);
        appWxToken.setCertFilePath(certFilePath + "/" + shopPayInfo.getShopId() + "/apiclient_cert.p12");
        appWechatpayTokenProvider.register(shopPayInfo.getShopId().toString(), protectToken(appWxToken));
        if (Objects.nonNull(shopPayInfo.getTradeType())) {
            appWechatpayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), protectToken(appWxToken));
        }
                    /*
                      JsApi方式注册
                     */
        Response<ShopWxa> rShopWxa = shopWxaReadService.findByShopIdAndStatus(shopPayInfo.getShopId(), ShopWxaStatus.RELEASED.getValue()); //TODO 微信支付,多支付
        ShopWxa shopWxa = null;
        if (rShopWxa.isSuccess()) {
            shopWxa = rShopWxa.getResult();
        } else {
            log.error("fail to find shopWxa by shopId={}, error code: {}", shopPayInfo.getShopId(), rShopWxa.getResult());
        }
        JsapiWxToken jsapiWxToken = new JsapiWxToken();
        jsapiWxToken.setMchId(shopPayInfo.getMchId());
        if (shopWxa == null) {
            log.info("{} confused token: shopPayInfo{} defaultAppId:{} defaultSecretId:{} defaultMchId:{}", LogUtil.getClassMethodName(), shopPayInfo, wechatAppId, wechatSecret, wechatMchId);
            if (Objects.equals(shopPayInfo.getAccountNo(), wechatAppId)) {
                log.info("{} replace (shopId:{}) JsApi from default to JsApi Default", LogUtil.getClassMethodName(), shopPayInfo.getShopId());
                jsapiWxToken.setAppId(paranaConfig.getParanaWxaAppId());
                jsapiWxToken.setSecret(paranaConfig.getParanaWxaAppSecret());
            } else {
                jsapiWxToken.setAppId(shopPayInfo.getAccountNo());
            }
        } else {
            jsapiWxToken.setAppId(shopWxa.getAppId());
            jsapiWxToken.setSecret(shopWxa.getAppSecret());
        }
        jsapiWxToken.setPartnerKey(shopPayInfo.getSecretKey());
        jsapiWxToken.setNotifyUrl(notifyUrl);
        jsapiWxToken.setRefundNotifyUrl(refundNotifyUrl);
        jsapiWxToken.setReturnUrl(h5ReturnUrl);
        jsapiWxToken.setCertFilePath(certFilePath + "/" + shopPayInfo.getShopId() + "/apiclient_cert.p12");
        jsapiWechatpayTokenProvider.register(shopPayInfo.getShopId().toString(), protectToken(jsapiWxToken));
        if (Objects.nonNull(shopPayInfo.getTradeType())) {
            jsapiWechatpayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), protectToken(jsapiWxToken));
        }
                    /*
                     Qr方式注册
                     */
        QrWxToken qrWxToken = new QrWxToken();
        qrWxToken.setMchId(shopPayInfo.getMchId());
        qrWxToken.setAppId(shopPayInfo.getAccountNo());
        qrWxToken.setPartnerKey(shopPayInfo.getSecretKey());
        qrWxToken.setNotifyUrl(notifyUrl);
        qrWxToken.setRefundNotifyUrl(refundNotifyUrl);
        qrWxToken.setReturnUrl(returnUrl);
        qrWxToken.setCertFilePath(certFilePath + "/" + shopPayInfo.getShopId() + "/apiclient_cert.p12");

        log.info("[registerWXA](token) show token:{}", JSON.toJSONString(jsapiWxToken));
        qrWechatpayTokenProvider.register(shopPayInfo.getShopId().toString(), protectToken(qrWxToken));
        if (Objects.nonNull(shopPayInfo.getTradeType())) {
            qrWechatpayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), protectToken(qrWxToken));
        }
    }

    private void registerAlipayToken(ShopPayInfo shopPayInfo) {
        log.info("{} shopPayInfo:{}", LogUtil.getClassMethodName("UNCOMPILABLE_REG"), shopPayInfo.toString());
                    /*
                      PC方式注册
                     */
        PcAlipayToken pcAlipayToken = new PcAlipayToken();
        //商户号：MchId/PID
        pcAlipayToken.setPid(shopPayInfo.getMchId());
        //公共账户：account/appId
        pcAlipayToken.setAccount(shopPayInfo.getAccountNo());
        //秘钥
        pcAlipayToken.setKey(shopPayInfo.getSecretKey());
        //异步回调url
        pcAlipayToken.setNotifyUrl(notifyUrl);
        //退款异步回调url
        pcAlipayToken.setRefundNotifyUrl(refundNotifyUrl);
        //回调url页面
        pcAlipayToken.setReturnUrl(returnUrl);
        pcAlipayTokenProvider.register(shopPayInfo.getShopId().toString(), protectToken(pcAlipayToken));
        if (Objects.nonNull(shopPayInfo.getTradeType())) {
            pcAlipayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), protectToken(pcAlipayToken));
        }
                    /*
                       APP方式注册
                     */
        AppAlipayToken appAlipayToken = new AppAlipayToken();
        appAlipayToken.setPid(shopPayInfo.getMchId());
        appAlipayToken.setAccount(shopPayInfo.getAccountNo());
        appAlipayToken.setKey(shopPayInfo.getSecretKey());
        appAlipayToken.setNotifyUrl(notifyUrl);
        appAlipayToken.setRefundNotifyUrl(refundNotifyUrl);
        appAlipayToken.setReturnUrl(returnUrl);
        appAlipayTokenProvider.register(shopPayInfo.getShopId().toString(), protectToken(appAlipayToken));
        if (Objects.nonNull(shopPayInfo.getTradeType())) {
            appAlipayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), protectToken(appAlipayToken));
        }
                    /*
                      wap方式注册
                     */
        WapAlipayToken wapAlipayToken = new WapAlipayToken();
        wapAlipayToken.setPid(shopPayInfo.getMchId());
        wapAlipayToken.setAccount(shopPayInfo.getAccountNo());
        wapAlipayToken.setKey(shopPayInfo.getSecretKey());
        wapAlipayToken.setNotifyUrl(notifyUrl);
        wapAlipayToken.setRefundNotifyUrl(refundNotifyUrl);
        wapAlipayToken.setReturnUrl(h5ReturnUrl);
        wapAlipayTokenProvider.register(shopPayInfo.getShopId().toString(), protectToken(wapAlipayToken));
        if (Objects.nonNull(shopPayInfo.getTradeType())) {
            wapAlipayTokenProvider.register(shopPayInfo.getShopId().toString() + shopPayInfo.getPayChannel() + shopPayInfo.getTradeType(), protectToken(wapAlipayToken));
        }

    }
}
