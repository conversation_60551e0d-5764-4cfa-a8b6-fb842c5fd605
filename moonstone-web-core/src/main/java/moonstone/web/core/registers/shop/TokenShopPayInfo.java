package moonstone.web.core.registers.shop;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.CopyUtil;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.StringUtils;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.enums.ShopPayInfoExtraIndexEnum;
import moonstone.shop.model.PayChannelRelation;
import moonstone.shop.model.ShopPayInfo;
import moonstone.shop.service.PayChannelRelationService;
import moonstone.shop.service.ShopPayInfoReadService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TokenShopPayInfo {

    @Resource
    private PayChannelRelationService payChannelRelationService;
    @Resource
    private ShopPayInfoReadService shopPayInfoReadService;
    @Resource
    private SkuOrderReadService skuOrderReadService;

    /**
     * 查询订单贸易类型
     *
     * @param orderId
     * @return
     */
    public Integer getShopOrderTradeType(Long orderId) {
        if (Objects.isNull(orderId)) {
            throw new RuntimeException("订单ID不能为空");
        }
        var result = skuOrderReadService.findByShopOrderId(orderId).getResult();
        if (CollectionUtils.isEmpty(result)) {
            throw new RuntimeException("订单不存在");
        }
        //0 大贸, 1 保税
        Integer isBonded = result.get(0).getIsBonded();
        if (Objects.equals(2, isBonded)) {
            throw new RuntimeException("贸易类型错误");
        }
        return isBonded;
    }

    /**
     * 根据订单查询支付信息
     *
     * @param orderId
     * @return
     */
    public List<ShopPayInfo> listShopPayInfo(Long orderId) {
        if (Objects.isNull(orderId)) {
            throw new RuntimeException("订单ID不能为空");
        }

        var result = skuOrderReadService.findByShopOrderId(orderId).getResult();
        if (CollectionUtils.isEmpty(result)) {
            throw new RuntimeException("订单不存在");
        }
        SkuOrder skuOrder = result.get(0);
        //0 大贸, 1 保税
        Integer isBonded = skuOrder.getIsBonded();
        if (Objects.equals(2, isBonded)) {
            throw new RuntimeException("贸易类型错误");
        }

        List<PayChannelRelation> relationList = payChannelRelationService.findByShopId(skuOrder.getShopId()).getResult();
        if (CollectionUtils.isEmpty(relationList)) {
            throw new RuntimeException("支付配置不存在");
        }
        List<Long> idList = relationList.stream().map(PayChannelRelation::getProFileId).collect(Collectors.toList());
        List<ShopPayInfo> shopPayInfoList = shopPayInfoReadService.findByIds(idList).getResult();
        if (CollectionUtils.isEmpty(shopPayInfoList)) {
            throw new RuntimeException("支付信息不存在");
        }
        Map<Long, ShopPayInfo> payInfoMap = shopPayInfoList.stream().collect(Collectors.toMap(ShopPayInfo::getId, Function.identity()));
        log.info("当前查询到的支付方式 {}", JSONObject.toJSONString(payInfoMap));

        List<ShopPayInfo> payInfoList = new ArrayList<>();
        for (PayChannelRelation relation : relationList) {
            if (!Objects.equals(isBonded, relation.getTradeType())) {
                continue;
            }
            ShopPayInfo shopPayInfo = payInfoMap.get(relation.getProFileId());
            if (Objects.isNull(shopPayInfo)) {
                continue;
            }
            ShopPayInfo payInfo = CopyUtil.copy(shopPayInfo, ShopPayInfo.class);
            payInfo.setTradeType(relation.getTradeType());
            payInfo.setRelationType(relation.getRelationType());
            payInfoList.add(payInfo);
        }
        return payInfoList;
    }




}
