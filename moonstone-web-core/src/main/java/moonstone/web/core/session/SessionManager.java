package moonstone.web.core.session;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public interface SessionManager {

    /**
     * 读取Session
     *
     * @param request httpRequest
     * @return Session
     */
    HttpSession getSession(HttpServletRequest request);

    /**
     * get session
     *
     * @param sessionId sessionId
     * @return HttpSession
     */
    HttpSession getSession(String sessionId);

    /**
     * get session
     *
     * @param request  request
     * @param response response
     * @return http session
     */
    HttpSession getSession(HttpServletRequest request, HttpServletResponse response);

    /**
     * add cookie
     *
     * @param requestWithSession session
     */
    void addCookie(HeraldRequestWithSession requestWithSession);

    /**
     * add cookie
     *
     * @param response  response
     * @param custom    decorate cookie
     * @param sessionId sessionId
     */
    void addCookie(HttpServletResponse response, Consumer<Cookie> custom, String sessionId);

    /**
     * create session
     *
     * @param request  request
     * @param response response
     * @return create session
     */
    HeraldRequestWithSession createSession(HttpServletRequest request, HttpServletResponse response);

    /**
     * 获取一个新的SessionId
     *
     * @return 获取一个新SessionId
     */
    String generateSessionId();

    /**
     * get Session Cookie
     *
     * @param cookies cookie
     * @return sessionId
     */
    String getSessionCookie(Cookie[] cookies);

    /**
     * 验证cookie是否有效
     *
     * @param sessionId cookie
     * @return 有效
     */
    boolean validate(String sessionId);
}
