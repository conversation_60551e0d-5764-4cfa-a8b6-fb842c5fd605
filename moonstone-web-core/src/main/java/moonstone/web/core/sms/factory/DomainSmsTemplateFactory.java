package moonstone.web.core.sms.factory;

import moonstone.common.utils.XML;
import moonstone.web.core.sms.DomainSmsTemplate;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public record DomainSmsTemplateFactory() implements FactoryBean<DomainSmsTemplate> {

    @Override
    public DomainSmsTemplate getObject() throws Exception {
        var inputStream = Optional.ofNullable(getClass()
                .getResourceAsStream("/sms-template.xml")).orElse(getClass()
                .getResourceAsStream("sms-template.xml"));
        if (inputStream == null) {
            throw new RuntimeException("Please check the sms-template exists");
        }
        return XML.parseXML(inputStream).toJava(DomainSmsTemplate.class);
    }

    @Override
    public Class<?> getObjectType() {
        return DomainSmsTemplate.class;
    }
}
