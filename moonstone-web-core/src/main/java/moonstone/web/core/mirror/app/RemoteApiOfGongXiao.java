package moonstone.web.core.mirror.app;

import moonstone.common.model.Either;
import moonstone.web.core.mirror.model.ItemDetail;
import moonstone.web.core.mirror.model.dto.GxShop;

import java.util.List;

public interface RemoteApiOfGongXiao {
    Either<List<ItemDetail>> querySku();

    Either<String> choose(String userId, String skuCode, String weShopId);

    /**
     * 注册用户信息
     *
     * @param userId 用户Id
     * @return 注册结果
     */
    Either<Boolean> register(String userId, String mobile);

    Either<Long> registerShop(String userId, GxShop gxShop);

    Either<Boolean> refundOrder(String orderId);

    /**
     * 获取用户是否注册过店铺
     *
     * @param userId 用户Id
     * @return 店铺
     */
    Either<GxShop> queryGxShop(String userId);

    Either<Boolean> updateShopName(String outShopCode, String name);
}
