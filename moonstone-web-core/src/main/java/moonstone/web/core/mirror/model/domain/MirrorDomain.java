package moonstone.web.core.mirror.model.domain;

import lombok.AllArgsConstructor;
import moonstone.common.model.Either;
import moonstone.web.core.mirror.action.DefaultDomainAction;
import moonstone.web.core.mirror.model.api.DomainAction;
import moonstone.web.core.mirror.model.api.DomainLifeCycle;
import moonstone.web.core.mirror.model.api.DomainStatus;
import moonstone.web.core.mirror.model.api.LifeAction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Domain 侵入注入 DSL 设计
 */
public interface MirrorDomain {

    Logger log = LoggerFactory.getLogger(MirrorDomain.class);

    Map<Class<?>, Future<Map<String, MethodWithParameter>>> CLASS_METHOD_MAP = new ConcurrentHashMap<>();

    @AllArgsConstructor
    class MethodWithParameter {
        private final Method method;
        private final Type type;
    }

    default long timeout() {
        return 15;
    }

    /**
     * 读取action对应的方法, 其需要一个反序列化的函数, 当完成反序列化后 自动调用方法, 并且返回
     * Spring的一个具体化反射抽象实现
     */
    default Function<Function<Type, Object>, ?> chain(String action) {
        if (!CLASS_METHOD_MAP.containsKey(this.getClass())) {
            CLASS_METHOD_MAP.putIfAbsent(this.getClass(), CompletableFuture.supplyAsync(() -> {
                        Map<String, MethodWithParameter> actionNameMap = new HashMap<>();
                        DomainLifeCycle domainLifeCycle = this.getClass().getAnnotation(DomainLifeCycle.class);
                        if (domainLifeCycle == null) {
                            return Collections.emptyMap();
                        }
                        Set<Class<?>> skipReturn = Arrays.stream(domainLifeCycle.skipReturn()).collect(Collectors.toSet());
                        Set<String> exception = Arrays.stream(domainLifeCycle.methodExceptForRule()).collect(Collectors.toSet());
                        for (Method method : this.getClass().getMethods()) {
                            LifeAction lifeAction = method.getAnnotation(LifeAction.class);
                            if (lifeAction == null) {
                                if (!domainLifeCycle.autoDiscovery()) {
                                    continue;
                                }
                                boolean skipRule = exception.contains(method.getName());
                                if (!skipRule && method.getReturnType() == Void.TYPE && domainLifeCycle.skipVoid()) {
                                    continue;
                                }
                                if (!skipRule && skipReturn.contains(method.getReturnType())) {
                                    continue;
                                }
                            } else {
                                if (lifeAction.ignore()) {
                                    continue;
                                }
                            }
                            if (method.getParameterCount() > 1) {
                                log.error("[chain#LifeActionDiscovery] Method[{}] @ Class[{}] is skip because of it require Parameter[count => {}] than 1",
                                        method.getName(), this.getClass().getName(), method.getParameterCount());
                                continue;
                            }
                            Type methodType = method.getParameterCount() == 0 ? null : method.getGenericParameterTypes()[0];
                            actionNameMap.put(Optional.ofNullable(lifeAction).map(LifeAction::value).filter(name -> !name.isEmpty()).orElseGet(method::getName), new MethodWithParameter(method, methodType));
                        }
                        return actionNameMap;
                    }
                    )
            );
        }
        try {
            MethodWithParameter method = CLASS_METHOD_MAP.get(this.getClass()).get(timeout(), TimeUnit.SECONDS).get(action);
            return parameterConvertor -> {
                try {
                    if (method.type != null) {
                        return method.method.invoke(this, parameterConvertor.apply(method.type));
                    }
                    return method.method.invoke(this);
                } catch (Exception e) {
                    if (e instanceof RuntimeException) {
                        if (method.method.getReturnType().equals(Either.class)) {
                            return Either.error(e);
                        }
                        throw (RuntimeException) e;
                    }
                    if (e.getCause() instanceof RuntimeException) {
                        if (method.method.getReturnType().equals(Either.class)) {
                            return Either.error(e.getCause());
                        }
                        throw (RuntimeException) e.getCause();
                    }
                    if (method.method.getReturnType().equals(Either.class)) {
                        return Either.error(e.getCause());
                    }
                    throw new RuntimeException(e.getMessage(), e.getCause());
                }
            };
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }
            throw new RuntimeException(e.getMessage(), e.getCause());
        }
    }

    /**
     * 读取domain现在处于的状态
     * 不一定会被使用, 仅仅用于空载
     * 只有在lifeAction中指定后才有用
     *
     * @return 状态
     */
    DomainStatus getStatus();

    /**
     * @return 领域业务来源
     */
    String getSource();

    /**
     * 设置业务来源
     *
     * @param source 业务来源
     */
    void setSource(String source);

    /**
     * 动作转移模式, 将action name转移到目标动作上, 实际上可以直接制定类型
     *
     * @see DomainAction
     */
    default Function<String, DomainAction> parseAction() {
        return DefaultDomainAction::valueOf;
    }
}
