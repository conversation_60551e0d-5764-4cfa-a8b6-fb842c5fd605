package moonstone.web.core.mirror.util;

import moonstone.common.api.remote.TypeConvertor;
import moonstone.web.core.component.OmsUserDetailQueryComponent;
import moonstone.web.core.mirror.model.UserDetail;

public class OmsUserDetailConvertor implements TypeConvertor<OmsUserDetailQueryComponent.OmsUserDetail, UserDetail> {
    @Override
    public UserDetail convert(OmsUserDetailQueryComponent.OmsUserDetail args) {
        UserDetail userDetail = new UserDetail();
        userDetail.setMobile(args.getMobile());
        userDetail.setUserId(args.getUserId().toString());
        userDetail.setSeller(null);
        userDetail.setWeShopSeller(true);
        userDetail.setName(args.getUserName());
        return userDetail;
    }
}
