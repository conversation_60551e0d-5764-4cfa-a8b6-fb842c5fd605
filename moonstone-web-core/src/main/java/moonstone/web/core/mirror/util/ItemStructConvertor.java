package moonstone.web.core.mirror.util;

import com.google.common.collect.ImmutableMap;
import moonstone.web.core.mirror.model.ItemDetail;
import moonstone.web.core.mirror.model.dto.item.gongxiao.ItemVo;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;

import static moonstone.web.core.mirror.model.dto.item.gongxiao.ItemVo.ItemStatus;
import static moonstone.web.core.mirror.model.dto.item.gongxiao.ItemVo.ItemStatus.*;

public interface ItemStructConvertor {
    static ItemDetail convert(ItemVo itemVo) {
        ItemDetail itemDetail = new ItemDetail();
        Optional.ofNullable(itemVo.getOrigin())
                .map(ItemVo.OriginVo::getOrigin)
                .ifPresent(itemDetail::setCountry);
        itemDetail.setName(itemVo.getName());
        Optional.ofNullable(itemVo.getId())
                .map(Objects::toString)
                .ifPresent(itemDetail::setCode);
        Optional.ofNullable(itemVo.getStockQuantity())
                .map(Long::intValue)
                .ifPresent(itemDetail::setQuantity);
        Optional.ofNullable(itemVo.getSaleQuantity())
                .map(Long::intValue)
                .ifPresent(itemDetail::setSaleQuantity);
        itemDetail.setMainImageUrl(itemVo.getMainPic());
        itemDetail.setCategoriesList(itemVo.getBackCategoryRelation());
        itemDetail.setSideImageUrlList(itemVo.getSubPics());
        itemDetail.setDescribeImageList(itemVo.getDetail());
        itemDetail.setBriefIntro(itemVo.getAdvertise());
        if (itemVo.getIsBonded() == 1L) {
            itemDetail.setBonded(true);
        }
        itemDetail.setSkuDetailList(new ArrayList<>(4));
        if (!CollectionUtils.isEmpty(itemVo.getSkusVO())) {
            for (ItemVo.SkuVo skuVo : itemVo.getSkusVO()) {
                ItemDetail.SkuDetail skuDetail = new ItemDetail.SkuDetail();
                BeanUtils.copyProperties(skuVo, skuDetail);
                skuDetail.setBatch(skuVo.getValidityPeriodStr());
                // 使用采购价 因为 将扣减采购价格作为利润基础
                skuDetail.setPrice(skuVo.getOriginPrice().intValue());
                skuDetail.setOriginPrice(skuVo.getOriginPrice().intValue());
                // 这是推荐的价格
                skuDetail.setSuggestPrice(Optional.ofNullable(skuVo.getPrice())
                        .map(Long::intValue).orElse(0));
                skuDetail.setQuantity(skuVo.getStockQuantity());
                skuDetail.setSkuCode(skuVo.getSkuId().toString());
                skuDetail.setLogicWareCode(skuVo.getLogicWareCode());
                skuDetail.setRealWareCode(skuVo.getRealWareCode());
                skuDetail.setOnSale(Objects.equals(1L, skuVo.getSales()));
                itemDetail.getSkuDetailList().add(skuDetail);
            }
        }
        if (itemVo.getStatus() != null) {
            itemDetail.setStatus(ImmutableMap.of(
                    DELETE, -3,
                    OFF_SELL, -1,
                    ON_SELL, 1
            ).get(ItemStatus.from(itemVo.getStatus().intValue())));
        }
        return itemDetail;
    }
}