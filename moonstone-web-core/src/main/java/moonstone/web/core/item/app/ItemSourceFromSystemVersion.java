package moonstone.web.core.item.app;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.event.ForceNewVersionByShopIdNotifyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class ItemSourceFromSystemVersion {

    Map<Long, Boolean> itemSourceVersion = new ConcurrentHashMap<>();

    @EventListener
    public void registerVersion(ForceNewVersionByShopIdNotifyEvent forceNewVersionByShopIdNotifyEvent) {
        itemSourceVersion.put(forceNewVersionByShopIdNotifyEvent.shopId(), forceNewVersionByShopIdNotifyEvent.force());
    }


    /**
     * check if it's from yang800
     *
     * @param shopId shopId
     * @return version
     */
    public boolean isYang800(Long shopId) {
        if (shopId == null) {
            return false;
        }
        return !itemSourceVersion.getOrDefault(shopId, true);
    }
}
