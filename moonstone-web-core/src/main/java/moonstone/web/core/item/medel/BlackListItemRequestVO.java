package moonstone.web.core.item.medel;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class BlackListItemRequestVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 4996460122069623584L;

    /**
     * itemId
     */
    private Long itemId;

    /**
     * item名称
     */
    private String itemName;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * true=查询黑名单， false=查询非黑名单
     */
    private Boolean isBlacklist = true;
}
