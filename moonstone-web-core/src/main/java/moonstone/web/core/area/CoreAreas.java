package moonstone.web.core.area;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import moonstone.order.model.ReceiverInfo;
import moonstone.order.service.ReceiverInfoReadService;
import moonstone.order.service.ReceiverInfoWriteService;
import moonstone.user.area.model.Area;
import moonstone.user.area.service.AreaReadService;
import moonstone.user.model.UserProfile;
import moonstone.user.service.UserProfileReadService;
import moonstone.user.service.UserProfileWriteService;
import moonstone.web.core.events.user.UserProfileUpdateEvent;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/api/area")
public class CoreAreas {
    @RpcConsumer
    private AreaReadService areaReadService;

    @RpcConsumer
    private ReceiverInfoReadService receiverInfoReadService;

    @RpcConsumer
    private ReceiverInfoWriteService receiverInfoWriteService;

    @RpcConsumer
    private UserProfileReadService userProfileReadService;

    @RpcConsumer
    private UserProfileWriteService userProfileWriteService;

    @RequestMapping(value = "/receiverInfo/convertAreaId", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean convertReceiverInfoAreaId(){
        try{
            Long max = receiverInfoReadService.maxId().getResult();
            for (Long i=0L;i<max;i++){
                ReceiverInfo receiverInfo = receiverInfoReadService.findById(i+1).getResult();
                if (receiverInfo != null){
                    String province = receiverInfo.getProvince();
                    Area ap = areaReadService.findLikeName(province).getResult();
                    if (ap != null){
                        receiverInfo.setProvinceId(ap.getId().intValue());
                    }else{
                        receiverInfo.setProvinceId(-1);
                    }
                    String city = receiverInfo.getCity();
                    Area ac = areaReadService.findLikeName(city).getResult();
                    if (ac != null){
                        receiverInfo.setCityId(ac.getId().intValue());
                    }else{
                        receiverInfo.setCityId(-1);
                    }
                    String region = receiverInfo.getRegion();
                    Area ar = areaReadService.findLikeName(region).getResult();
                    if (ar != null){
                        receiverInfo.setRegionId(ar.getId().intValue());
                    }else{
                        receiverInfo.setRegionId(-1);
                    }
                    receiverInfoWriteService.updateAreaId(receiverInfo).getResult();
                }
            }
        } catch (Exception e){
            log.warn("fail to convert AddressId to AreaId in ReceiverInfo, cause:{}", e.getMessage());
            throw new JsonResponseException(e.getMessage());
        }
        return true;
    }
    @RequestMapping(value = "/userProfile/convertAreaId", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean convertUserProfileAreaId(){
        try{
            Long max = userProfileReadService.maxId().getResult();
            for (Long i=0L;i<max;i++){
                UserProfile userProfile = userProfileReadService.findById(i+1).getResult();
                if (userProfile != null){
                    String province = userProfile.getProvince();
                    Area ap = areaReadService.findLikeName(province).getResult();
                    if (ap != null){
                        userProfile.setProvinceId(ap.getId().intValue());
                    }else{
                        userProfile.setProvinceId(-1);
                    }
                    String city = userProfile.getCity();
                    Area ac = areaReadService.findLikeName(city).getResult();
                    if (ac != null){
                        userProfile.setCityId(ac.getId().intValue());
                    }else{
                        userProfile.setCityId(-1);
                    }
                    String region = userProfile.getRegion();
                    Area ar = areaReadService.findLikeName(region).getResult();
                    if (ar != null){
                        userProfile.setRegionId(ar.getId().intValue());
                    }else{
                        userProfile.setRegionId(-1);
                    }
                    userProfileWriteService.updateProfileAreaId(userProfile).getResult();
                    EventSender.publish(new UserProfileUpdateEvent(userProfile.getUserId()));
                }
            }
        } catch (Exception e){
            log.warn("fail to convert AddressId to AreaId in UserProfile, cause:{}", e.getMessage());
            throw new JsonResponseException(e.getMessage());
        }
        return true;
    }
}
