/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.impl.dao;

import moonstone.BaseDaoTest;
import moonstone.category.model.CategoryBinding;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.hamcrest.Matchers.*;
import static org.hamcrest.core.Is.is;
import static org.junit.Assert.assertThat;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-16
 */
public class CategoryBindingDaoTest extends BaseDaoTest{

    @Autowired
    private CategoryBindingDao categoryBindingDao;

    @Test
    public void testFindByBackCategoryId() throws Exception {
        List<Long> actual = categoryBindingDao.findByBackCategoryId(2L);
        assertThat(actual.size(), is(2));

        assertThat(actual,contains(1L,3L));
    }

    @Test
    public void testFindByFrontCategoryId() throws Exception {
        List<Long> actual = categoryBindingDao.findByFrontCategoryId(3L);
        assertThat(actual.size(),is(1));
        assertThat(actual.get(0),is(2L));
    }

    @Test
    public void testFindByFrontBackCategoryId() throws Exception {
        CategoryBinding actual = categoryBindingDao.findByFrontBackCategoryId(1L,2L);
        assertThat(actual, notNullValue());

        assertThat(categoryBindingDao.findByFrontBackCategoryId(1L,-1L),nullValue());
    }

    @Test
    public void testDeleteByFrontCategoryId() throws Exception {
        final long frontCategoryId = 3L;
        List<Long> actual = categoryBindingDao.findByFrontCategoryId(frontCategoryId);
        assertThat(actual.size(),is(1));

        categoryBindingDao.deleteByFrontCategoryId(frontCategoryId);

        List<Long> again = categoryBindingDao.findByFrontCategoryId(frontCategoryId);
        assertThat(again.size(),is(0));
    }

    @Test
    public void testDeleteByBackCategoryId() throws Exception {
        final long backCategoryId = 2L;
        List<Long> actual = categoryBindingDao.findByBackCategoryId(backCategoryId);
        assertThat(actual.size(),is(2));

        categoryBindingDao.deleteByBackCategoryId(backCategoryId);

        List<Long> again = categoryBindingDao.findByBackCategoryId(backCategoryId);
        assertThat(again.size(),is(0));
    }
}