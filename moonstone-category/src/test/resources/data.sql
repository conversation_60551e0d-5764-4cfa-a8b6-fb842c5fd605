INSERT INTO `parana_back_categories`
(`pid`,`name`,`level`,`status`,`has_children`,`has_spu`,`outer_id`,`created_at`,`updated_at`)
VALUES
  (0, 'back-level1-cat1',1, 1, FALSE ,FALSE ,null,now(),now()),
  (0, 'back-level1-cat2',1, 1, FALSE ,FALSE ,null,now(),now()),
  (0, 'back-level1-cat3',1, 1, TRUE ,FALSE ,null,now(),now()),
  (3, 'back-level2-cat31',2,1,FALSE ,FALSE ,null, now(),now());

INSERT INTO `parana_front_categories`
(`pid`,`name`,`level`,`has_children`,`created_at`,`updated_at`)
VALUES
  (0, 'front-level1-cat1',1,  FALSE ,now(),now()),
  (0, 'front-level1-cat2',1,  FALS<PERSON> ,now(),now()),
  (0, 'front-level1-cat3',1,  FALSE ,now(),now());


-- init category binding table
INSERT INTO
  parana_category_bindings (`front_category_id`, `back_category_id`, `created_at`, `updated_at`)
VALUES
  (1, 1, now(), now()),
  (1, 2, now(), now()),
  (2, 3, now(), now()),
  (3, 2, now(), now());


-- init shop_category_items table

INSERT INTO
  parana_shop_category_items
  (`shop_id`, `item_id`, `shop_category_id`,  `created_at`, `updated_at`)
VALUES
  (1, 1, 0, now(), now()),
  (1, 1, 1, now(), now()),
  (1, 2,  2,  now(), now()),
  (1, 2, 0, now(), now()),
  (1, 3,  1,  now(), now()),
  (1, 3, 0, now(), now()),
  (1, 4, 0, now(), now()),
  (1, 5, 0, now(), now()),
  (-1, 1, -1,  now(), now());



INSERT INTO
  parana_brands
  (`name`, `unique_name`, `en_name`, `en_cap`, `logo`, `description`, `status`, `created_at`, `updated_at`)
VALUES
  ('Nike', 'nike','nike','N',null,'nike is good',1, now(), now());

