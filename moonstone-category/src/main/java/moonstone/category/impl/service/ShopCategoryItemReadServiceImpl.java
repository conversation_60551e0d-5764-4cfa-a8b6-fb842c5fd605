/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.impl.service;

import com.google.common.base.Throwables;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.category.impl.dao.ShopCategoryItemDao;
import moonstone.category.model.ShopCategory;
import moonstone.category.model.ShopCategoryItem;
import moonstone.category.service.ShopCategoryItemReadService;
import moonstone.common.model.Either;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 店铺类目商品读服务
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-25
 */
@Service
@Slf4j
public class ShopCategoryItemReadServiceImpl implements ShopCategoryItemReadService {

    private final ShopCategoryItemDao shopCategoryItemDao;

    @Override
    public Response<List<ShopCategoryItem>> findByShopIdsAndShopCategoryId(List<Long> shopIds, Long shopCatId) {
        try
        {
            if (shopIds.isEmpty())
                return Response.ok(new ArrayList<>());
            return Response.ok(shopCategoryItemDao.findByShopIdsAndShopCategoryId(shopIds,shopCatId));
        }
        catch (Exception ex)
        {
            log.error("fail to find ShopCategoryItem(shopId in {} and shopCategoryId = {}) cause:{}",shopIds,shopCatId,ex);
            return Response.fail("shop.category.item.find.fail");
        }
    }

    @Override
    public Response<List<ShopCategoryItem>> pageByShopIdsAndShopCategoryId(List<Long> shopIds, Long shopCatId, int offset, int limit) {
        try
        {
            if (shopIds.isEmpty())
                return Response.ok(new ArrayList<>());
            return Response.ok(shopCategoryItemDao.pageByShopIdsAndShopCategoryId(shopIds,shopCatId,offset,limit));
        }
        catch (Exception ex)
        {
            log.error("fail to page ShopCategoryItem(shopId in {} and shopCategoryId = {}) cause:{}",shopIds,shopCatId,ex);
            return Response.fail("shop.category.item.find.fail");
        }
    }

    @Autowired
    public ShopCategoryItemReadServiceImpl(ShopCategoryItemDao shopCategoryItemDao) {
        this.shopCategoryItemDao = shopCategoryItemDao;
    }

    /**
     * 根据店铺id和商品id查询关联的类目
     *
     * @param shopId 店铺id
     * @param itemId 商品id
     * @return 此商品关联的店铺内类目
     */
    @Override
    public Response<List<ShopCategoryItem>> findByShopIdAndItemId(Long shopId, Long itemId) {

        try {
            List<ShopCategoryItem> shopCategoryItems = shopCategoryItemDao.findByShopIdAndItemId(shopId, itemId);

            return Response.ok(shopCategoryItems);
        } catch (Exception e) {
            log.error("failed to find ShopCategoryItem(shopId={}, itemId={}), cause:{}",
                    shopId, itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.category.item.find.fail");
        }
    }

    @Override
    public Response<List<ShopCategoryItem>> findByShopIdAndItemIds(Long shopId, List<Long> itemIds) {
        try {
            List<ShopCategoryItem> shopCategoryItems = shopCategoryItemDao.findByShopIdAndItemIds(shopId, itemIds);

            return Response.ok(shopCategoryItems);
        } catch (Exception e) {
            log.error("failed to find ShopCategoryItem(shopId={}, itemIds={}), cause:{}",
                    shopId, itemIds, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.category.item.find.fail");
        }
    }

    @Override
    public Response<List<ShopCategoryItem>> findByItemIds(List<Long> itemIds) {
        try {
            List<ShopCategoryItem> shopCategoryItems = shopCategoryItemDao.findByItemIds(itemIds);

            return Response.ok(shopCategoryItems);
        } catch (Exception e) {
            log.error("failed to find ShopCategoryItem(itemIds={}), cause:{}",
                    itemIds, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.category.item.find.fail");
        }
    }

    @Override
    public Response<Paging<Long>> findByShopIdAndCategoryId(Long shopId,
                                                            Long shopCategoryId,
                                                            Integer pageNo,
                                                            Integer pageSize) {
        try {
            PageInfo pageInfo = new PageInfo(pageNo, pageSize);
            Paging<Long> items;
            if (shopCategoryId == null || shopCategoryId == ShopCategory.ID_UNKNOWN) {
                items = shopCategoryItemDao.findUnknownByShopId(shopId,
                        pageInfo.getOffset(), pageInfo.getLimit());
            } else {
                items = shopCategoryItemDao.findByShopIdAndCategoryId(shopId,
                        shopCategoryId, pageInfo.getOffset(), pageInfo.getLimit());
            }

            return Response.ok(items);
        } catch (Exception e) {
            log.error("failed to find ShopCategoryItem(shopId={}, shopCategoryId={}), cause:{}",
                    shopId, shopCategoryId, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.category.item.find.fail");
        }
    }

    /**
     * 更具店铺id查询未分类商品
     *
     * @param shopId 店铺 ID
     * @return 关联商品列表
     */
    public Either<List<ShopCategoryItem>> findByShopId(Long shopId) {
        try {
            return Either.ok(shopCategoryItemDao.findByShopId(shopId));
        }
        catch (Exception e){
            log.error("Fail to findByShopId", e);
            return Either.error(e);
        }
    }
}
