/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.impl.manager;

import moonstone.category.impl.dao.ShopCategoryDao;
import moonstone.category.impl.dao.ShopCategoryItemDao;
import moonstone.category.model.ShopCategory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-03-02
 */
@Component
public class ShopCategoryManager {

    private final ShopCategoryDao shopCategoryDao;

    private final ShopCategoryItemDao shopCategoryItemDao;

    @Autowired
    public ShopCategoryManager(ShopCategoryDao shopCategoryDao, ShopCategoryItemDao shopCategoryItemDao) {
        this.shopCategoryDao = shopCategoryDao;
        this.shopCategoryItemDao = shopCategoryItemDao;
    }

    @Transactional
    public void create(ShopCategory shopCategory, ShopCategory parent) {
        shopCategoryDao.create(shopCategory);
        shopCategoryDao.update(parent);
    }

    @Transactional
    public void delete(ShopCategory shopCategory, ShopCategory parent) {
        shopCategoryDao.delete(shopCategory.getId());
        shopCategoryItemDao.deleteByShopIdAndCategoryId(shopCategory.getShopId(), shopCategory.getId());
        if(parent!=null){
            shopCategoryDao.update(parent);
        }
    }

    @Transactional
    public void batchUpdate(List<ShopCategory> toUpdates) {
        for (ShopCategory toUpdate : toUpdates) {
            shopCategoryDao.update(toUpdate);
        }
    }
}
