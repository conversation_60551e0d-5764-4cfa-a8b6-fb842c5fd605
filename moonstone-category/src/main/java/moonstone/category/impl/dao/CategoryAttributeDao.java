/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.impl.dao;

import com.google.common.base.MoreObjects;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.category.model.CategoryAttribute;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-15
 */
@Repository
public class CategoryAttributeDao extends MyBatisDao<CategoryAttribute> {
    /**
     * 根据类目id查找该类目下的属性列表
     *
     * @param categoryId  类目id
     * @return  该类目下的属性列表
     */
    public List<CategoryAttribute> findByCategoryId(Long categoryId){
        return getSqlSession().selectList(sqlId("findByCategoryId"), categoryId);
    }

    /**
     * 更新属性的状态
     *
     * @param id  属性id
     * @param status   状态, 1:启用, -1:删除
     * @return  是否更新成功
     */
    public boolean updateStatus(Long id, Integer status){
        return getSqlSession().update(sqlId("updateStatus"), ImmutableMap.of("id",id, "status",status)) ==1;
    }

    /**
     * 更新属性排序位置
     *
     * @param id  属性id
     * @param index  排序位置
     * @return  是否更新成功
     */
    public boolean updateIndex(Long id, Integer index){
        return getSqlSession().update(sqlId("updateIndex"), ImmutableMap.of("id",id, "index",index)) ==1;
    }

    /**
     * 更具类目id和属性key查找对应的属性
     *
     * @param categoryId 类目id
     * @param attrKey   属性键
     * @return  对应的类目属性
     */
    public CategoryAttribute findByCategoryIdAndAttrKey(Long categoryId, String attrKey){
        return getSqlSession().selectOne(sqlId("findByCategoryIdAndAttrKey"),
                ImmutableMap.of("categoryId",categoryId, "attrKey",attrKey));
    }

    /**
     * 查找一个类目下属性的最大序号
     *
     * @param categoryId  类目id
     * @return 类目下属性的最大序号
     */
    public Integer maxIndexOfCategoryId(Long categoryId){
        final Integer maxIndexId = getSqlSession().selectOne(sqlId("categoryMaxIndexId"), categoryId);
        return MoreObjects.firstNonNull(maxIndexId,0);
    }


    /**
     * 获取一个类目下有效属性的数目
     *
     * @param categoryId   类目id
     * @return   有效属性个数
     */
    public Integer countOfValidCategoryAttribute(Long categoryId){
        Integer count = getSqlSession().selectOne(sqlId("count"), categoryId);
        return MoreObjects.firstNonNull(count, 0);
    }

}
