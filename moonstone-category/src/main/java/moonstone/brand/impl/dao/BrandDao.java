/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.brand.impl.dao;

import com.google.common.base.MoreObjects;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.brand.model.Brand;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-15
 */
@Repository
public class BrandDao extends MyBatisDao<Brand> {

    /**
     * 根据名称查找品牌
     *
     * @param name 品牌名
     * @return 对应的品牌
     */
    public Brand findByName(String name) {
        return getSqlSession().selectOne(sqlId("findByName"), name);
    }

    public Brand findByUniqueName(String name) {
        return getSqlSession().selectOne(sqlId("findByUniqueName"), name.toLowerCase());
    }

    /**
     * 根据品牌名前缀匹配品牌
     *
     * @param namePrefix 品牌名前缀
     * @param limit      最多返回结果数目
     * @return 匹配的品牌列表
     */
    public List<Brand> findByNamePrefix(String namePrefix, Integer limit) {
        final Integer size = MoreObjects.firstNonNull(limit, 10);
        return getSqlSession().selectList(sqlId("findByNamePrefix"),
                ImmutableMap.of("name", namePrefix.toLowerCase(),
                        "limit", size));
    }

    /**
     * 更新品牌的状态
     *
     * @param id     品牌id
     * @param status 状态
     * @return 是否更新成功
     */
    public boolean updateStatus(Long id, Integer status) {
        return getSqlSession().update(sqlId("updateStatus"),
                ImmutableMap.of("id", id, "status", status)) == 1;
    }

    /**
     * 根据外部编码查找品牌
     *
     * @param outerId 品牌名
     * @return 对应的品牌
     */
    public Brand findByOuterId(String outerId) {
        return getSqlSession().selectOne(sqlId("findByOuterId"), outerId);
    }
}
