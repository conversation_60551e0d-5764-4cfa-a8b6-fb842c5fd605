/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.brand.impl.service;

import com.google.common.base.MoreObjects;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.brand.impl.dao.BrandDao;
import moonstone.brand.model.Brand;
import moonstone.brand.service.BrandWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-25
 */
@Service
@Slf4j
@RpcProvider
public class BrandWriteServiceImpl implements BrandWriteService {

    private final BrandDao brandDao;

    @Autowired
    public BrandWriteServiceImpl(BrandDao brandDao) {
        this.brandDao = brandDao;
    }

    /**
     * 创建品牌
     *
     * @param brand 待创建的品牌
     * @return 新品牌的id
     */
    @Override
    public Response<Long> create(Brand brand) {
        String name = brand.getName();
        if(!StringUtils.hasText(name)){
            log.error("brand name can not be empty");
            return Response.fail("brand.name.empty");
        }
        final String normalizedName = name.trim();
        try {
            Brand existed = brandDao.findByUniqueName(normalizedName);
            if(existed!=null){
                log.error("duplicated brand name {}", name);
                return Response.fail("brand.name.duplicated");
            }
            brand.setName(normalizedName);
            brand.setUniqueName(normalizedName.toLowerCase());
            brand.setStatus(MoreObjects.firstNonNull(brand.getStatus(), 1));
            brandDao.create(brand);
            return Response.ok(brand.getId());
        } catch (Exception e) {
            log.error("failed to create {}, cause:{}", brand, Throwables.getStackTraceAsString(e));
            return Response.fail("brand.create.fail");
        }
    }

    /**
     * 更新品牌
     *
     * @param brand 待更新的品牌
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> update(Brand brand) {
        Long brandId = brand.getId();
        try {
            Brand existed = brandDao.findById(brandId);
            if(existed == null){
                log.error("brand(id={}) not exist", brandId);
                return Response.fail("brand.not.found");
            }

            if(StringUtils.hasText(brand.getName())){
                String normalized = brand.getName().trim();
                brand.setName(normalized);
                brand.setUniqueName(normalized.toLowerCase());
            }
            brandDao.update(brand);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update {}, cause:{}", brand, Throwables.getStackTraceAsString(e));
            return Response.fail("brand.update.fail");
        }
    }
}
