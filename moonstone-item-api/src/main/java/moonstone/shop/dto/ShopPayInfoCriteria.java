package moonstone.shop.dto;

import lombok.Data;
import moonstone.common.model.PagingCriteria;

import java.io.Serial;
import java.util.List;

@Data
public class ShopPayInfoCriteria extends PagingCriteria {
    @Serial
    private static final long serialVersionUID = 3354298588909183651L;

    private List<Long> shopIdList;

    /**
     * 支付渠道
     *
     * @see moonstone.shop.enums.ShopPayInfoPayChannelEnum
     */
    private String payChannel;

    /**
     * 使用渠道
     *
     * @see moonstone.shop.enums.ShopPayInfoUsageChannelEnum
     */
    private Integer usageChannel;
}
