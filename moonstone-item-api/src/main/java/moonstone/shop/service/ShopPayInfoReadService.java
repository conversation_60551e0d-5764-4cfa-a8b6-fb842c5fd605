package moonstone.shop.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.shop.dto.ShopPayInfoCriteria;
import moonstone.shop.enums.ShopPayInfoPayChannelEnum;
import moonstone.shop.enums.ShopPayInfoStatusEnum;
import moonstone.shop.enums.ShopPayInfoUsageChannelEnum;
import moonstone.shop.model.ShopPayInfo;

import java.util.List;

public interface ShopPayInfoReadService {

    /**
     * 根据ID查找支付信息
     *
     * @param id ID
     * @return 支付信息
     */
    Response<ShopPayInfo> findById(Long id);

    /**
     * 根据shopId查找该店铺下的所有支付信息记录
     *
     * @param shopId shopId
     * @return 支付信息记录
     */
    Response<List<ShopPayInfo>> findByShopId(Long shopId);

    /**
     * 根据shopId及payChannel查找一条唯一支付信息记录
     *
     * @param shopId
     * @param payChannel
     * @return
     */
    Response<ShopPayInfo> findByShopIdAndPayChannel(Long shopId, String payChannel);

    Response<List<ShopPayInfo>> findAllByShopIdAndPayChannel(Long shopId, ShopPayInfoPayChannelEnum payChannel,
                                                             List<ShopPayInfoStatusEnum> statusList);

    /**
     * 搜索全平台支付信息
     *
     * @return
     */
    Response<List<ShopPayInfo>> findAll();

    Response<Paging<ShopPayInfo>> paging(ShopPayInfoCriteria criteria);

    Response<ShopPayInfo> getById(Long shopPayInfoId);

    Response<List<ShopPayInfo>> find(Long shopId, ShopPayInfoUsageChannelEnum usageChannel, List<ShopPayInfoStatusEnum> statusList);

    Response<List<ShopPayInfo>> find(Long shopId, ShopPayInfoStatusEnum status);

    Response<List<ShopPayInfo>> findByStatus(Long shopId, ShopPayInfoStatusEnum status);

    Response<List<ShopPayInfo>> findByIds(List<Long> idList);

}
