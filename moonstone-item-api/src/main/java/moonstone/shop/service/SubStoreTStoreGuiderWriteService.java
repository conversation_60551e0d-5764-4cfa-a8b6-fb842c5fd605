package moonstone.shop.service;

import io.terminus.common.model.Response;
import moonstone.shop.model.SubStoreTStoreGuider;

import java.util.List;

public interface SubStoreTStoreGuiderWriteService {
    Response<Boolean> create(SubStoreTStoreGuider subStoreTStoreGuider);

    Response<Boolean> creates(List<SubStoreTStoreGuider> list);

    Response<Boolean> update(SubStoreTStoreGuider subStoreTStoreGuider);

    Response<Boolean> updateSelective(SubStoreTStoreGuider subStoreTStoreGuider);

    Response<Boolean> updateSubStore(Long recordId, Long subStoreId);

    Response<Boolean> deleteByIds(List<Long> recordIds);
}
