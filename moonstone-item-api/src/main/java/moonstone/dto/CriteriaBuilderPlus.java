package moonstone.dto;


import io.terminus.search.api.query.Criterias;
import io.terminus.search.api.query.CriteriasBuilder;

import java.util.List;

public class CriteriaBuilderPlus extends CriteriasBuilder {
    public List<Terms> getInTerms() {
        return inTerms;
    }

    boolean inTermsNotHead = false;
    List<Terms> inTerms;

    @Override
    public Criterias build() {
        super.build();
        if (getTerm() != null || getTerms() != null || getRanges() != null)
            inTermsNotHead = true;
        return new InTermCriteria(this);
    }

    public boolean isInTermsNotHead() {
        return inTermsNotHead;
    }

    public void withInTerms(List<Terms> inTermsList) {
        if (inTermsList==null)
            return;
        inTerms = inTermsList;
        for (Terms inTerm : inTermsList)
            inTerm.getValues().get(inTerm.getValues().size() - 1).setLast(true);
        inTerms.get(inTerms.size() - 1).setLast(true);
    }
}
