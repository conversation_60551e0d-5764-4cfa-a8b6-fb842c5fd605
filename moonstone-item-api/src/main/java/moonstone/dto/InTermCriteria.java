package moonstone.dto;

import io.terminus.search.api.query.Criterias;
import io.terminus.search.api.query.CriteriasBuilder;

import java.util.List;

/**
 * 添加一个InTerms和hasInTerm用于与模板同步
 */
public class InTermCriteria extends Criterias {
    boolean inTermsNotHead = false;
    boolean hasInTerms;
    List<moonstone.dto.Terms> inTerms;

    public boolean isHasInTerms() {
        return inTerms != null && !inTerms.isEmpty();
    }

    public List<Terms> getInTerms() {
        return inTerms;
    }

    public boolean isInTermsNotHead() {
        return inTermsNotHead;
    }

    public InTermCriteria(CriteriasBuilder cb) {
        super(cb);
        if (cb instanceof CriteriaBuilderPlus) {
            inTermsNotHead = ((CriteriaBuilderPlus) cb).isInTermsNotHead();
            inTerms = ((CriteriaBuilderPlus) cb).getInTerms();
            hasInTerms = isHasInTerms();
        }
    }
}
