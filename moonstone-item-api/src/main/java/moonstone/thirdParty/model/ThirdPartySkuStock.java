package moonstone.thirdParty.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ThirdPartySkuStock implements Serializable {
    private static final long serialVersionUID = -5572031490869667505L;
    private Long id;

    /**
     * belong to which shop
     * because the outerSkuId is not specify for all
     */
    private Long shopId;
    /**
     * 第三方平台标识（1:洋800）
     */
    private Integer thirdPartyId;

    /**
     * 商品来源(1：代塔仓自有，2：京东云交易)
     */
    private Integer sourceType;

    /**
     * 第三方sku编码
     */
    private String outerSkuId;

    /**
     * 第三方sku名称
     */
    private String outerSkuName;

    /**
     * 仓库编码
     */
    private String depotCode;

    /**
     * 仓库名称
     */
    private String depotName;

    /**
     * 批次
     */
    private String batch;

    /**
     * 货主名
     */
    private String supplierName;
    /**
     * 效期
     */
    private Date effectPeriod;

    /**
     * 正品库存
     */
    private Integer authenticStock;

    /**
     * 次品库存
     */
    private Integer defectiveStock;

    /**
     * 体积(m3)
     */
    private BigDecimal volume;

    /**
     * 包裹占用体积
     */
    private BigDecimal packVolume;

    /**
     * 重量(kg)
     */
    private BigDecimal weight;

    /**
     * 状态 1：启用，-1：禁用
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
}
