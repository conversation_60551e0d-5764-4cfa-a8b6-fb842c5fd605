package moonstone.thirdParty.service;

import moonstone.common.enums.ThirdPartySystem;
import moonstone.thirdParty.model.ThirdPartyUserShop;

public interface ThirdPartyJobService {
    void synchronizeJob();

    /**
     * 同步商品数据
     *
     * @param thirdPartySystem   外部平台
     * @param thirdPartyUserShop 外部平台凭据
     */
    void synchronize(ThirdPartySystem thirdPartySystem, ThirdPartyUserShop thirdPartyUserShop) throws Exception;

    /**
     * 清除已有同步数据(假删)
     *
     * @param thirdPartySystemId
     * @param thirdPartyUserShopId
     */
    void clearSynchronize(Integer thirdPartySystemId, Long thirdPartyUserShopId);
}
