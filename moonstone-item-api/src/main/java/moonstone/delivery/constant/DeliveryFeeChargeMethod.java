package moonstone.delivery.constant;

/**
 * <AUTHOR>
 * @see moonstone.delivery.model.DeliveryFeeBase 被限定的类
 */
public enum DeliveryFeeChargeMethod {
    /**
     * 运费计算方式 枚举
     */
    Unknown(-1, "未知"),
    byUnit(1, "按计量单位"),
    byStatic(2, "固定运费"),
    byPrice(3, "价格相关");
    int type;
    String desc;

    public static DeliveryFeeChargeMethod valueOf(int type) {
        for (DeliveryFeeChargeMethod value : DeliveryFeeChargeMethod.values()) {
            if (value.getType() == type) {
                return value;
            }
        }
        return Unknown;
    }

    DeliveryFeeChargeMethod(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }
}
