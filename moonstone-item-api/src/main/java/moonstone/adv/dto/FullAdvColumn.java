package moonstone.adv.dto;

import moonstone.adv.model.AdvColumn;
import moonstone.common.utils.StringUtils;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/10/9.
 */
public class FullAdvColumn extends AdvColumn {

    /**
     * 头部
     */
    public static final int POSTION_TOP_VALUE = 1;
    /**
     * 中部
     */
    public static final int POSTION_MIDDLE_VALUE = 2;
    /**
     * 底部
     */
    public static final int POSTION_BOTTOM_VALUE = 3;

    public String validate() {
        if(StringUtils.isBlank(getHeight())){
            return "高度未填写";
        }
        if(StringUtils.isBlank(getWidth())){
            return "宽度未填写";
        }
        if(StringUtils.isBlank(getName())){
            return "名称未填写";
        }
        if(getPosition()==0){
            return "未选择广告位";
        }
        return null;
    }
}