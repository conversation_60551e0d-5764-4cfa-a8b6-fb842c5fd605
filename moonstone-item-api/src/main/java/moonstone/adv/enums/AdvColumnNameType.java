package moonstone.adv.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 广告位
 */
public enum AdvColumnNameType {

    WEB_INDEX_BANNER(1, "web_index_banner", "web端首页"),
    WEB_SHOP_BANNER(2, "web_shop_banner", "web端店铺"),
    WEB_GOODS_BANNER(3, "web_goods_banner", "web端商品"),
    WXA_INDEX_BANNER(4, "wxa_index_banner", "微信小程序首页"),
    WXA_SHOP_BANNER(5, "wxa_shop_banner", "微信小程序店铺"),
    WXA_GOODS_BANNER(6, "wxa_goods_banner", "微信小程序商品"),
    WXA_WE_SHOP_BANNER(7, "wxa_we_shop_banner", "微分销商城店主端轮播图");

    private final int value;

    private final String sn;

    private final String desc;

    AdvColumnNameType(int value, String sn, String desc) {
        this.value = value;
        this.sn = sn;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getSn() {
        return sn;
    }

    public String getDesc() {
        return desc;
    }

    public AdvColumnNameType getEnum(int value) {
        for (AdvColumnNameType item : AdvColumnNameType.values()) {
            if (item.value == value)
                return item;
        }
        return WEB_INDEX_BANNER;
    }

    public AdvColumnNameType getEnum(String sn) {
        for (AdvColumnNameType item : AdvColumnNameType.values()) {
            if (item.sn.equals(sn))
                return item;
        }
        return WEB_INDEX_BANNER;
    }

    public static Map<String, AdvColumnNameType> getNameMap() {
        Map<String, AdvColumnNameType> map = new HashMap<>();
        for (AdvColumnNameType item : AdvColumnNameType.values()) {
            map.put(item.name(), item);
        }
        return map;
    }
}
