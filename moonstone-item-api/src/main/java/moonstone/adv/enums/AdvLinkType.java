package moonstone.adv.enums;

import com.google.common.base.Objects;

/**
 * Created by CaiZhy on 2018/12/4.
 */
public enum  AdvLinkType {

    ITEM(1),            //商品
    ACTIVITY_PAGE(2),   //活动页
    NONE(0);            //无， 则链接为#

    private final int value;

    AdvLinkType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static AdvLinkType fromInt(int value){
        for (AdvLinkType advLinkType : AdvLinkType.values()) {
            if(Objects.equal(advLinkType.getValue(), value)){
                return advLinkType;
            }
        }
        throw new IllegalArgumentException("unknown adv link type : " + value);
    }
}
