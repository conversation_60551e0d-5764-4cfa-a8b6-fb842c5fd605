/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Sets;
import lombok.*;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.component.dto.item.ItemCommissionConfigDTO;
import moonstone.delivery.model.ItemDeliveryFee;
import moonstone.item.emu.CashGiftUnitEnum;
import moonstone.item.model.Item;
import moonstone.item.model.ItemDetail;
import moonstone.item.model.Sku;
import moonstone.rule.dto.BaseInput;
import moonstone.rule.dto.GeneralSku;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 这个类应该是创建商品的主入口,无论是手工发商品, 还是接口导入, 最后都可以组装成这个对象
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2015-12-11
 */
@EqualsAndHashCode(callSuper = true)
@ToString
@Data
public class FullItem extends BaseInput {
    private static final long serialVersionUID = -6794381566838883200L;

    /**
     * 商品
     */
    @Getter
    @Setter
    private Item item;

    /**
     * 商品图相关
     */
    @Getter
    @Setter
    private ItemDetail itemDetail;

    /**
     * 商品运费
     */
    @Getter
    @Setter
    private ItemDeliveryFee itemDeliveryFee;

    /**
     * 商品下的sku及海关信息
     */
    @Getter
    @Setter
    private List<SkuWithCustom>  skuWithCustoms;

    /**
     * 商品所属的类目id
     */
    @Override
    @JsonIgnore
    public Long getCategoryId() {
        return item!=null?item.getCategoryId():null;
    }

    /**
     * 商品所属的spu id可能为空
     */
    @Override
    @JsonIgnore
    public Long getSpuId() {
        return item != null ? item.getSpuId() : null;
    }

    /**
     * 设置最高价格的
     */
    Long lowPrice;
    /**
     * 设置最低的价格
     */
    Long highPrice;

    /**
     * 一级佣金率
     */
    @Getter
    @Setter
    Long firstRate;

    /**
     * 一级佣金
     */
    @Getter
    @Setter
    Long firstFee;
    /**
     *二级佣金率
     */
    @Getter
    @Setter
    Long secondRate;
    /**
     * 二级佣金
     */
    @Getter
    @Setter
    Long secondFee;

    /**
     *复购佣金
     */
    @Getter
    @Setter
    Long commission;
    /**
     * 首购佣金
     */
    @Getter
    @Setter
    Long firstCommission;

    @Getter
            @Setter
    Long serviceProviderFee;

    @Getter
            @Setter
    Long serviceProviderRate;

    /**
     *是否开启单品拉新佣金 0-不开启 1-开启
     */
    @Getter
    @Setter
    Integer isCommission;

    /**
     * 是否可用礼金 0-不开启 1-开启
     */
    Integer isCashGift;

    BigDecimal maxDeduction;

    /**
     * @see CashGiftUnitEnum
     */
    Integer unit;

    Long cashGiftUseTimeStart;

    Long cashGiftUseTimeEnd;



    /**
     * 是否要删除活动佣金配置 true-删除，false或空-不删除
     */
    private String deleteActivityCommissionConfig;

    /**
     * 活动佣金配置
     */
    @Getter
    @Setter
    private ItemCommissionConfigDTO activityCommissionConfig;

    /**
     * 对应的sku或者skuTemplate的销售属性key
     */
    @Override
    @JsonIgnore
    public Set<String> getSkuAttrKeys() {
        if(CollectionUtils.isEmpty(skuWithCustoms)){
            return Collections.emptySet();
        }
        Set<String> skuAttrKeys = Sets.newLinkedHashSet();
        for (SkuWithCustom skuWithCustom : skuWithCustoms) {
            Sku sku = skuWithCustom.getSku();
            if(!CollectionUtils.isEmpty(sku.getAttrs())){
                for (SkuAttribute skuAttribute : sku.getAttrs()) {
                    skuAttrKeys.add(skuAttribute.getAttrKey());
                }
            }
        }
        return skuAttrKeys;
    }

    /**
     * 设置处理后的sku或者skuTemplate
     *
     * @return sku或者skuTemplate 列表
     */
    @Override
    @JsonIgnore
    public List<? extends GeneralSku> getGeneralSkus() {
        List<Sku> skus = new ArrayList<>();
        for (SkuWithCustom skuWithCustom:skuWithCustoms) {
            skus.add(skuWithCustom.getSku());
        }
        return skus;
    }


}
