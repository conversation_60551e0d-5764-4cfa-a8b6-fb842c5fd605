package moonstone.item.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * Created by CaiZhy on 2018/12/4.
 */
public class ItemDistributionPrice implements Serializable {
    private static final long serialVersionUID = -1890160827752828822L;

    /**
     * 商品id
     */
    @Getter
    @Setter
    private Long itemId;

    /**
     * sku分销价列表
     */
    @Getter
    @Setter
    private List<SkuDistributionPrice> skuPrices;
}
