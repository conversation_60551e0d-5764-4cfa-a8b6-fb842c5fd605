package moonstone.item.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.item.model.Item;

import java.io.Serializable;
import java.util.List;

/**
 * Created by CaiZhy on 2018/10/23.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ItemForList extends Item implements Serializable {
    private static final long serialVersionUID = -2795747163450497882L;

    private String wxaUrl;
    private List<String> tagList;

    /**
     * 售罄状态
     *
     * @see moonstone.item.emu.ItemSellOutStatusEnum
     */
    private Integer sellOutStatus;

    /**
     * 商品的完整类目
     */
    private String categoryPath;

    /**
     * 服务商佣金
     */
    private String serviceProviderCommission;

    /**
     * 门店佣金
     */
    private String subStoreCommission;

    /**
     * 导购佣金
     */
    private String guiderCommission;

    /**
     * 服务商的佣金比例
     */
    private Long serviceProviderCommissionRate;

    /**
     * 门店的佣金比例
     */
    private Long subStoreCommissionRate;

    /**
     * 导购的的佣金比例
     */
    private Long guiderCommissionRate;

    /**
     * 外部sku编号
     */
    private String outerSkuId;

    /**
     * 限售区域模板名称
     */
    private String restrictedSalesAreaTemplateName;
}
