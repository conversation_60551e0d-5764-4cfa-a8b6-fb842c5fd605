package moonstone.item.dto.paging;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.PagingCriteria;

import java.io.Serializable;
import java.util.List;

/**
 * Created by CaiZhy on 2018/10/26.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ItemCriteria extends PagingCriteria implements Serializable {
    private static final long serialVersionUID = 2164806785139211560L;

    // 从 ItemPageDto 和 ItemCriteria 中合并的字段
    private Long shopId; // 店铺ID
    private Long itemId; // 商品ID
    private List<Long> itemIdList; // 商品id集合
    private String itemName; // 商品名称
    private Integer status; // 商品状态
    private Integer sellOutStatus; // 售罄状态
    private List<Long> shopCategoryIds; // 商品分类ID
    private Long restrictedSalesAreaTemplateId; // 可售地区模板ID
    private List<String> outerSkuIds; // 仓库商品SKU集合
    private Boolean isCategory; // 是否分类
    private Integer sourceType; // 商品来源(1：代塔仓自有，2：京东云交易)
    private Integer isUseCashGift; // 是否使用礼金 0：否 1：是
    private String brandName; // 品牌名称
    private Long brandId; // 品牌ID
    private String idOrName; // 商品ID或名称

    // 仅从 ItemCriteria 中保留的字段
    private List<Long> ids; // 商品id
    private List<Long> exShopIds; // 排除在外的商店id
    private Long categoryId; // 类型id
    private String itemCode; // 商品编码
    private String name; // 商品名称
    private String sortBy; // 排序字段
    private Boolean sellOnSubStore; // 是否在门店上出售
    private Integer sortType; // 排序类型
    private Integer type; // 商品类型
    private String paranaWxaUrl; // 仅用于传参，不用于限制条件
    private String shopCategoryName; // 商品类目名称

}
