package moonstone.item.emu;

import org.springframework.util.StringUtils;

/**
 * 商品的”售罄状态“
 */
public enum ItemSellOutStatusEnum {
    IN_STOCK(1, "未售罄"),
    SOLD_OUT(2, "已售罄");

    private Integer code;
    private String description;

    ItemSellOutStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static ItemSellOutStatusEnum parse(Integer code) {
        for (var current : ItemSellOutStatusEnum.values()) {
            if (current.getCode().equals(code)) {
                return current;
            }
        }

        return null;
    }

    public static ItemSellOutStatusEnum clean(String code) {
        try {
            if (!StringUtils.hasText(code)) {
                return IN_STOCK;
            }

            return parse(Integer.parseInt(code));
        } catch (Exception ex) {
            ex.printStackTrace();
            return IN_STOCK;
        }
    }
}
