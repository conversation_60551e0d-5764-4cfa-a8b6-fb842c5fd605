package moonstone.item.emu;

/**
 * sku 的 extra_price_json 里的下标
 */
public enum SkuExtraPriceIndex {
    originPrice("originPrice", "市场价"),
    crossedPrice("crossedPrice", "划线价"),
    ;

    private String code;
    private String description;

    SkuExtraPriceIndex(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
