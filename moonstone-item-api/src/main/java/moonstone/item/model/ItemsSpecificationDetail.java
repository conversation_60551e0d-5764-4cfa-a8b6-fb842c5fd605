package moonstone.item.model;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ItemsSpecificationDetail {

    /**
     * id
     */
    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 商品规格id
     */
    private Long itemSpecificationId;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 名称
     */
    private String name;

    /**
     * 排序
     */
    private Integer sequence;

    /**
     * 前端生成的id
     */
    private String frontId;

    /**
     * 乐观锁
     */
    private Integer version;

    /**
     * 创建人（用户id）
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Long createdTime;

    /**
     * 修改人（用户id）
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Long updatedTime;

    /**
     * 是否删除（0：否，1：是）
     */
    private Integer deleted;


}
