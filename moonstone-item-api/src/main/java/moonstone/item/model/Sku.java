/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.model.Indexable;
import io.terminus.common.utils.JsonMapper;
import lombok.*;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.common.constants.JacksonType;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.ImageUrlHandler;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.rule.dto.GeneralSku;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(of = {"id", "shopId", "skuCode", "specification"})
@ToString
public class Sku implements GeneralSku, Serializable, Indexable {

    @Serial
    private static final long serialVersionUID = -2296705898220315438L;

    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();

    /**
     * ID
     */
    @Getter
    @Setter
    private Long id;
    /**
     * SKU 编码 (可能是标识sku的唯一编码)
     */
    @Getter
    @Setter
    private String skuCode;

    /**
     * 商品 ID , 如果是没有item的sku, 则表示此sku需要从spu中获取对应的信息, 那么本字段表示spu id
     */
    @Getter
    @Setter
    private Long itemId;

    /**
     * 店铺 ID (冗余自商品表)
     */
    @Getter
    @Setter
    private Long shopId;

    /**
     * 类型（1:保税，0:完税）
     *
     * @see moonstone.item.emu.SkuTypeEnum
     */
    @Getter
    @Setter
    private Integer type;

    /**
     * sku状态 1: 上架, -1:下架, -2:冻结, -3:删除
     *
     * @see moonstone.item.emu.SkuStatusEnum
     */
    @Getter
    @Setter
    private Integer status;

    /**
     * 型号/款式
     */
    @Getter
    @Setter
    private String specification;

    /**
     * 外部 sku 编号
     */
    @Getter
    @Setter
    private String outerSkuId;

    /**
     * 外部店铺 id
     */
    @Getter
    @Setter
    private String outerShopId;

    /**
     * 图片
     */
    private String image;

    /**
     * 样本图 (SKU 缩略图) URL
     */
    private String thumbnail;

    /**
     * 名称
     */
    @Getter
    @Setter
    private String name;

    /**
     * 其他各种价格, 如市场价, 阶梯价等的json表示形式, 存数据库
     */
    @Getter
    private String extraPriceJson;


    /**
     * 其他各种价格, 如市场价, 阶梯价等, 不存数据库
     */
    @Getter
    private Map<String, Integer> extraPrice;

    /**
     * SKU 级别售价, 真实售价
     */
    @Getter
    @Setter
    private Integer price;


    /**
     * sku的销售属性, 不存数据库
     */
    @Getter
    private List<SkuAttribute> attrs;


    /**
     * sku销售属性的json表示形式, 存数据库
     */
    @Getter
    @JsonIgnore
    private String attrsJson;


    /**
     * 库存类型, 0: 不分仓存储, 1: 分仓存储, 冗余自商品表
     */
    @Getter
    @Setter
    private Integer stockType;

    /**
     * 库存
     */
    @Getter
    @Setter
    private Integer stockQuantity;

    /**
     * 销量
     */
    @Getter
    @Setter
    private Integer saleQuantity;

    /**
     * 版本号
     */
    @Getter
    @Setter
    private Integer version;

    /**
     * 仓库编码缓存
     */
    @Getter
    @Setter
    private String depotCode;

    /**
     * 活动销售价开关 (0-不开启 1-开启)
     */
    @Getter
    @Setter
    private Integer activitySalesPriceSwitch;


    /**
     * 扩展字段, 不存数据库
     */
    @Getter
    private Map<String, String> extraMap;

    /**
     * 扩展字段 JSON, 存数据库
     */
    @Getter
    @JsonIgnore
    private String extraJson;

    /**
     * tag信息,不持久化到数据库
     */
    @Getter
    private Map<String, String> tags;

    /**
     * tag信息,持久化到数据库
     */
    @Getter
    @JsonIgnore
    private String tagsJson;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 修改时间
     */
    @Getter
    @Setter
    private Date updatedAt;

    /// 分销佣金比例 与profitFee冲突
    @Getter
    @Setter
    private Long guiderProfitRate;

    /// 固定分销佣金 如果非Null则profitRate无效
    @Getter
    @Setter
    private Long guiderProfitFee;
    /// 分销佣金比例 与profitFee冲突
    @Getter
    @Setter
    private Long subStoreProfitRate;

    /// 固定分销佣金 如果非Null则profitRate无效
    @Getter
    @Setter
    private Long subStoreProfitFee;

    @JsonIgnore
    public String getImage() {
        return ImageUrlHandler.simplify(this.image);
    }

    @JsonProperty("image")
    public String getImage_() {
        return this.image;
    }

    @JsonSetter
    public void setImage(String image) {
        this.image = ImageUrlHandler.complete(image);
    }

    @JsonIgnore
    public String getThumbnail() {
        return ImageUrlHandler.simplify(this.thumbnail);
    }

    @JsonProperty("thumbnail")
    public String getThumbnail_() {
        return this.thumbnail;
    }

    @JsonSetter
    public void setThumbnail(String thumbnail) {
        this.thumbnail = ImageUrlHandler.complete(thumbnail);
    }

    public void setAttrs(List<SkuAttribute> attrs) {
        this.attrs = attrs;
        if (attrs == null) {
            this.attrsJson = null;
        } else {
            try {
                this.attrsJson = objectMapper.writeValueAsString(attrs);
            } catch (Exception e) {
                //ignore this fucking exception
            }
        }
    }

    public void setAttrsJson(String attrsJson) throws Exception {
        this.attrsJson = attrsJson;
        if (Strings.isNullOrEmpty(attrsJson)) {
            this.attrs = Collections.emptyList();
        } else {
            this.attrs = objectMapper.readValue(attrsJson, new TypeReference<List<SkuAttribute>>() {
            });
        }
    }

    public void setExtraPriceJson(String extraPriceJson) throws Exception {
        this.extraPriceJson = extraPriceJson;
        if (Strings.isNullOrEmpty(extraPriceJson)) {
            this.extraPrice = Collections.emptyMap();
        } else {
            this.extraPrice = objectMapper.readValue(extraPriceJson, JacksonType.MAP_OF_INTEGER);
        }
    }

    @SneakyThrows
    public void setExtraPrice(Map<String, Integer> extraPrice) {
        this.extraPrice = extraPrice;
        if (extraPrice == null) {
            this.extraPriceJson = null;
        } else {
            this.extraPriceJson = objectMapper.writeValueAsString(extraPrice);
        }
    }

    @SneakyThrows
    public void setExtraJson(String extraJson) {
        this.extraJson = extraJson;
        if (Strings.isNullOrEmpty(extraJson)) {
            this.extraMap = Collections.emptyMap();
        } else {
            this.extraMap = objectMapper.readValue(extraJson, JacksonType.MAP_OF_STRING);
        }
    }

    @SneakyThrows
    public void setExtraMap(Map<String, String> extraMap) {
        this.extraMap = extraMap;
        if (extraMap == null || extraMap.isEmpty()) {
            this.extraJson = "{}";
        } else {
            this.extraJson = objectMapper.writeValueAsString(extraMap);
        }
    }

    public void setTagsJson(String tagsJson) throws Exception {
        this.tagsJson = tagsJson;
        if (Strings.isNullOrEmpty(tagsJson)) {
            this.tags = Collections.emptyMap();
        } else {
            this.tags = objectMapper.readValue(tagsJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
        if (tags == null || tags.isEmpty()) {
            this.tagsJson = null;
        } else {
            try {
                this.tagsJson = objectMapper.writeValueAsString(tags);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }

    /**
     * extra字段中记录的活动是否当前有效
     *
     * @return
     */
    public boolean isExtraActivityValid() {
        if (CollectionUtils.isEmpty(this.getExtraMap())) {
            return false;
        }

        //开始时间与结束时间任一为空，则活动无效
        String activityStart = this.getExtraMap().get(SkuExtraIndex.activityStartTime.name());
        String activityEnd = this.getExtraMap().get(SkuExtraIndex.activityEndTime.name());
        if (!StringUtils.hasText(activityStart) || !StringUtils.hasText(activityEnd)) {
            return false;
        }

        //当前时间须处于开始时与结束时间的范围内，否则活动无效
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtil.YMDHMS_FORMAT);

            Date start = simpleDateFormat.parse(activityStart);
            Date end = simpleDateFormat.parse(activityEnd);
            Date current = new Date();

            return start.before(current) && current.before(end);
        } catch (Exception ex) {
            return false;
        }
    }
}
