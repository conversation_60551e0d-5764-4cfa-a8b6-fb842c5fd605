package moonstone.membership.service;

import io.terminus.common.model.Response;
import moonstone.membership.model.MembershipPrice;


/**
 * 会员价写服务
 *
 * Author: g
 * Date: 2018-7-25
 */
public interface MembershipPriceWriteService {

    /**
     * 创建会员价
     *
     * @param membershipPrice 会员价
     * @return 创建成功的会员价
     */
    Response<Long> create(MembershipPrice membershipPrice);

    /**
     * 删除会员价记录
     *
     * @param id 待删除的会员价Id
     * @return 删除结果
     */
    Response<Boolean> delete(Long id);

    /**
     * 更新会员价信息
     * @param membershipPrice 更新内容
     * @return 更新结果
     */
    Response<Boolean> update(MembershipPrice membershipPrice);
}
