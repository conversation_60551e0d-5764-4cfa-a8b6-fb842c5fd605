/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.search.item;

import io.terminus.common.model.Response;
import moonstone.common.model.Either;
import moonstone.item.model.Item;

import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-15
 */
public interface ItemDumpService {
    /**
     * 全量dump商品
     */
    Response<Boolean> fullDump();

    /**
     * 增量dump商品
     *
     * @param interval 间隔时间(分钟)
     */
    Response<Boolean> deltaDump(Integer interval);

    /**
     * 对某个特定商品进行更新
     *
     * @param itemId 商品的id
     * @return 是否更新成功
     */
    Either<Boolean> updateDumpByItemId(Long itemId);

    /**
     * 隐藏商品
     * 仅仅这个应用生命周期有效
     */
    boolean hideItemAtThisLife(Long itemId);

    /**
     * 对应这个商品隐藏让他显示
     */
    boolean showItem(Long itemId);

    /**
     * 订正item 的 index 这个排序用的字段
     *
     * @param shopId
     * @return index发生了更新的商品
     */
    List<Item> fixItemSortIndex(Long shopId);
}
