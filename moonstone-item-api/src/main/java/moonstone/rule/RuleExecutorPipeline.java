/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 规则执行器注册中心
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-27
 */
@Component
class RuleExecutorPipeline {

    private final List<RuleExecutor> ruleExecutors = new ArrayList<>();

    public List<RuleExecutor> getRuleExecutors() {
        return ruleExecutors;
    }

    /**
     * 清空所有的规则
     */
    public void clear(){
        ruleExecutors.clear();
    }

    /**
     * 批量添加规则
     *
     * @param ruleExecutors   待添加的规则
     */
    protected void addAll(List<RuleExecutor> ruleExecutors){
        ruleExecutors.addAll(ruleExecutors);
    }


    /**
     * 单个添加规则
     *
     * @param ruleExecutor 待添加的规则
     */
    protected void add(RuleExecutor ruleExecutor){
        ruleExecutors.add(ruleExecutor);
    }

}
