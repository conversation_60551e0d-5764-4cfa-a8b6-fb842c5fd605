/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.spu.service;

import io.terminus.common.model.Response;
import moonstone.spu.dto.FullSpu;

import java.util.Map;

/**
 * Spu写服务
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-21
 */
public interface SpuWriteService {

    /**
     * 创建spu
     *
     * @param fullSpu 待创建的Spu, 这里已经过各种校验,且Spu信息已经完善
     * @return 新创建的Spuid
     */
    Response<Long> create(FullSpu fullSpu);

    /**
     * 更新Spu
     *
     * @param fullSpu 待更新的Spu, 这里已经过各种校验,且Spu信息已经完善
     * @return 是否更新成功
     */
    Response<Boolean> update(FullSpu fullSpu);


    /**
     * 删除Spu, 先都逻辑删除吧
     *
     * @param spuId 待删除的spu id
     * @return 是否删除成功
     */
    Response<Boolean> delete(Long spuId);


    /**
     * 编辑Spu详情富文本
     *
     * @param spuId  Spu id
     * @param richText   详情富文本
     * @return  是否编辑成功
     */
    Response<Boolean> editRichText(Long spuId, String richText);

    /**
     * 更新spu的标记
     *
     * @param id  spu id
     * @param extras   附加信息
     * @return  是否更新成功
     */
    Response<Boolean> extras(Long id, Map<String, String> extras);
}
