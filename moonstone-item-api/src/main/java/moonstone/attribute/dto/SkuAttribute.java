/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.attribute.dto;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import moonstone.common.enums.ImageUrlTransformType;
import moonstone.common.utils.ImageUrlHandler;

import java.io.Serializable;

/**
 * sku属性, 不是笛卡尔积, 对应商品属性表parana_item_attributes中的sku_attributes
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2015-12-11
 */
@EqualsAndHashCode(of = {"attrKey", "attrVal"})
@ToString
public class SkuAttribute implements Serializable {

    private static final long serialVersionUID = -4013141544103415365L;

    /**
     * 属性所属组 , 如颜色
     */
    @Getter
    @Setter
    private String attrKey;

    /**
     * 属性值, 如红色
     */
    @Getter
    @Setter
    private String attrVal;

    /**
     * 计量单位
     */
    @Getter
    @Setter
    private String unit;


    /**
     * 决定是否sku图片是否展现
     */
    @Getter
    @Setter
    private Boolean showImage;


    /**
     * 属性对应的缩略图
     */
    @Getter
    @Setter
    private String thumbnail;

    /**
     * 属性对应的原图
     */
    @Getter
    @Setter
    private String image;

    /**
     * 处理sku属性中的图片url
     *
     * @param skuAttribute  待处理的sku属性
     * @param transformType 转换类型
     * @return 图片url转化后的sku属性
     */
    public static SkuAttribute transform(SkuAttribute skuAttribute, ImageUrlTransformType transformType) {
        SkuAttribute transformedSkuAttribute = new SkuAttribute();
        transformedSkuAttribute.setAttrKey(skuAttribute.getAttrKey());
        transformedSkuAttribute.setAttrVal(skuAttribute.getAttrVal());
        transformedSkuAttribute.setUnit(skuAttribute.getUnit());
        transformedSkuAttribute.setShowImage(skuAttribute.getShowImage());
        transformedSkuAttribute.setImage(ImageUrlHandler.handle(skuAttribute.getImage(), transformType));
        transformedSkuAttribute.setThumbnail(ImageUrlHandler.handle(skuAttribute.getThumbnail(), transformType));
        return transformedSkuAttribute;
    }

}
