/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.attribute.dto;

import com.google.common.base.Strings;
import io.terminus.common.utils.Splitters;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 允许的属性值类型, 如果是数值类型, 还可以设置计量单位
 *
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-23
 */
public class AttributeValueType implements Serializable {

    private static final long serialVersionUID = -3545653259497157936L;

    public enum ValueType {
        TEXT,
        NUMBER,
        DATE
    }

    /**
     * 值类型
     */
    @Getter
    @Setter
    private ValueType valueType;

    /**
     * 计量单位
     */
    @Getter
    @Setter
    private String unit;

    @Override
    public String toString() {
        if(Strings.isNullOrEmpty(unit)){
            return valueType.name();
        }
        return valueType.name()+"_"+unit;
    }

    public static AttributeValueType from(String input){
        if(Strings.isNullOrEmpty(input)){
            return null;
        }
        List<String> parts =Splitters.UNDERSCORE.splitToList(input);
        AttributeValueType attrValueType = new AttributeValueType();

        attrValueType.valueType = ValueType.valueOf(parts.get(0));
        if(parts.size()>1){
            attrValueType.unit = parts.get(1);
        }
        return attrValueType;
    }
}
