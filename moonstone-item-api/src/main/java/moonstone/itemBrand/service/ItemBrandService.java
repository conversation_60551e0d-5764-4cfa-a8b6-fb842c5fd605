package moonstone.itemBrand.service;

import java.util.List;

import moonstone.common.model.vo.ComboBoxVo;
import moonstone.common.model.vo.DropDownBoxVo;
import moonstone.common.model.vo.PageVo;
import moonstone.itemBrand.dto.ItemBrandDto;
import moonstone.itemBrand.dto.ItemBrandPageDto;
import moonstone.itemBrand.model.ItemBrand;

/**
 * @Author: yousx
 * @Date: 2025/02/21
 * @Description:
 */
public interface ItemBrandService {

    void save(ItemBrand itemBrand);

    void delete(Long id);

    PageVo<ItemBrandDto> pages(ItemBrandPageDto itemBrandPageDto);

    List<ComboBoxVo> getDropDownBox(Long shopId);

    ItemBrandDto findByName(String name, Long shopId);

    ItemBrandDto findById(Long id);

    void update(ItemBrand itemBrand);
}
