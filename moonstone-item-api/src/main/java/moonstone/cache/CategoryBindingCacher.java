/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.category.model.BackCategory;
import moonstone.category.service.CategoryBindingReadService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 前后台类目绑定缓存, 可以从前台类目快速查找到绑定的后台类目
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-23
 */
@Component
@Slf4j
public class CategoryBindingCacher {

    @RpcConsumer
    private CategoryBindingReadService categoryBindingReadService;

    private LoadingCache<Long, List<BackCategory>> bindingCache;

    @Value("${cache.duration.in.minutes: 60}")
    private Integer duration;

    @PostConstruct
    public void init() {
        this.bindingCache = Caffeine.newBuilder()
                .expireAfterAccess(duration, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build(frontCategoryId -> {
                    Response<List<BackCategory>> r = categoryBindingReadService.findByFrontCategoryId(frontCategoryId);
                    if (!r.isSuccess()) {
                        log.error("failed to find responding back categories for front category(id={}), error code:{}",
                                frontCategoryId, r.getError());
                        throw new ServiceException(r.getError());
                    }
                    return r.getResult();
                });
    }

    /**
     * 根据前台类目id查找绑定的后台类目id列表
     *
     * @param frontCategoryId  前台类目id
     * @return   后台类目id列表
     */
    public List<Long> findByFrontCategoryId(Long frontCategoryId){
        List<BackCategory> backCategories = Optional.ofNullable(this.bindingCache.get(frontCategoryId)).orElseGet(ArrayList::new);
        List<Long> backCategoryIds = Lists.newArrayListWithCapacity(backCategories.size());
        for (BackCategory backCategory : backCategories) {
            backCategoryIds.add(backCategory.getId());
        }
        return backCategoryIds;
    }

    /**
     * 根据前台类目id查找绑定的后台类目列表
     *
     * @param frontCategoryId  前台类目id
     * @return  绑定的后台类目列表
     */
    public List<BackCategory> findBindingByFrontCategoryId(Long frontCategoryId){
        return this.bindingCache.get(frontCategoryId);
    }


}
