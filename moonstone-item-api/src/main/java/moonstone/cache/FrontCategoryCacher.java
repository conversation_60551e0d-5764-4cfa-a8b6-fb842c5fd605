/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Splitters;
import lombok.extern.slf4j.Slf4j;
import moonstone.category.dto.FrontCategoryTree;
import moonstone.category.model.FrontCategory;
import moonstone.category.model.FrontCategoryTreeVO;
import moonstone.category.model.FrontCategoryVO;
import moonstone.category.model.ShopCategory;
import moonstone.category.service.FrontCategoryReadService;
import moonstone.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 前台类目缓存
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-19
 */
@Component
@Slf4j
public class FrontCategoryCacher {

    @RpcConsumer
    private FrontCategoryReadService frontCategoryReadService;

    private LoadingCache<String, List<FrontCategoryTree>> frontCategoryCache;

    private LoadingCache<Long, List<FrontCategory>> childrenCache;

    @Value("${cache.duration.in.minutes: 20}")
    private Integer duration;

    @PostConstruct
    public void init() {
        this.frontCategoryCache = Caffeine.newBuilder()
                .maximumSize(10000)
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .build(ids -> {
                    if (StringUtils.isBlank(ids)) {
                        return Collections.emptyList();
                    }

                    List<String> parts = Splitters.COMMA.splitToList(ids);
                    List<Long> fcIds = new ArrayList<>(parts.size());
                    for (String part : parts) {
                        fcIds.add(Long.parseLong(part));
                    }
                    return buildForest(fcIds);
                });

        this.childrenCache = Caffeine.newBuilder()
                .expireAfterWrite(Math.max(1, duration / 12), TimeUnit.MINUTES)
                .build(categoryId -> {
                    Response<List<FrontCategory>> rBcChildren = frontCategoryReadService.findChildrenByPid(categoryId);
                    if (!rBcChildren.isSuccess()) {
                        log.error("failed to find children of front category(id={}), error code:{}",
                                categoryId, rBcChildren.getError());
                        throw new ServiceException("find front category children fail,code: " + rBcChildren.getError());
                    }
                    return rBcChildren.getResult();
                });

    }

    /**
     * 构建前台类目森林
     *
     * @param ids 根id列表
     * @return 前台类目森林
     */
    private List<FrontCategoryTree> buildForest(List<Long> ids) {
        List<FrontCategory> frontCategories = findByIdsWithOrder(ids);
        List<FrontCategoryTree> result = new ArrayList<>(frontCategories.size());
        for (FrontCategory frontCategory : frontCategories) {
            result.add(buildTree(frontCategory));
        }
        return result;
    }

    /**
     * 根据id列表查询前台类目，返回的前台类目列表顺序跟ids列表保持一致
     *
     * @param ids 前台类目id列表
     * @return 前台类目列表
     */
    private List<FrontCategory> findByIdsWithOrder(List<Long> ids) {
        Response<List<FrontCategory>> r = frontCategoryReadService.findByIds(ids);
        if (!r.isSuccess()) {
            log.error("failed to find front categories(ids={}), error code:{}",
                    ids, r.getError());
            throw new ServiceException(r.getError());
        }
        Map<Long, FrontCategory> frontCategoriesIndexedById = new HashMap<>();
        r.getResult().parallelStream().forEach(category -> {
            if (category != null)
                frontCategoriesIndexedById.put(category.getId(), category);
        });

        List<FrontCategory> frontCategories = new ArrayList<>(ids.size());
        for (Long id : ids) {
            FrontCategory frontCategory = frontCategoriesIndexedById.get(id);
            if (frontCategory != null) {
                frontCategories.add(frontCategory);
            }
        }
        return frontCategories;
    }

    /**
     * 递归构建前台类目树
     *
     * @param frontCategory 前台类目
     * @return 对应的前台类目树
     */
    private FrontCategoryTree buildTree(FrontCategory frontCategory) {

        FrontCategoryTree frontCategoryTree = new FrontCategoryTree();
        frontCategoryTree.setCurrent(frontCategory);

        final Long frontCategoryId = frontCategory.getId();
        Response<List<FrontCategory>> r = frontCategoryReadService.findChildrenByPid(frontCategoryId);
        if (!r.isSuccess()) {
            log.error("failed to find children of front categories(id={}), error code:{}",
                    frontCategoryId, r.getError());
            throw new ServiceException(r.getError());
        }
        List<FrontCategoryTree> children = frontCategoryTree.getChildren();
        for (FrontCategory fc : r.getResult()) {
            children.add(buildTree(fc));
        }
        return frontCategoryTree;
    }

    /**
     * 根据id列表构建前台类目森林
     *
     * @param ids 前台类目id列表
     * @return 前台类目森林，若ids为空，则默认返回所有的前台类目森林
     */
    public List<FrontCategoryTree> findByIds(String ids) {
        //若ids为空，则默认返回所有的前台类目森林
        if (ObjectUtils.isEmpty(ids)) {
            List<FrontCategory> childrens = childrenCache.get(ShopCategory.ID_ROOT);
            if (!CollectionUtils.isEmpty(childrens)) {
                StringBuffer childrenIds = new StringBuffer();
                for (int i = 0; i < childrens.size(); i++) {
                    childrenIds.append(childrens.get(i).getId());
                    if (i < childrens.size() - 1) {
                        childrenIds.append(",");
                    }
                }
                ids = childrenIds.toString();
            }
        }
        return frontCategoryCache.get(ids);
    }


    public List<FrontCategory> findChildrenOf(Long id) {
        return childrenCache.get(id);
    }

    public void invalidate(Long id) {
        childrenCache.invalidate(id);
    }

    /**
     * 获取分类树（VO版）
     *
     * @return
     */
    public List<FrontCategoryTreeVO> findFrontCategoryTree(Long pid) {
        return buildForestVO(pid);
    }

    /**
     * 简化版的分类树
     *
     * @return
     */
    private List<FrontCategoryTreeVO> buildForestVO(Long pid) {
        List<FrontCategory> frontCategories = childrenCache.get(pid);
        List<FrontCategoryTreeVO> result = new ArrayList<>(frontCategories.size());
        for (FrontCategory frontCategory : frontCategories) {
            result.add(buildTreeVO(frontCategory));
        }
        return result;
    }

    private FrontCategoryTreeVO buildTreeVO(FrontCategory frontCategory) {

        FrontCategoryTreeVO frontCategoryTree = new FrontCategoryTreeVO();
        FrontCategoryVO frontCategoryVO = new FrontCategoryVO();
        frontCategoryVO.setId(frontCategory.getId());
        frontCategoryVO.setLogo(frontCategory.getLogo_());
        frontCategoryVO.setName(frontCategory.getName());
        frontCategoryTree.setCurrent(frontCategoryVO);
        final Long frontCategoryId = frontCategory.getId();
        List<FrontCategory> r = childrenCache.get(frontCategoryId);
        List<FrontCategoryTreeVO> children = frontCategoryTree.getChildren();
        for (FrontCategory fc : r) {
            children.add(buildTreeVO(fc));
        }
        return frontCategoryTree;
    }

    public List<FrontCategory> findByPid(long pid) {
        return childrenCache.get(pid);
    }
}
