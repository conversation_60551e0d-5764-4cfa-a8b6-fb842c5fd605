package moonstone.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.Splitter;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.adv.dto.AdvDTO;
import moonstone.adv.model.Adv;
import moonstone.adv.service.AdvReadService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by CaiZhy on 2018/10/17.
 */
@Slf4j
@Component
public class AdvCacher {
    private LoadingCache<String, List<AdvDTO>> advCacher;

    @RpcConsumer
    private AdvReadService advReadService;

    @Value("${cache.duration.in.minutes: 15}")
    private Integer duration;

    @PostConstruct
    public void init() {
        this.advCacher = Caffeine.newBuilder()
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build(key -> {
                    List<String> keys = Splitter.on(":").splitToList(key);
                    String columnSn = keys.get(0);
                    Long shopId;
                    if (keys.size() < 2 || keys.get(1).equals("null")) {
                        shopId = null;
                    } else {
                        shopId = Long.valueOf(keys.get(1));
                    }
                    Response<List<AdvDTO>> rAdvDTOs = advReadService.listByColumnSnAndShopId(columnSn, shopId);
                    if (!rAdvDTOs.isSuccess()) {
                        log.error("failed to list advs by key={}, error code:{}",
                                key, rAdvDTOs.getError());
                        throw new ServiceException("list advs fail,error code: " + rAdvDTOs.getError());
                    }
                    return rAdvDTOs.getResult();
                });
    }

    /**
     * 根据广告位编码和店铺id获取广告图
     * @param shopId
     * @return
     */
    public List<AdvDTO> listByColumnSnAndShopId(String columnSn, Long shopId){
        return advCacher.get(columnSn + ":" + shopId);
    }

    /**
     * 根据shopIds获取AdvDTO
     * @param shopIds 获取的列表
     * @return 返回AdvDTO列表
     */
    public List<AdvDTO> listByShopIds(List<Long> shopIds)
    {
        List<AdvDTO> advDTOList=new ArrayList<>();
        StringBuilder sb=new StringBuilder();
        sb.append("[");
        for (int i=0;i<shopIds.size();i++
             ) {
            sb.append(shopIds.get(i));
            if (i+1<shopIds.size())
                sb.append(",");
        }
        sb.append("]");
        if (advCacher.asMap().containsKey(sb.toString()))//contain 貌似不适用于String 因为没有重载hashcode的话
        {
            return advCacher.asMap().get(sb.toString());
        }

       Response <List<Adv>>response= advReadService.findByShopIds(shopIds);
       if (!response.isSuccess()) {
           return new ArrayList<>();
       }
       List<Adv> advList=response.getResult().stream().filter(Objects::nonNull).collect(Collectors.toList());
       for(Adv adv:advList)
       {
           AdvDTO advDTO=new AdvDTO();
           advDTO.setId(adv.getId());
           advDTO.setImg(adv.getImg());
           advDTO.setUrl(adv.getUrl());
           advDTO.setSortIndex(advDTOList.size());
           advDTOList.add(advDTO);
       }
       advCacher.put(sb.toString(),advDTOList);//感觉会内存爆炸
       return advDTOList;
    }
    /**
     * 用于清空对应广告缓存
     */
    public void clearAdvCache(String columnSn, Long shopId){
        advCacher.invalidate(columnSn + ":" + shopId);
    }
}
