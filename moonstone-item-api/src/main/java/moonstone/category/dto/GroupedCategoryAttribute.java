/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.dto;

import lombok.Data;
import moonstone.category.model.CategoryAttribute;

import java.io.Serializable;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-20
 */
@Data
public class GroupedCategoryAttribute implements Serializable {
    private static final long serialVersionUID = -7116186367791114999L;

    private String group;

    private Iterable<CategoryAttribute> categoryAttributes;
}
