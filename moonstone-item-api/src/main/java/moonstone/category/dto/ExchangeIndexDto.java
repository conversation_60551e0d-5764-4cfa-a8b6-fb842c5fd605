/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 交换两个属性的相对位置
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-18
 */
@Data
public class ExchangeIndexDto implements Serializable {

    private static final long serialVersionUID = 4309909274133471011L;

    /**
     * 类目id
     */
    private  Long categoryId;

    /**
     * 属性id1
     */
    private  Long attributeId1;

    /**
     * 属性id2
     */
    private Long attributeId2;

    /**
     * 属性id1的新序号
     * @deprecated 由后端查询决定
     */
    @Deprecated
    private Integer attributeId1Index;

    /**
     * 属性id2的新序号
     * @deprecated 由后端查询决定
     */
    @Deprecated
    private Integer attributeId2Index;
}
