/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.component.dto.item;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import moonstone.common.model.vo.DropDownBoxVo;
import moonstone.delivery.model.ItemDeliveryFee;
import moonstone.item.dto.SkuWithCustom;
import moonstone.item.emu.CashGiftUnitEnum;
import moonstone.item.model.Item;
import moonstone.item.model.ItemDetail;
import moonstone.item.model.Sku;
import moonstone.rule.dto.BaseOutput;
import moonstone.rule.dto.GeneralSku;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 平台强管控的商品,会对待编辑的商品属性做规则校验
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-24
 */
@EqualsAndHashCode(of = {"item", "itemDetail"}, callSuper = true)
public class EditItem extends BaseOutput {
    private static final long serialVersionUID = 8812263263491749636L;

    /**
     * 商品信息
     */
    @Getter
    @Setter
    private Item item;

    /**
     * 商品运费
     */
    @Getter
    @Setter
    private ItemDeliveryFee itemDeliveryFee;

    /**
     * sku信息
     */
    @Getter
    @Setter
    private List<SkuWithCustom> skuWithCustoms;

    /**
     * 辅图信息
     */
    @Getter
    @Setter
    private ItemDetail itemDetail;

    /**
     * 一级佣金率
     */
    @Getter
    @Setter
    Long firstRate;

    /**
     * 一级佣金
     */
    @Getter
    @Setter
    Long firstFee;
    /**
     * 二级佣金率
     */
    @Getter
    @Setter
    Long secondRate;
    /**
     * 二级佣金
     */
    @Getter
    @Setter
    Long secondFee;

    @Getter
    @Setter
    Long serviceProviderFee;

    @Getter
    @Setter
    Long serviceProviderRate;

    /**
     *复购佣金
     */
    @Getter
    @Setter
    Long commission;
    /**
     * 首购佣金
     */
    @Getter
    @Setter
    Long firstCommission;
    /**
     * 是否开启比率和固定金额 0 固定 1比率
     */
    @Getter
    @Setter
    Integer flag;


    /**
     * 是否开启单品拉新佣金 0-不开启 1-开启
     */
    @Getter
    @Setter
    Integer isCommission;

    /**
     * 是否使用千分制度
     */
    @Getter
    @Setter
    Boolean isThousandMode;


    @Getter
    @Setter
    Integer isCashGift; //是否开启礼金 0不开启 1开启

    @Getter
    @Setter
    BigDecimal maxDeduction;

    /**
     * @see CashGiftUnitEnum
     */
    @Getter
    @Setter
    Integer unit;

    @Getter
    @Setter
    Long cashGiftUseTimeStart;

    @Getter
    @Setter
    Long cashGiftUseTimeEnd;

    /**
     * 活动佣金配置
     */
    @Getter
    @Setter
    private ItemCommissionConfigDTO activityCommissionConfig;


    /**
     * 后台完整类目
     */
    @Getter
    @Setter
    private List<DropDownBoxVo> backCategoryList;


    /**
     * 设置处理后的sku或者skuTemplate
     *
     * @param generalSkus sku或者skuTemplate
     */
    @Override
    @SuppressWarnings("unchecked")
    public void setGeneralSku(List<? extends GeneralSku> generalSkus) {
        List<SkuWithCustom> skuWithCustoms = new ArrayList<>();
        for (GeneralSku generalSku : generalSkus) {
            SkuWithCustom skuWithCustom = new SkuWithCustom();
            skuWithCustom.setSku((Sku) generalSku);
            skuWithCustoms.add(skuWithCustom);
        }
        setSkuWithCustoms(skuWithCustoms);
    }
}
