package moonstone.search.shop.impl;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.search.api.Searcher;
import io.terminus.search.api.model.WithAggregations;
import io.terminus.search.api.query.Criterias;
import lombok.extern.slf4j.Slf4j;
import moonstone.search.dto.SearchedShopWithAggs;
import moonstone.search.shop.SearchShopProperties;
import moonstone.search.shop.ShopSearchReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 店铺搜索服务
 * Author:cp
 * Created on 7/25/16.
 */
@Service
@RpcProvider
@Slf4j
public class ShopSearchReadServiceImpl implements ShopSearchReadService {

    private final SearchShopProperties searchShopProperties;
    private final Searcher searcher;
    private final BaseShopQueryBuilder shopQueryBuilder;

    @Autowired
    public ShopSearchReadServiceImpl(SearchShopProperties searchShopProperties,
                                     Searcher searcher,
                                     BaseShopQueryBuilder shopQueryBuilder) {
        this.searchShopProperties = searchShopProperties;
        this.searcher = searcher;
        this.shopQueryBuilder = shopQueryBuilder;
    }


    @Override
    public <T> Response<SearchedShopWithAggs<T>> searchWithAggs(Integer pageNo, Integer pageSize,
                                                                String templateName, Map<String, String> params,
                                                                Class<T> clazz) {
        try {
            String q = params.get("q"); //搜索关键字
            if (StringUtils.hasText(q)) {
                params.put("highlight", "name");
            }

            Criterias criterias = shopQueryBuilder.makeCriterias(pageNo, pageSize, params);
            WithAggregations<T> withAggs = searcher.searchWithAggs(
                    searchShopProperties.getIndexName(),
                    searchShopProperties.getIndexType(),
                    templateName,
                    criterias,
                    clazz);

            SearchedShopWithAggs searchWithAggs = new SearchedShopWithAggs();
            Paging<T> entities = new Paging<>(withAggs.getTotal(), withAggs.getData());
            searchWithAggs.setEntities(entities);

            return Response.ok(searchWithAggs);
        } catch (Exception e) {
            log.error("fail to search shops,pageNo={},pageSize={},templateName={},params={},cause:{}",
                    pageNo, pageSize, templateName, params, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.search.fail");
        }
    }
}
