package moonstone.search.shop.impl;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import io.terminus.common.utils.Splitters;
import io.terminus.search.api.query.*;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * Author:cp
 * Created on 7/25/16.
 */
public class DefaultShopQueryBuilder extends BaseShopQueryBuilder {
    @Override
    public List<moonstone.dto.Terms> buildInTerms(Map<String, String> params) {
        return null;
    }

    @Override
    public Keyword buildKeyword(Map<String, String> params) {
        String q = params.get("q");
        if (StringUtils.hasText(q)) {
            return new Keyword(ImmutableList.of("name"), q);
        }
        return null;
    }

    @Override
    public List<Term> buildTerm(Map<String, String> params) {
        return null;
    }

    @Override
    public List<Terms> buildTerms(Map<String, String> params) {
        return null;
    }

    @Override
    public List<Range> buildRanges(Map<String, String> params) {
        return null;
    }

    @Override
    public List<Aggs> buildAggs(Map<String, String> params) {
        return null;
    }

    @Override
    public List<Sort> buildSort(Map<String, String> params) {
        return null;
    }

    @Override
    public List<Highlight> buildHighlight(Map<String, String> params) {
        String highlight = params.get("highlight");
        if (StringUtils.hasText(highlight)) {
            List<String> fields = Splitters.UNDERSCORE.splitToList(highlight);
            List<Highlight> highlights = Lists.newArrayListWithCapacity(fields.size());
            for (String field : fields) {
                highlights.add(new Highlight(field));
            }
            return highlights;
        }
        return null;
    }
}
