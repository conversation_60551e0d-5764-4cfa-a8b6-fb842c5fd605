package moonstone.search.shop.impl;

import io.terminus.common.utils.BeanMapper;
import moonstone.search.dto.IndexedShop;
import moonstone.shop.model.Shop;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;

/**
 * Author:cp
 * Created on 7/25/16.
 */
public abstract class BaseIndexedShopFactory<T extends IndexedShop> implements IndexedShopFactory<T> {

    protected final Class<T> clazz;

    protected BaseIndexedShopFactory() {

        final Type genericSuperclass = getClass().getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType) {
            clazz = ((Class<T>) ((ParameterizedType) genericSuperclass).getActualTypeArguments()[0]);
        } else {
            //解决cglib实现aop时转换异常
            clazz = ((Class<T>) ((ParameterizedType) getClass().getSuperclass().getGenericSuperclass())
                    .getActualTypeArguments()[0]);
        }
    }

    @Override
    public T create(Shop shop, Object... others) {
        T indexedShop = BeanMapper.map(shop, clazz);
        indexedShop.setImageUrl(shop.getImageUrl());
        Map<String, String> extra = shop.getExtra();
        if (!CollectionUtils.isEmpty(extra)) {
            if (extra.containsKey("contactPhone")) {
                indexedShop.setPhone(extra.get("contactPhone"));
            }
            fillAddresses(indexedShop, extra);
        }
        return indexedShop;
    }

    private void fillAddresses(T indexedShop, Map<String, String> extra) {
        if (extra.containsKey("provinceId")) {
            indexedShop.setProvinceId(Integer.parseInt(extra.get("provinceId")));
        }
        if (extra.containsKey("province")) {
            indexedShop.setProvince(extra.get("province"));
        }
        if (extra.containsKey("cityId")) {
            indexedShop.setCityId(Integer.parseInt(extra.get("cityId")));
        }
        if (extra.containsKey("city")) {
            indexedShop.setCity(extra.get("city"));
        }
        if (extra.containsKey("regionId")) {
            indexedShop.setRegionId(Integer.parseInt(extra.get("regionId")));
        }
        if (extra.containsKey("region")) {
            indexedShop.setRegion(extra.get("region"));
        }
        if (extra.containsKey("street")) {
            indexedShop.setStreet(extra.get("street"));
        }
    }
}
