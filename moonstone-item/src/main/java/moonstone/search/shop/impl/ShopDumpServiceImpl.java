package moonstone.search.shop.impl;

import com.google.common.base.Stopwatch;
import com.google.common.base.Throwables;
import com.google.common.collect.Iterables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import io.terminus.search.api.IndexExecutor;
import io.terminus.search.api.model.IndexTask;
import lombok.extern.slf4j.Slf4j;
import moonstone.search.dto.IndexedShop;
import moonstone.search.shop.SearchShopProperties;
import moonstone.search.shop.ShopDumpService;
import moonstone.shop.impl.dao.ShopDao;
import moonstone.shop.model.Shop;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Author:cp
 * Created on 7/25/16.
 */
@Component
@RpcProvider
@Slf4j
public class ShopDumpServiceImpl implements ShopDumpService {

    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    private final ShopDao shopDao;

    private final SearchShopProperties searchShopProperties;

    private final IndexExecutor indexExecutor;

    private final IndexedShopFactory indexedShopFactory;

    private final IndexedShopIndexAction indexedShopIndexAction;

    private final IndexedShopGuarder indexedShopGuarder;

    @Autowired
    public ShopDumpServiceImpl(ShopDao shopDao,
                               SearchShopProperties searchShopProperties,
                               IndexExecutor indexExecutor,
                               IndexedShopFactory indexedShopFactory,
                               IndexedShopIndexAction indexedShopIndexAction,
                               IndexedShopGuarder indexedShopGuarder) {
        this.shopDao = shopDao;
        this.searchShopProperties = searchShopProperties;
        this.indexExecutor = indexExecutor;
        this.indexedShopFactory = indexedShopFactory;
        this.indexedShopIndexAction = indexedShopIndexAction;
        this.indexedShopGuarder = indexedShopGuarder;
    }

    @Override
    public Response<Boolean> fullDump() {
        try {
            Integer fullDumpRange = searchShopProperties.getFullDumpRange();
            if (fullDumpRange <= 0) {
                fullDumpRange = 365 * 3;
            }
            String since = DATE_TIME_FORMAT.print(DateTime.now()
                    .withTimeAtStartOfDay()
                    .minusDays(fullDumpRange));

            log.info("shop full dump start");
            Stopwatch watch = Stopwatch.createStarted();
            int allIndexed = doIndex(since);
            watch.stop();
            log.info("shop full dump end, cost:{} ms, dumped {} shops",
                    watch.elapsed(TimeUnit.MILLISECONDS), allIndexed);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to full dump shops,cause:{}", Throwables.getStackTraceAsString(e));
            return Response.fail("shop.full.dump.fail");
        }
    }

    @Override
    public Response<Boolean> deltaDump(Integer interval) {
        try {
            // interval分钟前的时间
            String since = DATE_TIME_FORMAT.print(new DateTime().minusMinutes(interval));

            log.info("shop delta dump start");
            Stopwatch watch = Stopwatch.createStarted();
            int allIndexed = doIndex(since);
            watch.stop();
            log.info("shop delta dump end,cost:{} ms, dumped {} shops",
                    watch.elapsed(TimeUnit.MILLISECONDS), allIndexed);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to delta dump shops with interval:{},cause:{}",
                    interval, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.delta.dump.fail");
        }
    }

    private int doIndex(String since) {
        // 最大的店铺id
        Long lastId = shopDao.maxId() + 1;

        // 计数本次full dump的店铺数
        int allIndexed = 0;
        while (true) {
            List<Shop> shops = shopDao.listSince(lastId, since, searchShopProperties.getBatchSize());
            if (Iterables.isEmpty(shops)) {
                break;
            }

            for (Shop shop : shops) {
                try {
                    if (indexedShopGuarder.indexable(shop)) {  //更新
                        IndexedShop indexedShop = indexedShopFactory.create(shop);
                        IndexTask indexTask = indexedShopIndexAction.indexTask(indexedShop);
                        indexExecutor.submit(indexTask);
                    } else { //删除
                        IndexTask indexTask = indexedShopIndexAction.deleteTask(shop.getId());
                        indexExecutor.submit(indexTask);
                    }
                } catch (Exception e) {
                    log.error("failed to index shop(id={}),cause:{}", shop.getId(), Throwables.getStackTraceAsString(e));
                }
            }

            allIndexed += shops.size();
            log.info("has indexed {} shops,and last handled id is {}", allIndexed, lastId);
            lastId = Iterables.getLast(shops).getId();
            // 已经是最后一批
            if (shops.size() < searchShopProperties.getBatchSize()) {
                break;
            }
        }
        return allIndexed;
    }
}
