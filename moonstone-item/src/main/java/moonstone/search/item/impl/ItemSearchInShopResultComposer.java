package moonstone.search.item.impl;

import io.terminus.search.api.model.WithAggregations;
import moonstone.search.dto.SearchedItemInShopWithAggs;

import java.util.Map;

/**
 * 店铺内搜索结果组装器
 * Author:cp
 * Created on 10/11/2016.
 */
public interface ItemSearchInShopResultComposer<T extends SearchedItemInShopWithAggs> {

    /**
     * 组装店铺内搜索结果
     *
     * @param withAggs 原始的搜索结果
     * @param params   参数
     * @param context  上下文
     * @return 组装好的商品信息
     */
    T compose(WithAggregations withAggs, Map<String, String> params, Map<String, Object> context);

}
