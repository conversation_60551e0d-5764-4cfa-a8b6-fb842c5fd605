package moonstone.search.item.impl;

import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import io.terminus.common.model.Paging;
import io.terminus.search.api.model.WithAggregations;
import io.terminus.search.model.Bucket;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCategoryCacher;
import moonstone.category.model.ShopCategory;
import moonstone.search.dto.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * Author:cp
 * Created on 10/11/2016.
 */
@Slf4j
public class DefaultItemSearchInShopResultComposer extends BaseItemSearchResultComposer implements ItemSearchInShopResultComposer<SearchedItemInShopWithAggs> {

    private final ShopCategoryCacher shopCategoryCacher;

    private static final String SHOP_CATEGORY_AGGS = "shop_category_aggs";

    public DefaultItemSearchInShopResultComposer(ShopCategoryCacher shopCategoryCacher) {
        this.shopCategoryCacher = shopCategoryCacher;
    }

    public SearchedItemInShopWithAggs compose(WithAggregations withAggs,
                                              Map<String, String> params,
                                              Map<String, Object> context) {
        SearchedItemInShopWithAggs searchWithAggs = new SearchedItemInShopWithAggs();

        //处理搜索结果
        Paging entities = new Paging<>(withAggs.getTotal(), withAggs.getData());
        searchWithAggs.setEntities(entities);

        //处理聚合
        Map<String, List<Bucket>> aggregations = withAggs.getAggregations();

        //处理属性聚合
        String chosenAttrs = params.get("attrs");
        List<Bucket> attrAggs = aggregations.get(ATTR_AGGS);
        List<GroupedAggNav> attrNavs = makeGroupedAttrNavs(attrAggs, chosenAttrs);
        searchWithAggs.setAttributes(attrNavs);

        //处理店铺内类目聚合
        String chosenShopCatId = params.get("shopCatId");
        List<Bucket> shopCategoryAggs = aggregations.get(SHOP_CATEGORY_AGGS);
        List<AggNav> shopCategoryNavs = makeShopCategoryNavs(shopCategoryAggs, chosenShopCatId);
        searchWithAggs.setShopCategories(shopCategoryNavs);

        //处理面包屑
        Long chosenShopCategoryId = 0L;
        if (StringUtils.hasText(chosenShopCatId)) {
            chosenShopCategoryId = Long.valueOf(chosenShopCatId);
        } else if (shopCategoryNavs != null && shopCategoryNavs.size() == 1) {
            chosenShopCategoryId = (Long) shopCategoryNavs.get(0).getKey();
        }
        List<IdAndName> breadCrumbs = makeBreadCrumbsOfShopCategory(chosenShopCategoryId);
        searchWithAggs.setBreadCrumbs(breadCrumbs);

        //处理已选择的属性
        List<Chosen> chosens = Lists.newArrayList();
        addChosenAttributes(chosenAttrs, chosens);

        searchWithAggs.setChosen(chosens);
        return searchWithAggs;
    }

    /**
     * 构造店铺类目面包屑
     *
     * @param currentShopCategoryId 当前店铺类目id
     * @return 面包屑
     */
    private List<IdAndName> makeBreadCrumbsOfShopCategory(Long currentShopCategoryId) {
        List<IdAndName> idAndNames = Lists.newArrayList();
        while (currentShopCategoryId > 0) {
            ShopCategory shopCategory = shopCategoryCacher.findById(currentShopCategoryId);
            if (shopCategory != null && shopCategory.getId() > 0) {
                log.warn("shopCategoryId[{}] is invalidate", currentShopCategoryId);
                idAndNames.add(new IdAndName(shopCategory.getId(), shopCategory.getName()));
                currentShopCategoryId = shopCategory.getPid();
            } else {
                currentShopCategoryId = -1L;
            }
        }
        List<IdAndName> breadCrumbs = Lists.newArrayListWithCapacity(idAndNames.size() + 1);
        breadCrumbs.add(new IdAndName(0L, "所有分类"));
        breadCrumbs.addAll(Lists.reverse(idAndNames));
        return breadCrumbs;
    }

    private List<AggNav> makeShopCategoryNavs(List<Bucket> shopCategoryAggs, String chosenShopCatId) {
        if (CollectionUtils.isEmpty(shopCategoryAggs)) {
            return null;
        }

        List<AggNav> shopCategoryNavs = Lists.newArrayListWithCapacity(shopCategoryAggs.size());
        if (StringUtils.hasText(chosenShopCatId)) {
            //选择了店铺内类目,若是选择未分类则不需要聚合了,否则返回下一级类目聚合
            Long chosenShopCategoryId = Long.valueOf(chosenShopCatId);
            if (Objects.equal(chosenShopCategoryId, ShopCategory.ID_UNKNOWN)) {
                return null;
            }

            for (Bucket shopCategoryAgg : shopCategoryAggs) {
                Long shopCategoryId = Long.valueOf(shopCategoryAgg.getKey());

                if (Objects.equal(shopCategoryId, chosenShopCategoryId)) {
                    continue;
                }

                ShopCategory shopCategory = shopCategoryCacher.findById(shopCategoryId);
                if (shopCategory.getId() <= 0) {
                    break;
                }
                if (Objects.equal(shopCategory.getPid(), chosenShopCategoryId)) {
                    AggNav aggNavOfNextLevel = new AggNav(shopCategoryId, shopCategory.getName(), shopCategoryAgg.getDoc_count());
                    shopCategoryNavs.add(aggNavOfNextLevel);
                }
            }

        } else {
            //未选择店铺内类目,则只返回第一级类目聚合和未分类聚合
            for (Bucket shopCategoryAgg : shopCategoryAggs) {
                Long shopCategoryId = Long.valueOf(shopCategoryAgg.getKey());

                if (Objects.equal(shopCategoryId, ShopCategory.ID_UNKNOWN)) {
                    AggNav aggNavOfUnknown = new AggNav(ShopCategory.ID_UNKNOWN, "未分类", shopCategoryAgg.getDoc_count());
                    shopCategoryNavs.add(aggNavOfUnknown);
                } else {
                    ShopCategory shopCategory = shopCategoryCacher.findById(shopCategoryId);
                    if (shopCategory == null || shopCategory.getId() <= 0) {
                        break;
                    }
                    if (Objects.equal(shopCategory.getPid(), ShopCategory.ID_ROOT)) {
                        AggNav aggNavOfFirstLevel = new AggNav(shopCategoryId, shopCategory.getName(), shopCategoryAgg.getDoc_count());
                        shopCategoryNavs.add(aggNavOfFirstLevel);
                    }
                }
            }
        }
        return shopCategoryNavs;
    }


}
