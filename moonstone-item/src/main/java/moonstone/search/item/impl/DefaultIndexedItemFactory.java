/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.search.item.impl;


import moonstone.cache.BackCategoryCacher;
import moonstone.cache.BrandCacher;
import moonstone.cache.CategoryAttributeCacher;
import moonstone.category.impl.dao.ShopCategoryItemDao;
import moonstone.countryImage.impl.dao.CountryImageDao;
import moonstone.item.api.IndexedItemProcessor;
import moonstone.item.model.Item;
import moonstone.item.model.ItemAttribute;
import moonstone.search.dto.IndexedItem;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2015-12-31
 */
public class DefaultIndexedItemFactory extends BaseIndexedItemFactory<IndexedItem> {

    @Autowired
    public DefaultIndexedItemFactory(BackCategoryCacher backCategoryCacher,
                                     ShopCategoryItemDao shopCategoryItemDao,
                                     CountryImageDao countryImageDao,
                                     BrandCacher brandCacher,
                                     CategoryAttributeCacher categoryAttributeCacher) {
        super(backCategoryCacher, shopCategoryItemDao, countryImageDao, brandCacher, categoryAttributeCacher);
    }

    @Autowired
    List<IndexedItemProcessor> indexedItemProcessors;

    /**
     * 创建dump到搜索引擎的商品对象, 包含属性, 类目等信息
     *
     * @param item          原有的商品对象
     * @param itemAttribute 商品属性信息
     * @param others        可能还需要其他信息来组装
     * @return dump到搜索引擎的商品对象
     */
    @Override
    public IndexedItem create(Item item, ItemAttribute itemAttribute, Object... others) {
        IndexedItem indexedItem = super.create(item, itemAttribute, others);
        if (indexedItemProcessors != null) {
            for (IndexedItemProcessor indexedItemProcessor : indexedItemProcessors) {
                indexedItem = indexedItemProcessor.process(indexedItem);
            }
        }
        return indexedItem;
    }
}
