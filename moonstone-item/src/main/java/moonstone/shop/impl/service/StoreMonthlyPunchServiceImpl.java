package moonstone.shop.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.shop.impl.dao.StoreMonthlyPunchDao;
import moonstone.shop.model.StoreMonthlyPunch;
import moonstone.shop.service.StoreMonthlyPunchService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * author：书生
 */
@Service
@Slf4j
public class StoreMonthlyPunchServiceImpl implements StoreMonthlyPunchService {

    @Resource
    private StoreMonthlyPunchDao storeMonthlyPunchDao;

    @Override
    public void save(StoreMonthlyPunch storeMonthlyPunch) {
        storeMonthlyPunchDao.create(storeMonthlyPunch);
    }

    @Override
    public List<StoreMonthlyPunch> list(Map<String, Object> query) {
        return storeMonthlyPunchDao.selectList(query);
    }
}
