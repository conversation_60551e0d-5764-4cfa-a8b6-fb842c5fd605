package moonstone.shop.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.shop.impl.dao.ShopPayCustomsDao;
import moonstone.shop.model.ShopPayCustoms;
import moonstone.shop.service.ShopPayCustomsReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
@RpcProvider
public class ShopPayCustomsReadServiceImpl implements ShopPayCustomsReadService{
    private final ShopPayCustomsDao shopPayCustomsDao;

    @Autowired
    public ShopPayCustomsReadServiceImpl(ShopPayCustomsDao shopPayCustomsDao) {
        this.shopPayCustomsDao = shopPayCustomsDao;
    }

    /**
     * 根据ID查找支付海关信息
     * @param id ID
     * @return 支付海关信息
     */
    @Override
    public Response<ShopPayCustoms> findById(Long id) {
        try{
            ShopPayCustoms shopPayCustoms = shopPayCustomsDao.findById(id);
            if (shopPayCustoms == null){
                log.error("failed to find ShopPayCustoms by id = {}",id);
                return Response.fail("shop.pay.customs.not.find");
            }
            return Response.ok(shopPayCustoms);
        }catch (Exception e){
            log.error("failed to find ShopPayCustoms by id = {},cause:{}",id,e.getMessage());
            return Response.fail("shop.pay.customs.not.find");
        }
    }
    /**
     * 根据支付信息ID查找海关信息列表
     * @param payInfoId 支付信息ID
     * @return 海关信息列表
     */
    @Override
    public Response<List<ShopPayCustoms>> findListByPayInfoId(Long payInfoId) {
        try{
            List<ShopPayCustoms> shopPayCustomsList = shopPayCustomsDao.findListByPayInfoId(payInfoId);
            if (CollectionUtils.isEmpty(shopPayCustomsList)){
                log.error("failed to find ShopPayCustoms by payInfoId = {}",payInfoId);
                return Response.fail("shop.pay.customs.not.find");
            }
            return Response.ok(shopPayCustomsList);
        }catch (Exception e){
            log.error("failed to find ShopPayCustoms by payInfoId = {},cause:{}",payInfoId,e.getMessage());
            return Response.fail("shop.pay.customs.not.find");
        }
    }

}
