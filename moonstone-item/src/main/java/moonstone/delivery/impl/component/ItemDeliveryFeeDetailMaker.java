package moonstone.delivery.impl.component;

import com.google.common.base.Function;
import com.google.common.base.Predicates;
import com.google.common.collect.*;
import io.terminus.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import moonstone.delivery.dto.RichItemDeliveryFee;
import moonstone.delivery.impl.dao.DeliveryFeeTemplateDao;
import moonstone.delivery.impl.dao.ItemDeliveryFeeDao;
import moonstone.delivery.impl.dao.SpecialDeliveryFeeDao;
import moonstone.delivery.model.DeliveryFeeTemplate;
import moonstone.delivery.model.ItemDeliveryFee;
import moonstone.delivery.model.SpecialDeliveryFee;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 商品运费详细信息构造器
 * Author:cp
 * Created on 8/26/16.
 */
@Component
@Slf4j
public class ItemDeliveryFeeDetailMaker {

    private final ItemDeliveryFeeDao itemDeliveryFeeDao;

    private final DeliveryFeeTemplateDao deliveryFeeTemplateDao;

    private final SpecialDeliveryFeeDao specialDeliveryFeeDao;

    @Autowired
    public ItemDeliveryFeeDetailMaker(ItemDeliveryFeeDao itemDeliveryFeeDao,
                                      DeliveryFeeTemplateDao deliveryFeeTemplateDao,
                                      SpecialDeliveryFeeDao specialDeliveryFeeDao) {
        this.itemDeliveryFeeDao = itemDeliveryFeeDao;
        this.deliveryFeeTemplateDao = deliveryFeeTemplateDao;
        this.specialDeliveryFeeDao = specialDeliveryFeeDao;
    }

    /**
     * 根据商品id列表构造商品运费详细信息
     *
     * @param itemIds 商品id列表
     * @return 商品运费详情信息
     */
    public List<RichItemDeliveryFee> makeFromItemIds(List<Long> itemIds) {
        List<ItemDeliveryFee> itemDeliveryFees = itemDeliveryFeeDao.findByItemIds(itemIds);
        return makeRichItemDeliveryFees(itemDeliveryFees);
    }

    /**
     * 根据商品基本运费信息构造商品运费详细信息
     *
     * @param itemDeliveryFees 商品基本运费信息
     * @return 商品运费详细信息
     */
    public List<RichItemDeliveryFee> makeFromItemDeliveryFees(List<? extends ItemDeliveryFee> itemDeliveryFees) {
        return makeRichItemDeliveryFees(itemDeliveryFees);
    }

    private List<RichItemDeliveryFee> makeRichItemDeliveryFees(List<? extends ItemDeliveryFee> itemDeliveryFees) {
        if (CollectionUtils.isEmpty(itemDeliveryFees)) {
            return Collections.emptyList();
        }

        List<Long> templateIds = FluentIterable.from(itemDeliveryFees).transform(new Function<ItemDeliveryFee, Long>() {
            @Override
            public Long apply(ItemDeliveryFee itemDeliveryFee) {
                return itemDeliveryFee.getDeliveryFeeTemplateId();
            }
        }).filter(Predicates.notNull()).toList();

        List<RichItemDeliveryFee> richItemDeliveryFees;
        if (CollectionUtils.isEmpty(templateIds)) {//没有使用运费模板
            richItemDeliveryFees = makeWithoutTemplate(itemDeliveryFees);
        } else {//使用了运费模板
            richItemDeliveryFees = makeWithTemplate(itemDeliveryFees, templateIds);
        }
        return richItemDeliveryFees;
    }

    private List<RichItemDeliveryFee> makeWithoutTemplate(List<? extends ItemDeliveryFee> itemDeliveryFees) {
        List<RichItemDeliveryFee> richItemDeliveryFees = Lists.newArrayListWithCapacity(itemDeliveryFees.size());

        for (ItemDeliveryFee itemDelivery : itemDeliveryFees) {
            RichItemDeliveryFee richItemDeliveryFee = new RichItemDeliveryFee();
            richItemDeliveryFee.setItemDeliveryFee(itemDelivery);
            richItemDeliveryFees.add(richItemDeliveryFee);
        }
        return richItemDeliveryFees;
    }

    private List<RichItemDeliveryFee> makeWithTemplate(List<? extends ItemDeliveryFee> itemDeliveryFees, List<Long> templateIds) {
        List<DeliveryFeeTemplate> deliveryFeeTemplates = deliveryFeeTemplateDao.findByIds(templateIds);
        if (CollectionUtils.isEmpty(deliveryFeeTemplates)) {
            log.error("delivery fee templates not found where ids={}", templateIds);
            throw new ServiceException("delivery.fee.template.not.found");
        }

        //templateId->template
        Map<Long, DeliveryFeeTemplate> deliveryFeeTemplateIdIndex = Maps.uniqueIndex(deliveryFeeTemplates, new Function<DeliveryFeeTemplate, Long>() {
            @Override
            public Long apply(DeliveryFeeTemplate deliveryFeeTemplate) {
                return deliveryFeeTemplate.getId();
            }
        });

        //templateId->specialDeliveryFees
        List<SpecialDeliveryFee> specialDeliveryFees = specialDeliveryFeeDao.findByDeliveryFeeTemplateIds(templateIds);
        ListMultimap<Long, SpecialDeliveryFee> specialDeliveryFeeIndexByTemplateId = ArrayListMultimap.create();
        for (SpecialDeliveryFee specialDeliveryFee : specialDeliveryFees) {
            specialDeliveryFeeIndexByTemplateId.put(specialDeliveryFee.getDeliveryFeeTemplateId(), specialDeliveryFee);
        }

        List<RichItemDeliveryFee> richItemDeliveryFees = Lists.newArrayListWithCapacity(itemDeliveryFees.size());
        for (ItemDeliveryFee itemDelivery : itemDeliveryFees) {
            RichItemDeliveryFee richItemDeliveryFee = new RichItemDeliveryFee();
            richItemDeliveryFee.setItemDeliveryFee(itemDelivery);

            if (itemDelivery.getDeliveryFeeTemplateId() != null) {
                DeliveryFeeTemplate deliveryFeeTemplate = deliveryFeeTemplateIdIndex.get(itemDelivery.getDeliveryFeeTemplateId());
                if (deliveryFeeTemplate == null) {
                    log.error("delivery fee template not found where id={}", itemDelivery.getDeliveryFeeTemplateId());
                    throw new ServiceException("delivery.fee.template.not.found");
                }
                richItemDeliveryFee.setDeliveryFeeTemplate(deliveryFeeTemplate);

                List<SpecialDeliveryFee> specialDeliveryFeesByTemplateId = specialDeliveryFeeIndexByTemplateId.get(itemDelivery.getDeliveryFeeTemplateId());
                if (specialDeliveryFeeIndexByTemplateId == null) {
                    richItemDeliveryFee.setSpecialDeliveryFees(Collections.EMPTY_LIST);
                } else {
                    richItemDeliveryFee.setSpecialDeliveryFees(Lists.newArrayList(specialDeliveryFeesByTemplateId));
                }
            }
            richItemDeliveryFees.add(richItemDeliveryFee);
        }
        return richItemDeliveryFees;
    }

}
