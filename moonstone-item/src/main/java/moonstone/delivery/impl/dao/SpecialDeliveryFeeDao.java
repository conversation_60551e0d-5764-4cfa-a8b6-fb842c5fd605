package moonstone.delivery.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.delivery.model.SpecialDeliveryFee;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Author:cp
 * Created on 06/07/16
 */
@Repository
public class SpecialDeliveryFeeDao extends MyBatisDao<SpecialDeliveryFee> {

    public void deleteByTemplateId(Long deliveryFeeTemplateId) {
        getSqlSession().delete(sqlId("deleteByTemplateId"), deliveryFeeTemplateId);
    }

    public List<SpecialDeliveryFee> findByDeliveryFeeTemplateId(Long templateId) {
        return getSqlSession().selectList(sqlId("findByDeliveryFeeTemplateId"), templateId);
    }

    public List<SpecialDeliveryFee> findByDeliveryFeeTemplateIds(List<Long> templateIds) {
        return getSqlSession().selectList(sqlId("findByDeliveryFeeTemplateIds"), templateIds);
    }

}