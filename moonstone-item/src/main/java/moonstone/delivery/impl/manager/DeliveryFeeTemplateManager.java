package moonstone.delivery.impl.manager;

import moonstone.delivery.impl.dao.DeliveryFeeTemplateDao;
import moonstone.delivery.impl.dao.SpecialDeliveryFeeDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 运费模板管理
 * Author:cp
 * Created on 6/7/16.
 */
@Component
public class DeliveryFeeTemplateManager {

    private final DeliveryFeeTemplateDao deliveryFeeTemplateDao;

    private final SpecialDeliveryFeeDao specialDeliveryFeeDao;

    @Autowired
    public DeliveryFeeTemplateManager(DeliveryFeeTemplateDao deliveryFeeTemplateDao,
                                      SpecialDeliveryFeeDao specialDeliveryFeeDao) {
        this.deliveryFeeTemplateDao = deliveryFeeTemplateDao;
        this.specialDeliveryFeeDao = specialDeliveryFeeDao;
    }

    @Transactional
    public void deleteDeliveryFeeTemplate(Long templateId) {
        deliveryFeeTemplateDao.delete(templateId);
        specialDeliveryFeeDao.deleteByTemplateId(templateId);
    }

    @Transactional
    public void setDefaultDeliveryFeeTemplate(Long shopId, Long templateId) {
        deliveryFeeTemplateDao.notDefault(shopId);
        deliveryFeeTemplateDao.setDefault(shopId, templateId);
    }

}
