package moonstone.delivery.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.delivery.impl.dao.SpecialDeliveryFeeDao;
import moonstone.delivery.model.SpecialDeliveryFee;
import moonstone.delivery.service.SpecialDeliveryFeeWriteService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class SpecialDeliveryFeeWriteServiceImpl implements SpecialDeliveryFeeWriteService {

    @Resource
    private SpecialDeliveryFeeDao specialDeliveryFeeDao;

    @Override
    public Response<Boolean> creates(List<SpecialDeliveryFee> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return Response.fail("入参为空");
            }

            return Response.ok(specialDeliveryFeeDao.creates(list).equals(list.size()));
        } catch (Exception ex) {
            log.error("SpecialDeliveryFeeWriteServiceImpl.creates error, list={}", JSON.toJSONString(list), ex);
            return Response.fail(ex.getMessage());
        }
    }
}
