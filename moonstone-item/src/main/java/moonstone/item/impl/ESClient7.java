package moonstone.item.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.base.CharMatcher;
import com.google.common.base.Splitter;
import io.terminus.search.core.ESClient;
import io.terminus.search.model.AliasAction;
import io.terminus.search.model.CutWords;
import io.terminus.search.model.ESSearchResponse;
import io.terminus.search.model.Token;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.ApiException;

import java.util.*;

/**
 * author：书生
 */
@Slf4j
public class ESClient7 extends ESClient {


    private final String hostname;

    private final int port;

    private final String username;

    private final String password;

    public ESClient7(String hostname, int port,String username, String password) {
        super(hostname, port);
        this.hostname = hostname;
        this.port = port;
        this.username = username;
        this.password = password;
    }

    public boolean health() {
        try {
            String url = "http://" + this.hostname + ":" + this.port + "/_cluster/health";
            HttpRequest request = HttpRequest.get(url);
            if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
                request.basicAuth(this.username, this.password);
            }
            return request.execute().isOk();
        } catch (Exception e) {
            log.error("failed to elasticsearch check status ", e);
            return false;
        }
    }

    public void createIndexIfNotExists(String indexName) {
        try {
            String url = "http://" + this.hostname + ":" + this.port + "/" + indexName;
            HttpRequest get = HttpRequest.head(url);
            if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
                get.basicAuth(this.username, this.password);
            }
            if (!get.execute().isOk()) {
                HttpRequest post = HttpRequest.post(url);
                if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
                    post.basicAuth(this.username, this.password);
                }
                post.body("");
                post.execute();
            }
            log.info("create index {} success", indexName);
        } catch (Exception e) {
            log.error("failed to create index {}", indexName, e);
            throw new ApiException(e.getMessage());
        }
    }

    public void deleteIndex(String indexName) {
        try {
            String url = "http://" + this.hostname + ":" + this.port + "/" + indexName;
            HttpRequest delete = HttpRequest.delete(url);
            if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
                delete.basicAuth(this.username, this.password);
            }
            delete.execute();
            log.info("delete index {} success", indexName);
        } catch (Exception e) {
            log.error("failed to delete index {}", indexName, e);
            throw new ApiException(e.getMessage());
        }
    }

    /**
     * 创建索引 以及对应的映射
     *
     * @param indexName 索引名称
     * @param indexType 索引类型（废弃）
     * @param mapping   索引映射
     */
    public void createMappingIfNotExists(String indexName, String indexType, String mapping) {
        try {
            String url = "http://" + this.hostname + ":" + this.port + "/" + indexName;
            HttpRequest get = HttpRequest.get(url);
            if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
                get.basicAuth(this.username, this.password);
            }
            if (!get.execute().isOk()) {
                HttpRequest put = HttpRequest.put(url);
                if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
                    put.basicAuth(this.username, this.password);
                }
                put.body(mapping);
                put.execute();
            }
            log.info("create index mapping index {} success", indexName);
        } catch (Exception e) {
            log.error("failed to create index mapping index {}", indexName, e);
            throw new ApiException(e.getMessage());
        }
    }


    public void createIndexTemplateIfNotExists(String templateName, String template) {
        try {
            String url = "http://" + this.hostname + ":" + this.port + "/_template/" + templateName;
            HttpRequest get = HttpRequest.head(url);
            if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
                get.basicAuth(this.username, this.password);
            }
            if (!get.execute().isOk()) {
                HttpRequest put = HttpRequest.put(url);
                if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
                    put.basicAuth(this.username, this.password);
                }
                put.body(template);
                put.execute();
            }
            log.info("create index template {} success", templateName);
        } catch (Exception e) {
            log.error("failed to create index template {}", templateName, e);
            throw new ApiException(e.getMessage());
        }
    }

    @Override
    public void index(String indexName, String indexType, Object id, String document) {
        try {
            String url = "http://" + this.hostname + ":" + this.port + "/" + indexName + "/_doc/" + id;
            HttpRequest request = HttpRequest.post(url).body(document);
            if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
                request.basicAuth(this.username, this.password);
            }
            HttpResponse response = request.execute();
            log.debug("ESClient7.index, url={}, id={}, document={}, body={}", url, id, document, response.body());
            if (!response.isOk()) {
                throw new ApiException(response.body());
            }
        } catch (Exception e) {
            log.error("插入文档失败 indexName={}, id={}, error:{}", indexName, id, e.getMessage(), e);
            throw new ApiException(e.getMessage());
        }

    }

    @Override
    public void update(String indexName, String indexType, Object id, Object document) throws Exception {
        try {
            String url = "http://" + this.hostname + ":" + this.port + "/" + indexName + "/_doc/" + id;
            HttpRequest request = HttpRequest.put(url).body(StrUtil.toString(document));
            if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
                request.basicAuth(this.username, this.password);
            }
            HttpResponse response = request.execute();
            log.debug("ESClient7.update, url={}, id={}, document={}, body={}", url, id, document, response.body());
            if (!response.isOk()) {
                throw new ApiException(response.body());
            }
        } catch (Exception e) {
            log.error("更新文档失败 indexName={}, id={}, error:{}", indexName, id, e.getMessage(), e);
            throw new ApiException(e.getMessage());
        }
    }

    @Override
    public void delete(String indexName, String indexType, Object id) {
        String url = "http://" + this.hostname + ":" + this.port + "/" + indexName + "/_doc" + "/" + id;
        HttpRequest delete = HttpRequest.delete(url);
        if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
            delete.basicAuth(this.username, this.password);
        }
        HttpResponse response = delete.execute();
        if (!response.isOk()) {
            log.error("failed to delete index (indexName={}, indexType={}, id={}),error:{}", indexName, indexType, id, response.body());
            throw new ApiException(response.body());
        }
    }

    @Override
    public void deleteByQuery(String indexName, String indexType, String criteria) {

    }

    @Override
    public void bulk(String indexName, String indexType, String payload) {
        String url = "http://" + this.hostname + ":" + this.port + "/_bulk";
        HttpRequest request = HttpRequest.post(url).body(payload);
        if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
            request.basicAuth(this.username, this.password);
        }
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            log.error("failed to bulk index (indexName={}, indexType={}, payload={}),error:{}", indexName, indexType, payload, response.body());
            throw new ApiException(response.body());
        }
    }

    @Override
    public boolean addWord(String httpHost, Integer httpPort, String word) {
        return true;
    }

    @Override
    public List<String> getNodesIp() {
        String url = "http://" + this.hostname + ":" + this.port + "/_cat/nodes?h=ip";
        HttpRequest request = HttpRequest.get(url);
        if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
            request.basicAuth(this.username, this.password);
        }
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            log.error("failed to get node ips from host {} port {} error {}", this.hostname, this.port, response.body());
            throw new ApiException(response.body());
        } else {
            return Splitter.on(CharMatcher.breakingWhitespace()).omitEmptyStrings().trimResults().splitToList(response.body());
        }
    }

    @Override
    public boolean addWord(String word) {
        List<String> nodes = this.getNodesIp();
        for (String node : nodes) {
            this.addWord(node, this.port, word);
        }
        return true;
    }

    @Override
    public List<String> cutWords(String sentence, String mode) throws Exception {
        String url = "http://" + this.hostname + ":" + this.port + "/_analyze";
        Map<String,Object> params = new HashMap<>();
        params.put("analyzer", mode);
        params.put("text", sentence);
        HttpRequest request = HttpRequest.post(url).body(JSONUtil.toJsonStr(params));
        if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
            request.basicAuth(this.username, this.password);
        }
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            log.error("failed to analyze sentence (sentence={}, mode={}) error {}", sentence, mode, response.body());
            throw new ApiException(response.body());
        }else {
            CutWords cutWords = JSONUtil.toBean(response.body(), CutWords.class);
            List<String> result = new ArrayList<>(cutWords.getTokens().size());
            for (Token token : cutWords.getTokens()) {
                result.add(token.getToken());
            }
            return result;
        }
    }

    @Override
    public boolean addAlias(String indexName, String aliasName) throws Exception {
        AliasAction action = new AliasAction(AliasAction.Operate.ADD, indexName, aliasName);
        return this.alias(action);
    }

    @Override
    public boolean removeAlias(String indexName, String aliasName) throws Exception {
        AliasAction action = new AliasAction(AliasAction.Operate.REMOVE, indexName, aliasName);
        return this.alias(action);
    }

    @Override
    public boolean renameAlias(String indexName, String oldAliasName, String newAliasName) throws Exception {
        AliasAction remove = new AliasAction(AliasAction.Operate.REMOVE, indexName, oldAliasName);
        AliasAction add = new AliasAction(AliasAction.Operate.ADD, indexName, newAliasName);
        return this.alias(remove, add);
    }

    @Override
    public boolean changeAliasIndex(String aliasName, String oldIndexName, String newIndexName) throws Exception {
        AliasAction remove = new AliasAction(AliasAction.Operate.REMOVE, oldIndexName, aliasName);
        AliasAction add = new AliasAction(AliasAction.Operate.ADD, newIndexName, aliasName);
        return this.alias(remove, add);
    }

    @Override
    public boolean alias(AliasAction... aliasActions) throws Exception {
        String url = "http://" + this.hostname + ":" + this.port + "/_aliases";
        JSONObject body = new JSONObject();
        List<JSONObject> actions = new ArrayList<>();
        for (AliasAction aliasAction : aliasActions) {
            JSONObject action = new JSONObject();
            JSONObject operation = new JSONObject();
            List<String> indexes = new ArrayList<>();
            indexes.add(aliasAction.getIndexName());
            operation.set("index", indexes);
            operation.set("alias", aliasAction.getAliasName());
            action.set(aliasAction.getOperate().value(), operation);
            actions.add(action);
        }
        body.set("actions", actions);
        HttpRequest request = HttpRequest.post(url).body(body.toString());
        if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
            request.basicAuth(this.username, this.password);
        }
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            log.error("failed to alias (aliasActions={}),error:{}", Arrays.toString(aliasActions), response.body());
            throw new ApiException(response.body());
        } else {
            return true;
        }

    }

    @Override
    public List<String> getIndexesOfAlias(String aliasName) {
        String url = "http://" + this.hostname + ":" + this.port + "/" + aliasName;
        HttpRequest request = HttpRequest.get(url);
        if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
            request.basicAuth(this.username, this.password);
        }
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            if (response.getStatus() == HttpStatus.HTTP_NOT_FOUND) {
                return Collections.emptyList();
            } else {
                log.error("failed to query alias {} host:{} port:{} error:{}", aliasName, this.hostname, this.port, response.body());
                throw new ApiException(response.body());
            }
        }
        String body = response.body();
        JSONObject jsonObject = JSONUtil.toBean(body, JSONObject.class);
        Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
        List<String> indexes = new ArrayList<>();
        for (Map.Entry<String, Object> entry : entries) {
            indexes.add(entry.getKey());
        }
        return indexes;
    }

    @Override
    public ESSearchResponse search(String indexName, String indexType, String criteria) {
        String url = "http://" + this.hostname + ":" + this.port + "/" + indexName + "/_search";
        log.info("es 查询语句 {}", criteria);
        HttpRequest request = HttpRequest.post(url).body(criteria);
        if (StrUtil.isNotBlank(this.username) && StrUtil.isNotBlank(this.password)) {
            request.basicAuth(this.username, this.password);
        }
        HttpResponse response = request.execute();
        if (!response.isOk()) {
            log.error("failed to search (indexName={}, criteria={}),error:{}", indexName, criteria, response.body());
            throw new ApiException(response.body());
        }
        String body = response.body();
        JSONObject jsonObject = JSONUtil.toBean(body, JSONObject.class);
        JSONObject hits = jsonObject.get("hits", JSONObject.class);
        Long total = hits.getByPath("total.value", Long.class);
        hits.set("total", total);
        jsonObject.set("hits", hits);
        return BeanUtil.toBean(jsonObject, ESSearchResponse.class);
    }

}


