package moonstone.item.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.item.model.GbActivityConfigSku;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class GbActivityConfigSkuDao extends MyBatisDao<GbActivityConfigSku> {

    public List<GbActivityConfigSku> findByItemId(Long itemId){
        List<GbActivityConfigSku> list = getSqlSession().selectList(sqlId("findByItemId"),
                ImmutableMap.of("itemId", itemId));
        return list;
    }

    public GbActivityConfigSku findByActivityIdAndSkuId(Integer activityId, Long skuId, Integer marketingToolId) {
        GbActivityConfigSku configSku = getSqlSession().selectOne(sqlId("findByActivityIdAndSkuId"),
                ImmutableMap.of("activityId", activityId, "skuId", skuId, "marketingToolId", marketingToolId));
        return configSku;
    }
}
