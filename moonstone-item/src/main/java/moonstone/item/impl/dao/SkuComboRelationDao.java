package moonstone.item.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.item.model.SkuComboRelation;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public class SkuComboRelationDao extends MyBatisDao<SkuComboRelation> {
	/**
	 * 逻辑删除
	 * @param id 主键id
	 * @return 默认都成功
	 */
	public Boolean logicDelete(Long id) {
		this.getSqlSession().update(sqlId("logicDelete"), id);
		return true;
	}

	/**
	 * 根据条件查询单条记录
	 * @param query 条件查询
	 * @return 单条记录
	 */
	public SkuComboRelation selectOne(Map<String, Object> query) {
		return this.getSqlSession().selectOne(sqlId("selectOne"), query);
	}

	/**
	 * 根据条件查询多条记录
	 * @param query 条件查询
	 * @return 多条记录
	 */
	public List<SkuComboRelation> selectList(Map<String, Object> query) {
		return this.getSqlSession().selectList(sqlId("selectList"), query);
	}

	/**
	 * 根据条件查询对应的数量
	 * @param query 条件查询
	 * @return 数量
	 */
	public Long selectCount(Map<String, Object> query) {
		return this.getSqlSession().selectOne(sqlId("count"), query);
	}

}
