package moonstone.item.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.model.RestrictedSalesAreaTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Slf4j
@Repository
public class RestrictedSalesAreaTemplateDao extends MyBatisDao<RestrictedSalesAreaTemplate> {

    public List<RestrictedSalesAreaTemplate> findByName(Long shopId, String name) {
        return getSqlSession().selectList(sqlId("findByName"), Map.of("shopId", shopId,
                "name", name));
    }

    public RestrictedSalesAreaTemplate findDefault(Long shopId) {
        return getSqlSession().selectOne(sqlId("findDefault"), Map.of("shopId", shopId));
    }


    public List<RestrictedSalesAreaTemplate> selectByShopId(Long shopId) {
        return getSqlSession().selectList(sqlId("selectByShopId"), Map.of("shopId", shopId));
    }
}
