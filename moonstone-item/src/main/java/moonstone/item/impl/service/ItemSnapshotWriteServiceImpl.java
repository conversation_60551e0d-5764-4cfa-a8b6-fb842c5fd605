/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import io.terminus.common.utils.BeanMapper;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.common.Digestors;
import moonstone.item.impl.dao.ItemSnapshotDao;
import moonstone.item.model.Item;
import moonstone.item.model.ItemAttribute;
import moonstone.item.model.ItemDetail;
import moonstone.item.model.ItemSnapshot;
import moonstone.item.service.ItemSnapshotWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-26
 */
@Service
@Slf4j
@RpcProvider
public class ItemSnapshotWriteServiceImpl implements ItemSnapshotWriteService {

    private final ItemSnapshotDao itemSnapshotDao;

    @Autowired
    public ItemSnapshotWriteServiceImpl(ItemSnapshotDao itemSnapshotDao) {
        this.itemSnapshotDao = itemSnapshotDao;
    }

    /**
     * 为商品创建快照
     *
     * @param item          商品
     * @param itemDetail    商品详情
     * @param itemAttribute 商品属性
     * @return 快照id
     */
    @Override
    public Response<Long> create(Item item, ItemDetail itemDetail, ItemAttribute itemAttribute) {
        try {
            ItemSnapshot itemSnapshot = new ItemSnapshot();
            BeanMapper.copy(item, itemSnapshot);
            BeanMapper.copy(itemDetail, itemSnapshot);
            BeanMapper.copy(itemAttribute, itemSnapshot);
            String itemInfoMd5 = Digestors.itemDigest(item, itemDetail, itemAttribute);
            itemSnapshot.setItemInfoMd5(itemInfoMd5);
            itemSnapshot.setItemId(item.getId());
            itemSnapshot.setImages(itemDetail.getImages());
            itemSnapshotDao.create(itemSnapshot);
            return Response.ok(itemSnapshot.getId());
        } catch (Exception e) {
            log.error("failed to create snapshot for item (id={}), cause:{}", item.getId(),
                    Throwables.getStackTraceAsString(e));
            return Response.fail("snapshot.create.fail");
        }
    }
}
