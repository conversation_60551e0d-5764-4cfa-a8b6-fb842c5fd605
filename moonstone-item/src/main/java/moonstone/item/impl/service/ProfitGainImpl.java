package moonstone.item.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.item.api.ProfitGain;
import moonstone.item.dto.ProfitBaseDTO;
import moonstone.item.dto.ProfitDTO;
import moonstone.item.dto.ProfitReVO;
import moonstone.item.emu.ProfitFor;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.model.Sku;
import moonstone.item.service.IntermediateInfoReadService;
import moonstone.item.service.IntermediateInfoWriteService;
import moonstone.item.service.SkuReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Stream;

@RpcProvider
@Component
@Slf4j
public class ProfitGainImpl implements ProfitGain {
    @Autowired
    private SkuReadService skuReadService;
    @Autowired
    private ShopReadService shopReadService;
    @Autowired
    private IntermediateInfoReadService intermediateInfoReadService;
    @Autowired
    private IntermediateInfoWriteService intermediateInfoWriteService;

    ///　获取Sku单品的某个指向的利润计算值
    @Override
    public Response<Long> getProfit(ProfitFor profitFor, Sku sku) {
        try {
            ProfitDTO profit = getProfit(sku).getResult();
            if (profit == null) {
                log.error("{} error in get profit for skuId:{}", LogUtil.getClassMethodName(), sku.getId());
                return Response.fail(new Translate("获取利润信息失败").toString());
            }
            ProfitBaseDTO profitBaseDTO;
            switch (profitFor) {
                case GuiderProfit:
                    profitBaseDTO = profit.getGuider();
                    break;
                case SubStoreProfit:
                    profitBaseDTO = profit.getSubStore();
                    break;
                case ServiceProviderProfit:
                    profitBaseDTO = profit.getServiceProvider();
                    if (profit.isHundredMode()) {
                        profitBaseDTO.setBaseProfit(profitBaseDTO.getBaseProfit() * (100 - profit.getSubStore().getBaseProfit()) / 100);
                    } else {
                        profitBaseDTO.setBaseProfit(profitBaseDTO.getBaseProfit() * (10000 - profit.getSubStore().getBaseProfit()) / 10000);
                    }
                    break;
                default:
                    throw new RuntimeException("未知利润类型");
            }
            long profitByHundred;
            if (profit.isHundredMode()) {
                profitByHundred = new BigDecimal(profitBaseDTO.getBaseProfit() * sku.getPrice()).add(new BigDecimal(profitBaseDTO.getExtraProfit()))
                        .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).longValue();
            } else {
                profitByHundred = new BigDecimal(profitBaseDTO.getBaseProfit() * sku.getPrice())
                        .divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP)
                        .add(new BigDecimal(profitBaseDTO.getExtraProfit()))
                        .longValue();
            }
            return Response.ok(profitByHundred);
        } catch (Exception ex) {
            log.error("{} profit-gain error skuId:{}", LogUtil.getClassMethodName("cal-profit"), sku.getId(), ex);
            return Response.fail(ex.getMessage());
        }
    }

    /// 获取供应商的利润设置信息
    @Override
    public Response<ProfitDTO> getProfit(Shop shop) {
        ProfitDTO profitVO = new ProfitDTO();
        List<IntermediateInfo> rateList = intermediateInfoReadService.findByThirdAndType(shop.getId(), ThirdIntermediateType.SHOP.getValue()).take();
        if (!rateList.isEmpty()) {
            IntermediateInfo rateInfo = rateList.get(0);
            rateInfo.fillNullWithZero();
            if (rateInfo.getVersion() == rateInfo.getNowVersion()) {
                profitVO.setHundredMode(false);
            }
            profitVO.getSubStore().setBaseProfit(nullOr(rateInfo.getFirstRate(), 0L));
            profitVO.getSubStore().setExtraProfit(nullOr(rateInfo.getFirstFee(), 0L));
            profitVO.getSubStore().setBaseProfit(nullOr(rateInfo.getSecondRate(), 0L));
            profitVO.getSubStore().setExtraProfit(nullOr(rateInfo.getSecondFee(), 0L));

            profitVO.getServiceProvider().setBaseProfit(nullOr(rateInfo.getServiceProviderRate(), 0L));
            profitVO.getServiceProvider().setExtraProfit(nullOr(rateInfo.getServiceProviderFee(), 0L));
            return Response.ok(profitVO);
        }
        if (shop.getExtra() != null) { /// 即将被去除
            String shopStaticFeeStr = shop.getExtra().get(getFeeIndex(ProfitFor.SubStoreProfit));
            String shopRateStr = shop.getExtra().get(getRateIndex(ProfitFor.SubStoreProfit));
            String guiderStaticFeeStr = shop.getExtra().get(getFeeIndex(ProfitFor.GuiderProfit));
            String guiderRateStr = shop.getExtra().get(getRateIndex(ProfitFor.GuiderProfit));
            try {
                if (!ObjectUtils.isEmpty(shopRateStr)) {
                    profitVO.getSubStore().setBaseProfit(Long.parseLong(shopRateStr));
                }
                if (!ObjectUtils.isEmpty(shopStaticFeeStr)) {
                    profitVO.getSubStore().setExtraProfit(Long.parseLong(shopStaticFeeStr));
                }
                if (!ObjectUtils.isEmpty(guiderRateStr)) {
                    profitVO.getGuider().setBaseProfit(Long.parseLong(guiderRateStr));
                }
                if (!ObjectUtils.isEmpty(guiderStaticFeeStr)) {
                    profitVO.getGuider().setExtraProfit(Long.parseLong(guiderStaticFeeStr));
                }
                convertOldShopProfitData(shop.getId(), profitVO);
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("[profit](parse) error GBP:{} GEP:{} SBP:{} SFP:{}", shopRateStr, shopStaticFeeStr, guiderRateStr, guiderStaticFeeStr);
                return Response.fail(new Translate("利润数值提取失败").toString());
            }
        }
        return Response.ok(profitVO);
    }

    <T> T nullOr(T t, T o) {
        return t == null ? o : t;
    }

    private boolean isEmptyStr(String str) {
        return ObjectUtils.isEmpty(str);
    }

    /// 转换Long为String数据如果lon不是null并且str为空字符串
    private String cLongToStrRollBackOnStr(String str, Long lon) {
        return lon == null ? str : lon + "";
    }

    /// 获取Sku单品所有的利润信息
    @Override
    public Response<ProfitDTO> getProfit(Sku sku) {
        ProfitDTO profitVO = new ProfitDTO();
        List<IntermediateInfo> rateList = intermediateInfoReadService.findByThirdAndType(sku.getId(), ThirdIntermediateType.SKU.getValue()).take();
        if (!rateList.isEmpty()) {
            IntermediateInfo rate = rateList.get(0);
            if (!Objects.equals(1, rate.getIsCommission())) {
                // use the shop rate instead sku rate
                List<IntermediateInfo> shopRateList = intermediateInfoReadService.findByThirdAndType(sku.getShopId(), ThirdIntermediateType.SHOP.getValue()).take();
                rate = shopRateList.isEmpty() ? null : shopRateList.get(0);
            }
            // if rate is null mean it has not shopRate and use not sku Rate rollback
            if (rate != null) {
                if (rate.getVersion() == rate.getNowVersion()) {
                    profitVO.setHundredMode(false);
                }
                rate.fillNullWithZero();
                profitVO.getSubStore().setBaseProfit(rate.getFirstRate());
                profitVO.getSubStore().setExtraProfit(rate.getFirstFee());
                profitVO.getGuider().setBaseProfit(rate.getSecondRate());
                profitVO.getGuider().setExtraProfit(rate.getSecondFee());
                profitVO.getServiceProvider().setBaseProfit(rate.getServiceProviderRate());
                profitVO.getServiceProvider().setExtraProfit(rate.getServiceProviderFee());
                return Response.ok(profitVO);
            }
        } else {
            List<IntermediateInfo> shopRateList = intermediateInfoReadService.findByThirdAndType(sku.getShopId(), ThirdIntermediateType.SHOP.getValue()).take();
            if (!shopRateList.isEmpty()) {
                IntermediateInfo rate = shopRateList.get(0);
                if (rate.getNowVersion() == rate.getVersion()) {
                    profitVO.setHundredMode(false);
                }
                rate.fillNullWithZero();
                profitVO.getSubStore().setBaseProfit(rate.getFirstRate());
                profitVO.getSubStore().setExtraProfit(rate.getFirstFee());
                profitVO.getGuider().setBaseProfit(rate.getSecondRate());
                profitVO.getGuider().setExtraProfit(rate.getSecondFee());
                profitVO.getServiceProvider().setBaseProfit(rate.getServiceProviderRate());
                profitVO.getServiceProvider().setExtraProfit(rate.getServiceProviderFee());
                return Response.ok(profitVO);
            }
        }
        Map<String, String> extra = sku.getExtraMap() == null ? new HashMap<>() : sku.getExtraMap();
        String shopStaticFeeStr = extra.get(getFeeIndex(ProfitFor.SubStoreProfit));
        shopStaticFeeStr = cLongToStrRollBackOnStr(shopStaticFeeStr, sku.getSubStoreProfitFee());
        String shopRateStr = extra.get(getRateIndex(ProfitFor.SubStoreProfit));
        shopRateStr = cLongToStrRollBackOnStr(shopRateStr, sku.getSubStoreProfitRate());
        String guiderStaticFeeStr = extra.get(getFeeIndex(ProfitFor.GuiderProfit));
        guiderStaticFeeStr = cLongToStrRollBackOnStr(guiderStaticFeeStr, sku.getGuiderProfitFee());
        String guiderRateStr = extra.get(getRateIndex(ProfitFor.GuiderProfit));
        guiderRateStr = cLongToStrRollBackOnStr(guiderRateStr, sku.getGuiderProfitRate());
        try {
            if (!Stream.of(shopRateStr, shopStaticFeeStr, guiderRateStr, guiderStaticFeeStr).allMatch(Objects::isNull)) {
                // 该行代码即将删除
                if (!ObjectUtils.isEmpty(shopRateStr)) {
                    profitVO.getSubStore().setBaseProfit(Long.parseLong(shopRateStr));
                }
                if (!ObjectUtils.isEmpty(shopStaticFeeStr)) {
                    profitVO.getSubStore().setExtraProfit(Long.parseLong(shopStaticFeeStr));
                }
                if (!ObjectUtils.isEmpty(guiderRateStr)) {
                    profitVO.getGuider().setBaseProfit(Long.parseLong(guiderRateStr));
                }
                if (!ObjectUtils.isEmpty(guiderStaticFeeStr)) {
                    profitVO.getGuider().setExtraProfit(Long.parseLong(guiderStaticFeeStr));
                }
                convertOldSkuProfitData(sku.getId(), profitVO);
            }
            return Response.ok(profitVO);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("[profit](parse) error GBP:{} GEP:{} SBP:{} SFP:{}", shopRateStr, shopStaticFeeStr, guiderRateStr, guiderStaticFeeStr);
            return Response.fail(new Translate("利润数值提取失败").toString());
        }
    }

    private void convertOldShopProfitData(Long shopId, ProfitDTO profitVO) {
        if (intermediateInfoReadService.findByThirdAndType(shopId, ThirdIntermediateType.SHOP.getValue()).orElse(new ArrayList<>()).isEmpty()) {
            log.info("{} init new profit by shopId:{} profit:{}", LogUtil.getClassMethodName(), shopId, JSON.toJSONString(profitVO));
            IntermediateInfo rateInfo = new IntermediateInfo();
            rateInfo.setThirdId(shopId);
            rateInfo.setType(ThirdIntermediateType.SHOP.getValue());
            rateInfo.setFirstRate(profitVO.getSubStore().getBaseProfit());
            rateInfo.setFirstFee(profitVO.getSubStore().getExtraProfit());
            rateInfo.setSecondRate(profitVO.getGuider().getBaseProfit());
            rateInfo.setSecondFee(profitVO.getGuider().getExtraProfit());
            if (profitVO.isHundredMode()) {
                rateInfo.setFirstRate(rateInfo.getFirstRate() * 100);
                rateInfo.setSecondRate(rateInfo.getSecondRate() * 100);
            }
            rateInfo.fillNullWithZero();
            rateInfo.setStatus(1);
            // 热代码 每次需要根据变动而修改
            rateInfo.setVersion(3);
            intermediateInfoWriteService.create(rateInfo);
        }
    }

    private void convertOldSkuProfitData(Long skuId, ProfitDTO profitVO) {
        if (intermediateInfoReadService.findByThirdAndType(skuId, ThirdIntermediateType.SKU.getValue()).orElse(new ArrayList<>()).isEmpty()) {
            log.info("{} init new profit by skuId:{} profit:{}", LogUtil.getClassMethodName(), skuId, JSON.toJSONString(profitVO));
            IntermediateInfo rateInfo = new IntermediateInfo();
            rateInfo.setThirdId(skuId);
            rateInfo.setType(ThirdIntermediateType.SKU.getValue());
            rateInfo.setFirstRate(profitVO.getSubStore().getBaseProfit());
            rateInfo.setFirstFee(profitVO.getSubStore().getExtraProfit());
            rateInfo.setSecondRate(profitVO.getGuider().getBaseProfit());
            rateInfo.setSecondFee(profitVO.getGuider().getExtraProfit());
            if (profitVO.isHundredMode()) {
                rateInfo.setFirstRate(rateInfo.getFirstRate() * 100);
                rateInfo.setSecondRate(rateInfo.getSecondRate() * 100);
            }
            rateInfo.setIsCommission(1);
            rateInfo.fillNullWithZero();
            rateInfo.setStatus(1);
            // 热代码 每次需要根据变动而修改
            rateInfo.setVersion(3);
            intermediateInfoWriteService.create(rateInfo);
        }
    }

    /// 以后试着直接使用ProfitType的code来代替该函数，下面的getFeeIndex同
    private String getRateIndex(ProfitFor profitFor) {
        switch (profitFor) {
            case SubStoreProfit: {
                return ShopExtra.SubProfitRate.getCode();
            }
            case GuiderProfit: {
                return ShopExtra.GuiderProfitRate.getCode();
            }
            default:
                log.error("[ProfitGainImpl](getRateIndex) parse profitType into Extra failed");
                return null;
        }
    }

    private String getFeeIndex(ProfitFor profitFor) {
        switch (profitFor) {
            case GuiderProfit: {
                return ShopExtra.GuiderProfitFee.getCode();
            }
            case SubStoreProfit: {
                return ShopExtra.SubProfitFee.getCode();
            }
            default:
                return null;
        }
    }

    /// 获取某个商品的第一个利润值
    @Override
    public ProfitReVO getFirstProfitByItemId(Long itemId) {
        List<Sku> skuList = skuReadService.findSkusByItemId(itemId).getResult();
        if (CollectionUtils.isEmpty(skuList)) {
            return null;
        }
        return new ProfitReVO(getProfit(skuList.get(0)).getResult(), skuList.get(0));
    }
}
