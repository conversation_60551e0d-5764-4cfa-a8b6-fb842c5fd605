package moonstone.item.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.IntermediateInfoMatchingTypeEnum;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.common.utils.LogUtil;
import moonstone.item.impl.dao.IntermediateInfoDao;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.service.IntermediateInfoWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/22 18:19
 */
@Service
@RpcProvider
@Slf4j
public class IntermediateInfoWriteServiceImpl implements IntermediateInfoWriteService {

    @Autowired
    IntermediateInfoDao intermediateInfoDao;

    @Override
    public Response<Boolean> create(IntermediateInfo intermediateInfo) {
        try {
            return Response.ok(intermediateInfoDao.create(intermediateInfo));
        } catch (Exception ex) {
            log.error("{} update for id:{}", LogUtil.getClassMethodName(), intermediateInfo.getId());
            return Response.fail("IntermediateInfo create fail");
        }
    }

    @Override
    public Response<Boolean> update(IntermediateInfo intermediateInfo) {
        try {
            return Response.ok(intermediateInfoDao.update(intermediateInfo));
        } catch (Exception ex) {
            log.error("{} update for id:{}", LogUtil.getClassMethodName(), intermediateInfo.getId());
            return Response.fail("IntermediateInfo update fail");
        }
    }

    @Override
    public Response<Boolean> delete(Long thirdId, ThirdIntermediateType type, IntermediateInfoMatchingTypeEnum matchingTypeEnum) {
        try {
            if (thirdId == null || type == null || matchingTypeEnum == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(intermediateInfoDao.deleteByThirdAndType(thirdId, type.getValue(), matchingTypeEnum.getCode()) > 0);
        } catch (Exception ex) {
            log.error("IntermediateInfoWriteServiceImpl.delete failed, thirdId={}, type={}, matchingType={}",
                    thirdId, type, matchingTypeEnum);
            return Response.fail(ex.getMessage());
        }
    }
}
