package moonstone.item.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.item.impl.dao.ItemsSpecificationDetailDao;
import moonstone.item.model.ItemsSpecificationDetail;
import moonstone.item.service.ItemsSpecificationDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ItemsSpecificationDetailServiceImpl implements ItemsSpecificationDetailService {

	@Resource
	private ItemsSpecificationDetailDao itemsSpecificationDetailDao;

	@Override
	public List<ItemsSpecificationDetail> getItemSpecListByItemId(Long itemId) {
		Map<String, Object> query = new HashMap<>();
		query.put("itemId", itemId);
		query.put("deleted", 0);
		return itemsSpecificationDetailDao.selectList(query);
	}

}
