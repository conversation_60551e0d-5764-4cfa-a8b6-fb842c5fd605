package moonstone.item.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.impl.dao.ShopExtraUserDao;
import moonstone.item.service.ShopExtraUserWriteService;
import moonstone.shop.model.ShopExtraUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@RpcProvider
@Component
@Slf4j
public class ShopExtraUserWriteServiceImpl implements ShopExtraUserWriteService {
    @Autowired
    ShopExtraUserDao shopExtraUserDao;

    @Override
    public Response<Boolean> create(ShopExtraUser shopExtraUser) {
        try
        {
            return Response.ok(shopExtraUserDao.create(shopExtraUser));
        }
        catch (Exception ex)
        {
            log.error("create shop extra user(shopId:{},apiId:{}) failed,cause:{}",shopExtraUser.getShopId(),shopExtraUser.getApiId(),ex.getMessage());
            return Response.fail("shop.extra.user.create.fail");
        }
    }

    @Override
    public Response<Boolean> update(ShopExtraUser shopExtraUser) {
        try
        {
            return Response.ok(shopExtraUserDao.update(shopExtraUser));
        }
        catch (Exception ex)
        {
            log.error("update shop extra user(shopId:{},apiId:{}) failed,cause:{}",shopExtraUser.getShopId(),shopExtraUser.getApiId(),ex.getMessage());
            return Response.fail("shop.extra.user.update.fail");
        }
    }

    @Override
    public Response<Boolean> delete(long shopExtraUserId) {
        try
        {
            return Response.ok(shopExtraUserDao.delete(shopExtraUserId));
        }
        catch (Exception ex)
        {
            log.error("delete shop extra user(Id:{}) failed,cause:{}",shopExtraUserId,ex.getMessage());
            return Response.fail("shop.extra.user.delete.fail");
        }
    }
}
