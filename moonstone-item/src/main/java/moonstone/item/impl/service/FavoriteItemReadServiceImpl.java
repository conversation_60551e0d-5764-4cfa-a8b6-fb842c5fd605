package moonstone.item.impl.service;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.dto.RichFavoriteItem;
import moonstone.item.impl.dao.FavoriteItemDao;
import moonstone.item.impl.dao.ItemDao;
import moonstone.item.model.FavoriteItem;
import moonstone.item.model.Item;
import moonstone.item.service.FavoriteItemReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by cp on 5/15/17.
 */
@Service
@RpcProvider
@Slf4j
public class FavoriteItemReadServiceImpl implements FavoriteItemReadService {

    private final FavoriteItemDao favoriteItemDao;

    private final ItemDao itemDao;

    @Autowired
    public FavoriteItemReadServiceImpl(FavoriteItemDao favoriteItemDao,
                                       ItemDao itemDao) {
        this.favoriteItemDao = favoriteItemDao;
        this.itemDao = itemDao;
    }

    @Override
    public Response<Paging<RichFavoriteItem>> findForBuyer(Long buyerId, Long shopId,
                                                           Integer pageNo, Integer pageSize) {
        boolean deleteAndRetry = false;
        try {
            if (buyerId == null) {
                return Response.fail("buyer.id.required");
            }

            Map<String, Object> params = Maps.newHashMap();
            params.put("buyerId", buyerId);
            if (shopId != null) {
                params.put("shopId", shopId);
            }
            PageInfo pageInfo = PageInfo.of(pageNo, pageSize);
            Paging<FavoriteItem> paging = favoriteItemDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), params);
            if (paging.isEmpty()) {
                return Response.ok(Paging.empty());
            }
            List<FavoriteItem> favoriteItems = paging.getData();

            List<Long> itemIds = Lists.newArrayListWithCapacity(favoriteItems.size());
            for (FavoriteItem favoriteItem : favoriteItems) {
                itemIds.add(favoriteItem.getItemId());
            }

            List<Item> items = itemDao.findByIds(itemIds);
            Map<Long, Item> itemById = Maps.newHashMap();
            for (Item item : items) {
                itemById.put(item.getId(), item);
            }

            List<RichFavoriteItem> richFavoriteItems = Lists.newArrayListWithCapacity(items.size());
            for (FavoriteItem favoriteItem : favoriteItems) {
                RichFavoriteItem richFavoriteItem = new RichFavoriteItem();
                richFavoriteItem.setCollectedAt(favoriteItem.getCreatedAt());
                richFavoriteItem.setItem(itemById.get(favoriteItem.getItemId()));
                if (itemById.get(favoriteItem.getItemId()) == null) {
                    favoriteItemDao.delete(favoriteItem.getId());
                    deleteAndRetry = true;
                    continue;
                }
                richFavoriteItems.add(richFavoriteItem);
            }
            if (deleteAndRetry) {
                return findForBuyer(buyerId, shopId, pageNo, pageSize);
            }
            return Response.ok(new Paging<>(paging.getTotal(), richFavoriteItems));
        } catch (Exception e) {
            log.error("fail to find favorite items for buyer(id={}) with params:shopId={},pageNo={},pageSize={},cause:{}",
                    buyerId, shopId, pageNo, pageSize, Throwables.getStackTraceAsString(e));
            return Response.fail("favorite.item.find.fail");
        }
    }

    @Override
    public Response<Boolean> checkIfCollect(Long buyerId, Long itemId) {
        try {
            if (buyerId == null) {
                return Response.fail("buyer.id.required");
            }
            if (itemId == null) {
                return Response.fail("item.id.required");
            }

            FavoriteItem existed = favoriteItemDao.findByBuyerIdAndItemId(buyerId, itemId);
            return Response.ok(existed != null);
        } catch (Exception e) {
            log.error("fail to check buyer(id={}) if has collect item(id={}),cause:{}",
                    buyerId, itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("favorite.item.check.fail");
        }
    }
}
