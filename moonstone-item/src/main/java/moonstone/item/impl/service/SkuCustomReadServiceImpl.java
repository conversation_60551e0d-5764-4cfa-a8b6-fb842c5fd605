package moonstone.item.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.BondedType;
import moonstone.item.dto.SkuWithCustom;
import moonstone.item.impl.dao.SkuCustomDao;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@RpcProvider
public class SkuCustomReadServiceImpl implements SkuCustomReadService {
    private final SkuCustomDao skuCustomDao;

    @Autowired
    public SkuCustomReadServiceImpl(SkuCustomDao skuCustomDao) {
        this.skuCustomDao = skuCustomDao;
    }

    @Override
    public Response<List<SkuCustom>> findBySkuIds(List<Long> skuIds) {

        try {
            if (skuIds.isEmpty())
                return Response.ok(new ArrayList<>());
        return Response.ok(skuCustomDao.findBySkuIds(skuIds));
        } catch (Exception e){
            log.error("fail to find SkuCustom(id in {}) cause:{}", skuIds, e);
            return Response.fail("category.findBySkuIds.fail");
        }
    }

    @Override
    public SkuCustom getBySkuId(Long skuId) {
        return skuCustomDao.getBySkuId(skuId);
    }

    /**
     * 通过sku表，生成对应的SkuWithCustom表，填入SkuCustom信息
     * @param skus
     * @return List<SkuWithCustom>
     */
    @Override
    public List<SkuWithCustom> makeSkuWithCustomsBySkus(List<Sku> skus){
        List<SkuWithCustom> skuWithCustoms = new ArrayList<>();
        for (Sku sku:skus) {
            SkuWithCustom skuWithCustom = new SkuWithCustom();
            skuWithCustom.setSku(sku);
            if(BondedType.fromInt(sku.getType()).isBonded()){
                SkuCustom skuCustom = skuCustomDao.findNormalBySkuId(sku.getId());
                skuWithCustom.setSkuCustom(skuCustom);
            }
            skuWithCustoms.add(skuWithCustom);
        }
        return skuWithCustoms;
    }

    /**
     * 传入带有sku信息的SkuWithCustom表，填入SkuCustom信息
     * @param skuWithCustoms
     * @return
     */
    @Override
    public void fillSkuWithCustoms(List<SkuWithCustom> skuWithCustoms){
        for (SkuWithCustom skuWithCustom:skuWithCustoms) {
            Sku sku = skuWithCustom.getSku();
            if(BondedType.fromInt(sku.getType()).isBonded()){
                SkuCustom skuCustom = skuCustomDao.findNormalBySkuId(sku.getId());
                skuWithCustom.setSkuCustom(skuCustom);
            }
        }
    }

    @Override
    public SkuCustom findBySkuId(Long skuId){
        return skuCustomDao.findNormalBySkuId(skuId);
    }
}
