package moonstone.adv.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.adv.model.AdvColumn;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/10/9.
 */
@Repository
public class AdvColumnDao extends MyBatisDao<AdvColumn>{
    /**
     * 总记录数
     * @return
     */
    public int countTotal() {
        return getSqlSession().selectOne(sqlId("countTotal"));
    }

    /**
     * 获取所有
     * @return
     * @throws Exception
     */
    public List<AdvColumn> list() {
        return getSqlSession().selectList(sqlId("list"));
    }


    public List<AdvColumn> findBy(Map<String, Object> criteria) {
        return getSqlSession().selectList(sqlId("findBy"), criteria);
    }

    /**
     * 通过广告位置
     * @param position
     * @return
     */
    public AdvColumn findByPosition(Integer position) {
        return getSqlSession().selectOne(sqlId("findByPosition"), position);
    }

    /**
     * 通过名称和广告位置
     * @param position
     * @return
     */
    public AdvColumn findBySnPosition(String sn, Integer position) {
        Map<String, Object> whereMap = new HashMap<>();
        whereMap.put("sn", sn);
        whereMap.put("position", position);
        return getSqlSession().selectOne(sqlId("findBySnPosition"), whereMap);
    }
}
