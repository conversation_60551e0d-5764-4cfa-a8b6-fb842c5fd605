package moonstone.membership.impl.service;

import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.membership.impl.dao.MembershipPriceDao;
import moonstone.membership.model.MembershipPrice;
import moonstone.membership.service.MembershipPriceReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
@RpcProvider
public class MembershipPriceReadServiceImpl implements MembershipPriceReadService{
    private final MembershipPriceDao membershipPriceDao;

    @Autowired
    public MembershipPriceReadServiceImpl(MembershipPriceDao membershipPriceDao) {
        this.membershipPriceDao = membershipPriceDao;
    }

    /**
     * 根据ID查找会员价记录
     * @param id
     * @return 查找到的会员价记录
     */
    @Override
    public Response<MembershipPrice> findById(Long id) {
        try {
            MembershipPrice membershipPrice = membershipPriceDao.findById(id);
            if(membershipPrice == null){
                log.error("no membershipPrice(id={}) found", membershipPrice);
                return Response.fail("membershipPrice.not.found");
            }
            return Response.ok(membershipPrice);
        } catch (Exception e) {
            log.error("failed to find MembershipPrice by id={}, cause:{}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("membershipPrice.find.fail");
        }
    }

    /**
     * 分页查找会员价记录
     * @param shopId 店铺id
     * @param pageNo 页码
     * @param pageSize 页内记录数量
     * @return
     */
    @Override
    public Response<Paging<MembershipPrice>> findBy(Long shopId, Integer pageNo, Integer pageSize) {
        try {
            PageInfo page = new PageInfo(pageNo, pageSize);

            Map<String, Object> criteria = Maps.newHashMap();
            criteria.put("shopId", shopId);
            criteria.put("offset", page.getOffset());
            criteria.put("limit", page.getLimit());

            return Response.ok(membershipPriceDao.paging(criteria));
        } catch (Exception e) {
            log.error("failed to find membershipPrice, cause: {}", Throwables.getStackTraceAsString(e));
            return Response.fail("membershipPrice.find.fail");
        }
    }

    /**
     * 在userIds和skuIds中分页查找会员价记录
     * @param ids
     * @param userIds
     * @param skuIds
     * @param shopId
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public Response<Paging<MembershipPrice>> findInUserIdsAndSkuIdsBy(@Nullable List<Long> ids,
                                                                      @Nullable List<Long> userIds,
                                                                      @Nullable List<Long> skuIds,
                                                                      Long shopId,
                                                                      Integer pageNo,
                                                                      Integer pageSize) {
        try {
            PageInfo page = new PageInfo(pageNo, pageSize);

            Map<String, Object> criteria = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(ids)) {
                criteria.put("ids", ids);
            }
            criteria.put("userIds", userIds);
            criteria.put("skuIds", skuIds);
            criteria.put("shopId", shopId);
            criteria.put("offset", page.getOffset());
            criteria.put("limit", page.getLimit());

            return Response.ok(membershipPriceDao.pagingInUserIdsAndSkuIds(criteria));
        } catch (Exception e) {
            log.error("failed to find membershipPrice in userIds({}), skuIds({}), cause: {}",
                    userIds, skuIds, Throwables.getStackTraceAsString(e));
            return Response.fail("membershipPrice.find.fail");
        }
    }

    /**
     * 根据SKU ID和用户ID查找记录
     * @param skuId
     * @param userId
     * @return
     */
    @Override
    public Response<MembershipPrice> findBySkuIdAndUserId(Long skuId, Long userId) {
        try {
            MembershipPrice membershipPrice = membershipPriceDao.findByUserIdAndSkuId(userId,skuId);
            return Response.ok(membershipPrice);
        } catch (Exception e) {
            log.error("failed to find MembershipPrice, cause:{}", Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("membershipPrice.find.fail");
        }
    }

}
