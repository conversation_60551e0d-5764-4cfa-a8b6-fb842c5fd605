package moonstone.thirdParty.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.thirdParty.model.ThirdPartySkuShop;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ThirdPartySkuShopDao extends MyBatisDao<ThirdPartySkuShop> {
    public List<String> findOuterSkuIdByThirdPartyIdAndShopId(Integer thirdPartyId, Long shopId, Boolean blockV2) {
        Map<String, Object> params = new HashMap<>();
        params.put("thirdPartyId", thirdPartyId);
        params.put("shopId", shopId);
        if (Boolean.TRUE.equals(blockV2)) {
            params.put("blockV2", blockV2);
        }

        return getSqlSession().selectList(sqlId("findOuterSkuIdByThirdPartyIdAndShopId"), params);
    }

    public List<ThirdPartySkuShop> findByThirdPartyIdAndShopId(Integer thirdPartyId, Long shopId) {
        return getSqlSession().selectList(sqlId("findByThirdPartyIdAndShopId"), ImmutableMap.of(
                "thirdPartyId", thirdPartyId, "shopId", shopId));
    }

    public ThirdPartySkuShop findByThreeId(Integer thirdPartyId, Long shopId, String outerSkuId) {
        return getSqlSession().selectOne(sqlId("findByThreeId"), ImmutableMap.of(
                "shopId", shopId, "thirdPartyId", thirdPartyId, "outerSkuId", outerSkuId));
    }

    public Boolean updateStatusByThirdPartyIdAndShopId(Integer thirdPartyId, Long shopId, Integer status) {
        return getSqlSession().update(sqlId("updateStatusByThirdPartyIdAndShopId"), ImmutableMap.of(
                "thirdPartyId", thirdPartyId, "shopId", shopId, "status", status)) == 1;
    }

    public Boolean updateStatus(Long id, Integer status) {
        return getSqlSession().update(sqlId("updateStatus"), ImmutableMap.of("id", id, "status", status)) == 1;
    }

    public List<ThirdPartySkuShop> pagingByThirdPartyIdAndShopId(Integer thirdPartyId,
                                                                 Long shopId,
                                                                 @Nullable String outerSkuId,
                                                                 @Nullable String outerSkuName,
                                                                 @Nullable Integer matchStatus,
                                                                 @Nullable String depotName,
                                                                 Integer offset,
                                                                 Integer limit,
                                                                 Boolean blockV2) {
        Map<String, Object> params = new HashMap<>();
        params.put("thirdPartyId", thirdPartyId);
        params.put("shopId", shopId);
        params.put("outerSkuId", outerSkuId);
        params.put("outerSkuName", outerSkuName);
        params.put("matchStatus", matchStatus);
        params.put("depotName", StringUtils.isBlank(depotName) ? null : depotName);
        params.put("offset", offset);
        params.put("limit", limit);
        if (Boolean.TRUE.equals(blockV2)) {
            params.put("blockV2", blockV2);
        }
        return getSqlSession().selectList(sqlId("pagingByThirdPartyIdAndShopId"), params);
    }

    public Long countByThirdPartyIdAndShopId(Integer thirdPartyId,
                                             Long shopId,
                                             @Nullable String outerSkuId,
                                             @Nullable String outerSkuName,
                                             @Nullable Integer matchStatus,
                                             @Nullable String depotName,
                                             Boolean blockV2) {
        Map<String, Object> params = new HashMap<>();
        params.put("thirdPartyId", thirdPartyId);
        params.put("shopId", shopId);
        params.put("outerSkuId", outerSkuId);
        params.put("outerSkuName", outerSkuName);
        params.put("matchStatus", matchStatus);
        params.put("depotName", StringUtils.isBlank(depotName) ? null : depotName);
        if (Boolean.TRUE.equals(blockV2)) {
            params.put("blockV2", blockV2);
        }
        return getSqlSession().selectOne(sqlId("countByThirdPartyIdAndShopId"), params);
    }

    public Long countThirdPartySkuShopWithStock(Map<String, Object> query) {
        return this.getSqlSession().selectOne(sqlId("countThirdPartySkuShopWithStock"), query);
    }

    public List<ThirdPartySkuShop> pagesThirdPartySkuShopWithStock(Map<String, Object> query) {
        return this.getSqlSession().selectList(sqlId("pagesThirdPartySkuShopWithStock"), query);
    }

    public ThirdPartySkuShop selectOne(Map<String, Object> query) {
        return this.getSqlSession().selectOne(sqlId("selectOne"), query);
    }

    public List<ThirdPartySkuShop> listByOuterSkuIds(List<String> outerSkuIds) {
        return this.getSqlSession().selectList(sqlId("listByOuterSkuIds"), ImmutableMap.of("outerSkuIds", outerSkuIds));
    }
}
