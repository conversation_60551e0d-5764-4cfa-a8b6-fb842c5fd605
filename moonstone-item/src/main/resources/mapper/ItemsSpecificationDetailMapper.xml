<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ItemsSpecificationDetail">
    <!-- 通用查询映射结果 -->
    <resultMap id="itemsSpecificationDetailMap" type="ItemsSpecificationDetail">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="item_specification_id" property="itemSpecificationId" />
        <result column="item_id" property="itemId" />
        <result column="name" property="name" />
        <result column="sequence" property="sequence" />
        <result column="front_id" property="frontId" />
        <result column="version" property="version" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id ,
    </sql>

    <sql id="tb">
        dt_items_specification_detail
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
         shop_id, item_specification_id, item_id, `name`, `sequence`, front_id, version, created_by, created_time, updated_by, updated_time, deleted
    </sql>

    <sql id="vals">
        #{shopId}, #{itemSpecificationId}, #{itemId}, #{name}, #{sequence}, #{frontId}, #{version}, #{createdBy}, #{createdTime}, #{updatedBy}, #{updateTime}, 0
    </sql>

    <sql id="criteria">
        deleted = 0
        <if test="id !=null"> AND id= #{id}</if>
        <if test="ids != null"> AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="shopId != null"> AND shop_id = #{shopId}</if>
        <if test="itemSpecificationId != null">AND item_specification_id = #{itemSpecificationId}</if>
        <if test="itemId != null">AND item_id = #{itemId}</if>
        <if test="name != null">AND `name` = #{name}</if>
        <if test="sequence != null">AND `sequence` = #{sequence}</if>
        <if test="frontId != null">AND front_id = #{frontId}</if>
        <if test="version != null">AND version = #{version}</if>
        <if test="createdBy != null">AND created_by = #{createdBy}</if>
        <if test="createdTime != null">AND created_time = #{createdTime}</if>
        <if test="updatedBy != null">AND updated_by = #{updatedBy}</if>
        <if test="updateTime != null">AND updated_time = #{updateTime}</if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <insert id="create" parameterType="ItemsSpecificationDetail" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="ItemsSpecificationDetail">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="id !=null">id= #{id},</if>
            <if test="shopId != null">  shop_id = #{shopId},</if>
            <if test="itemSpecificationId != null">  item_specification_id = #{itemSpecificationId},</if>
            <if test="itemId != null">  item_id = #{item}</if>
            <if test="name != null">  `name` = #{name},</if>
            <if test="sequence != null">  `sequence` = #{sequence},</if>
            <if test="frontId != null">  front_id = #{frontId},</if>
            <if test="version != null">  version = #{version},</if>
            <if test="updatedBy != null">  updated_by = #{updatedBy},</if>
            <if test="deleted != null">  deleted = #{deleted},</if>
            updated_time = #{updateTime}
        </set>
        WHERE id = #{id}
        and deleted = 0
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <delete id="deletes" parameterType="list">
        DELETE FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="logicDelete" parameterType="long">
        UPDATE
        <include refid="tb"/>
        SET deleted = 1
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="long" resultMap="itemsSpecificationDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} and deleted = 0
    </select>

    <select id="selectOne" parameterType="map" resultMap="itemsSpecificationDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="selectList" parameterType="map" resultMap="itemsSpecificationDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="itemsSpecificationDetailMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>


</mapper>