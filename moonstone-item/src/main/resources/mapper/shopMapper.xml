<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Shop">
    <resultMap id="ShopMap" type="Shop">
        <id column="id" property="id"/>
        <result column="outer_id" property="outerId"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="name" property="name"/>
        <result column="status" property="status"/>
        <result column="type" property="type"/>
        <result column="is_supply" property="isSupply"/>
        <result column="invite_code" property="inviteCode"/>
        <result column="phone" property="phone"/>
        <result column="business_id" property="businessId"/>
        <result column="image_url" property="imageUrl"/>
        <result column="address" property="address"/>
        <result column="jd_stock_check" property="jdStockCheck"/>
        <result column="extra_json" property="extraJson"/>
        <result column="tags_json" property="tagsJson"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_shops
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        outer_id, user_id, user_name, `name`, status, `type`,`is_supply`, `invite_code`, phone, business_id,
        image_url, address, jd_stock_check,extra_json, tags_json, created_at, updated_at
    </sql>

    <sql id="vals">
        #{outerId}, #{userId}, #{userName}, #{name}, #{status}, #{type},#{isSupply}, #{inviteCode}, #{phone},
        #{businessId},
        #{imageUrl},#{address},#{jdStockCheck},#{extraJson},#{tagsJson}, now(), now()
    </sql>


    <insert id="create" parameterType="Shop" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findById" parameterType="long" resultMap="ShopMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByUserId" parameterType="long" resultMap="ShopMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE user_id = #{userId} and status != -99
    </select>

    <select id="findByName" parameterType="string" resultMap="ShopMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE name = #{name} and status != -99
    </select>

    <select id="findByInviteCode" parameterType="string" resultMap="ShopMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE invite_code = #{inviteCode} and status != -99
    </select>

    <select id="allShopId" resultType="Long">
        SELECT id
        FROM
        <include refid="tb"/>
        WHERE status != -99
    </select>

    <select id="listByNamePart" parameterType="string" resultMap="ShopMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE name like CONCAT('%', #{namePart}, '%') and status != -99
    </select>

    <select id="findByIds" parameterType="list" resultMap="ShopMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <update id="update" parameterType="Shop">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="outerId != null">outer_id = #{outerId},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="inviteCode != null">`invite_code` = #{inviteCode},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="isSupply != null">is_supply = #{isSupply},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="address != null">address = #{address},</if>
            <if test="jdStockCheck != null">jd_stock_check = #{jdStockCheck},</if>
            <if test="extraJson != null">extra_json = #{extraJson},</if>
            <if test="jdStockCheck != null">jd_stock_check = #{jdStockCheck},</if>
            updated_at=now()
        </set>
        WHERE id=#{id}
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id = #{id}
    </update>


    <update id="batchUpdateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id IN
        <foreach item="id" collection="ids"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- for search -->
    <select id="lastId" resultType="long">
        SELECT MAX(id) FROM
        <include refid="tb"/>
    </select>


    <select id="listTo" resultMap="ShopMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE <![CDATA[
          id < #{lastId}
        ]]>
        ORDER BY id DESC LIMIT #{limit}
    </select>


    <select id="listSince" resultMap="ShopMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE  <![CDATA[
          id < #{lastId} AND updated_at > #{since}
        ]]>
        and status != -99
        ORDER BY id DESC LIMIT #{limit}
    </select>

    <select id="findByOuterId" parameterType="string" resultMap="ShopMap">
        SELECT * FROM
        <include refid="tb"/>
        WHERE outer_id=#{outerId}
    </select>

    <update id="updateTags" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET
        tags_json = #{tagsJson},
        updated_at= now()
        WHERE id = #{id}
    </update>

    <sql id="criteria">
        <if test="name != null">AND `name` LIKE CONCAT(#{name}, '%')</if>
        <if test="userId != null">AND `user_id` = #{userId}</if>
        <if test="type != null">AND `type` = #{type}</if>
        <if test="isSupply != null">AND `is_suppy` = #{isSupply}</if>
        <if test="status != null">AND `status` = #{status}</if>
        <if test="status == null">AND `status` != -99</if>
    </sql>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="ShopMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        order by `id` desc
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findByIsSupply" parameterType="int" resultMap="ShopMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE is_supply = #{isSupply}
        and status = #{status}
    </select>

    <select id="findIdByNameLike" parameterType="map" resultType="long">
        select id
        from <include refid="tb"/>
        where `name` like CONCAT('%', #{name}, '%')
    </select>

    <select id="list" resultMap="ShopMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER BY id DESC
    </select>

</mapper>