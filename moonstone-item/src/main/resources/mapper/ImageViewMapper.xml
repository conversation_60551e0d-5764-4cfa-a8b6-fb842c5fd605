<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ImageView">
    <resultMap id="ImageViewMapper" type="moonstone.common.model.ImageView">
        <id column="id" property="id"/>
        <result column="hash" property="hash"/>
        <result column="image" property="image"/>
    </resultMap>
    <sql id="tb">
        image_view
    </sql>

    <select id="findById" parameterType="long" resultMap="ImageViewMapper">
        SELECT
        `id`, `image`, `hash`
        FROM
        <include refid="tb"/>
        <where>
            `id` = #{id}
        </where>
    </select>

    <insert id="create" parameterType="moonstone.common.model.ImageView" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb"/>
        (`hash`, `image`)
        VALUES (#{hash}, #{image}) on duplicate key  update `hash` = #{hash}
    </insert>

    <select id="findByHash" parameterType="map" resultMap="ImageViewMapper">
        SELECT
        `id`, `image`, `hash`
        FROM
        <include refid="tb"/>
        <where>
            `hash` = #{hash}
        </where>
    </select>

</mapper>