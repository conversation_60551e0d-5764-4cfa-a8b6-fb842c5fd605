<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ShopPayInfo">
    <resultMap id="ShopPayInfoMap" type="ShopPayInfo">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="pay_channel" property="payChannel" />
        <result column="mch_id" property="mchId" />
        <result column="account_no" property="accountNo" />
        <result column="secret_key" property="secretKey" />
        <result column="wx_cert_file_status" property="wxCertFileStatus" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="usage_channel" property="usageChannel"/>
        <result column="extra_json" property="extraJson"/>
        <result column="public_key" property="publicKey"/>
        <result column="status_time" property="statusTime"/>
    </resultMap>
    <!--表-->
    <sql id="tb">parana_shop_pay_info</sql>
    <!--除ID外的所有字段-->
    <sql id="cols_exclude_id">
        shop_id,
        pay_channel,
        mch_id,
        account_no,
        secret_key,
        wx_cert_file_status,
        status,
        created_at,
        updated_at,
        usage_channel,
        extra_json,
        public_key,
        status_time
    </sql>
    <!--所有字段-->
    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>
    <!--值-->
    <sql id="values">
        #{shopId},
        #{payChannel},
        #{mchId},
        #{accountNo},
        #{secretKey},
        #{wxCertFileStatus},
        #{status},
        now(),
        now(),
        #{usageChannel},
        #{extraJson},
        #{publicKey},
        UNIX_TIMESTAMP(NOW())
    </sql>
    <!--更新条件-->
    <sql id="updateCondition">
        <set>
            updated_at = now(),
            <if test="payChannel!=null">`pay_channel` = #{payChannel},</if>
            <if test="mchId!=null">`mch_id` = #{mchId},</if>
            <if test="accountNo!=null">`account_no` = #{accountNo},</if>
            <if test="secretKey!=null">`secret_key` = #{secretKey},</if>
            <if test="wxCertFileStatus!=null">`wx_cert_file_status` = #{wxCertFileStatus},</if>
            <if test="status!=null">`status` = #{status},</if>
            <if test="usageChannel!=null">`usage_channel` = #{usageChannel},</if>
            <if test="extraJson!=null">`extra_json` = #{extraJson},</if>
            <if test="publicKey!=null">`public_key` = #{publicKey},</if>
            <if test="statusTime!=null">`status_time` = #{statusTime},</if>
        </set>
    </sql>
    <!--查询条件-->
    <sql id="queryCondition">
        <where>
            <if test="payChannel!=null">AND pay_channel = #{payChannel}</if>
            <if test="mchId!=null">AND mch_id = #{mchId}</if>
            <if test="accountNo!=null">AND account_no = #{accountNo}</if>
            <if test="shopIdList != null">
                AND shop_id in
                <foreach collection="shopIdList" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="usageChannel != null">
                AND usage_channel = #{usageChannel}
            </if>
        </where>
    </sql>
    <!--新增一条记录-->
    <insert id="create"
            parameterType="ShopPayInfo"
            keyProperty="id"
            useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="values"/>)
    </insert>
    <!--新增多条记录-->
    <insert id="creates" parameterType="ShopPayInfo" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
                #{i.shopId},
                #{i.payChannel},
                #{i.mchId},
                #{i.accountNo},
                #{i.secretKey},
                #{i.wxCertFileStatus},
                #{i.status},
                now(),
                now(),
                #{i.usageChannel},
                #{i.extraJson},
                #{i.publicKey},
                UNIX_TIMESTAMP(NOW())
            )
        </foreach>
    </insert>
    <!--删除一条记录-->
    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>
    <!--更新一条记录-->
    <update id="update" parameterType="ShopPayInfo">
        UPDATE <include refid="tb"/>
        <include refid="updateCondition"/>
        WHERE id = #{id}
    </update>
    <!--根据ID查找一条记录-->
    <select id="findById" parameterType="long" resultMap="ShopPayInfoMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} AND status != -1
    </select>
    <!--根据页信息查找多条记录-->
    <select id="paging" parameterType="map" resultMap="ShopPayInfoMap">
        SELECT <include refid="cols_all"/>
        FROM <include refid="tb"/>
        <include refid="queryCondition"/>
        order by created_at desc limit #{offset},#{limit}
    </select>

    <select id="count" parameterType="map" resultType="long">
        select count(*)
        FROM <include refid="tb"/>
        <include refid="queryCondition"/>
    </select>

    <!--查询所有记录-->
    <select id="findAll" resultMap="ShopPayInfoMap">
      SELECT <include refid="cols_all"/>
      FROM <include refid="tb"/>
      WHERE status != -1 ORDER BY created_at DESC
    </select>
    <!--根据shopId查找记录-->
    <select id="findByShopId" parameterType="long" resultMap="ShopPayInfoMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId}
    </select>
    <!--根据shopId和支付渠道查找有效的记录-->
    <select id="findByShopIdAndPayChannel" parameterType="map" resultMap="ShopPayInfoMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId} AND pay_channel = #{payChannel} AND status != -1
        LIMIT 1
    </select>

    <select id="findAllByShopIdAndPayChannel" parameterType="map" resultMap="ShopPayInfoMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId}
        AND pay_channel = #{payChannel}
        AND `status` in
        <foreach collection="statusList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getById" resultMap="ShopPayInfoMap" parameterType="map">
        select <include refid="cols_all"/>
        from <include refid="tb"/>
        where id = #{id}
    </select>

    <update id="revokeActive" parameterType="map">
        update <include refid="tb"/>
        set `status` = -1,
            updated_at = now()
        where shop_id = #{shopId}
        and usage_channel = #{usageChannel}
        and `status` = 1
    </update>

    <update id="updateStatus" parameterType="map">
        update <include refid="tb"/>
        set `status` = #{newStatus},
            updated_at = now()
        where id = #{id}
        and `status` = #{oldStatus}
    </update>

    <select id="find" parameterType="map" resultMap="ShopPayInfoMap">
        select <include refid="cols_all"/>
        from <include refid="tb"/>
        where shop_id = #{shopId}
        and `status` in
        <foreach collection="statusList" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
        <if test="usageChannel != null">
            and usage_channel = #{usageChannel}
        </if>
    </select>

    <select id="findByStatus" parameterType="map" resultMap="ShopPayInfoMap">
        select <include refid="cols_all"/>
        from <include refid="tb"/>
        where shop_id = #{shopId} and `status` = #{status}
    </select>

    <select id="findByIds" parameterType="list" resultMap="ShopPayInfoMap">
        SELECT id,
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach collection="list" separator="," open="("
                 close=")" item="id">
            #{id}
        </foreach>
    </select>
</mapper>