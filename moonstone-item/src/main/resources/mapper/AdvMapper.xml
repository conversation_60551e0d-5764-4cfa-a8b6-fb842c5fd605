<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Adv">
    <resultMap id="AdvMap" type="Adv">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="column_id" property="columnId"/>
        <result column="column_sn" property="columnSn"/>
        <result column="name" property="name"/>
        <result column="sort_index" property="sortIndex"/>
        <result column="started_at" property="startedAt"/>
        <result column="ended_at" property="endedAt"/>
        <result column="img" property="img"/>
        <result column="link_type" property="linkType"/>
        <result column="link_id" property="linkId"/>
        <result column="url" property="url"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_adv
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `shop_id`, `column_id`, `column_sn`, `name`, `sort_index`, `started_at`, `ended_at`,
        `img`, `link_type`, `link_id`, `url`, `status`, `created_at`, `updated_at`
    </sql>

    <sql id="vals">
        #{shopId}, #{columnId}, #{columnSn}, #{name}, #{sortIndex}, #{startedAt}, #{endedAt},
        #{img}, #{linkType}, #{linkId}, #{url}, #{status}, now(), now()
    </sql>

    <sql id="criteria">
        <if test="ids != null">AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="shopId!=null">AND shop_id = #{shopId}</if>
        <if test="columnId!=null">AND column_id = #{columnId}</if>
        <if test="columnSn!=null">AND column_sn = #{columnSn}</if>
        <if test="name != null">AND `name` LIKE CONCAT(#{name} ,'%')</if>
        <if test="sortIndex!=null">AND sort_index = #{sortIndex}</if>
        <if test="img != null">AND img = #{img}</if>
        <if test="linkType != null">AND link_type = #{linkType}</if>
        <if test="linkId != null">AND link_id = #{linkId}</if>
        <if test="url != null">AND url = #{url}</if>
        <if test="statuses == null and status == null">
            AND `status` != -1
        </if>
        <if test="status != null">AND status = #{status}</if>
        <if test="statuses != null">
            and status in
            <foreach collection="statuses" open="(" close=")" separator="," item="s">
                #{s}
            </foreach>
        </if>
        <if test="startedAt != null">AND <![CDATA[started_at >= #{startedAt}]]> </if>
        <if test="endedAt != null">AND <![CDATA[ended_at < #{endedAt}]]> </if>
        <if test="updatedFrom != null">AND <![CDATA[updated_at >= #{updatedFrom}]]> </if>
        <if test="updatedTo != null">AND <![CDATA[updated_at < #{updatedTo}]]> </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'shopId'">ORDER BY shop_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'columnId'">ORDER BY column_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'linkType'">ORDER BY link_type
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'linkId'">ORDER BY link_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'sortIndex'">ORDER BY sort_index
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'startedAt'">ORDER BY started_at
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'endedAt'">ORDER BY ended_at
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'updatedAt'">ORDER BY updated_at
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'columnIdAscSortIndex'">ORDER BY column_id ASC, sortIndex
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'shopIdAscColumnIdAscSortIndex '">ORDER BY shop_id ASC, column_id ASC, sortIndex
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <insert id="create" parameterType="Sku" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="countByColumnId" resultType="int">
        select count(*)
        from
        <include refid="tb"/>
        where column_id=#{columnId} and status != -1
    </select>

    <select id="countTotal" resultType="int">
        select count(1)
        from
        <include refid="tb"/>
        where status != -1
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="AdvMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findById" resultMap="AdvMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        where id=#{id}
    </select>

    <select id="findByColumnIdShopIdInspire" parameterType="map" resultMap="AdvMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        where column_id=#{columnId} and shop_id=#{shopId}
        and <![CDATA[started_at<=now()]]> and <![CDATA[ended_at>=now()]]>
        and status=1
        order by sort_index asc
    </select>

    <select id="findByColumnSnShopIdInspire" parameterType="map" resultMap="AdvMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        where column_sn=#{columnSn}
        <if test="shopId!=null">and shop_id = #{shopId}</if>
        and <![CDATA[started_at<=now()]]> and <![CDATA[ended_at>=now()]]>
        and status=1
        order by sort_index asc
    </select>

    <select id="findByShopIdsInspire" parameterType="map" resultMap="AdvMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        where
        shop_id in
        <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        and <![CDATA[started_at<=now()]]> and <![CDATA[ended_at>=now()]]>
        and status=1
        order by sort_index asc
    </select>

    <select id="findByColumnIdShopId" parameterType="map" resultMap="AdvMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        where column_id=#{columnId} and shop_id=#{shopId}
        and status != -1
        order by sort_index asc
    </select>

    <select id="maxSortIndexByColumnIdShopId" parameterType="map" resultType="Integer">
        select MAX(`sort_index`)
        from
        <include refid="tb"/>
        where column_id=#{columnId} and shop_id=#{shopId}
        and status != -1
    </select>

    <select id="listByColumnSn" resultMap="AdvMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        where column_sn=#{columnSn}
        and <![CDATA[started_at<=now()]]> and <![CDATA[ended_at>=now()]]>
        and status != -1
    </select>

    <select id="getListFilter"  parameterType="map" resultMap="AdvMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        where 1=1
        <if test="status != '' and status != null">
            and status=#{status}
        </if>
        order by id desc
    </select>

    <select id="list" resultMap="AdvMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        order by id desc
    </select>

    <update id="update" parameterType="map">
        update
        <include refid="tb"/>
        <set>
            <if test="shopId != null">`shop_id` = #{shopId},</if>
            <if test="columnId != null">`column_id` = #{columnId},</if>
            <if test="columnSn != null">`column_sn` = #{columnSn},</if>
            <if test="startedAt != null">`started_at` = #{startedAt},</if>
            <if test="endedAt != null">`ended_at` = #{endedAt},</if>
            <if test="name != null">`name` = #{name},</if>
            <if test="sortIndex != null">`sort_index` = #{sortIndex},</if>
            <if test="img != null">`img` = #{img},</if>
            <if test="linkType != null">`link_type` = #{linkType},</if>
            <if test="linkId != null">`link_id` = #{linkId},</if>
            <if test="url != null">`url` = #{url},</if>
            <if test="status != null">`status` = #{status},</if>
            updated_at=now()
        </set>
        where id=#{id}
    </update>

    <update id="updateStatus" parameterType="map">
        update
        <include refid="tb"/>
        set
        status=#{status},
        updated_at=now()
        where id=#{id}
    </update>

    <delete id="deletes">
        delete from
        <include refid="tb"/>
        where id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="delete">
        delete from
        <include refid="tb"/>
        where id=#{id}
    </delete>

</mapper>