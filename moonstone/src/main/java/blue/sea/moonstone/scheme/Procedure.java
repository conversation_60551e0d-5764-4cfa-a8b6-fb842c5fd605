package blue.sea.moonstone.scheme;

import java.util.List;

public class Procedure implements SchemeFuc {
    @Override
    public AST execute(Executor executor, AST ast) {
        if (!ast.isBracket()) {
            return ast;
        }
        AST lambda = executor.eval(ast.lisp.get(0));
        List<AST> args = lambda.lisp.get(1).lisp;
        int realArgIndex = 1;
        for (AST arg : args) {
            AST argument = executor.eval(ast.lisp.get(realArgIndex++));
            executor.env.put(arg.name(), (c, a) -> argument);
        }
        AST begin = new AST();
        begin.lisp.add(AST.ofElement("core.begin"));
        for (int i = 2; i < lambda.size(); i++) {
            begin.lisp.add(lambda.lisp.get(i));
        }
        return executor.execute(begin);
    }
}
