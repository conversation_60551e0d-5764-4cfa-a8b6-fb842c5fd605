package blue.sea.moonstone.scheme;

import java.nio.charset.StandardCharsets;

public class Newline implements SchemeFuc {
    @Override
    public AST execute(Executor executor, AST ast) {
        if (ast.size() == 0) {
            return ast;
        }
        try {
            executor.getIO("current").write("\n".getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return new AST();
    }
}
