package moonstone.shopWxa.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.model.Either;
import moonstone.shopWxa.model.ShopWxa;

import java.util.List;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/10/15.
 */
public interface ShopWxaReadService {
    Response<ShopWxa> findById(Long id);

    Response<List<ShopWxa>> findByShopId(Long shopId);

    Response<ShopWxa> findByWxaUserName(String wxaUserName);

    Response<ShopWxa> findByAppId(String appId);

    Response<ShopWxa> findByAppIdAndAppType(String appId, AppTypeEnum appType);

    Response<ShopWxa> findByShopIdAndStatus(Long shopId, Integer status);

    Response<Paging<ShopWxa>> findBy(String ids, Long shopId, String name, String appId,
                                     List<Integer> statuses, Integer status, Integer pageNo, Integer pageSize);

    /**
     * 查找一个`shopId`对应的地一个符合`status`的`AppId`
     *
     * @param shopId 店铺Id
     * @param status 状态
     * @return 对应的AppId
     */
    Either<String> findAppIdByShopId(Long shopId, Integer status);

    /**
     * 根据查询条件分页店铺小程序信息列表
     * @param query 查询条件
     * @return 店铺小程序信息列表
     */
    Paging<ShopWxa> pages(Map<String,Object> query);

    /**
     * 根据查询条件获取店铺小程序信息
     * @param queryMap 查询条件
     * @return 店铺小程序信息
     */
    ShopWxa getOne(Map<String, Object> queryMap);

    /**
     * 根据查询条件获取店铺小程序信息列表
     * @param queryMap 查询条件
     * @return 店铺小程序信息列表
     */
    List<ShopWxa> list(Map<String, Object> queryMap);

}
