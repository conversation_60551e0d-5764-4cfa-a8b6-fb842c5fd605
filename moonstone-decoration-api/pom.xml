<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>moonstone-mall</artifactId>
        <groupId>moonstone</groupId>
        <version>1.0.0.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>moonstone-decoration-api</artifactId>
    <version>1.0.0.RELEASE</version>

    <dependencies>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>moonstone</groupId>
            <artifactId>moonstone-item-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-open</artifactId>
            <version>4.2.0</version>
        </dependency>
    </dependencies>
</project>