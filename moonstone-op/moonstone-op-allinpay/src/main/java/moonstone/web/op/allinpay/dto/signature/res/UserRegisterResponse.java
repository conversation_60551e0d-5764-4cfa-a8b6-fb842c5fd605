package moonstone.web.op.allinpay.dto.signature.res;

import lombok.Data;
import moonstone.web.op.allinpay.dto.signature.AllinyBaseResponse;

import java.io.Serializable;

@Data
public class UserRegisterResponse extends AllinyBaseResponse implements Serializable {
    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 通联统一认证订单号
     */
    private String orderId;

    /**
     * 是否收费
     */
    private Boolean fee;

    /**
     * 证书申请状态信息
     */
    private Info info;

    @Data
    public static class Info implements Serializable {
        /**
         * 任务编号，可以查询状态和数字证书
         * taskid 只是一个申请提交数字证书的临时任务，时限是 24 小时
         */
        private String taskId;
    }

}
