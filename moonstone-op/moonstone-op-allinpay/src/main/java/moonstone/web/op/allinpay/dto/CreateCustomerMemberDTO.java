
package moonstone.web.op.allinpay.dto;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 创建通联会员
 *
 * @since 2021-02-19
 */
@Data
@ApiModel(value = "CreateCustomerMemberDTO对象", description = "静默创建通联会员")
public class CreateCustomerMemberDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "商户系统用户标识，商户系统中唯一编号", required = true)
	@NotEmpty(message = "商户系统用户标识不能为空")
	private String bizUserId;

	@ApiModelProperty(value = "类型 2-企业会员，3-个人会员", required = true)
	@NotNull(message = "类型不能为空")
	private Long memberType;

	@ApiModelProperty(value = "访问终端类型 1-mobile 2-pc", required = true)
	@NotEmpty(message = "访问终端类型不能为空")
	private Long source;

	@ApiModelProperty(value = "扩展参数")
	private JSONObject extendParam;

	@ApiModelProperty(value = "租客必须 账户标识 微信-openid,支付宝-userid", required = true)
	private String acct;

	@ApiModelProperty(value = "消费者必须 " +
		"支付账户类型 weChatPublic -微信公众号\n" +
		"weChatMiniProgram -微信小程序\n" +
		"aliPayService -支付宝生活号\n" +
		"unionPayjs -银联JS\n" +
		"操作类型是“set”必须上送")
	private String acctType;

}
