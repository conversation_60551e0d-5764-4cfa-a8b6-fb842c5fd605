package moonstone.web.op.allinpay.util;

import com.allinpay.sdk.bean.BizParameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
@Slf4j
public class AllinPayUtil {

	public static BizParameter getMap(Object object) {
		BizParameter ret = new BizParameter();
		Field[] field = object.getClass().getDeclaredFields();
		Field[] var3 = field;
		int var4 = field.length;

		for(int var5 = 0; var5 < var4; ++var5) {
			Field f = var3[var5];

			try {
				f.setAccessible(true);
				Object val = f.get(object);
				if (val != null && !"sign".equals(f.getName()) && !"serialVersionUID".equals(f.getName())) {
					ret.put(f.getName(), val);
				}
			} catch (Exception var8) {
				var8.printStackTrace();
			}
		}

		return ret;
	}


	/**
	 * 打开默认浏览器，支持苹果和windows
	 *
	 * @param url
	 */
	static void browser(final String url) {
		try {
			final String osName = System.getProperty("os.name");
			if (osName.startsWith("Windows")) {
				Runtime.getRuntime().exec("rundll32 url.dll,FileProtocolHandler  " + url);
			} else if (osName.startsWith("Mac OS")) {
				final Class<?> fileMgr = Class.forName("com.apple.eio.FileManager");
				final Method openURL = fileMgr.getDeclaredMethod("openURL", new Class[] { String.class });
				openURL.invoke(null, new Object[] { url });
			}
		} catch (final Exception e) {
			e.printStackTrace();
		}
	}

	public static String createSign(Map<String, String> params) {
		StringBuilder sb = new StringBuilder();
		Map<String, Object> sortParams = new TreeMap(params);
		Iterator var3 = sortParams.entrySet().iterator();

		while(var3.hasNext()) {
			Map.Entry<String, Object> entry = (Map.Entry)var3.next();
			String key = (String)entry.getKey();
			String value = String.valueOf(entry.getValue()).trim();
			if (!StringUtils.isEmpty(value)&&!"sign".equals(key)&&!"signType".equals(key)) {
				sb.append("&").append(key).append("=").append(value);
			}
		}

		String result = sb.toString().replaceFirst("&", "");
		System.out.println("签名字符串：       " + result);
		return result;
	}

}
