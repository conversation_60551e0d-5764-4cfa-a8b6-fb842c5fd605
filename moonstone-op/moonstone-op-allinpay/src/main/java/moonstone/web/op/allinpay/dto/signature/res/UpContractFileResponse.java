package moonstone.web.op.allinpay.dto.signature.res;

import lombok.Data;
import moonstone.web.op.allinpay.dto.signature.AllinyBaseResponse;

import java.io.Serializable;

@Data
public class UpContractFileResponse extends AllinyBaseResponse implements Serializable {
    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 通联统一认证订单号
     */
    private String orderId;

    /**
     * 是否收费
     */
    private Boolean fee;

    /**
     * 证书申请状态信息
     */
    private Info info;

    /**
     * 证书申请状态信息内部类
     */
    @Data
    public static class Info implements Serializable {
        /**
         * 创建的合同文件 ID，在后续接口中使用
         */
        private String fid;
    }
}
