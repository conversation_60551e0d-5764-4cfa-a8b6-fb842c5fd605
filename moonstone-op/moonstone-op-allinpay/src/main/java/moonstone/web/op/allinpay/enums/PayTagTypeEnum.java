package moonstone.web.op.allinpay.enums;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;

@Slf4j
@Getter
public enum PayTagTypeEnum {
	PHONE("PHONE", "绑定手机"),
	ACCT("ACCT", "绑定支付标识，例如微信openId"),
	;

	private String key;

	private String desc;

	PayTagTypeEnum(String key, String desc) {
		this.key = key;
		this.desc = desc;
	}

	public static String parse(Integer key) {
		PayTagTypeEnum[] faceId = PayTagTypeEnum.values();
		for (PayTagTypeEnum sub : faceId) {
			if (key.equals(sub.getKey())) {
				return sub.getDesc();
			}
		}
		return String.valueOf(key);
	}

	public static boolean bindAcctTag(CommonUser user) {
//		List<String> roles = user.getRoles();
//		if (CollectionUtils.isEmpty(roles)) {
//			log.error("未获取到用户角色 {}", user.getId());
//			throw new RuntimeException("未知的角色");
//		}
//		// 导购、普通消费者需要绑定acct
//		if(roles.contains(UserRole.BUYER.name()) || roles.contains(UserRole.SELLER.name())){
//			/**
//			 * 导购
//			 *
//
//			 //当前用户是导购
//			 var guider = guiderCache.findByShopIdAndUserId(shopId, userId);
//			 if (guider.isPresent()) {
//				 criteria.setGuiderUserId(userId);
//				 return criteria;
//			 }
//			 */
//			return true;
//		}

		// 全部绑定acct
		return true;
	}
}
