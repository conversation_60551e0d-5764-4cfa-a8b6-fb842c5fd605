
package moonstone.web.op.allinpay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 解绑银行卡
 *

 * @since 2021-02-19
 */
@Data
@ApiModel(value = "UnbindBankCardDTO对象", description = "解绑银行卡")
public class UnbindBankCardDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "商户系统用户标识，商户系统中唯一编号", required = true)
	@NotEmpty(message = "商户系统用户标识不能为空")
	private String bizUserId;

	@ApiModelProperty(value = "银行卡号", required = true)
	@NotNull(message = "银行卡号不能为空")
	private String cardNo;
}
