<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2014 杭州端点网络科技有限公司
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="CommissionRule">
    <resultMap id="CommissionRuleMap" type="CommissionRule">
        <id column="id" property="id"/>
        <result column="business_type" property="businessType"/>
        <result column="business_id" property="businessId"/>
        <result column="business_name" property="businessName"/>
        <result column="rate" property="rate"/>
        <result column="description" property="description"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_commission_rules
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        business_type, business_id, business_name, rate, description,created_at, updated_at
    </sql>

    <sql id="vals">
        #{businessType},  #{businessId}, #{businessName}, #{rate},#{description},now(), now()
    </sql>

    <sql id="criteria">
        WHERE 1 = 1
        <if test="id != null">AND id = #{id}</if>
        <if test="businessType != null">AND business_type = #{businessType}</if>
        <if test="businessId != null">AND business_id = #{businessId}</if>
        <if test="businessName != null">AND business_name = #{businessName}</if>
        <if test="startAt != null">AND created_at &gt;= #{startAt}</if>
        <if test="endAt != null">AND created_at &lt; #{endAt}</if>
    </sql>

    <sql id="update_criteria">
        <set>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="businessId != null">business_id = #{businessId},</if>
            <if test="businessName != null">business_name = #{businessName},</if>
            <if test="rate != null">rate = #{rate},</if>
            <if test="description != null">description = #{description},</if>
            updated_at=now()
        </set>
    </sql>


    <select id="findById" parameterType="long" resultMap="CommissionRuleMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByBusinessIdAndBusinessType" parameterType="map" resultMap="CommissionRuleMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE business_id = #{businessId} AND business_type = #{businessType} LIMIT 1
    </select>


    <select id="list" parameterType="map" resultMap="CommissionRuleMap">
        SELECT
        <include refid="cols_all"/>
        FROM <include refid="tb"/>
        <include refid="criteria"/>
    </select>

    <insert id="create" parameterType="CommissionRule" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <update id="update" parameterType="CommissionRule">
        UPDATE
        <include refid="tb"/>
        <include refid="update_criteria"/>
        WHERE id=#{id}
    </update>

    <!-- 计数 -->
    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <include refid="criteria"/>
    </select>

    <!-- 分页查询 -->
    <select id="paging" parameterType="map" resultMap="CommissionRuleMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <include refid="criteria"/>
        ORDER BY id DESC LIMIT #{offset}, #{limit}
    </select>

    <delete id="delete" parameterType="map">
        delete from <include refid="tb"/>
        where id=#{id}
    </delete>



</mapper>