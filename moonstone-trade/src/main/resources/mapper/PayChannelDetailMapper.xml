<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2015 杭州端点网络科技有限公司
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="PayChannelDetail">
    <resultMap id="PayChannelDetailMap" type="PayChannelDetail">
        <id column="id" property="id" />
        <result column="pay_channel_code" property="payChannelCode"/>
		<result column="channel" property="channel"/>
		<result column="gateway_commission" property="gatewayCommission"/>
		<result column="gateway_rate" property="gatewayRate"/>
		<result column="trade_fee" property="tradeFee"/>
		<result column="actual_income_fee" property="actualIncomeFee"/>
		<result column="trade_type" property="tradeType"/>
		<result column="trade_no" property="tradeNo"/>
		<result column="gateway_trade_no" property="gatewayTradeNo"/>
		<result column="check_status" property="checkStatus"/>
		<result column="check_finished_at" property="checkFinishedAt"/>
		<result column="trade_finished_at" property="tradeFinishedAt"/>
        <result column="channel_account" property="channelAccount"/>
        <result column="extra_json" property="extraJson"/>

        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="table_name">
        parana_pay_channel_details
    </sql>

    <sql id="columns_exclude_id">
        `pay_channel_code`,`channel`,`gateway_commission`,`gateway_rate`,`trade_fee`,actual_income_fee,
		`trade_type`,`trade_no`,`gateway_trade_no`,`check_status`,`check_finished_at`,
		`trade_finished_at`,`channel_account`,
		`extra_json`,
        created_at, updated_at
    </sql>

    <sql id="columns">
        id,
        <include refid="columns_exclude_id"/>
    </sql>

    <sql id="values">
        #{payChannelCode},#{channel},#{gatewayCommission},#{gatewayRate},#{tradeFee},#{actualIncomeFee},
		#{tradeType},#{tradeNo},#{gatewayTradeNo},#{checkStatus},#{checkFinishedAt},
		#{tradeFinishedAt},#{channelAccount},
		#{extraJson},
        now(),now()
    </sql>

    <sql id="criteria">
        1 = 1
        <if test="payChannelCode != null"> and `pay_channel_code` = #{payChannelCode}</if>
		<if test="channel != null"> and `channel` = #{channel}</if>
		<if test="gatewayCommission != null"> and `gateway_commission` = #{gatewayCommission}</if>
		<if test="gatewayRate != null"> and `gateway_rate` = #{gatewayRate}</if>
		<if test="tradeFee != null"> and `trade_fee` = #{tradeFee}</if>
		<if test="tradeType != null"> and `trade_type` = #{tradeType}</if>
		<if test="tradeNo != null"> and `trade_no` = #{tradeNo}</if>
		<if test="gatewayTradeNo != null"> and `gateway_trade_no` = #{gatewayTradeNo}</if>
		<if test="checkStatus != null"> and `check_status` = #{checkStatus}</if>
		<if test="checkFinishedAtStart != null"> and `check_finished_at` &gt;= #{checkFinishedAtStart}</if>
        <if test="checkFinishedAtEnd != null"> and `check_finished_at` &lt;= #{checkFinishedAtEnd}</if>
		<if test="tradeFinishedAtStart != null"> and `trade_finished_at` &gt;= #{tradeFinishedAtStart}</if>
        <if test="tradeFinishedAtEnd != null"> and `trade_finished_at` &lt;= #{tradeFinishedAtEnd}</if>
        <if test="channelAccount != null"> and `channel_account` = #{channelAccount}</if>
    </sql>

    <insert id="create" parameterType="PayChannelDetail" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES (<include refid="values"/>)
    </insert>

    <update id="update" parameterType="PayChannelDetail">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="payChannelCode != null"> `pay_channel_code` = #{payChannelCode}, </if>
			<if test="channel != null"> `channel` = #{channel}, </if>
			<if test="gatewayCommission != null"> `gateway_commission` = #{gatewayCommission}, </if>
			<if test="gatewayRate != null"> `gateway_rate` = #{gatewayRate}, </if>
			<if test="tradeFee != null"> `trade_fee` = #{tradeFee}, </if>
			<if test="tradeType != null"> `trade_type` = #{tradeType}, </if>
			<if test="tradeNo != null"> `trade_no` = #{tradeNo}, </if>
			<if test="gatewayTradeNo != null"> `gateway_trade_no` = #{gatewayTradeNo}, </if>
			<if test="checkStatus != null"> `check_status` = #{checkStatus}, </if>
            <if test="checkStatus == 0">
                `check_finished_at` = null,
            </if>
			<if test="checkFinishedAt != null"> `check_finished_at` = #{checkFinishedAt}, </if>
			<if test="tradeFinishedAt != null"> `trade_finished_at` = #{tradeFinishedAt}, </if>
            <if test="channelAccount != null"> `channel_account` = #{channelAccount},</if>
            <if test="actualIncomeFee != null"> `actual_income_fee` = #{actualIncomeFee},</if>
            <if test="extraJson != null">`extra_json` =  #{extraJson},</if>
            updated_at=now()
        </set>
        WHERE id=#{id}
    </update>

    <select id="findById" parameterType="long" resultMap="PayChannelDetailMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="PayChannelDetailMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach collection="list" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="table_name"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="PayChannelDetailMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER BY id DESC
        <if test="limit != null">
            LIMIT
            <if test="offset != null">#{offset},</if>
            #{limit}
        </if>
    </select>

    <select id="list" parameterType="map" resultMap="PayChannelDetailMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER BY id DESC
    </select>

    <delete id="delete" parameterType="map">
        delete from <include refid="table_name"/>
        where id=#{id}
    </delete>


    <select id="findPayChannelDetailByTradeNo" parameterType="map" resultMap="PayChannelDetailMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        WHERE trade_no=#{tradeNo} and trade_type=#{tradeType}
        limit 1
    </select>

    <select id="sumPayChannelDetail" parameterType="map" resultMap="PayChannelDailySummary.PayChannelDailySummaryMap">

        select channel as channel,
        sum(trade_fee) as trade_fee,
        sum(gateway_commission) as gateway_commission,
        sum(actual_income_fee) as net_income_fee,
        now() as sum_at
        from
        <include refid="table_name"/>
        WHERE <![CDATA[ check_finished_at >= #{startAt} ]]> AND <![CDATA[ check_finished_at < #{endAt}]]>
        <if test="tradeType != null">
            AND trade_type = #{tradeType}
        </if>
        group by channel

    </select>


</mapper>