<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="AccountStatementDetail">

    <resultMap id="BaseResultMap" type="AccountStatementDetail">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="account_statement_id" property="accountStatementId"/>
        <result column="order_id" property="orderId"/>
        <result column="order_type" property="orderType"/>

        <result column="balance_detail_id" property="balanceDetailId"/>
        <result column="commission_amount" property="commissionAmount"/>
        <result column="promote_amount" property="promoteAmount"/>
        <result column="reward_amount" property="rewardAmount"/>

        <result column="extra_json" property="extraJson"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>

        <result column="updated_by" property="updatedBy"/>
    </resultMap>

    <sql id="tb">
        parana_account_statement_detail
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `shop_id`, `account_statement_id`, `order_id`, `order_type`, `balance_detail_id`, `commission_amount`,
        `promote_amount`, `reward_amount`, `extra_json`, `is_valid`, `created_at`, `updated_at`, `created_by`, `updated_by`
    </sql>

    <sql id="cols_all_with_table_alias">
        t.`id`, t.`shop_id`, t.`account_statement_id`, t.`order_id`, t.`order_type`, t.`balance_detail_id`, t.`commission_amount`,
        t.`promote_amount`, t.`reward_amount`, t.`extra_json`, t.`is_valid`, t.`created_at`, t.`updated_at`, t.`created_by`, t.`updated_by`
    </sql>

    <insert id="batchInsert" parameterType="list">
        insert into <include refid="tb"/>
        ( <include refid="cols_exclude_id"/> )
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.shopId}, #{item.accountStatementId}, #{item.orderId}, #{item.orderType}, #{item.balanceDetailId},
              #{item.commissionAmount}, #{item.promoteAmount}, #{item.rewardAmount}, #{item.extraJson}, #{item.isValid},
              now(), now(), #{item.createdBy}, #{item.updatedBy} )
        </foreach>
    </insert>

    <insert id="insertSelective" parameterType="AccountStatementDetail" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
        <if test="shopId != null">
            `shop_id`,
        </if>
        <if test="accountStatementId != null">
            `account_statement_id`,
        </if>
        <if test="orderId != null">
            `order_id`,
        </if>
        <if test="orderType != null">
            `order_type`,
        </if>
        <if test="balanceDetailId != null">
            `balance_detail_id`,
        </if>
        <if test="commissionAmount != null">
            `commission_amount`,
        </if>
        <if test="promoteAmount != null">
            `promote_amount`,
        </if>
        <if test="rewardAmount != null">
            `reward_amount`,
        </if>
        <if test="extraJson != null">
            `extra_json`,
        </if>
        <if test="isValid != null">
            `is_valid`,
        </if>
        <if test="createdBy != null">
            `created_by`,
        </if>
        <if test="updatedBy != null">
            `updated_by`,
        </if>
        `created_at`,
        `updated_at`
        )
        VALUES
        (
        <if test="shopId != null">
            #{shopId},
        </if>
        <if test="accountStatementId != null">
            #{accountStatementId},
        </if>
        <if test="orderId != null">
            #{orderId},
        </if>
        <if test="orderType != null">
            #{orderType},
        </if>
        <if test="balanceDetailId != null">
            #{balanceDetailId},
        </if>
        <if test="commissionAmount != null">
            #{commissionAmount},
        </if>
        <if test="promoteAmount != null">
            #{promoteAmount},
        </if>
        <if test="rewardAmount != null">
            #{rewardAmount},
        </if>
        <if test="extraJson != null">
            #{extraJson},
        </if>
        <if test="isValid != null">
            #{isValid},
        </if>
        <if test="createdBy != null">
            #{createdBy},
        </if>
        <if test="updatedBy != null">
            #{updatedBy},
        </if>
        now(),
        now()
        )
    </insert>

    <delete id="delete" parameterType="long">
        update <include refid="tb"/>
        set is_valid = 2
        WHERE id = #{id}
    </delete>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER by id desc
        LIMIT #{offset}, #{limit}
    </select>

    <sql id="criteria">
        and is_valid = 1
        <if test="accountStatementId != null">
            and account_statement_id = #{accountStatementId}
        </if>
        <if test="accountStatementIds != null">
            and account_statement_id in
            <foreach collection="accountStatementIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="findByAccountStatementIds" resultMap="BaseResultMap" parameterType="map">
        select <include refid="cols_all"/>
          from <include refid="tb"/>
         where is_valid = 1
           and account_statement_id in
            <foreach collection="accountStatementIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
    </select>

    <select id="findByBalanceDetailIds" resultMap="BaseResultMap" parameterType="map">
        select <include refid="cols_all"/>
        from <include refid="tb"/>
        where is_valid = 1
        and balance_detail_id in
        <foreach collection="balanceDetailIds" open="(" item="item" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findMaxSkuOrderNum" parameterType="map" resultType="long">
        SELECT COUNT(s.`id`)

          FROM parana_sku_orders s

         WHERE s.`order_id` IN (
            SELECT order_id
              FROM `parana_account_statement_detail`
             WHERE is_valid = 1
               AND order_type = 1
               AND account_statement_id = #{accountStatementId}
           )

         GROUP BY s.`order_id`

         ORDER BY (s.`id`) DESC
         LIMIT 1
    </select>

    <select id="findCompletedWithNoWithdrawRecord" parameterType="map" resultType="moonstone.order.model.result.AccountStatementDetailDO">
        SELECT t_aswr.`withdraw_apply_id` as withdrawApplyId,
               t.`balance_detail_id` as balanceDetailId,
               t.`account_statement_id` as accountStatementId,
               t.`id` as accountStatementDetailId

          FROM `parana_account_statement_detail` t

          JOIN `parana_account_statement` t_as ON t.account_statement_id = t_as.`id`
                                              AND t_as.`shop_id` = #{shopId}
                                              AND t_as.`is_valid` = 1
                                              AND t_as.`user_id` = #{userId}

          JOIN `parana_account_statement_withdraw_relation` t_aswr ON t_aswr.account_statement_id = t_as.`id`
                                                                  AND t_aswr.withdraw_apply_status in
                                                                   <foreach collection="withdrawStatus" open="(" item="item" separator="," close=")">
                                                                       #{item}
                                                                   </foreach>
                                                                  AND t_aswr.is_valid = 1

        WHERE t.`is_valid` = 1
          AND NOT EXISTS(
              SELECT *
                FROM profit_withdraw_record t_wr
               WHERE t_wr.`profit_id`= t.`balance_detail_id`
                 AND t_wr.`withdraw_id`=t_aswr.`withdraw_apply_id`
              )

        order by t.id asc
        limit #{offset}, #{limit}
    </select>

    <select id="findValidByBalanceDetailId" parameterType="map" resultMap="BaseResultMap">
        select <include refid="cols_all_with_table_alias"/>
        from <include refid="tb"/> t
        join parana_account_statement t_main on t.account_statement_id = t_main.id
                                            and t.balance_detail_id = #{balanceDetailId}
                                            and t.is_valid = 1
                                            and t_main.is_valid = 1
        limit 1
    </select>

    <select id="findOrderIds" parameterType="map" resultType="long">
        select distinct order_id
        from <include refid="tb"/>
        where is_valid = 1
        and account_statement_id = #{accountStatementId}
        ORDER by id desc
        LIMIT #{offset}, #{limit}
    </select>
</mapper>