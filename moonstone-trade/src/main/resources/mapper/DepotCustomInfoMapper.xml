<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2015 杭州端点网络科技有限公司
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="DepotCustomInfo">
    <resultMap id="DepotCustomInfoMap" type="DepotCustomInfo">
        <id property="id" column="id"/>
        <result property="depotCode" column="depot_code"/>
        <result property="customName" column="custom_name"/>
        <result property="customCode" column="custom_code"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <sql id="table">
        `depot2custom`
    </sql>

    <sql id="columns">
        `depot_code`,`custom_name`,`custom_code`,`updated_at`
    </sql>

    <sql id="values">
        #{depotCode},#{customName},#{customCode},now()
    </sql>

    <sql id="order">
        order by id desc
    </sql>
    <sql id="orderTime">
        order by updated_at desc
    </sql>

    <insert id="create" parameterType="DepotCustomInfo" keyProperty="id" useGeneratedKeys="true">
        insert into
        <include refid="table"/>
        (<include refid="columns"/>)
        values (<include refid="values"/>)
    </insert>

    <update id="update" parameterType="DepotCustomInfo">
        update
        <include refid="table"/>
        <set>
            `depot_code`=#{depotCode},
            `custom_name`=#{customName},
            `custom_code`=#{customCode},
            `updated_at`=now()
        </set>
        where id = #{id}
    </update>


    <select id="findByDepotCode" parameterType="map" resultMap="DepotCustomInfoMap">
        select id,
        <include refid="columns"/>
        from
        <include refid="table"/>
        where `depot_code`=#{depotCode} limit 1
    </select>

    <select id="findAll" parameterType="long" resultMap="DepotCustomInfoMap">
        select id,
        <include refid="columns"/>
        from
        <include refid="table"/>
    </select>
    <select id="findById" parameterType="long" resultMap="DepotCustomInfoMap">
        select id,
        <include refid="columns"/>
        from
        <include refid="table"/>
        where id = #{id}
    </select>

    <select id="count" parameterType="map" resultType="long">
        select SUM(quantity) from
        <include refid="table"/>
    </select>

    <delete id="delete" parameterType="long">
        delete from
        <include refid="table"/>
        where id = #{id}
    </delete>

    <delete id="deletes">
        DELETE FROM
        <include refid="table"/>
        WHERE id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </delete>
</mapper>
