<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="OrderShipment">

    <resultMap id="OrderShipmentMap" type="OrderShipment">
        <id column="id" property="id"/>
        <result column="shipment_id" property="shipmentId"/>
        <result column="order_id" property="orderId"/>
        <result column="order_type" property="orderType"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_order_shipments
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `shipment_id`,`order_id`,`order_type`,`status`,`created_at`,`updated_at`
    </sql>

    <sql id="vals">
        #{shipmentId},#{orderId},#{orderType},#{status},now(),now()
    </sql>

    <insert id="create" parameterType="OrderShipment" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="OrderShipment" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" separator=",">
          (
            #{i.shipmentId},#{i.orderId},#{i.orderType},#{i.status},now(),now()
          )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="OrderShipmentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByShipmentId" parameterType="long" resultMap="OrderShipmentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shipment_id = #{shipmentId}
        ORDER BY id DESC
    </select>

    <select id="findShipmentIdByOrderIdAndOrderType" parameterType="map" resultMap="OrderShipmentMap">
        SELECT
        shipment_id
        FROM
        <include refid="tb"/>
        WHERE order_id = #{orderId} and order_type=#{orderType}
        ORDER BY id DESC
    </select>

    <select id="findByOrderIdAndOrderType" parameterType="map" resultMap="OrderShipmentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE order_id = #{orderId} and order_type=#{orderType}
        ORDER BY id DESC
    </select>

    <select id="findByOrderIdsAndOrderType" parameterType="map" resultMap="OrderShipmentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE order_id IN
        <foreach collection="orderIds" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
        AND order_type = #{orderType}
        ORDER BY id DESC
    </select>

    <update id="update" parameterType="OrderShipment">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at = now()
            <if test="shipmentId != null">,`shipment_id` = #{shipmentId}</if>
            <if test="orderId != null">,`order_id` = #{orderId}</if>
            <if test="orderType != null">,`order_type` = #{orderType}</if>
            <if test="status != null">,`status` = #{status}</if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateStatusByShipmentId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET updated_at = now(), status=#{status}
        WHERE shipment_id = #{shipmentId}
    </update>

    <update id="updateStatusByOrderIdAndOrderType" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET updated_at = now(), status=#{status}
        WHERE order_id = #{orderId} AND order_type = #{orderType}
    </update>


    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>


</mapper>