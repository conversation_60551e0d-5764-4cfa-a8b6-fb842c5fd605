<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="AccountStatement">

    <resultMap id="BaseResultMap" type="AccountStatement">
        <id column="id" property="id"/>
        <result column="account_statement_no" property="accountStatementNo"/>
        <result column="shop_id" property="shopId"/>
        <result column="user_id" property="userId"/>
        <result column="user_role" property="userRole"/>

        <result column="total_amount" property="totalAmount"/>
        <result column="promote_amount" property="promoteAmount"/>
        <result column="reward_amount" property="rewardAmount"/>
        <result column="period" property="period"/>
        <result column="period_start_at" property="periodStartAt"/>
        <result column="period_end_at" property="periodEndAt"/>

        <result column="extra_json" property="extraJson"/>
        <result column="is_valid" property="isValid"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="created_by" property="createdBy"/>

        <result column="updated_by" property="updatedBy"/>
    </resultMap>

    <sql id="tb">
        parana_account_statement
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `account_statement_no`, `shop_id`, `user_id`, `user_role`, `total_amount`, `promote_amount`, `reward_amount`,
        `period`, `period_start_at`, `period_end_at`, `extra_json`, `is_valid`, `created_at`, `updated_at`, `created_by`,
        `updated_by`
    </sql>

    <sql id="columns_all_with_table_alias">
        t.`id`, t.`account_statement_no`, t.`shop_id`, t.`user_id`, t.`user_role`, t.`total_amount`, t.`promote_amount`, t.`reward_amount`,
        t.`period`, t.`period_start_at`, t.`period_end_at`, t.`extra_json`, t.`is_valid`, t.`created_at`, t.`updated_at`, t.`created_by`,
        t.`updated_by`
    </sql>

    <insert id="insertSelective" parameterType="AccountStatement" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (
        <if test="accountStatementNo != null">
            `account_statement_no`,
        </if>
        <if test="shopId != null">
            `shop_id`,
        </if>
        <if test="userId != null">
            `user_id`,
        </if>
        <if test="userRole != null">
            `user_role`,
        </if>
        <if test="totalAmount != null">
            `total_amount`,
        </if>
        <if test="promoteAmount != null">
            `promote_amount`,
        </if>
        <if test="rewardAmount != null">
            `reward_amount`,
        </if>
        <if test="period != null">
            `period`,
        </if>
        <if test="periodStartAt != null">
            `period_start_at`,
        </if>
        <if test="periodEndAt != null">
            `period_end_at`,
        </if>
        <if test="extraJson != null">
            `extra_json`,
        </if>
        <if test="isValid != null">
            `is_valid`,
        </if>
        <if test="createdBy != null">
            `created_by`,
        </if>
        <if test="updatedBy != null">
            `updated_by`,
        </if>
        `created_at`,
        `updated_at`
        )
        VALUES
        (
        <if test="accountStatementNo != null">
            #{accountStatementNo},
        </if>
        <if test="shopId != null">
            #{shopId},
        </if>
        <if test="userId != null">
            #{userId},
        </if>
        <if test="userRole != null">
            #{userRole},
        </if>
        <if test="totalAmount != null">
            #{totalAmount},
        </if>
        <if test="promoteAmount != null">
            #{promoteAmount},
        </if>
        <if test="rewardAmount != null">
            #{rewardAmount},
        </if>
        <if test="period != null">
            #{period},
        </if>
        <if test="periodStartAt != null">
            #{periodStartAt},
        </if>
        <if test="periodEndAt != null">
            #{periodEndAt},
        </if>
        <if test="extraJson != null">
            #{extraJson},
        </if>
        <if test="isValid != null">
            #{isValid},
        </if>
        <if test="createdBy != null">
            #{createdBy},
        </if>
        <if test="updatedBy != null">
            #{updatedBy},
        </if>
        now(),
        now()
        )
    </insert>

    <delete id="delete" parameterType="long">
        update <include refid="tb"/>
        set is_valid = 2
        WHERE id = #{id}
    </delete>

    <select id="findOverlap" parameterType="map" resultMap="BaseResultMap">
        select <include refid="cols_all"/>
          from <include refid="tb"/>
         where is_valid = 1
           and shop_id = #{shopId}
           and user_id = #{userId}
           and user_role = #{userRole}
           and ( <![CDATA[ period_start_at <= #{periodStartAt} and period_end_at >= #{periodStartAt} ]]>
                 or
                 <![CDATA[ period_start_at <= #{periodEndAt} and period_end_at >= #{periodEndAt} ]]>
                 or
                 <![CDATA[ period_start_at >= #{periodStartAt} and period_end_at <= #{periodEndAt} ]]>
                 or
                 <![CDATA[ period_start_at <= #{periodStartAt} and period_end_at >= #{periodEndAt} ]]> )
        limit 1
    </select>

    <select id="findNoWithdraw" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="columns_all_with_table_alias"/>

          FROM `parana_account_statement` t

     LEFT JOIN `parana_account_statement_withdraw_relation` t_aswr ON t.`id` = t_aswr.`account_statement_id`
                                                                  AND t_aswr.`is_valid` = 1
                                                                  AND t_aswr.`withdraw_apply_status` in
                                                                    <foreach collection="validWithdrawStatusList" open="(" item="item" separator="," close=")">
                                                                        #{item}
                                                                    </foreach>

         WHERE t.`shop_id` = #{shopId}
           AND t.`user_id` =  #{userId}
           AND t.`is_valid` = 1
           AND t_aswr.`withdraw_apply_status` IS NULL
    </select>

    <select id="findById" parameterType="long" resultMap="BaseResultMap">
        select <include refid="cols_all"/>
        from <include refid="tb"/>
        where id = #{id}
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER by id desc
        LIMIT #{offset}, #{limit}
    </select>

    <sql id="criteria">
        and is_valid = 1
        <if test="userId != null">
            and user_id = #{userId}
        </if>
        <if test="shopId != null">
            and shop_id = #{shopId}
        </if>
        <if test="period != null">
            and `period` = #{period}
        </if>
        <if test="userRole != null">
            and user_role = #{userRole}
        </if>
        <if test="userIds != null">
            and user_id in
            <foreach collection="userIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
</mapper>