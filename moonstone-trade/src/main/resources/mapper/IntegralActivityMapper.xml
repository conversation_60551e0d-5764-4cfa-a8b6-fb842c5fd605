<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2016 杭州端点网络科技有限公司, Code Generated by terminus code gen
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="IntegralActivity">

    <resultMap id="IntegralActivityMap" type="IntegralActivity">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="item_code" property="itemCode"/>
        <result column="name" property="name"/>
        <result column="freeze_item_code_str" property="freezeItemCodeStr"/>
        <result column="rate_reward" property="rateReward"/>
        <result column="extra_reward" property="extraReward"/>
        <result column="start_at" property="startAt"/>
        <result column="end_at" property="endAt"/>
        <result column="limit" property="limit"/>
        <result column="long_term" property="longTerm"/>
        <result column="status" property="status"/>
        <result column="extra_str" property="extraStr"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_integral_activity
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <select id="count" parameterType="map" resultType="long">
        select count(1) from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>
    <sql id="cols_exclude_id">
        shop_id,item_code,name,freeze_item_code_str,rate_reward,extra_reward
        ,start_at,end_at,`limit`,long_term,status,extra_str,created_at,updated_at
    </sql>

    <sql id="vals">
        #{shopId},#{itemCode},#{name},#{freezeItemCodeStr},#{rateReward},#{extraReward}
        ,#{startAt},#{endAt},#{limit},#{longTerm},#{status},#{extraStr},now(),now()
    </sql>

    <insert id="create" parameterType="IntegralActivity" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findById" parameterType="long" resultMap="IntegralActivityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="paging" parameterType="map" resultMap="IntegralActivityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <sql id="criteria">
        <if test="shopId != null">and shop_id = #{shopId}</if>
        <if test="name != null">and name = #{name}</if>
        <if test="itemCode != null">and item_code = #{itemCode}</if>
        <if test="freezeItemCodeStr != null">and freeze_item_code_str = #{freezeItemCodeStr}</if>
        <if test="rateReward != null">and rate_reward = #{rateReward}</if>
        <if test="extraReward != null">and extra_reward = #{extraReward}</if>
        <if test="longTerm != null">and long_term = #{longTerm}</if>
        <if test="status != null">and status = #{status}</if>
        <if test="status == null">and status != -1</if>
        <if test="startAfter != null">and start_at >= #{startAfter}</if>
        <if test="endBefore != null">and end_at <![CDATA[<=]]> #{endBefore}</if>
        <if test="limit != null">and `limit` = #{limit}</if>
        <if test="limit == null">and `limit` >= 0</if>
    </sql>
    <select id="findByIds" parameterType="list" resultMap="IntegralActivityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach collection="list" open="(" separator="," close=")" item="id">
            ${id}
        </foreach>
    </select>

    <select id="findByShopId" parameterType="long" resultMap="IntegralActivityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `shop_id` = #{shopId} and `status`=1
        ORDER by `shop_id` DESC
    </select>

    <select id="findByItemCode" parameterType="map" resultMap="IntegralActivityMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `item_code` = #{itemCode} and `status`=1
        ORDER by `item_code` DESC limit 1
    </select>

    <update id="updateStatus" parameterType="IntegralActivity">
        UPDATE
        <include refid="tb"/>
        SET
        updated_at = now(),
        `status` = #{status}
        WHERE id=#{id}
    </update>

    <update id="update" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="itemCode != null">item_code = #{itemCode},</if>
            <if test="freezeItemCodeStr != null">freeze_item_code_str = #{freezeItemCodeStr},</if>
            <if test="rateReward != null">rate_reward = #{rateReward},</if>
            <if test="extraReward != null">extra_reward = #{extraReward},</if>
            <if test="longTerm != null">long_term = #{longTerm},</if>
            <if test="startAt != null">start_at = #{startAt},</if>
            <if test="status != null">status = #{status},</if>
            <if test="extraStr != null">extra_str = #{extraStr},</if>
            <if test="endAt != null">end_at = #{endAt},</if>
            <if test="limit != null">`limit` = #{limit},</if>
            updated_at = now()
        </set>
        <where>id = #{id}</where>
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

</mapper>
