<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ExtraView">
    <resultMap id="ExtraViewMapper" type="moonstone.common.model.ExtraView">
        <id column="id" property="id"/>
        <result column="extra_json" property="extraJson"/>
        <result column="tags_json" property="tagsJson"/>
        <result column="hash" property="hash"/>
    </resultMap>
    <sql id="tb">
        extra_view
    </sql>

    <select id="findById" parameterType="long" resultMap="ExtraViewMapper">
        SELECT
        `id`, `extra_json`, `tags_json`, `hash`
        FROM
        <include refid="tb"/>
        <where>
            `id` = #{id}
        </where>
    </select>

    <insert id="create" parameterType="moonstone.common.model.ExtraView" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb"/>
        (`hash`, `extra_json`, `tags_json`)
        VALUES (#{hash}, #{extraJson}, #{tagsJson}) on duplicate key  update `hash` = #{hash}
    </insert>

    <select id="findByHash" parameterType="map" resultMap="ExtraViewMapper">
        SELECT
        `id`, `hash`, `extra_json`, `tags_json`
        FROM
        <include refid="tb"/>
        <where>
            `hash` = #{hash}
        </where>
    </select>

</mapper>