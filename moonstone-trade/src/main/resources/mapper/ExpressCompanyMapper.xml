<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- ~ Copyright (c) 2016 杭州端点网络科技有限公司 -->

<mapper namespace="ExpressCompany">
    <resultMap id="ExpressCompanyMap" type="ExpressCompany">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">parana_express_companies</sql>

    <sql id="cols_all">
        id,<include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        code,name,status,created_at,updated_at
    </sql>

    <sql id="vals">
        #{code},#{name},#{status},now(),now()
    </sql>

    <sql id="criteria">
        <if test="id != null">id = #{id}</if>
        <if test="code != null">and code = #{code}</if>
        <if test="name != null">and name = #{name}</if>
        <if test="status != null">and status = #{status}</if>
    </sql>

    <insert id="create" parameterType="ExpressCompany" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO<include refid="tb"/>(<include refid="cols_exclude_id"/>) VALUES(<include refid="vals"/>)
    </insert>

    <delete id="delete" parameterType="long">
        delete from <include refid="tb"/>
        where id = #{id}
    </delete>

    <update id="update" parameterType="ExpressCompany">
        UPDATE <include refid="tb"/>
        <set>
            <if test="code != null">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="long" resultMap="ExpressCompanyMap">
        select id,<include refid="cols_exclude_id"/>
        from <include refid="tb"/>
        where id = #{id}
    </select>

    <select id="findByCode" parameterType="string" resultMap="ExpressCompanyMap">
        select id,<include refid="cols_exclude_id"/>
        from <include refid="tb"/>
        where code = #{code} limit 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="ExpressCompanyMap">
        select id,<include refid="cols_exclude_id"/>
        from <include refid="tb"/>
        where id in
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="findActive" resultMap="ExpressCompanyMap">
        select id,<include refid="cols_exclude_id"/>
        from <include refid="tb"/>
        where `status`=1
    </select>

    <select id="findByFuzzyName" parameterType="string" resultMap="ExpressCompanyMap">
        select id,<include refid="cols_exclude_id"/>
        from <include refid="tb"/>
        where name like concat('%',#{name},'%')
    </select>

    <select id="count" parameterType="map" resultType="long">
        select count(1) from <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="ExpressCompanyMap">
        select id,<include refid="cols_exclude_id"/>
        from <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        LIMIT #{offset}, #{limit}
    </select>
</mapper>
