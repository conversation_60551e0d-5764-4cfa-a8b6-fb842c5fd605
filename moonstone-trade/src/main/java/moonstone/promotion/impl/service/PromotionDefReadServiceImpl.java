/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.impl.service;

import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.promotion.enums.PromotionStatus;
import moonstone.promotion.impl.dao.PromotionDao;
import moonstone.promotion.impl.dao.PromotionDefDao;
import moonstone.promotion.model.PromotionDef;
import moonstone.promotion.service.PromotionDefReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 营销工具定义读服务
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-06-06
 */
@Slf4j
@Service
public class PromotionDefReadServiceImpl implements PromotionDefReadService {

    private final PromotionDefDao promotionDefDao;

    private final PromotionDao promotionDao;

    @Autowired
    public PromotionDefReadServiceImpl(PromotionDefDao promotionDefDao,
                                       PromotionDao promotionDao) {
        this.promotionDefDao = promotionDefDao;
        this.promotionDao = promotionDao;
    }


    /**
     * 根据营销工具定义id查找营销工具定义
     *
     * @param id 营销工具定义id
     * @return 对应的营销id
     */
    @Override
    public Response<PromotionDef> findById(Long id) {

        try {
            PromotionDef promotionDef = promotionDefDao.findById(id);
            if (promotionDef == null) {
                log.error("promotionDef(id={}) not found", id);
                return Response.fail("promotionDef.not.found");
            }
            return Response.ok(promotionDef);
        } catch (Exception e) {
            log.error("failed to find promotionDef(id={}), cause:{}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("promotionDef.find.fail");
        }
    }

    /**
     * 查找所有有效的营销工具定义
     *
     * @return 所有有效的营销工具定义
     */
    @Override
    public Response<List<PromotionDef>> findAllValid() {

        try {
            List<PromotionDef> promotionDefs = promotionDefDao.findAllValid();

            return Response.ok(promotionDefs);
        } catch (Exception e) {
            log.error("failed to find all valid promotionDefs, cause:{}", Throwables.getCausalChain(e));
            return Response.fail("promotionDef.find.fail");
        }
    }

    @Override
    public Response<Paging<PromotionDef>> paging(Integer status, Integer pageNo, Integer pageSize) {
        try {
            PageInfo pageInfo = PageInfo.of(pageNo, pageSize);
            PromotionDef criteria = new PromotionDef();
            criteria.setStatus(status);
            Paging<PromotionDef> paging = promotionDefDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), criteria);
            return Response.ok(paging);
        } catch (Exception e) {
            log.error("fail to paging promotionDef with params:status={},pageNo={},pageSize={},cause:{}",
                    status, pageNo, pageSize, Throwables.getStackTraceAsString(e));
            return Response.fail("promotionDef.find.fail");
        }
    }

    @Override
    public Response<Map<Long, Long>> countInProcess(Long shopId, List<Long> promotionDefIds) {
        try {
            Map<Long, Long> countByPromotionDefId = Maps.newHashMapWithExpectedSize(promotionDefIds.size());
            List<Integer> filterStatuses = Arrays.asList(PromotionStatus.PUBLISHED.getValue(), PromotionStatus.ONGOING.getValue());
            for (Long promotionDefId : promotionDefIds) {
                long inProcessCount = promotionDao.countBy(shopId, promotionDefId, filterStatuses);
                countByPromotionDefId.put(promotionDefId, inProcessCount);
            }
            return Response.ok(countByPromotionDefId);
        } catch (Exception e) {
            log.error("fail to count promotionDefs in process where shopId={},promotionDefIds={},cause:{}",
                    shopId, promotionDefIds, Throwables.getStackTraceAsString(e));
            return Response.fail("promotionDef.count.fail");
        }
    }

}
