package moonstone.stock.impl.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import moonstone.common.model.vo.DropDownBoxVo;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ItemCustomsEum {

    JIN_YI(1, "金义"),
    YI_WU(2, "义乌"),
    SHAO_XING(3, "绍兴"),
    HANG_ZHOU(4, "杭州"),
    GUANG_ZHOU(5, "广州"),
    NING_BO(6, "宁波"),
    ZHENG_ZHOU(7, "郑州"),
    CHONG_QING(8, "重庆"),
    SHANG_HAI(9, "上海"),
    shen_zhen(10, "深圳"),
    TIAN_JIN(11, "天津"),
    ;


    private final int code;

    private final String name;


    public static List<DropDownBoxVo> getDefaultCustoms() {
        List<DropDownBoxVo> customs = new ArrayList<>();
        for (ItemCustomsEum itemCustomsEum : ItemCustomsEum.values()) {
            DropDownBoxVo entity = new DropDownBoxVo();
            entity.setId(String.valueOf(itemCustomsEum.getCode()));
            entity.setName(itemCustomsEum.getName());
            customs.add(entity);
        }
        return customs;
    }

    public static ItemCustomsEum findByName(String customName) {
        if(StringUtils.isEmpty(customName)){
            return null;
        }
        for (ItemCustomsEum itemCustomsEum : ItemCustomsEum.values()) {
            if (itemCustomsEum.getName().equals(customName)) {
                return itemCustomsEum;
            }
        }
        return null;
    }
}
