/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.service;

import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.Either;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.dto.DataBoardDto;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.impl.dao.ExtraViewDao;
import moonstone.order.impl.dao.FeeViewDao;
import moonstone.order.impl.dao.ProfitViewDao;
import moonstone.order.impl.dao.ShopOrderDao;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.result.*;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.vo.OrderCriteriaAPPVO;
import moonstone.user.cache.UserCacheHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 店铺订单读服务
 * 尽可能以第一范式设计
 */
@Service
@Slf4j
public record ShopOrderReadServiceImpl(
        ShopOrderDao shopOrderDao,
        FeeViewDao feeViewDao,
        ExtraViewDao extraViewDao,
        ProfitViewDao profitViewDao,
        ShopCacheHolder shopCacheHolder,
        UserCacheHolder userCacheHolder) implements ShopOrderReadService {

    @Override
    public Paging<ShopOrder> pagingShopOrder(OrderCriteria criteria) {
        Long total = shopOrderDao.count(criteria);
        var shopOrders = shopOrderDao.findShopOrderBy(criteria);
        for (var order : shopOrders) {
            long id = order.getId();
            var feeInfo = feeViewDao.cacheId(order.getFeeId());
            if (feeInfo != null) {
                BeanUtils.copyProperties(feeInfo, order);
            }
            var extraInfo = extraViewDao.cacheId(order.getExtraId());
            if (extraInfo != null) {
                BeanUtils.copyProperties(extraInfo, order);
            }
            var profitInfo = profitViewDao.cacheId(order.getProfitId());
            if (profitInfo != null) {
                BeanUtils.copyProperties(profitInfo, order);
            }
            order.setId(id);
            var shop = shopCacheHolder.findShopById(order.getShopId());
            order.setShopName(shop.getName());
            userCacheHolder.findByUserId(order.getBuyerId())
                    .ifPresent(buyer -> order.setBuyerName(buyer.getName()));
        }
        return new Paging<>(total, shopOrders);
    }

    @Override
    public Either<List<Long>> listOrderIdAndCreatedAtByShopId(Long shopId, Long startIdAt, int limit) {
        return Either.ok(shopOrderDao.listOrderIdAndCreatedAtByShopId(shopId, startIdAt, limit));
    }

    @Override
    public Either<Integer> queryOrderStatus(Long orderId) {
        return Either.ok(shopOrderDao.queryOrderStatus(orderId));
    }

    /**
     * 根据订单id查询店铺订单
     *
     * @param id 订单id
     * @return 店铺订单
     */
    @Override
    public Response<ShopOrder> findById(Long id) {
        try {
            ShopOrder shopOrder = shopOrderDao.findById(id);
            if (null == shopOrder) {
                log.error("shop order id = {} not found", id);
                return Response.fail("order.not.found");
            }
            return Response.ok(shopOrder);
        } catch (Exception e) {
            log.error("fail to find shop order by id {}, cause:{}",
                    id, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.order.find.fail");
        }
    }

    @Override
    public Response<List<ShopOrder>> findByDeclaredId(String declaredId) {
        try {
            if (StringUtils.isBlank(declaredId)) {
                return Response.fail("入参为空");
            }

            var criteria = new OrderCriteria();
            criteria.setDeclaredId(declaredId);
            return Response.ok(shopOrderDao.listShopOrdersBy(criteria));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findByDeclaredId error, declaredId={}", declaredId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    /**
     * 根据id列表查询店铺订单
     *
     * @param ids 订单id列表
     * @return 合并支付订单列表
     */
    @Override
    public Response<List<ShopOrder>> findByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Response.ok(Collections.emptyList());
        }
        try {
            List<ShopOrder> shopOrders = shopOrderDao.findByIds(ids);

            return Response.ok(shopOrders);
        } catch (Exception e) {
            log.error("fail to find shop order by ids {}, cause:{}",
                    ids, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.order.find.fail");
        }
    }

    /**
     * 获取店铺订单id列表
     *
     * @param orderCriteria criteria
     * @return 店铺订单id列表
     */
    @Override
    public Response<List<Long>> listIdsBy(OrderCriteria orderCriteria) {
        try {
            return Response.ok(shopOrderDao.listIdsBy(orderCriteria));
        } catch (Exception e) {
            log.error("failed to list shop order ids by orderCriteria={}, cause: {}", orderCriteria, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.order.ids.list.fail");
        }
    }

    /**
     * 获取店铺订单列表
     *
     * @param orderCriteria
     * @return
     */
    @Override
    public Response<List<ShopOrder>> listShopOrdersBy(OrderCriteria orderCriteria) {
        try {
            return Response.ok(shopOrderDao.listShopOrdersBy(orderCriteria));
        } catch (Exception e) {
            log.error("failed to list shopOrder by orderCriteria={}, cause: {}", orderCriteria, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.order.list.fail");
        }
    }


    /**
     * 店铺订单分页列表
     *
     * @param pageNo 页码
     * @param size   每页大小
     * @return 分页订单
     */
    @Override
    public Response<Paging<ShopOrder>> findBy(Integer pageNo, Integer size, OrderCriteria orderCriteria) {
        try {
            PageInfo pageInfo = new PageInfo(pageNo, size);
            Map<String, Object> params = orderCriteria == null ? Maps.newHashMap() : orderCriteria.toMap();
            Paging<ShopOrder> shopOrders = shopOrderDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), params);
            return Response.ok(shopOrders);
        } catch (Exception e) {
            log.error("failed to find shop orders by {}, pageNo={}, size={}, cause:{}",
                    orderCriteria, pageNo, size, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.order.find.fail");
        }
    }

    @Override
    public Response<Paging<ShopOrder>> findByShopOrder(Integer pageNo, Integer size, OrderCriteriaAPPVO orderCriteriaAPPVO) {
        try {
            PageInfo pageInfo = new PageInfo(pageNo, size);
            Map<String, Object> params = orderCriteriaAPPVO == null ? Maps.newHashMap() : orderCriteriaAPPVO.toMap();
            Paging<ShopOrder> shopOrders = shopOrderDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), params);
            return Response.ok(shopOrders);
        } catch (Exception e) {
            log.error("failed to find shop orders by {}, pageNo={}, size={}, cause:{}",
                    orderCriteriaAPPVO, pageNo, size, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.order.find.fail");
        }
    }

    @Override
    public Either<Long> sum(OrderCriteria orderCriteria) {
        try {
            Long num = shopOrderDao.sumFeeByCriteria(orderCriteria);
            return Either.ok(num);
        } catch (Exception e) {
            log.error("failed to count shop orders by {}, cause: {}", orderCriteria, Throwables.getStackTraceAsString(e));
            return Either.error("shop.order.count.fail");
        }
    }

    @Override
    public Response<Long> count(OrderCriteria orderCriteria) {
        try {
            Long num = shopOrderDao.count(orderCriteria);
            return Response.ok(num);
        } catch (Exception e) {
            log.error("failed to count shop orders by {}, cause: {}", orderCriteria, Throwables.getStackTraceAsString(e));
            return Response.fail("shop.order.count.fail");
        }
    }

    @Override
    public Response<ShopOrder> findFirstOrderFromCriteria(OrderCriteria orderCriteria) {
        try {
            return Response.ok(shopOrderDao.findFirstOrderFromCriteria(orderCriteria));
        } catch (Exception exception) {
            log.error("{} fail to find first order of criteria [{}]", LogUtil.getClassMethodName(), orderCriteria.toMap());
            return Response.fail(Translate.of("查找订单信息失败"));
        }
    }

    @Override
    public Either<List<ShopOrder>> findByGatherOrderId(Long gatherOrderId) {
        try {
            return Either.ok(shopOrderDao.findByGatherOrderId(gatherOrderId));
        } catch (Exception ex) {
            log.error("{} fail to find by gatherOrder[{}]", LogUtil.getClassMethodName(), gatherOrderId, ex);
            return Either.error(ex);
        }
    }

    @Override
    public Either<Long> sumFeeBy(Long shopId, Long referenceId, Integer status, OrderOutFrom orderOutFrom) {
        try {
            Long sum = shopOrderDao.sumFeeBy(shopId, referenceId, orderOutFrom.Code(), status);
            return sum == null ? Either.ok(0L) : Either.ok(sum);
        } catch (Exception e) {
            return Either.error(e);
        }
    }

    @Override
    public Response<List<Long>> findStrangeOrderBefore(String date) {
        return Response.ok(shopOrderDao.findStrangeOrderBefore(date));
    }

    @Override
    public Either<List<Long>> findFeeByReferenceId(Long sourceId, Long referenceId) {
        return Either.ok(shopOrderDao.findFeeByReferenceId(sourceId, referenceId));
    }

    @Override
    public Map<Long, ShopOrderGuiderSummaryDO> findGuiderStatistics(Long shopId, List<Long> guiderUserIds) {
        try {
            if (shopId == null || CollectionUtils.isEmpty(guiderUserIds)) {
                return Collections.emptyMap();
            }

            var list = shopOrderDao.findStatisticsByReferenceId(shopId, guiderUserIds);
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyMap();
            }

            return list.stream().collect(Collectors.toMap(ShopOrderGuiderSummaryDO::getGuiderUserId, o -> o, (k1, k2) -> k1));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findGuiderStatistics error, shopId={}, guiderUserIds={}", shopId, guiderUserIds, ex);
            return Collections.emptyMap();
        }
    }

    @Override
    public Response<ShopOrderSummaryDO> findGuiderTodaySummary(Long shopId, Long guiderUserId) {
        try {
            Date now = new Date();

            return Response.ok(shopOrderDao.findGuiderSummary(shopId, guiderUserId, OrderStatus.getSuccessStatusValues(),
                    DateUtil.withTimeAtStartOfDay(now), DateUtil.withTimeAtEndOfDay(now)));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findGuiderTodaySummary error, shopId={}, guiderUserId={}", shopId, guiderUserId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<ShopOrderSummaryDO> findGuiderTodaySummary(Long shopId, Long guiderUserId, Date startTime, Date endTime) {
        try {
            return Response.ok(shopOrderDao.findGuiderSummary(shopId, guiderUserId, OrderStatus.getSuccessStatusValues(),
                    startTime, endTime));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findGuiderTodaySummary error, shopId={}, guiderUserId={}", shopId, guiderUserId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<ShopOrderSummaryDO> findSubStoreTodaySummary(Long shopId, Long subStoreId) {
        try {
            Date now = new Date();

            return Response.ok(shopOrderDao.findSubStoreSummary(shopId, subStoreId, OrderStatus.getSuccessStatusValues(),
                    DateUtil.withTimeAtStartOfDay(now), DateUtil.withTimeAtEndOfDay(now)));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findSubStoreTodaySummary error, shopId={}, subStoreId={}", shopId, subStoreId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<ShopOrderSummaryDO> findSubStoreTodaySummary(Long shopId, Long subStoreId, Date startTime, Date endTime) {
        try {
            return Response.ok(shopOrderDao.findSubStoreSummary(shopId, subStoreId, OrderStatus.getSuccessStatusValues(),
                    startTime, endTime));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findSubStoreTodaySummary error, shopId={}, subStoreId={}", shopId, subStoreId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<ShopOrderSummaryDO> findServiceProviderTodaySummary(Long shopId, List<Long> subStoreIds) {
        try {
            Date now = new Date();

            return Response.ok(shopOrderDao.findServiceProviderTodaySummary(shopId, subStoreIds, OrderStatus.getSuccessStatusValues(),
                    DateUtil.withTimeAtStartOfDay(now), DateUtil.withTimeAtEndOfDay(now)));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findSubStoreTodaySummary error, shopId={}, subStoreIds={}", shopId, subStoreIds, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Long> findGuiderProfitSummary(Long shopId, Long guiderUserId, Long serviceProviderUserId, Date startAt, Date endAt) {
        try {
            if (serviceProviderUserId == null) {
                throw new RuntimeException("导购所属的服务商为空");
            }

            var result = shopOrderDao.findGuiderProfitSummary(shopId, guiderUserId, serviceProviderUserId, startAt, endAt);

            return Response.ok(ShopOrderProfitSummaryDO.calculateTotalProfit(result));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findGuiderProfitSummary error, shopId={}, guiderUserId={}, startAt={}, endAt={}",
                    shopId, guiderUserId, startAt, endAt, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Long> findServiceProviderProfitSummary(Long shopId, Long serviceProviderUserId, List<Long> subStoreIds, Date startAt, Date endAt) {
        try {
            var result = shopOrderDao.findServiceProviderProfitSummary(shopId, serviceProviderUserId, subStoreIds, startAt, endAt);

            return Response.ok(ShopOrderProfitSummaryDO.calculateTotalProfit(result));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findServiceProviderProfitSummary error, shopId={}, subStoreIds={}, startAt={}, endAt={}",
                    shopId, subStoreIds, startAt, endAt, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Long> findSubStoreAloneProfitSummary(Long shopId, Long subStoreId, Long subStoreUserId, List<Long> guiderUserIds) {
        try {
            Date now = new Date();

            var result = shopOrderDao.findSubStoreAloneProfitSummary(shopId, subStoreId, subStoreUserId, guiderUserIds,
                    DateUtil.withTimeAtStartOfDay(now), DateUtil.withTimeAtEndOfDay(now));

            return Response.ok(ShopOrderProfitSummaryDO.calculateTotalProfit(result));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findSubStoreTodayProfitSummary error, shopId={}, subStoreId={}",
                    shopId, subStoreId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Long> findSubStoreGuidersProfitSummary(Long shopId, Long subStoreId, Long subStoreUserId, List<Long> guiderUserIdList) {
        try {
            Date now = new Date();

            var result = shopOrderDao.findSubStoreGuidersProfitSummary(shopId, subStoreId, subStoreUserId, guiderUserIdList,
                    DateUtil.withTimeAtStartOfDay(now), DateUtil.withTimeAtEndOfDay(now));

            return Response.ok(ShopOrderProfitSummaryDO.calculateTotalProfit(result));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findSubStoreGuidersProfitSummary error, shopId={}, subStoreId={}",
                    shopId, subStoreId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Long> findSubStoreProfitSummary(Long shopId, Long subStoreId, Long subStoreUserId, Date startTime, Date endTime) {
        try {
            var result = shopOrderDao.findSubStoreProfitSummary(shopId, subStoreId, subStoreUserId,
                    startTime, endTime);

            return Response.ok(ShopOrderProfitSummaryDO.calculateTotalProfit(result));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findSubStoreProfitSummary error, shopId={}, subStoreId={}",
                    shopId, subStoreId, ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<SimpleShopOrderView>> findSimpleOrderView(List<Long> shopOrderIdList) {
        try {
            if (CollectionUtils.isEmpty(shopOrderIdList)) {
                return Response.ok(Collections.emptyList());
            }

            return Response.ok(shopOrderDao.findSimpleOrderView(shopOrderIdList));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findSimpleOrderView error", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Map<Long, ShopOrder> getShopOrderMap(List<Long> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return Collections.emptyMap();
        }

        List<ShopOrder> shopOrderList = findByIds(orderIdList).getResult();
        if (CollectionUtils.isEmpty(shopOrderList)) {
            return Collections.emptyMap();
        }

        return shopOrderList.stream().collect(Collectors.toMap(ShopOrder::getId, e -> e, (o1, o2) -> o1));
    }

    @Override
    public Response<List<DataBoardDto>> findServiceProviderGmvTop(Long shopId, Date startTime, Date endTime) {
        try {
            List<DataBoardDto> dataBoardDtoList = shopOrderDao.findServiceProviderGmvTop(shopId, startTime, endTime);
            return Response.ok(dataBoardDtoList);
        } catch (Exception ex) {
            log.error("fail to find serviceProviderGmvTop-dataBoardDtoList", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<DataBoardDto>> findServiceProviderOrderTop(Long shopId, Date startTime, Date endTime) {
        try {
            List<DataBoardDto> dataBoardDtoList = shopOrderDao.findServiceProviderOrderTop(shopId, startTime, endTime);
            return Response.ok(dataBoardDtoList);
        } catch (Exception ex) {
            log.error("fail to find serviceProviderOrderTop-dataBoardDtoList", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<DataBoardDto>> findSubStoreGmvTop(Long shopId, Date startTime, Date endTime) {
        try {
            List<DataBoardDto> dataBoardDtoList = shopOrderDao.findSubStoreGmvTop(shopId, startTime, endTime);
            return Response.ok(dataBoardDtoList);
        } catch (Exception ex) {
            log.error("fail to find subStoreGmvTop-dataBoardDtoList", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<DataBoardDto>> findSubStoreOrderTop(Long shopId, Date startTime, Date endTime) {
        try {
            List<DataBoardDto> dataBoardDtoList = shopOrderDao.findSubStoreOrderTop(shopId, startTime, endTime);
            return Response.ok(dataBoardDtoList);
        } catch (Exception ex) {
            log.error("fail to find subStoreOrderTop-dataBoardDtoList", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<DataBoardDto>> findSkuGmvTop(Long shopId, Date startTime, Date endTime) {
        try {
            List<DataBoardDto> dataBoardDtoList = shopOrderDao.findSkuGmvTop(shopId, startTime, endTime);
            return Response.ok(dataBoardDtoList);
        } catch (Exception ex) {
            log.error("fail to find skuGmvTop-dataBoardDtoList", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<DataBoardDto>> findSkuOrderTop(Long shopId, Date startTime, Date endTime) {
        try {
            List<DataBoardDto> dataBoardDtoList = shopOrderDao.findSkuOrderTop(shopId, startTime, endTime);
            return Response.ok(dataBoardDtoList);
        } catch (Exception ex) {
            log.error("fail to find skuOrderTop-dataBoardDtoList", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<DataBoardDto>> findProvinceGmvTop(Long shopId, Date startTime, Date endTime) {
        try {
            List<DataBoardDto> dataBoardDtoList = shopOrderDao.findProvinceGmvTop(shopId, startTime, endTime);
            return Response.ok(dataBoardDtoList);
        } catch (Exception ex) {
            log.error("fail to find ProvinceGmvTop-dataBoardDtoList", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<DataBoardDto>> findProvinceOrderTop(Long shopId, Date startTime, Date endTime) {
        try {
            List<DataBoardDto> dataBoardDtoList = shopOrderDao.findProvinceOrderTop(shopId, startTime, endTime);
            return Response.ok(dataBoardDtoList);
        } catch (Exception ex) {
            log.error("fail to find ProvinceOrderTop-dataBoardDtoList", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<List<ShopOrder>> findConfirmedOrderWithNoConfirmAt(Long minId, Integer pageSize) {
        try {
            if (minId == null || pageSize == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(shopOrderDao.findConfirmedOrderWithNoConfirmAt(minId, pageSize));
        } catch (Exception ex) {
            log.error("ShopOrderReadServiceImpl.findConfirmedOrderWithNoConfirmAt error ", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Map<Long, List<ShopOrderWithPaymentDO>> findMapWithPayment(List<Long> paymentIds) {
        if (CollectionUtils.isEmpty(paymentIds)) {
            return Collections.emptyMap();
        }

        var list = shopOrderDao.findByPaymentIds(paymentIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.groupingBy(ShopOrderWithPaymentDO::getPaymentId));
    }

    @Override
    public long getPendingPaymentOrderCountLastHour(Long shopId, Long subStoreId) {
        Long count = shopOrderDao.getPendingPaymentOrderCountLastHour(shopId, subStoreId);
        return count;
    }

    @Override
    public List<ShopOrder> list(Map<String, Object> query) {
        return shopOrderDao.selectList(query);
    }

    @Override
    public Paging<ShopOrder> pages(Map<String, Object> query) {
        return shopOrderDao.paging(query);
    }

    @Override
    public long countWithSnapshot(Map<String, Object> query) {
        return shopOrderDao.countWithSnapshot(query);
    }

    @Override
    public List<ShopOrder> pagesWithSnapshot(Map<String, Object> query) {
        return shopOrderDao.pagesWithSnapshot(query);
    }

    @Override
    public List<Long> listIdsWithSnapshot(Map<String, Object> query) {
        return shopOrderDao.listIdsWithSnapshot(query);
    }

    @Override
    public ShopOrder getOne(Map<String, Object> query) {
        return shopOrderDao.selectOne(query);
    }

    @Override
    public long countAdminWithSnapshot(Map<String, Object> query) {
        return shopOrderDao.countAdminWithSnapshot(query);
    }

    @Override
    public List<ShopOrder> pagesAdminWithSnapshot(Map<String, Object> query) {
        return shopOrderDao.pagesAdminWithSnapshot(query);
    }

    @Override
    public List<Long> adminListIdsWithSnapshot(Map<String, Object> query) {
        return shopOrderDao.adminListIdsWithSnapshot(query);
    }

    @Override
    public List<ShopOrderWithPaymentDO> findByPaymentId(Long paymentId) {
        return shopOrderDao.findByPaymentId(paymentId);
    }
}
