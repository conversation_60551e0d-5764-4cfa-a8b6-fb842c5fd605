package moonstone.order.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.EmptyUtils;
import moonstone.common.utils.LogUtil;
import moonstone.order.model.IntegralMallCodes;
import moonstone.order.service.IntegralMallCodesWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/9/4 12:04
 */
@Slf4j
@Service
@RpcProvider
public class IntegralMallCodesWriteServiceImpl implements IntegralMallCodesWriteService {

    @Autowired
    private MongoTemplate mongoTemplate;


    @Override
    public Either<Boolean> insert(IntegralMallCodes integralMallCodes) {
        try {
            if (integralMallCodes != null && Objects.equals(integralMallCodes.getBatch(), "5ba31874deb2b3a19ec5164e6360a477")) {
                //处理异常批次号
                Query query1 = new Query(Criteria.where("genCode").is(integralMallCodes.getGenCode()));
                IntegralMallCodes integralMallCodes1 = mongoTemplate.findOne(query1, IntegralMallCodes.class);
                if (integralMallCodes1 == null) {
                    return Either.ok(true);
                }
                if (EmptyUtils.isEmpty(integralMallCodes1.getCode())) {
                    Query query = Query.query(Criteria.where("genCode").is(integralMallCodes.getGenCode()))
                            .addCriteria(Criteria.where("batch").is(integralMallCodes.getBatch()))
                            .addCriteria(Criteria.where("shopId").is(integralMallCodes.getShopId()));
                    Update update = Update.update("num", integralMallCodes.getNum()).set("createAt", integralMallCodes.getCreateAt())
                            .set("code", integralMallCodes.getCode()).set("status", integralMallCodes.getStatus())
                            .set("thirdId", integralMallCodes.getThirdId())
                            .set("faceValue", integralMallCodes.getFaceValue());
                    mongoTemplate.upsert(query, update, IntegralMallCodes.class);
                }
            } else {
                mongoTemplate.insert(integralMallCodes);
            }
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{} entity:{}", LogUtil.getClassMethodName(), integralMallCodes);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> update(long shopId, String genCode, String code, int status) {
        try {
            Query query = Query.query(Criteria.where("shopId").is(shopId).andOperator(Criteria.where("genCode").is(genCode),
                    Criteria.where("code").is(code)));
            Update update = new Update();
            update.set("updateAt", System.currentTimeMillis());
            update.set("status", status);
            mongoTemplate.updateFirst(query, update, IntegralMallCodes.class);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{}  genCode:{} ", LogUtil.getClassMethodName(), genCode);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }
}
