/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.service;

import com.google.common.base.MoreObjects;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Arguments;
import io.terminus.common.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.Json;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.dto.AfterSaleTimeRecord;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.impl.dao.OrderRefundDao;
import moonstone.order.impl.dao.RefundDao;
import moonstone.order.impl.manager.RefundManager;
import moonstone.order.model.OrderBase;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderRefund;
import moonstone.order.model.Refund;
import moonstone.order.service.RefundWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 退款相关写服务
 */
@Service
@Slf4j
public record RefundWriteServiceImpl(RefundManager refundManager,
                                     RefundDao refundDao,
                                     OrderRefundDao orderRefundDao) implements RefundWriteService {

    @Autowired
    public RefundWriteServiceImpl {
    }

    /**
     * 创建退款单, 同时也会创建相应的订单和退款单的关联关系
     *
     * @param refund     退款单信息
     * @param orderIds   关联的(子)订单id列表
     * @param orderLevel 订单对应的级别
     * @return 退款单id
     */
    @Override
    public Response<Long> create(Refund refund, List<Long> orderIds, OrderLevel orderLevel) {

        try {
            //默认状态为申请退款
            refund.setStatus(MoreObjects.firstNonNull(refund.getStatus(), OrderStatus.REFUND_APPLY.getValue()));
            List<OrderRefund> orderRefunds = Lists.newArrayListWithCapacity(orderIds.size());
            for (Long orderId : orderIds) {
                OrderRefund orderRefund = new OrderRefund();
                orderRefund.setOrderId(orderId);
                orderRefund.setOrderLevel(orderLevel);
                orderRefund.setStatus(refund.getStatus());
                orderRefunds.add(orderRefund);
            }
            Long refundId = refundManager.create(refund, orderRefunds);
            return Response.ok(refundId);
        } catch (Exception e) {
            log.error("failed to create {}, cause:{}", refund, Throwables.getStackTraceAsString(e));
            return Response.fail("refund.create.fail");
        }
    }

    @Override
    public Response<Boolean> update(Refund refund) {
        try {
            if (null == refund) {
                return Response.fail("refund.not.found");
            }
            if (null == refund.getId()) {
                return Response.fail("refund.id.is.null");
            }


            boolean success = refundDao.update(refund);
            if (!success) {
                log.error("update mysql fail, refund={}", refund);
                return Response.fail("refund.update.fail");
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to update refund {}, cause:{}",
                    refund, Throwables.getStackTraceAsString(e));
            return Response.fail("refund.update.fail");
        }
    }

    /**
     * 更新退款单的退款流水号和状态
     *
     * @param refundId       退款单id
     * @param refundSerialNo 退款流水号
     * @param refundAt       退款成功时间
     * @param status         退款成功的状态
     * @return 是否更新成功
     */
    @Override
    @Deprecated
    public Response<Boolean> refundCallback(Long refundId, String refundSerialNo, Date refundAt, Integer status) {
        try {
            refundManager.refundCallback(refundId, refundSerialNo, refundAt, status);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update refund(id={}) to refundSerialNo={}, status={}, cause:{}",
                    refundId, refundSerialNo, status, Throwables.getStackTraceAsString(e));
            return Response.fail("refund.update.fail");
        }
    }

    @Override
    public Response<Boolean> updateTimeRecord(Long refundId, AfterSaleTimeRecord timeRecord) {

        try {
            Refund exist = refundDao.findById(refundId);
            if (Arguments.isNull(exist)) {
                log.error("not find refund by id:{}", refundId);
                return Response.fail("refund.not.exist");
            }
            Map<String, String> extraMap = exist.getExtra();
            if (CollectionUtils.isEmpty(extraMap)) {
                extraMap = Maps.newHashMap();
            }
            extraMap.put(AfterSaleTimeRecord.class.getSimpleName(), JsonMapper.nonEmptyMapper().toJson(timeRecord));
            Refund u = new Refund();
            u.setId(refundId);
            u.setExtra(extraMap);
            return Response.ok(refundDao.update(u));
        } catch (Exception e) {
            log.error("fail to update timeRecord({}) to refund(id={}), cause:{}", timeRecord, refundId, Throwables.getStackTraceAsString(e));
            return Response.fail("refund.update.fail");
        }

    }


    /**
     * 更新退款单的状态, 同时也会更新和订单关联表记录的状态
     *
     * @param refundId 退款单id
     * @param status   状态
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> updateStatus(Long refundId, Integer status) {
        try {
            refundManager.updateStatus(refundId, status);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update refund(id={}) to status={}, cause:{}",
                    refundId, status, Throwables.getStackTraceAsString(e));
            return Response.fail("refund.update.fail");
        }
    }

    /**
     * 批量更新多个退款单的状态,保证事务(在分批支付场景中会用到)
     *
     * @param refundIds 退款单id列表
     * @param status    状态
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> batchUpdateStatus(List<Long> refundIds, Integer status) {
        try {
            refundManager.batchUpdateStatus(refundIds, status);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to batch update refund(ids={}) to status={}, cause:{}",
                    refundIds, status, Throwables.getStackTraceAsString(e));
            return Response.fail("refund.update.fail");
        }
    }

    /**
     * 更新商家备注, 买家备注一般在创建时就已添加
     *
     * @param refundId   退款单id
     * @param sellerNote 商家备注
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> updateSellerNote(Long refundId, String sellerNote) {
        try {
            refundManager.updateSellerNote(refundId, sellerNote);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update refund(id={}) to sellerNote={}, cause:{}",
                    refundId, sellerNote, Throwables.getStackTraceAsString(e));
            return Response.fail("refund.update.fail");
        }
    }

    @Override
    public Response<Boolean> updateStatusAndRefundAt(Long refundId, Integer status, Date refundAt) {
        try {
            refundManager.updateStatusAndRefundAt(refundId, status, refundAt);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update refund (id={}) to status={}, refundAt={}, cause:{}",
                    refundId, status, refundAt, Throwables.getStackTraceAsString(e));
            return Response.fail("refund.update.fail");
        }
    }

    @Override
    public Either<Boolean> updateOrderRefundStatus(Long refundId, Integer status) {
        try {
            return Either.ok(orderRefundDao.updateStatusByRefundId(refundId, status));
        } catch (Exception exception) {
            log.error("{} fail to update orderRefundStatus refundId=>{} status=>{}", LogUtil.getClassMethodName(), refundId, status, exception);
            return Either.error(exception);
        }
    }

    @Override
    public Either<List<Long>> create(List<Refund> refundList, List<OrderBase> relatedOrderList) {
        try {
            for (Refund refund : refundList) {
                for (Refund exists : refundDao.findByTradeNo(refund.getTradeNo())) {
                    switch (OrderStatus.fromInt(exists.getStatus())) {
                        case REFUND:
                        case RETURN:
                        case REFUND_CANCEL:
                        case REFUND_APPLY_REJECTED:
                        case RETURN_APPLY_REJECTED:
                        case RETURN_REJECTED:
                            break;
                        default:
                            return Either.error(Translate.of("已有正在处理的退款单[%s]", exists.getId()));
                    }
                }
            }
            return Either.ok(refundManager.createRefund(refundList, relatedOrderList));
        } catch (Exception exception) {
            log.error("{} fail to create refund [{}]", LogUtil.getClassMethodName(), Json.toJson(refundList), exception);
            return Either.error(exception);
        }
    }

    @Override
    public Either<Boolean> updateStatus(Long refundId, Integer status, Integer originStatus) {
        try {
            return Either.ok(refundDao.updateStatus(refundId, status, originStatus));
        } catch (Exception e) {
            log.error("{} fail to update DataBase of Refund(Id => {}, aimStatus => {}, currentStatusShouldBe => {})"
                    , LogUtil.getClassMethodName(), refundId, status, originStatus, e);
            return Either.error(e);
        }
    }

    @Override
    public Response<Boolean> updateStatusForCompulsoryAgree(Long refundId) {
        try{
            //修改主子订单的对应状态
            refundDao.updateStatusOfOrders(refundId);
            //修改退款单，订单-退款单的对应状态
            refundDao.updateStatusOfRefunds(refundId);
            return Response.ok(Boolean.TRUE);
        }catch(Exception e){
            log.error("failed to update all status for CompulsoryAgree, cause:{}", Throwables.getStackTraceAsString(e));
            return Response.fail("update.status.for.compulsoryAgree.fail");
        }
    }

    @Override
    public void save(Refund refund) {
        refundDao.create(refund);
    }
}
