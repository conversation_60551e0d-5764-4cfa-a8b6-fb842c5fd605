package moonstone.order.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.IntegralStatus;
import moonstone.common.model.Either;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.LogUtil;
import moonstone.order.model.IntegralUseRecord;
import moonstone.order.service.IntegralUseRecordWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/24 15:15
 */
@Slf4j
@Service
@RpcProvider
public class IntegralUseRecordWriteServiceImpl implements IntegralUseRecordWriteService {
    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public Either<Boolean> createIntegralUseRecord(IntegralUseRecord integralUseRecord) {
        try {
            integralUseRecord.setValidAt(DateUtil.getTimeMillisOfYearAfter(2));//设置有效期一年
            mongoTemplate.insert(integralUseRecord);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{} entity:{}", LogUtil.getClassMethodName(), integralUseRecord);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> updateFrozenUserRecord(long userId, long shopId, int status) {
        try {
            Query query = Query.query(Criteria.where("userId").is(userId).andOperator(Criteria.where("shopId").is(shopId),
                    Criteria.where("status").is(IntegralStatus.FROZEN.value())));
            Update update = new Update();
            update.set("status", status);
            update.set("updateAt", System.currentTimeMillis());
            mongoTemplate.updateMulti(query, update, IntegralUseRecord.class);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{}  userId:{} shopId:{}", LogUtil.getClassMethodName(), userId, shopId);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> updateUserRecord(Long userId, Long shopId, Long tradeId, int oStatus, int eStatus) {
        try {
            Query query = Query.query(Criteria.where("userId").is(userId).andOperator(Criteria.where("shopId").is(shopId),
                    Criteria.where("status").is(oStatus), Criteria.where("tradeId").is(tradeId)));
            Update update = new Update();
            update.set("status", eStatus);
            update.set("updateAt", System.currentTimeMillis());
            mongoTemplate.updateMulti(query, update, IntegralUseRecord.class);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{}  userId:{} shopId:{}", LogUtil.getClassMethodName(), userId, shopId);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }
}
