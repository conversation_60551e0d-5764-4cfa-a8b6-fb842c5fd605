package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.dto.EnterpriseWithdrawCriteria;
import moonstone.order.impl.dao.EnterpriseWithdrawDao;
import moonstone.order.model.EnterpriseWithdraw;
import moonstone.order.service.EnterpriseWithdrawReadService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class EnterpriseWithdrawReadServiceImpl implements EnterpriseWithdrawReadService {

    @Resource
    private EnterpriseWithdrawDao enterpriseWithdrawDao;

    @Override
    public Response<Paging<EnterpriseWithdraw>> paging(EnterpriseWithdrawCriteria criteria) {
        try {
            return Response.ok(enterpriseWithdrawDao.paging(criteria.toMap()));
        } catch (Exception ex) {
            log.error("EnterpriseWithdrawReadServiceImpl.paging error, criteria={}", JSON.toJSONString(criteria), ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<EnterpriseWithdraw> findById(Long id) {
        try {
            if (id == null) {
                return Response.fail("入参缺失");
            }

            return Response.ok(enterpriseWithdrawDao.findById(id));
        } catch (Exception ex) {
            log.error("EnterpriseWithdrawReadServiceImpl.findById error, id={}", id, ex);
            return Response.fail(ex.getMessage());
        }
    }
}
