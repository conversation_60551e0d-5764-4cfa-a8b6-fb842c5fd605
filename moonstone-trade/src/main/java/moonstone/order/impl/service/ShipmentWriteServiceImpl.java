/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import com.google.common.base.MoreObjects;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.OrderShipmentDao;
import moonstone.order.impl.manager.ShipmentManager;
import moonstone.order.model.OrderBase;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderShipment;
import moonstone.order.model.Shipment;
import moonstone.order.service.ShipmentWriteService;
import moonstone.order.service.SkuOrderReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 发货单写服务
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-18
 */
@Service
@Slf4j
@RpcProvider
public class ShipmentWriteServiceImpl implements ShipmentWriteService {

    private final OrderShipmentDao orderShipmentDao;

    private final ShipmentManager shipmentManager;

    private final SkuOrderReadService skuOrderReadService;

    @Autowired
    public ShipmentWriteServiceImpl(OrderShipmentDao orderShipmentDao, ShipmentManager shipmentManager,
                                    SkuOrderReadService skuOrderReadService) {
        this.orderShipmentDao = orderShipmentDao;
        this.shipmentManager = shipmentManager;
        this.skuOrderReadService = skuOrderReadService;
    }

    /**
     * 创建发货单, 同时也会创建相应的订单和发货单的关联关系,  如果没有设置发货单的状态, 那么默认状态为已发货
     *
     * @param shipment   发货单
     * @param orderIds   (子)订单id列表
     * @param orderLevel 订单对应的级别
     * @return 新创建发货单的id
     */
    @Override
    public Response<Long> create(Shipment shipment, List<Long> orderIds, OrderLevel orderLevel) {
        try {
            shipment.setStatus(MoreObjects.firstNonNull(shipment.getStatus(), 1));
            List<OrderShipment> orderShipments = Lists.newArrayListWithCapacity(orderIds.size());
            for (Long orderId : orderIds) {
                OrderShipment orderShipment = new OrderShipment();
                orderShipment.setOrderId(orderId);
                orderShipment.setOrderLevel(orderLevel);
                orderShipment.setStatus(shipment.getStatus());
                orderShipments.add(orderShipment);
            }
            Long shipmentId = shipmentManager.create(shipment, orderShipments);
            return Response.ok(shipmentId);
        } catch (Exception e) {
            log.error("failed to create {}, cause:{}", JSON.toJSONString(shipment), Throwables.getStackTraceAsString(e));
            return Response.fail("shipment.create.fail");
        }
    }

    /**
     * 更新发货单本身信息,不更新状态,  注意, 如果要更新状态请使用updateStatus接口
     *
     * @param shipment 发货单信息
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> update(Shipment shipment) {
        //确保发货单状态不被更新
        try {
            shipment.setStatus(null);
            shipmentManager.update(shipment);

            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update {}, cause:{}", shipment, Throwables.getStackTraceAsString(e));
            return Response.fail("shipment.update.fail");
        }
    }

    /**
     * 根据发货单id更新发货单状态
     *
     * @param shipmentId 发货单id
     * @param status     状态
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> updateStatusByShipmentId(Long shipmentId, Integer status) {
        try {
            shipmentManager.updateStatusByShipmentId(shipmentId, status);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update orderShipment status to {} by shipment(id={}), cause:{}",
                    status, shipmentId, Throwables.getStackTraceAsString(e));
            return Response.fail("order.shipment.update.fail");
        }
    }

    /**
     * 根据(子)订单id更新发货单状态
     *
     * @param orderId    (子)订单id
     * @param orderLevel 订单级别
     * @param status     状态
     * @return 是否更新成功
     */
    @Override
    public Response<Boolean> updateStatusByOrderIdAndOrderLevel(Long orderId, OrderLevel orderLevel, Integer status) {
        try {
            List<OrderShipment> orderShipments = findOrderShipment(orderId, orderLevel);

            Set<Long> shipmentIds = Sets.newHashSetWithExpectedSize(orderShipments.size());
            for (OrderShipment orderShipment : orderShipments) {
                shipmentIds.add(orderShipment.getShipmentId());
            }

            shipmentManager.updateStatusByOrderIdAndOrderType(orderId, orderLevel, shipmentIds, status);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update orderShipment(orderId={}, orderType={}) status to {}, cause:{}",
                    orderId, orderLevel.getValue(), status, Throwables.getStackTraceAsString(e));
            return Response.fail("order.shipment.update.fail");
        }
    }

    private List<OrderShipment> findOrderShipment(Long orderId, OrderLevel orderLevel) {
        List<OrderShipment> orderShipments = orderShipmentDao.findByOrderIdAndOrderType(orderId, orderLevel.getValue());
        if (!CollectionUtils.isEmpty(orderShipments)) {
            return orderShipments;
        }
        log.warn("no shipment found for order(id={}, type={})", orderId, orderLevel);

        //以主订单查不到时，从子订单的角度查一遍
        if (OrderLevel.SHOP.equals(orderLevel)) {
            orderShipments = findOrderShipmentBySkuOrder(orderId);
            if (CollectionUtils.isEmpty(orderShipments)) {
                log.warn("no shipment found for order(id={}, type={})", orderId, OrderLevel.SKU);
            }
        }

        return orderShipments;
    }

    private List<OrderShipment> findOrderShipmentBySkuOrder(Long shopOrderId) {
        var skuOrders = skuOrderReadService.findByShopOrderId(shopOrderId).getResult();
        if (CollectionUtils.isEmpty(skuOrders)) {
            return Collections.emptyList();
        }

        return orderShipmentDao.findByOrderIdsAndOrderType(
                skuOrders.stream().map(OrderBase::getId).collect(Collectors.toList()), OrderLevel.SKU.getValue());
    }
}
