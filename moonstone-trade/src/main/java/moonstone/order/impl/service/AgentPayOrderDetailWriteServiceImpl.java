package moonstone.order.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.AgentPayOrderDetailDao;
import moonstone.order.model.AgentPayOrderDetail;
import moonstone.order.service.AgentPayOrderDetailWriteService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class AgentPayOrderDetailWriteServiceImpl implements AgentPayOrderDetailWriteService {

    @Resource
    private AgentPayOrderDetailDao agentPayOrderDetailDao;

    @Override
    public Response<Boolean> creates(List<AgentPayOrderDetail> list) {
        try {
            if (CollectionUtils.isEmpty(list)) {
                return Response.fail("入参缺失");
            }

            return Response.ok(agentPayOrderDetailDao.creates(list) > 0);
        } catch (Exception ex) {
            log.error("AgentPayOrderDetailWriteServiceImpl.creates error, list={}", JSON.toJSONString(list), ex);
            return Response.fail(ex.getMessage());
        }
    }
}
