package moonstone.order.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.model.WithdrawPrinciple;
import moonstone.order.service.WithdrawPrincipleManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;


@Slf4j
@Repository
public class WithdrawPrincipleManagerImpl implements WithdrawPrincipleManager {
    @Autowired
    private MongoTemplate mongoTemplate;

    public Either<WithdrawPrinciple> findByShopId(Long shopId) {
        try {
            return Either.ok(mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(shopId)), WithdrawPrinciple.class));
        } catch (Exception ex) {
            log.error("{} find by shopId:{} faild:", LogUtil.getClassMethodName(), shopId, ex);
            return Either.error(ex);
        }
    }

    public Either<WithdrawPrinciple> save(WithdrawPrinciple withdrawPrinciple) {
        try {
            mongoTemplate.save(withdrawPrinciple);
            return Either.ok(withdrawPrinciple);
        } catch (Exception ex) {
            log.error("{} save :{} error:", LogUtil.getClassMethodName(), withdrawPrinciple, ex);
            return Either.error(ex);
        }
    }

    public Either<Boolean> modify(WithdrawPrinciple withdrawPrinciple) {
        try {
            Update update = Update.update("shopId", withdrawPrinciple.getShopId())
                    .set("moneyLimit", withdrawPrinciple.getMoneyLimit())
                    .set("timeLimit", withdrawPrinciple.getTimeLimit());
            return Either.ok(mongoTemplate.upsert(Query.query(Criteria.where("shopId").is(withdrawPrinciple.getShopId())), update, WithdrawPrinciple.class).getModifiedCount() > 0);
        } catch (Exception ex) {
            log.error("{} modify:{} failed", LogUtil.getClassMethodName(), withdrawPrinciple, ex);
            return Either.error(ex);
        }
    }
}
