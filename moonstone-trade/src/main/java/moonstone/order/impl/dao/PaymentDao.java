/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.dao;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.Payment;
import moonstone.order.model.result.OrderPaymentInfoDO;
import moonstone.order.model.result.PaymentForEnterpriseWithdrawDO;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-20
 */
@Repository
public class PaymentDao extends MyBatisDao<Payment> {

    public boolean checkPaymentById(Payment payment) {
        return getSqlSession().selectOne(sqlId("checkPaymentById"), JSONObject.toJSON(payment));
    }

    /**
     * 根据外部支付流水号和支付渠道来查找对应的支付单
     *
     * @param serialNo 外部支付流水号
     * @param channel  支付渠道
     * @return 支付单
     */
    public Payment findBySerialNoAndChannel(String serialNo, String channel) {
        return getSqlSession().selectOne(sqlId("findBySerialNoAndChannel"),
                ImmutableMap.of("paySerialNo", serialNo, "channel", channel));
    }

    public Payment findByOutId(String outId) {
        return getSqlSession().selectOne(sqlId("findByOutId"), ImmutableMap.of("outId", outId));
    }

    public Payment findByPayInfoMd5(String payInfoMd5) {
        return getSqlSession().selectOne(sqlId("findByPayInfoMd5"), payInfoMd5);
    }

    public List<Payment> listByPushStatus(Integer pushStatus) {
        return getSqlSession().selectList(sqlId("listByPushStatus"), pushStatus);
    }

    /**
     * 更新支付单推送状态
     *
     * @param paymentId         支付单Id
     * @param currentPushStatus 支付单目前状态
     * @param targetPushStatus  支付单更新后的目标状态
     */
    public boolean updatePushStatusFrom(Long paymentId, int currentPushStatus, int targetPushStatus) {
        return getSqlSession().update(sqlId("updatePushStatusFrom")
                , ImmutableMap.of("id", paymentId
                        , "currentPushStatus", currentPushStatus
                        , "targetPushStatus", targetPushStatus)) > 0;
    }

    public List<OrderPaymentInfoDO> findByOrderIds(List<Long> orderIds, OrderLevel orderLevel) {
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("orderType", orderLevel.getValue());
        parameter.put("orderIds", orderIds);

        return getSqlSession().selectList(sqlId("findByOrderIds"), parameter);
    }

    public List<Long> findOrderIdsByOutId(String outId, Integer orderType) {
        return getSqlSession().selectList(sqlId("findOrderIdsByOutId"), ImmutableMap.of("outId", outId,
                "orderType", orderType));
    }

    public Long countForEnterpriseWithdraw(Map<String, Object> map) {
        return getSqlSession().selectOne(sqlId("countForEnterpriseWithdraw"), map);
    }

    public List<PaymentForEnterpriseWithdrawDO> pagingForEnterpriseWithdraw(Map<String, Object> map) {
        return getSqlSession().selectList(sqlId("pagingForEnterpriseWithdraw"), map);
    }

    public boolean pushedWaitCallback(Long paymentId) {
        return getSqlSession().update(sqlId("pushedWaitCallback"), Map.of("paymentId", paymentId)) > 0;
    }
}
