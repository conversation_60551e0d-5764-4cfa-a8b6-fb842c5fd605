package moonstone.order.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.AgentPayOrder;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class AgentPayOrderDao extends MyBatisDao<AgentPayOrder> {

    public int insertSelective(AgentPayOrder parameter) {
        return getSqlSession().insert(sqlId("insertSelective"), parameter);
    }

    public List<AgentPayOrder> findByRelatedOrderId(Long relatedOrderId) {
        return getSqlSession().selectList(sqlId("findByRelatedOrderId"), Map.of("relatedOrderId", relatedOrderId));
    }

    public List<AgentPayOrder> pageList(Map<String, Object> parameter) {
        return getSqlSession().selectList(sqlId("paging"), parameter);
    }

    public List<AgentPayOrder> findByRelatedOrderIds(List<Long> relatedOrderIds) {
        return getSqlSession().selectList(sqlId("findByRelatedOrderIds"), Map.of("relatedOrderIds", relatedOrderIds));
    }
}
