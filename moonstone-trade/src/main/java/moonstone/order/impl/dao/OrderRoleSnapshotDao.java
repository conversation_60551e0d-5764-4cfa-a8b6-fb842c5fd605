package moonstone.order.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.OrderRoleSnapshot;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

@Repository
public class OrderRoleSnapshotDao extends MyBatisDao<OrderRoleSnapshot> {

    public List<OrderRoleSnapshot> findByShopOrderIds(List<Long> shopOrderIds, Integer orderType) {
        return getSqlSession().selectList(sqlId("findByShopOrderIds"), ImmutableMap.of("shopOrderIds", shopOrderIds,
                "orderType", orderType));
    }

    public List<Long> findUserIds(Long shopId, Integer orderType, Integer userRole, String name, String mobile) {
        var map = new HashMap<String, Object>();
        map.put("shopId", shopId);
        map.put("orderType", orderType);
        map.put("name", name);
        map.put("mobile", mobile);
        map.put("userRole", userRole);

        return getSqlSession().selectList(sqlId("findUserIds"), map);
    }

    public List<Long> findUserIdsLike(Long shopId, Integer orderType, Integer userRole, String name, String mobile) {
        var map = new HashMap<String, Object>();
        map.put("shopId", shopId);
        map.put("orderType", orderType);
        map.put("nameLike", name);
        map.put("mobileLike", mobile);
        map.put("userRole", userRole);

        return getSqlSession().selectList(sqlId("findUserIds"), map);
    }

    /**
     * 按创建时间desc取第一个值
     *
     * @param userId
     * @return
     */
    public OrderRoleSnapshot findByUserIdAndShopId(Long userId, Long shopId) {
        return getSqlSession().selectOne(sqlId("findByUserIdAndShopId"), ImmutableMap.of("userId", userId, "shopId", shopId));
    }

    public List<OrderRoleSnapshot> findAllByPage(Long maxId, int page, int pageSize) {
        return getSqlSession().selectList(sqlId("findAllByPage"), ImmutableMap.of(
                "maxId", maxId,
                "offset", (page - 1) * pageSize,
                "limit", pageSize));
    }

    public List<OrderRoleSnapshot> listByShopOrderIdAndSnapshotType(Long orderId, Integer orderType) {
        return getSqlSession().selectList(sqlId("listByShopOrderIdAndSnapshotType"), ImmutableMap.of(
                "shopOrderId", orderId,
                "orderType", orderType));
    }
}
