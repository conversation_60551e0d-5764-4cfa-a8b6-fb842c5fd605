package moonstone.order.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.utils.DateUtil;
import moonstone.order.model.AccountStatement;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public class AccountStatementDao extends MyBatisDao<AccountStatement> {

    public int insertSelective(AccountStatement accountStatement) {
        return getSqlSession().insert(sqlId("insertSelective"), accountStatement);
    }

    public AccountStatement findOverlap(Long shopId, Long userId, SubStoreUserIdentityEnum userRole, Date startTime, Date endTime) {
        return getSqlSession().selectOne(sqlId("findOverlap"),
                ImmutableMap.of("shopId", shopId,
                        "userId", userId,
                        "userRole", userRole.getCode(),
                        "periodStartAt", DateUtil.toString(startTime),
                        "periodEndAt", DateUtil.toString(endTime)));
    }

    public List<AccountStatement> findNoWithdraw(Long shopId, Long userId, List<Integer> validWithdrawStatusList) {
        return getSqlSession().selectList(sqlId("findNoWithdraw"),
                ImmutableMap.of("shopId", shopId,
                        "userId", userId,
                        "validWithdrawStatusList", validWithdrawStatusList));
    }
}
