/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.OrderPayment;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-21
 */
@Repository
public class OrderPaymentDao extends MyBatisDao<OrderPayment> {

    /**
     * 通过支付单id查找关联的(子)订单id
     *
     * @param paymentId  支付单id
     * @return  关联的(子)订单id
     */
    public List<OrderPayment> findByPaymentId(Long paymentId){
        return getSqlSession().selectList(sqlId("findByPaymentId"), paymentId);
    }

    /**
     * 通过(子)订单id查找关联的支付单id列表
     *
     * @param orderId  (子)订单id
     * @param orderType   订单类型
     * @return  (子)订单关联的支付单id列表
     */
    public List<OrderPayment> findByOrderIdAndOrderType(Long orderId, Integer orderType){
        return getSqlSession().selectList(sqlId("findByOrderIdAndOrderType"),
                ImmutableMap.of("orderId", orderId, "orderType", orderType));
    }

    /**
     * 根据支付单id更新对应和(子)订单关联的记录
     *
     * @param paymentId  支付单id
     * @param status  状态
     */
    public void updateStatusByPaymentId(Long paymentId, Integer status) {
        getSqlSession().update(sqlId("updateStatusByPaymentId"),
                ImmutableMap.of("paymentId", paymentId, "status",status));
    }

    public OrderPayment findOrderPaymentByOrderIdAndOrderLevel(Long id, int orderType) {
        return getSqlSession().selectOne(sqlId("findOrderPaymentByOrderIdAndOrderLevel"),
                ImmutableMap.of("orderId", id, "orderType", orderType));
    }

    public int updateOrderStatusOnPaid(Long paymentId) {
        return getSqlSession().update(sqlId("updateOrderStatusOnPaid"), ImmutableMap.of("paymentId", paymentId,
                "beforeStatus", OrderStatus.NOT_PAID.getValue(),
                "afterStatus", OrderStatus.PAID.getValue()));
    }
}
