package moonstone.order.impl.dao;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import io.terminus.common.mysql.dao.MyBatisDao;
import io.terminus.common.utils.JsonMapper;
import moonstone.common.utils.DateUtil;
import moonstone.order.dto.DataBoardDto;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.result.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Mail: <EMAIL>
 * Data: 16/3/1
 * Author: yangzefeng
 *
 * <AUTHOR>
 */
@Repository
public class ShopOrderDao extends MyBatisDao<ShopOrder> {

    /**
     * 更新店铺订单状态 , 乐观锁, 需要比较当前状态
     *
     * @param shopOrderId   店铺订单id
     * @param currentStatus 当前状态
     * @param newStatus     新状态
     * @return 是否更新成功
     */
    public boolean updateStatus(Long shopOrderId, Integer currentStatus, Integer newStatus) {
        return getSqlSession().update(sqlId("updateStatus"), Map.of("id", shopOrderId,
                "currentStatus", currentStatus, "newStatus", newStatus)) == 1;
    }

    /**
     * 更新店铺订单状态, 无锁, 不用比较当前状态
     *
     * @param shopOrderId 店铺订单id
     * @param newStatus   新状态
     * @return 是否更新成功
     */
    public boolean updateStatus(Long shopOrderId, Integer newStatus) {
        return getSqlSession().update(sqlId("updateStatus"), Map.of("id", shopOrderId,
                "newStatus", newStatus)) == 1;
    }

    /**
     * 批量标记店铺订单列表为有过逆向流程
     *
     * @param ids       订单列表
     * @param hasRefund 是否申请逆向流程
     */
    public void batchMarkHasRefundByIds(List<Long> ids, Boolean hasRefund) {
        getSqlSession().update(sqlId("batchMarkHasRefundByIds"),
                Map.of("ids", ids, "hasRefund", hasRefund));
    }

    /**
     * 更新店铺信息 (在需要手动派单的业务场景下需要)
     *
     * @param shopOrderId 订单id
     * @param shopId      待更新的店铺id
     * @return 是否更新成功
     */
    public boolean updateShopInfoById(Long shopOrderId, Long shopId, String shopName) {

        return getSqlSession().update(sqlId("updateShopInfoById"),
                Map.of("id", shopOrderId, "shopId", shopId, "shopName", shopName)) > 0;
    }

    /**
     * 更新店铺订单扩展信息
     *
     * @param shopOrderId 店铺订单id
     * @param extra       扩展信息
     * @return 是否更新成功
     */
    public boolean updateExtra(Long shopOrderId, Map<String, String> extra) {
        return getSqlSession().update(sqlId("updateExtraJson"), Map.of("id", shopOrderId,
                "extraJson", JsonMapper.JSON_NON_DEFAULT_MAPPER.toJson(extra))) == 1;
    }

    /**
     * find ShopOrder only important info
     *
     * @param criteria query condition
     * @return main shopOrder info
     */
    public List<ShopOrder> findShopOrderBy(OrderCriteria criteria) {
        if (criteria.getPageNo() == null) {
            criteria.setPageNo(1);
        }
        if (criteria.getSize() == null) {
            criteria.setSize(20);
        }

        var param = new HashMap<>(criteria.toMap());
        param.put("offset", Math.max(criteria.getPageNo() * criteria.getSize() - criteria.getSize(), 0));
        param.put("limit", criteria.getSize());
        return sqlSession.selectList(sqlId("findShopOrderBy"), param);
    }

    /**
     * 更新店铺订单tag信息
     *
     * @param shopOrderId 店铺订单id
     * @param tags        tag信息
     * @return 是否更新成功
     */
    public boolean updateTags(Long shopOrderId, Map<String, String> tags) {
        return getSqlSession().update(sqlId("updateTagsJson"), Map.of("id", shopOrderId,
                "tagsJson", JsonMapper.JSON_NON_DEFAULT_MAPPER.toJson(tags))) == 1;
    }

    public Long count(OrderCriteria criteria) {
        return getSqlSession().selectOne(sqlId("count"), criteria.toMap());
    }

    public List<Long> listIdsBy(OrderCriteria criteria) {
        var parameter = criteria.toMap();
        if (criteria.getPageNo() != null && criteria.getSize() != null) {
            parameter.put("offset", (criteria.getPageNo() - 1) * criteria.getSize());
            parameter.put("limit", criteria.getSize());
        }

        return getSqlSession().selectList(sqlId("listIdsBy"), parameter);
    }

    /**
     * TODO 临时用
     */
    public List<ShopOrder> listDeliveredShopOrdersWithoutShipmentTime() {
        return getSqlSession().selectList(sqlId("listDeliveredShopOrdersWithoutShipmentTime"));
    }

    public List<ShopOrder> listShopOrdersBy(OrderCriteria orderCriteria) {
        var parameter = orderCriteria.toMap();
        if (orderCriteria.getPageNo() != null && orderCriteria.getSize() != null) {
            parameter.put("offset", (orderCriteria.getPageNo() - 1) * orderCriteria.getSize());
            parameter.put("limit", orderCriteria.getSize());
        }

        return getSqlSession().selectList(sqlId("listShopOrdersBy"), parameter);
    }

    public Long sumFeeByCriteria(OrderCriteria orderCriteria) {
        return getSqlSession().selectOne(sqlId("sumPriceByCriteria"), orderCriteria.toMap());
    }

    public Long findShopOrderByRefererId(Long userId, Long shopId) {
        return getSqlSession().selectOne(sqlId("findShopOrderByRefererId"),
                Map.of("refererId", userId, "shopId", shopId));
    }

    public ShopOrder findFirstOrderFromCriteria(OrderCriteria orderCriteria) {
        return getSqlSession().selectOne(sqlId("findFirstOrderFromCriteria"), orderCriteria.toMap());
    }

    public List<ShopOrder> findByGatherOrderId(Long gatherOrderId) {
        return getSqlSession().selectList(sqlId("findByGatherOrderId"), Map.of("gatherOrderId", gatherOrderId));
    }

    public Integer removeGatherId(Long gatherOrderId) {
        return sqlSession.update(sqlId("removeGatherId"), Map.of("gatherOrderId", gatherOrderId));
    }

    public Long sumFeeBy(Long shopId, Long referenceId, String orderOutFrom, Integer status) {
        return sqlSession.selectOne(sqlId("sumFeeBy"), Map.of("shopId", shopId,
                "referenceId", referenceId,
                "orderOutFrom", orderOutFrom,
                "status", status));
    }

    public List<Long> findStrangeOrderBefore(String date) {
        return sqlSession.selectList(sqlId("findStrangeOrderBefore"), Map.of("date", date));
    }

    public List<Long> findFeeByReferenceId(Long sourceId, Long referenceId) {
        return sqlSession.selectList(sqlId("findFeeByReferenceId"), Map.of("shopId", sourceId,
                "referenceId", referenceId, "status", OrderStatus.getSuccessStatusValues()));
    }

    public List<ShopOrderGuiderSummaryDO> findStatisticsByReferenceId(Long shopId, List<Long> guiderUserIds) {
        return sqlSession.selectList(sqlId("findStatisticsByReferenceId"), Map.of("shopId", shopId,
                "guiderUserIds", guiderUserIds, "statusList", OrderStatus.getSuccessStatusValues()));
    }

    public Integer queryOrderStatus(Long orderId) {
        Map<String, Integer> res = sqlSession.selectOne(sqlId("queryOrderStatus"), Map.of("id", orderId));
        return res.get("status");
    }

    public List<Long> listOrderIdAndCreatedAtByShopId(Long shopId, Long startIdAt, int limit) {
        return getSqlSession().selectList(sqlId("listOrderIdAndCreatedAtByShopId"), Map.of("shopId", shopId,
                "id", startIdAt,
                "limit", limit));
    }

    public ShopOrderSummaryDO findGuiderSummary(Long shopId, Long guiderUserId, List<Integer> statusList, Date startAt,
                                                Date endAt) {
        return getSqlSession().selectOne(sqlId("findOrderSummary"), Map.of("shopId", shopId,
                "guiderUserId", guiderUserId,
                "statusList", statusList,
                "startAt", startAt,
                "endAt", endAt));
    }

    public ShopOrderSummaryDO findSubStoreSummary(Long shopId, Long subStoreId, List<Integer> statusList, Date startAt,
                                                  Date endAt) {
        return getSqlSession().selectOne(sqlId("findOrderSummary"), Map.of("shopId", shopId,
                "subStoreId", subStoreId,
                "statusList", statusList,
                "startAt", startAt,
                "endAt", endAt));
    }

    public ShopOrderSummaryDO findServiceProviderTodaySummary(Long shopId, List<Long> subStoreIds, List<Integer> statusList,
                                                              Date startAt, Date endAt) {
        return getSqlSession().selectOne(sqlId("findOrderSummary"), Map.of("shopId", shopId,
                "subStoreIdList", subStoreIds,
                "statusList", statusList,
                "startAt", startAt,
                "endAt", endAt));
    }

    public ShopOrderProfitSummaryDO findGuiderProfitSummary(Long shopId, Long guiderUserId, Long serviceProviderUserId,
                                                            Date startAt, Date endAt) {
        return getSqlSession().selectOne(sqlId("findOrderProfitSummary"), Map.of("shopId", shopId,
                "guiderUserId", guiderUserId,
                "profitBelongUserId", serviceProviderUserId,
                "queryGuiderProfit", true,
                "startAt", DateUtil.toString(startAt),
                "endAt", DateUtil.toString(endAt)));
    }

    public ShopOrderProfitSummaryDO findServiceProviderProfitSummary(Long shopId, Long serviceProviderUserId, List<Long> subStoreIdList,
                                                                     Date startAt, Date endAt) {
        return getSqlSession().selectOne(sqlId("findOrderProfitSummary"), Map.of("shopId", shopId,
                "subStoreIdList", subStoreIdList,
                "profitBelongUserId", serviceProviderUserId,
                "queryServiceProviderProfit", true,
                "startAt", DateUtil.toString(startAt),
                "endAt", DateUtil.toString(endAt)));
    }

    public ShopOrderProfitSummaryDO findSubStoreAloneProfitSummary(Long shopId, Long subStoreId, Long subStoreUserId,
                                                                   List<Long> guiderUserIdList, Date startAt, Date endAt) {
        return getSqlSession().selectOne(sqlId("findOrderProfitSummary"), Map.of("shopId", shopId,
                "subStoreId", subStoreId,
                "guiderUserIdExcludeList", guiderUserIdList,
                "profitBelongUserId", subStoreUserId,
                "startAt", DateUtil.toString(startAt),
                "endAt", DateUtil.toString(endAt)));
    }

    public ShopOrderProfitSummaryDO findSubStoreGuidersProfitSummary(Long shopId, Long subStoreId, Long subStoreUserId,
                                                                     List<Long> guiderUserIdList, Date startAt, Date endAt) {
        return getSqlSession().selectOne(sqlId("findOrderProfitSummary"), Map.of("shopId", shopId,
                "subStoreId", subStoreId,
                "guiderUserIdList", guiderUserIdList,
                "profitBelongUserId", subStoreUserId,
                "startAt", DateUtil.toString(startAt),
                "endAt", DateUtil.toString(endAt)));
    }

    public ShopOrderProfitSummaryDO findSubStoreProfitSummary(Long shopId, Long subStoreId, Long subStoreUserId,
                                                              Date startTime, Date endTime) {
        return getSqlSession().selectOne(sqlId("findOrderProfitSummary"), Map.of("shopId", shopId,
                "subStoreIdList", Lists.newArrayList(subStoreId),
                "profitBelongUserId", subStoreUserId,
                "startAt", DateUtil.toString(startTime),
                "endAt", DateUtil.toString(endTime)));
    }

    public List<SimpleShopOrderView> findSimpleOrderView(List<Long> shopOrderIds) {
        return getSqlSession().selectList(sqlId("findSimpleOrderView"), Map.of("shopOrderIdList", shopOrderIds));
    }

    /**
     * 以下六个为数据看板的查询服务
     */
    public List<DataBoardDto> findServiceProviderGmvTop(Long shopId, Date startTime, Date endTime) {
        return getSqlSession().selectList(sqlId("findServiceProviderGmvTop"),
                Map.of("shopId", shopId, "startTime", startTime, "endTime", endTime));
    }

    public List<DataBoardDto> findServiceProviderOrderTop(Long shopId, Date startTime, Date endTime) {
        return getSqlSession().selectList(sqlId("findServiceProviderOrderTop"),
                Map.of("shopId", shopId, "startTime", startTime, "endTime", endTime));
    }

    public List<DataBoardDto> findSubStoreGmvTop(Long shopId, Date startTime, Date endTime) {
        return getSqlSession().selectList(sqlId("findSubStoreGmvTop"),
                Map.of("shopId", shopId, "startTime", startTime, "endTime", endTime));
    }

    public List<DataBoardDto> findSubStoreOrderTop(Long shopId, Date startTime, Date endTime) {
        return getSqlSession().selectList(sqlId("findSubStoreOrderTop"),
                Map.of("shopId", shopId, "startTime", startTime, "endTime", endTime));
    }

    public List<DataBoardDto> findSkuGmvTop(Long shopId, Date startTime, Date endTime) {
        return getSqlSession().selectList(sqlId("findSkuGmvTop"),
                Map.of("shopId", shopId, "startTime", startTime, "endTime", endTime));
    }

    public List<DataBoardDto> findSkuOrderTop(Long shopId, Date startTime, Date endTime) {
        return getSqlSession().selectList(sqlId("findSkuOrderTop"),
                Map.of("shopId", shopId, "startTime", startTime, "endTime", endTime));
    }

    public List<DataBoardDto> findProvinceGmvTop(Long shopId, Date startTime, Date endTime) {
        return getSqlSession().selectList(sqlId("findProvinceGmvTop"),
                Map.of("shopId", shopId, "startTime", startTime, "endTime", endTime));
    }

    public List<DataBoardDto> findProvinceOrderTop(Long shopId, Date startTime, Date endTime) {
        return getSqlSession().selectList(sqlId("findProvinceOrderTop"),
                Map.of("shopId", shopId, "startTime", startTime, "endTime", endTime));
    }

    public List<ShopOrder> findConfirmedOrderWithNoConfirmAt(Long minId, Integer limit) {
        return getSqlSession().selectList(sqlId("findConfirmedOrderWithNoConfirmAt"), Map.of("minId", minId,
                "limit", limit));
    }

    public int batchUpdateConfirmAtInfo(List<ShopOrder> updateList) {
        return getSqlSession().update(sqlId("batchUpdateConfirmAtInfo"), ImmutableMap.of("updateList", updateList));
    }

    public List<ShopOrderWithPaymentDO> findByPaymentIds(List<Long> paymentIdList) {
        return getSqlSession().selectList(sqlId("findByPaymentIds"), Map.of("paymentIdList", paymentIdList));
    }

    /**
     * 注意 该方法只返回了部分字段
     * @param paymentId
     * @return
     */
    public List<ShopOrderWithPaymentDO> findByPaymentId(Long paymentId) {
        return getSqlSession().selectList(sqlId("findByPaymentId"), Map.of("paymentId", paymentId));
    }

    public Long getPendingPaymentOrderCountLastHour(Long shopId, Long subStoreId) {
        return getSqlSession().selectOne(sqlId("getPendingPaymentOrderCountLastHour"), Map.of("shopId", shopId, "subStoreId", subStoreId));
    }

    /**
     * 根据查询条件查询对应的店铺订单信息列表
     *
     * @param query 查询条件
     * @return 店铺订单信息列表
     */
    public List<ShopOrder> selectList(Map<String, Object> query) {
        return getSqlSession().selectList(sqlId("selectList"), query);
    }

    public long countWithSnapshot(Map<String, Object> query) {
        return getSqlSession().selectOne(sqlId("countWithSnapshot"), query);
    }

    public List<ShopOrder> pagesWithSnapshot(Map<String, Object> query) {
        return getSqlSession().selectList(sqlId("pagesWithSnapshot"), query);
    }

    public List<Long> listIdsWithSnapshot(Map<String, Object> query) {
        return getSqlSession().selectList(sqlId("listIdsWithSnapshot"), query);
    }

    public void updateOrderPushException(Long shopOrderId, Integer exceptionType, String exceptionReason) {
        getSqlSession().update(sqlId("updateOrderPushException"), Map.of("id", shopOrderId, "exceptionType", exceptionType, "exceptionReason", exceptionReason));
    }

    public ShopOrder selectOne(Map<String, Object> query) {
        return getSqlSession().selectOne(sqlId("selectOne"), query);
    }

    public long countAdminWithSnapshot(Map<String, Object> query) {
        return getSqlSession().selectOne(sqlId("countAdminWithSnapshot"), query);
    }

    public List<ShopOrder> pagesAdminWithSnapshot(Map<String, Object> query) {
        return getSqlSession().selectList(sqlId("pagesAdminWithSnapshot"), query);
    }

    public List<Long> adminListIdsWithSnapshot(Map<String, Object> query) {
        return getSqlSession().selectList(sqlId("adminListIdsWithSnapshot"), query);

    }

    public void updateOrderActualPurchaser(List<Long> orderIds, Long buyerId) {
        getSqlSession().update(sqlId("updateOrderActualPurchaser"),Map.of("orderIds",orderIds,"buyerId",buyerId));
    }
}
