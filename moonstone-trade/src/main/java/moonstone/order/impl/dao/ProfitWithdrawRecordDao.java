package moonstone.order.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.ProfitWithdrawRecord;
import moonstone.order.model.result.ProfitWithdrawRecordInfoDO;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ProfitWithdrawRecordDao extends MyBatisDao<ProfitWithdrawRecord> {

    public List<ProfitWithdrawRecord> findByProfitIdsAndStatus(List<Long> profitIds, List<Integer> statusList) {
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("profitIds", profitIds);
        parameter.put("statusList", statusList);

        return getSqlSession().selectList(sqlId("findByProfitIdsAndStatus"), parameter);
    }

    public List<ProfitWithdrawRecord> findByProfitIds(List<Long> profitIds) {
        return getSqlSession().selectList(sqlId("findByProfitIds"), ImmutableMap.of("profitIds", profitIds));
    }

    public List<ProfitWithdrawRecordInfoDO> findProfitByStatus(Long shopId, Long userId, Integer status,
                                                               Date createdStartAt, Date createdEndAt) {
        return getSqlSession().selectList(sqlId("findProfitByStatus"), ImmutableMap.of("sourceId", shopId,
                "userId", userId,
                "status", status,
                "createdStartAt", createdStartAt,
                "createdEndAt", createdEndAt));
    }

    public int removeByWithdrawId(Long withdrawId) {
        return getSqlSession().delete(sqlId("removeByWithdrawId"), ImmutableMap.of("withdrawId", withdrawId));
    }

    public int updateStatusByWithdrawId(Long withdrawId, Integer status) {
        return getSqlSession().update(sqlId("updateByWithdrawId"), ImmutableMap.of("withdrawId", withdrawId,
                "status", status));
    }
}
