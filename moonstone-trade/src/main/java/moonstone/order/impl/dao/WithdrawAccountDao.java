package moonstone.order.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.WithdrawAccount;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class WithdrawAccountDao extends MyBatisDao<WithdrawAccount> {
    public List<WithdrawAccount> findByShopIdAndUserId(Long shopId, Long userId) {
        return getSqlSession().selectList(sqlId("findByShopIdAndUserId"), ImmutableMap.of("shopId", shopId, "userId", userId));
    }

    public Long revokeDefault(Long shopId, Long userId) {
        return (long) getSqlSession().update(sqlId("revokeDefault"), ImmutableMap.of("shopId", shopId, "userId", userId));
    }

    public List<WithdrawAccount> findByIds(List<Long> idList) {
        return getSqlSession().selectList(sqlId("findByIds"), idList);
    }

    public List<WithdrawAccount> find(Long shopId, Long userId, String from, String account) {
        return getSqlSession().selectList(sqlId("findAccount"), ImmutableMap.of("shopId", shopId,
                "userId", userId,
                "from", from,
                "account", account));
    }
}
