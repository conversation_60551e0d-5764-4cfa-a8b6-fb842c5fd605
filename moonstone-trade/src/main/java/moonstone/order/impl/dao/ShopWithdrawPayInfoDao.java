package moonstone.order.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.ShopWithdrawPayInfo;
import org.springframework.stereotype.Repository;

@Repository
public class ShopWithdrawPayInfoDao extends MyBatisDao<ShopWithdrawPayInfo> {

    public int insertSelective(ShopWithdrawPayInfo parameter) {
        return getSqlSession().insert(sqlId("insertSelective"), parameter);
    }

    public ShopWithdrawPayInfo findByShopId(Long shopId) {
        return getSqlSession().selectOne(sqlId("findByShopId"), ImmutableMap.of("shopId", shopId));
    }
}
