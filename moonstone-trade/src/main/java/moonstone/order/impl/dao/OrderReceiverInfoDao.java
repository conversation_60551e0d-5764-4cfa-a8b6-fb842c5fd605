/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderReceiverInfo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-21
 */
@Repository
public class OrderReceiverInfoDao extends MyBatisDao<OrderReceiverInfo> {

    /**
     * 查找(子)订单关联的收货地址信息
     *
     * @param orderId    (子)订单id
     * @param orderLevel 订单类型
     * @return 关联的收货地址信息
     */
    public List<OrderReceiverInfo> findByOrderIdAndOrderLevel(Long orderId, OrderLevel orderLevel) {

        return getSqlSession().selectList(sqlId("findByOrderIdAndOrderType"),
                ImmutableMap.of("orderId", orderId, "orderType", orderLevel.getValue()));
    }

    /**
     * 查找(子)订单关联的收货地址信息
     *
     * @param orderIds    (子)订单id
     * @param orderLevel 订单类型
     * @return 关联的收货地址信息
     */
    public List<OrderReceiverInfo> findByOrderIdsAndOrderLevel(List<Long> orderIds, OrderLevel orderLevel) {
        return getSqlSession().selectList(sqlId("findByOrderIdsAndOrderType"),
                ImmutableMap.of("orderIds", orderIds, "orderType", orderLevel.getValue()));
    }

    /**
     * 查找(子)订单关联的收货地址信息
     *
     * @param orderId   (子)订单id
     * @param orderType 订单类型
     * @return 关联的收货地址信息
     */
    public OrderReceiverInfo findByOrderIdAndOrderLevel(Long orderId, Integer orderType) {
        return getSqlSession().selectOne(sqlId("findByOrderIdAndOrderType"),
                ImmutableMap.of("orderId", orderId, "orderType", orderType));
    }


}
