package moonstone.order.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.FundsTransferPayment;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public class FundsTransferPaymentDao extends MyBatisDao<FundsTransferPayment> {

    public int insertSelective(FundsTransferPayment parameter) {
        return getSqlSession().insert(sqlId("insertSelective"), parameter);
    }

    public List<FundsTransferPayment> findBy(Long relatedOrderId, Integer relatedOrderType) {
        return getSqlSession().selectList(sqlId("findBy"), Map.of("relatedOrderId", relatedOrderId,
                "relatedOrderType", relatedOrderType));
    }
}
