/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.OrderInvoice;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-21
 */
@Repository
public class OrderInvoiceDao extends MyBatisDao<OrderInvoice> {

    /**
     * 查找(子)订单关联的发票信息
     *
     * @param orderId   (子)订单id
     * @param orderType 订单类型
     * @return 关联的发票
     */
    public List<OrderInvoice> findByOrderIdAndOrderType(Long orderId, Integer orderType) {
        return getSqlSession().selectList(sqlId("findByOrderIdAndOrderType"),
                ImmutableMap.of("orderId", orderId, "orderType", orderType));
    }

    /**
     * 删除 (子)订单关联的发票信息
     *
     * @param orderId   orderId (子)订单id
     * @param orderType 订单类型
     */
    public void deleteByOrderIdAndOrderType(Long orderId, Integer orderType) {
        getSqlSession().delete(sqlId("deleteByOrderIdAndOrderType"),
                ImmutableMap.of("orderId", orderId, "orderType", orderType));
    }
}
