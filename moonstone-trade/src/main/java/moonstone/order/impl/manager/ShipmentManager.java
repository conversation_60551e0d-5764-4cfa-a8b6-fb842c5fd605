/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.manager;

import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.impl.dao.OrderShipmentDao;
import moonstone.order.impl.dao.ShipmentDao;
import moonstone.order.model.*;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Predicate;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ShipmentManager {

    private final ShipmentDao shipmentDao;

    private final OrderShipmentDao orderShipmentDao;

    private final OrderManager orderManager;

    private final ShopOrderReadService shopOrderReadService;

    private final SkuOrderReadService skuOrderReadService;

    @Autowired
    public ShipmentManager(ShipmentDao shipmentDao, OrderShipmentDao orderShipmentDao, OrderManager orderManager, SkuOrderReadService skuOrderReadService, ShopOrderReadService shopOrderReadService) {
        this.shipmentDao = shipmentDao;
        this.orderShipmentDao = orderShipmentDao;
        this.orderManager = orderManager;
        this.skuOrderReadService = skuOrderReadService;
        this.shopOrderReadService = shopOrderReadService;
    }

    /**
     * 创建发货单, 同时也会创建相应的订单和发货单的关联关系
     *
     * @param shipment       待创建的发货单
     * @param orderShipments 订单和发货单的关联关系
     * @return 新创建的发货单id
     */
    @Transactional(rollbackFor = Exception.class)
    public Long create(Shipment shipment, List<OrderShipment> orderShipments) {
        boolean success = shipmentDao.create(shipment);
        if (!success) {
            throw new ServiceException("shipment.create.fail");
        }
        Long shipmentId = shipment.getId();
        for (OrderShipment orderShipment : orderShipments) {
            orderShipment.setShipmentId(shipmentId);
        }
        orderShipmentDao.creates(orderShipments);
        updateOrderShipmentAtByShipments(shipment.getId(), shipment.getStatus());
        return shipmentId;
    }

    /**
     * 更新发货单本身信息,不更新状态,  注意, 如果要更新状态请使用updateStatus接口
     *
     * @param shipment 发货单信息
     */
    public void update(Shipment shipment) {
        boolean success = shipmentDao.update(shipment);
        if (!success) {
            throw new ServiceException("shipment.update.fail");
        }
    }

    /**
     * 根据发货单id更新发货单状态, 同时也会更新相应的订单和发货单的关联记录的状态
     *
     * @param shipmentId 发货单id
     * @param status     状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusByShipmentId(Long shipmentId, Integer status) {

        Shipment u = new Shipment();
        u.setId(shipmentId);
        u.setStatus(status);
        boolean success = shipmentDao.update(u);
        if (!success) {
            throw new ServiceException("shipment.update.fail");
        }

        orderShipmentDao.updateStatusByShipmentId(shipmentId, status);
        updateOrderShipmentAtByShipments(shipmentId, status);
    }

    /**
     * 根据(子)订单id更新关联记录的状态, 同时更新发货单状态
     *
     * @param orderId     (子)订单id
     * @param orderLevel  订单级别
     * @param shipmentIds 对应的发货单id列表
     * @param status      状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusByOrderIdAndOrderType(Long orderId, OrderLevel orderLevel, Set<Long> shipmentIds, Integer status) {
        orderShipmentDao.updateStatusByOrderIdAndOrderType(orderId, orderLevel.getValue(), status);
        updateOrderShipmentAtByShipments(orderId, orderLevel, shipmentIds, status);
        for (Long shipmentId : shipmentIds) {
            Shipment u = new Shipment();
            u.setId(shipmentId);
            u.setStatus(status);
            //如果是更新已收货状态,同时更新确认收货时间
            if (Objects.equals(status, 2)) {
                u.setConfirmAt(new Date());
            }
            boolean success = shipmentDao.update(u);
            if (!success) {
                throw new ServiceException("shipment.update.fail");
            }
        }

    }

    private void updateOrderShipmentAtByShipments(Long orderId, OrderLevel orderLevel, Set<Long> shipmentIds, Integer shipmentStatus) {
        // 只在发货的时候触发
        // shipments#getStatus() == 1 => 发货
        if (!shipmentStatus.equals(1)) {
            return;
        }
        List<Shipment> shipmentList;
        try {
            shipmentList = shipmentDao.findByIds(new ArrayList<>(shipmentIds));
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException("shipment.find.failed");
        }
        if (orderLevel.equals(OrderLevel.SHOP)) {
            List<SkuOrder> skuOrderList = Optional.ofNullable(skuOrderReadService.findByShopOrderId(orderId).getResult())
                    .orElseThrow(() -> new RuntimeException(Translate.of("单品订单查找失败[orderId=>%s]", orderId)));
            boolean failToUpdate = skuOrderList.stream()
                    .map(skuOrder -> {
                        SkuOrder update = new SkuOrder();
                        update.setId(skuOrder.getId());
                        return update;
                    })
                    // 默认获取第一个发货单 因为认为其是发货的 并且属于同一个包裹, (拆单逻辑保证)
                    .peek(update -> update.setShipmentAt(shipmentList.get(0).getShipmentAt()))
                    .map(orderManager::update).anyMatch(Predicate.isEqual(false));
            if (failToUpdate) {
                throw new ServiceException("sku.update.failed");
            }
            updateOrderShipmentAtByShipments(shopOrderReadService.findById(orderId).getResult(), shipmentList);
        } else {
            Response<SkuOrder> rSkuOrder = skuOrderReadService.findById(orderId);
            if (!rSkuOrder.isSuccess()) {
                throw new ServiceException("sku.find.failed");
            }
            SkuOrder update = new SkuOrder();
            update.setId(rSkuOrder.getResult().getId());
            update.setShipmentAt(new Date());
            if (!orderManager.update(update)) {
                throw new ServiceException("sku.update.failed");
            }

            var shopOrderId = skuOrderReadService.findById(orderId).getResult().getOrderId();
            updateOrderShipmentAtByShipments(shopOrderReadService.findById(shopOrderId).getResult(), shipmentList);
        }
    }

    private void updateOrderShipmentAtByShipments(Long shipmentId, Integer shipmentStatus) {
        // 只在发货的时候触发
        // shipments#getStatus() == 1 => 发货
        if (!shipmentStatus.equals(1)) {
            return;
        }
        try {
            List<OrderShipment> orderShipments = orderShipmentDao.findByShipmentId(shipmentId);
            if (CollectionUtils.isEmpty(orderShipments)) {
                return;
            }
            for (OrderShipment orderShipment : orderShipments) {
                updateOrderShipmentAtByShipments(orderShipment.getOrderId(), OrderLevel.fromInt(orderShipment.getOrderType()), Collections.singleton(shipmentId), shipmentStatus);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            throw new ServiceException("shipment.update.fail");
        }

    }

    private void updateOrderShipmentAtByShipments(ShopOrder shopOrder, List<Shipment> shipments) {
        if (CollectionUtils.isEmpty(shipments)) {
            return;
        }
        try {
            Date first = shopOrder.getFirstShipmentAt(), last = shopOrder.getLastShipmentAt();
            for (Shipment shipment : shipments) {
                if (Objects.nonNull(shipment.getShipmentAt())) {
                    if (first == null) {
                        first = shipment.getShipmentAt();
                    }
                    if (last == null) {
                        last = shipment.getShipmentAt();
                    }
                    if (shipment.getShipmentAt().before(first)) {
                        first = shipment.getShipmentAt();
                    }
                    if (shipment.getShipmentAt().after(last)) {
                        last = shipment.getShipmentAt();
                    }
                } else {
                    if (first == null) {
                        first = shipment.getCreatedAt();
                    }
                    if (last == null) {
                        last = shipment.getCreatedAt();
                    }
                    if (shipment.getCreatedAt().before(first)) {
                        first = shipment.getCreatedAt();
                    }
                    if (shipment.getCreatedAt().after(last)) {
                        last = shipment.getCreatedAt();
                    }
                }
            }
            ShopOrder update = new ShopOrder();
            update.setId(shopOrder.getId());
            update.setFirstShipmentAt(first);
            update.setLastShipmentAt(last);
            if (orderManager.updateShopOrder(update).equals(false)) {
                throw new ServiceException("shopOrder.update.failed");
            }
        } catch (Exception syncShipmentFail) {
            log.error("{} fail to sync the shipmentAt for order[{}]", LogUtil.getClassMethodName(), shopOrder.getId(), syncShipmentFail);
        }
    }

}
