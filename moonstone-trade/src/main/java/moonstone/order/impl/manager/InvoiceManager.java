/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.impl.manager;

import moonstone.order.impl.dao.InvoiceDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-19
 */
@Component
public class InvoiceManager {

    private final InvoiceDao invoiceDao;

    @Autowired
    public InvoiceManager(InvoiceDao invoiceDao) {
        this.invoiceDao = invoiceDao;
    }

    @Transactional
    public void setDefault(Long userId, Long invoiceId) {
        invoiceDao.notDefault(userId);
        invoiceDao.setDefault(userId, invoiceId);
    }
}
