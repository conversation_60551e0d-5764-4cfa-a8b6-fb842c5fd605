/*
 * Copyright (c) 2014 杭州端点网络科技有限公司
 */

package moonstone.cart.impl.service;

import com.google.common.base.MoreObjects;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cart.impl.dao.CartItemDao;
import moonstone.cart.model.CartItem;
import moonstone.cart.service.CartReadService;
import moonstone.common.model.Either;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Copyright (c) 2015 杭州端点网络科技有限公司
 * Date: 3/10/16
 * Time: 4:50 PM
 * Author: 2015年 <a href="mailto:<EMAIL>">张成栋</a>
 */
@Slf4j
@Service
@RpcProvider
public class CartReadServiceImpl implements CartReadService {
    private final CartItemDao cartItemDao;

    @Autowired
    public CartReadServiceImpl(CartItemDao cartItemDao) {
        this.cartItemDao = cartItemDao;
    }

    /**
     * 查询当前登录用户购物车（某店铺）的sku的总数
     *
     * @param userId 当前登录用户
     * @param shopId 店铺id （查全部则null）
     */
    @Override
    public Response<Integer> count(Long userId, Long shopId) {
        try {
            Integer total;
            if (shopId == null) {
                total = cartItemDao.countCartQuantity(userId);
            } else {
                total = cartItemDao.countCartQuantityByShopId(userId, shopId);
            }
            return Response.ok(MoreObjects.firstNonNull(total, 0));
        } catch (Exception e) {
            log.error("fail to get cart count by user(Id={}), cause:{}",
                      userId, Throwables.getStackTraceAsString(e));
            return Response.fail("cart.count.query.fail");
        }
    }

    /**
     * 列出用户的所有购物车商品记录
     *
     * @param buyerId 买家id
     * @return 购物车商品记录
     */
    @Override
    public Response<List<CartItem>> findByUserId(Long buyerId) {
        try {
            List<CartItem> cartItems = cartItemDao.list(ImmutableMap.of("buyerId", buyerId));
            return Response.ok(cartItems);
        } catch (Exception e) {
            log.error("fail to list cart for user(id={}), cause:{}", buyerId, Throwables.getStackTraceAsString(e));
            return Response.fail("cart.list.fail");
        }
    }

    /**
     * 列出用户某店铺的所有购物车商品记录
     *
     * @param buyerId    买家id
     * @param shopId     店铺id
     * @return 购物车商品记录
     */
    @Override
    public Response<List<CartItem>> findByUserIdAndShopId(Long buyerId, Long shopId) {
        try {
            List<CartItem> cartItems = cartItemDao.list(ImmutableMap.of("buyerId", buyerId, "shopId", shopId));
            return Response.ok(cartItems);
        } catch (Exception e) {
            log.error("fail to list cart for user(id={}), cause:{}", buyerId, Throwables.getStackTraceAsString(e));
            return Response.fail("cart.list.fail");
        }
    }

    @Override
    public Response<List<CartItem>> findByUserIdAndSkuId(Long buyerId, Long skuId) {
        try {
            List<CartItem> cartItems = cartItemDao.list(ImmutableMap.of("buyerId", buyerId, "skuId", skuId));
            return Response.ok(cartItems);
        } catch (Exception e) {
            log.error("fail to list cart for user(id={}) by sku(id={}), cause:{}", buyerId, skuId, Throwables.getStackTraceAsString(e));
            return Response.fail("cart.list.fail");
        }
    }

    @Override
    public Either<CartItem> findById(Long id) {
        try {
            CartItem cartItems = cartItemDao.findById(id);
            return Either.ok(cartItems);
        } catch (Exception e) {
            log.error("fail to list cart for user(CartItem_id={}) ), cause:{}", id, Throwables.getStackTraceAsString(e));
            return Either.fail();
        }
    }

    @Override
    public Either<List<CartItem>> findCartsByIds(List<Long> ids) {
        try {
            List<CartItem> cartItems = cartItemDao.findCartsByIds(ids);
            return Either.ok(cartItems);
        } catch (Exception e) {
            log.error("fail to list cart for user(ids={}) ), cause:{}", ids, Throwables.getStackTraceAsString(e));
            return Either.fail();
        }
    }
}
