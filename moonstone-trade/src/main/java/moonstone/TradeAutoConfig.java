package moonstone;

import moonstone.common.config.ImageConfig;
import moonstone.order.api.AbstractPersistedOrderMaker;
import moonstone.order.api.TradeBatchNoGenerator;
import moonstone.order.component.DefaultPersistedOrderMaker;
import moonstone.order.component.DefaultTradeBatchNoGenerator;
import moonstone.order.model.OrderLevel;
import moonstone.order.strategy.DefaultShopOrderStatusStrategy;
import moonstone.order.strategy.ShopOrderStatusStrategy;
import moonstone.settle.impl.SettleServiceAutoConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Mail: <EMAIL>
 * Data: 16/4/26
 * Author: yangzefeng
 */
@Configuration
@ComponentScan({"moonstone.weCart.impl",
        "moonstone.cart.impl",
        "moonstone.order.impl",
        "moonstone.promotion.impl",
        "moonstone.express.impl",
        "moonstone.stock.impl",
        "moonstone.integral.impl",
        "moonstone.order.convert",
})
@Import({SettleServiceAutoConfig.class, ImageConfig.class})
public class TradeAutoConfig {

    @ConditionalOnMissingBean(ShopOrderStatusStrategy.class)
    @Bean
    public ShopOrderStatusStrategy shopOrderStatusStrategy() {
        return new DefaultShopOrderStatusStrategy();
    }

    /**
     * 默认一个店铺订单一个收货地址, 一个店铺订单一张发票
     */
    @ConditionalOnMissingBean(AbstractPersistedOrderMaker.class)
    @Bean
    public AbstractPersistedOrderMaker orderMaker() {
        return new DefaultPersistedOrderMaker(OrderLevel.SHOP, OrderLevel.SHOP);
    }

    @ConditionalOnMissingBean(TradeBatchNoGenerator.class)
    @Bean
    public TradeBatchNoGenerator tradeBatchNoGenerator(){
        return new DefaultTradeBatchNoGenerator();
    }
}
