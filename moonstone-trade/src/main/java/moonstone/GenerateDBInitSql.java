package moonstone;

import moonstone.common.utils.MybatisGenerator;
import moonstone.common.utils.XML;
import moonstone.order.model.OrderProfitView;
import moonstone.order.model.ProfitWithdrawRecord;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.PrintStream;
import java.nio.charset.StandardCharsets;

import static moonstone.common.api.SqlInitDatabaseGenerator.generateInitSql;

public interface GenerateDBInitSql {
    static void main(String[] args) throws Exception {
        var clazz = new Class<?>[]{
                ProfitWithdrawRecord.class,
                OrderProfitView.class
        };
        var mainResourceClass = GenerateDBInitSql.class;
        var sqlXmlPath = File.separator + "resources" + File.separator + "init-table.mysql.sql";
        var dir = new File(mainResourceClass.getResource("/").toURI());
        var filePath = dir.getParentFile().getParentFile().getAbsolutePath()
                + File.separator + "src" + File.separator + "main" + sqlXmlPath;
        var p = "";
        var builder = new StringBuilder();
        // generate the SQL
        for (Class<?> i : clazz) {
            builder.append(generateInitSql(p, false, i)).append("\n\n");
        }

        System.out.println(builder);
        new FileOutputStream(filePath)
                .write(builder.toString().getBytes(StandardCharsets.UTF_8));
        // write into file
        filePath = (dir.getParentFile().getParentFile().getAbsolutePath()
                + "/src/main/resources/mapper/").replaceAll("/", File.separator);
        for (Class<?> i : clazz) {
            var xml = MybatisGenerator.generate(MybatisGenerator::camelNameConvert, f -> f.substring(f.lastIndexOf('.') + 1), i);
            var f = filePath + i.getSimpleName() + "Mapper.xml";
            if (new File(f).exists()) {
                xml = MybatisGenerator.mergeMybatisXML(XML.parseXML(new FileInputStream(f)), xml);
            }
            new PrintStream(new FileOutputStream(f))
                    .append("<?xml version=\"1.0\" encoding=\"UTF-8\" ?>" + "\n" +
                            "<!-- ~Simple XML Generate By moonstone.common.util.MybatisGenerator~ -->\n" +
                            "<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\" >\n\n")
                    .println(xml);
        }
    }
}
