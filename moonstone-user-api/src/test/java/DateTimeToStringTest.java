import moonstone.common.utils.DateUtil;
import org.joda.time.DateTime;
import org.junit.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DateTimeToStringTest {
    @Test
    public void testToString() {
        assert new DateTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()).toString(DateUtil.YMDHMS_FORMAT).equals(DateUtil.toString(LocalDateTime.now()));
        assert DateUtil.toString(LocalDateTime.now()).equals(LocalDateTime.now().format(DateTimeFormatter.ofPattern(DateUtil.YMDHMS_FORMAT)));
        System.out.println(DateUtil.toString(LocalDateTime.now()));
        assert new DateTime(LocalDate.now().atTime(LocalTime.ofSecondOfDay(0)).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()).toString(DateUtil.YMDHMS_FORMAT).equals(DateUtil.toString(LocalDate.now()));
        System.out.println(DateUtil.toString(LocalDate.now()));
        assert new DateTime(LocalTime.now().atDate(LocalDate.now()).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()).toString(DateUtil.YMDHMS_FORMAT).equals(DateUtil.toString(LocalTime.now()));
        System.out.println(DateUtil.toString(LocalTime.now()));
        assert new DateTime(new Date()).toString(DateUtil.YMDHMS_FORMAT).equals(DateUtil.toString(new Date()));
        System.out.println(DateUtil.toString(new Date()));
    }
}
