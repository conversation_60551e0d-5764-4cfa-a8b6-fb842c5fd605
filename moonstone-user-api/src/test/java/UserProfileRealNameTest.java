import moonstone.user.model.UserProfile;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.BeanUtils;

import java.util.Base64;

public class UserProfileRealNameTest {

    @Test
    public void testRealName() {
        String emoji = "\uD83C\uDDE8";
        UserProfile userProfile = new UserProfile();
        userProfile.setRealName("a" + emoji);
        Assert.assertArrayEquals(Base64.getEncoder().encode(("a" + emoji).getBytes()), userProfile.getEncodeName().getBytes());
        Assert.assertEquals("a", userProfile.getRealName_());
        Assert.assertEquals("a" + emoji, userProfile.getRealName());

        userProfile.setRealName("b" + emoji);
        Assert.assertArrayEquals(Base64.getEncoder().encode(("b" + emoji).getBytes()), userProfile.getEncodeName().getBytes());
        Assert.assertEquals("b", userProfile.getRealName_());
        Assert.assertEquals("b" + emoji, userProfile.getRealName());

        UserProfile copy = new UserProfile();
        BeanUtils.copyProperties(userProfile, copy);

        Assert.assertArrayEquals(Base64.getEncoder().encode(("b" + emoji).getBytes()), copy.getEncodeName().getBytes());
        Assert.assertEquals("b", copy.getRealName_());
        Assert.assertEquals("b" + emoji, copy.getRealName());
    }
}