package moonstone.user.service;

import io.terminus.common.model.Response;
import moonstone.user.model.Operator;

/**
 * <AUTHOR>
 */
public interface OperatorWriteService {

    /**
     * 创建运营
     *
     * @param operator 运营信息
     * @return 运营用户 ID
     */
    Response<Long> create(Operator operator);

    /**
     * 更新运营
     *
     * @param operator 运营信息
     * @return 是否更新成功
     */
    Response<Boolean> update(Operator operator);
}
