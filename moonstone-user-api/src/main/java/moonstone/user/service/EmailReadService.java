package moonstone.user.service;

import io.terminus.common.model.Response;
import moonstone.user.enums.EmailBusinessTypeEnum;
import moonstone.user.model.Email;

import java.util.List;

public interface EmailReadService {

    /**
     * 通过平台id查询接收邮箱列表
     * @param shopId 平台id
     * @return 接收邮箱列表
     */
    Response<List<Email>> findEmailsByShopId(Long shopId, EmailBusinessTypeEnum businessType);
}
