package moonstone.user.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.user.model.SubSeller;

import java.util.List;
import java.util.Optional;

/**
 * 商家读服务
 *
 * <AUTHOR>
 */
public interface SellerReadService {

    /**
     * 通过主键查商家子账户
     *
     * @param id 子账号关联表主键
     * @return 子账号信息
     */
    Response<SubSeller> findSubSellerById(Long id);

    /**
     * 通过子账户用户 ID 查询子账号关联
     *
     * @param userId 子账户用户 ID
     * @return 子账户关联列表
     */
    Response<Optional<SubSeller>> findSubSellerByUserId(Long userId);

    /**
     * 分页子账户信息
     *
     * @param masterUserId 主账户用户 ID
     * @param status 子账户绑定状态
     * @param pageNo 页号
     * @param size 查询数量
     * @return 子账户分页
     */
    Response<Paging<SubSeller>> subSellerPagination(Long masterUserId, Integer status, Integer pageNo, Integer size);

    /**
     * 根据主账号id 和 角色id 查询
     *
     * @param masterUserId
     * @param roleIds
     * @return
     */
    Response<List<SubSeller>> findByMasterUserIdAndRoleIds(Long masterUserId, List<Long> roleIds);

    Long countByRoleId(Long id);
}
