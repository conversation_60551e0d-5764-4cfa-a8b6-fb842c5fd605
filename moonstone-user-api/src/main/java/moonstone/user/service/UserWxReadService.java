package moonstone.user.service;

import io.terminus.common.model.Response;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.model.Either;
import moonstone.user.dto.UserWxRoadDTO;
import moonstone.user.model.User;
import moonstone.user.model.UserWx;

import java.util.List;
import java.util.Optional;

/**
 * Created by CaiZhy on 2018/10/12.
 */
public interface UserWxReadService {
    Response<UserWx> findByOpenIdAndAppId(String openId, String appId);

    Response<UserWx> findByOpenIdAndAppIdAndAppType(String openId, String appId, AppTypeEnum appType);

    List<Long> findUserIdByAppId(String appId);

    User queryByUnionIdAndAppId(String unionId, String appId);

    User queryByUnionIdAndAppIdAndAppType(String unionId, String appId, AppTypeEnum appType);

    User queryByOpenIdAndAppId(String openId, String appId);

    User queryByOpenIdAndAppIdAndAppType(String openId, String appId, AppTypeEnum appType);

    List<UserWx> queryByUnionId(String unionId, Integer size);

    List<UserWx> queryByUnionIdAndAppType(String unionId, AppTypeEnum appType, Integer size);

    UserWxRoadDTO wxRoad(UserWx userWx);

    Response<List<UserWx>> queryByUserId(long userId);

    Either<List<UserWx>> findByOpenId(String openId);

    Either<Optional<UserWx>> findByAppIdAndUserId(String appId, Long userId);

    Either<Optional<UserWx>> findByAppIdAndUserIdAndAppType(String appId, Long userId, AppTypeEnum appType);

    UserWx getById(Long miniUserId);
}
