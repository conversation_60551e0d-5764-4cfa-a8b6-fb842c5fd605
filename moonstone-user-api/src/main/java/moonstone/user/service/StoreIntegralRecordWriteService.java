package moonstone.user.service;

import moonstone.common.model.Either;
import moonstone.user.model.StoreIntegralRecord;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/9/4 16:03
 */
public interface StoreIntegralRecordWriteService {

    Either<Long> create(StoreIntegralRecord storeIntegralRecord);

    Either<Boolean> update(StoreIntegralRecord storeIntegralRecord);

    Either<Boolean> updateByThirdAndIntegralId(Long thirdId, Long integralId, int status);
}
