package moonstone.user.service;

import moonstone.common.model.Either;
import moonstone.user.model.StoreProxyInfo;

import java.util.List;
import java.util.Optional;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/26 14:09
 */
public interface StoreProxyInfoReadService {
    Either<Optional<StoreProxyInfo>> findByProxyId(Long proxyId);

    Either<Long> getSubProxyInfo(List<Long> ids, long l);

    Either<List<StoreProxyInfo>> getCountSubProxyInfo(List<Long> proxyIds, Integer status);
}
