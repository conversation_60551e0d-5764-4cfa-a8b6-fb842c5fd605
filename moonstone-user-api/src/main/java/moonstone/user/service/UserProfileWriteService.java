package moonstone.user.service;

import io.terminus.common.model.Response;
import moonstone.user.model.UserProfile;

/**
 * Created by cu<PERSON>wen<PERSON><PERSON> on 16/3/8.
 */
public interface UserProfileWriteService {
    /**
     * 更新当前登录用户的信息
     * @param  profile 用户信息
     * @return 更新成功返回true, 反之false
     */
    Response<Boolean> updateProfile(UserProfile profile);

    /**
     * 创建当前登录用户的信息
     * @param  profile 用户信息
     * @return 更新成功返回true, 反之false
     */
    Response<Boolean> createProfile(UserProfile profile);

    /**
     * 修改用户的地区id
     * @param  profile 用户信息
     * @return 更新成功返回true, 反之false
     */
    Response<Boolean> updateProfileAreaId(UserProfile profile);
}
