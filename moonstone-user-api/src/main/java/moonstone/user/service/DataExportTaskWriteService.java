package moonstone.user.service;

import io.terminus.common.model.Response;
import moonstone.user.model.DataExportTask;

public interface DataExportTaskWriteService {

    /**
     * 新增
     *
     * @param dataExportTask
     * @return
     */
    Response<Boolean> create(DataExportTask dataExportTask);

    /**
     * 更新
     *
     * @param dataExportTask
     * @return
     */
    Response<Boolean> update(DataExportTask dataExportTask);
}
