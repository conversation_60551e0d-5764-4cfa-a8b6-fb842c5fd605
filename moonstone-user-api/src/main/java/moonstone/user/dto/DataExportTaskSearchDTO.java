package moonstone.user.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class DataExportTaskSearchDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -4066570692456522112L;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 平台id
     */
    private Long shopId;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 分页大小
     */
    private Integer pageSize;

    /**
     * 导出任务的状态
     */
    private Integer status;

    /**
     * 任务的类型
     */
    private String type;

    /**
     * 导出开始时间 from
     * yyyy-MM-dd
     */
    private String startTimeFrom;

    /**
     * 导出开始时间 to
     * yyyy-MM-dd
     */
    private String startTimeTo;
}
