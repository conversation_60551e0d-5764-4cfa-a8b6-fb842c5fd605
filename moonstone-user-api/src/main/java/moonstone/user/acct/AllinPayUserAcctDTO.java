package moonstone.user.acct;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AllinPayUserAcctDTO implements Serializable {

    @NotNull(message = "分享码 不能为空")
    @ApiModelProperty("分享码的内容")
    private String key;

    @NotNull(message = "openId 不能为空")
    @ApiModelProperty("openId 用户身份标识")
    private String openId;

//    @ApiModelProperty("授权码")
//    @NotNull(message = "wxCode 不能为空")
//    private String wxCode;

    @ApiModelProperty("应用ID")
    @NotNull(message = "appId 不能为空")
    private String appId;

//    @ApiModelProperty("projectId")
//    @NotNull(message = "projectId 不能为空")
//    private Long projectId;
}
