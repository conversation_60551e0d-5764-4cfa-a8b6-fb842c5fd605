package moonstone.user.auth;

import com.google.common.base.Throwables;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.enums.UserRole;
import moonstone.common.model.Either;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.User;
import moonstone.user.service.AdminUserService;
import moonstone.user.service.RoleMenuManager;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.UserReadService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 默认用户角色加载器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class DefaultUserRoleLoader implements UserRoleLoader {
    private final UserReadService<User> userReadService;
    private final AdminUserService adminUserService;
    private final RoleProviderRegistry roleProviderRegistry;
    private final StoreProxyReadService storeProxyReadService;
    private final RoleMenuManager roleMenuManager;

    @Override
    public Response<RoleContent> hardLoadRoles(Long userId) {
        try {
            if (userId == null) {
                log.warn("hard load roles failed, userId=null");
                return Response.fail("user.id.empty");
            }
            val findResp = userReadService.findById(userId);
            if (!findResp.isSuccess()) {
                log.warn("find user failed, userId={}, error={}", userId, findResp.getError());
                return Response.fail(findResp.getError());
            }
            User user = findResp.getResult();
            if (user == null) {
                // findById 已经保证不会进入这里
                log.warn("hard load roles failed, user not found, id={}", userId);
                return Response.fail("user.not.found");
            }

            if (user.getType() == null) {
                log.warn("user has no type, userId={}, we treat is as empty permission", userId);
                return Response.ok(initRoles());
            }
            int userType = user.getType();
            RoleContent mutableRoles = initRoles();
            Set<String> allowMenuSet = new HashSet<>();
            List<RoleProvider> roleProviders = roleProviderRegistry.getRoleProviders();
            if (!CollectionUtils.isEmpty(roleProviders)) {
                for (RoleProvider roleProvider : roleProviders) {
                    if (roleProvider.acceptType() != userType) {
                        continue;
                    }
                    Role role = roleProvider.getRoleByUserId(userId);
                    if (role == null) {
                        continue;
                    }
                    if (role.getType() == 1) {
                        // static
                        mutableRoles.getRoles().add(role);
                        if (role.getBase().equals(UserRole.SELLER.name())) {
                            mutableRoles.getDynamicRoles().add(role);
                            if (role.getTreeNodeSelection() == null) {
                                roleMenuManager.defaultAllowMenuForRole(role.getBase(), userId)
                                        .ifSuccess(role::setTreeNodeSelection);
                            }
                        }
                    } else {
                        mutableRoles.getDynamicRoles().add(role);
                    }
                }
            }
            // 回写 user 表, 做同步
            postTryUpdateRoles(user, mutableRoles);

            return Response.ok(mutableRoles);
        } catch (Exception e) {
            log.error("hard load rich roles failed, userId={}, cause:{}",
                    userId, Throwables.getStackTraceAsString(e));
            return Response.fail("user.role.load.fail");
        }
    }

    private Response<RoleContent> initProxyUsers(int userType, User user) {

        //局部将userType修改为5 --子账户
        int lsType = userType;

        Either<Optional<StoreProxy>> optionalResult = storeProxyReadService.findByUserId(user.getId());
        if (optionalResult.isSuccess() && optionalResult.take().isPresent()
                && optionalResult.take().get() != null
                && Objects.equals(3, optionalResult.take().get().getStatus())) {
            userType = 5;
            RoleContent mutableRoles = initRoles();

            List<RoleProvider> roleProviders = roleProviderRegistry.getRoleProviders();
            if (!CollectionUtils.isEmpty(roleProviders)) {
                for (RoleProvider roleProvider : roleProviders) {
                    if (roleProvider.acceptType() != userType && roleProvider.acceptType() != lsType) {
                        continue;
                    }
                    Role role = roleProvider.getRoleByUserId(user.getId());
                    if (role != null) {
                        if (role.getType() != 1) {
                            mutableRoles.getDynamicRoles().add(role);
                        } else {
                            mutableRoles.getRoles().add(role);
                        }
                    }
                }
            }
//            // 回写 user 表, 做同步
            postTryUpdateRoles(user, mutableRoles);
            return Response.ok(mutableRoles);
        }
        return Response.fail("");
    }


    private RoleContent initRoles() {
        RoleContent non = new RoleContent();
        non.setRoles(new ArrayList<Role>());
        non.setDynamicRoles(new ArrayList<Role>());
        return non;
    }

    private void postTryUpdateRoles(final User user, final RoleContent roles) {
        // TODO: 做成异步
        Set<String> originRoles = new HashSet<>();
        if (user.getRoles() != null) {
            originRoles.addAll(user.getRoles());
        }
        Set<String> currentRoles = buildVanillaRole(roles);

        if (!currentRoles.equals(originRoles)) {
            List<String> toUpdateRoles = new ArrayList<>(currentRoles);
            val resp = adminUserService.updateRoles(user.getId(), toUpdateRoles);
            if (!resp.isSuccess()) {
                log.warn("try update user roles failed, userId={}, roles={}, error={}",
                        user.getId(), toUpdateRoles, resp.getError());
            }
        }
    }

    private Set<String> buildVanillaRole(final RoleContent roles) {
        Set<String> rs = new HashSet<>();
        for (Role staticRole : roles.getRoles()) {
            rs.add(staticRole.getBase());
        }
        for (Role dynamicRole : roles.getDynamicRoles()) {
            rs.add(dynamicRole.getBase());
        }
        return rs;
    }
}
