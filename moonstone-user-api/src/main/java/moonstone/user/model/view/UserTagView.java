package moonstone.user.model.view;

import lombok.AllArgsConstructor;
import moonstone.common.constants.ParanaConstants;
import moonstone.common.model.CommonUser;
import moonstone.user.model.User;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface UserTagView {
    /**
     * 是否为新微信用户
     *
     * @return 是否为新微信用户
     */
    boolean isNewWxUser();

    /**
     * 是否为微信用户
     *
     * @return 是否为微信用户
     */
    boolean isWxUser();

    /**
     * 构建
     *
     * @param user 原始类
     * @return view
     */
    static UserTagView build(User user) {
        return new Impl.UserTagViewImpl(user);
    }

    /**
     * 构建
     *
     * @param commonUser 原始类
     * @return view
     */
    static UserTagView build(CommonUser commonUser) {
        return new Impl.UserTagViewImpl(commonUser == null ? null : new User() {
            @Override
            public Map<String, String> getTags() {
                return commonUser.getTags();
            }
        });
    }

    class Impl {
        @AllArgsConstructor
        private static class UserTagViewImpl implements UserTagView {
            User user;

            @Override
            public boolean isNewWxUser() {
                return Optional.ofNullable(user)
                        .map(User::getTags)
                        .map(tags -> tags.get(ParanaConstants.USER_IS_NEW_WX_USER))
                        .filter(b -> b.equals("1") || b.equals(Boolean.TRUE.toString().toLowerCase()))
                        .isPresent();
            }

            @Override
            public boolean isWxUser() {
                return Optional.ofNullable(user)
                        .map(User::getTags)
                        .map(tags -> tags.get(ParanaConstants.USER_IS_WX_USER))
                        .filter(b -> b.equals("1") || b.equals(Boolean.TRUE.toString().toLowerCase()))
                        .isPresent();
            }
        }
    }
}
