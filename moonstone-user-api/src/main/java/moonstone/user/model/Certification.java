package moonstone.user.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 功能描述:  认证表，进行供应商和分销商的认证记录
 * 创建时间:  2020/7/1 2:39 下午
 *
 * <AUTHOR>
 */
@Data
public class Certification implements Serializable {

    private static final long serialVersionUID = 8864976729780317681L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 认证未通过的理由
     */
    private Long reasonForFailure;

    /**
     * 认证状态，0：未认证，10：供应商认证中，11：认证通过，12：认证失败。13：认证通过跳转首页
     * 20：分销商实体认证中，21：分销商实体认证成功，22：分销商实体认证失败，23：认证通过跳转首页。
     * 30：分销商微商认证中，31分销商微商认证成功，32：分销商微商认证失败，33：认证通过跳转首页。
     */
    private Integer status;


}
