package moonstone.user.model;

import lombok.Data;
import moonstone.common.enums.AppTypeEnum;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/10/10.
 */
@Data
public class UserWx implements Serializable {
    private static final long serialVersionUID = -5131652746763690084L;
    private Long id;
    private String appId;

    /**
     * 小程序类型
     * @see moonstone.common.enums.AppTypeEnum
     */
    private Integer appType;

    private String openId;
    /**
     * 也作为sessionKey
     */
    private String accessToken;
    private String sessionKey;
    private String refreshToken;
    private Long accessTime;
    private Long refreshTime;
    private String scope;
    private String nickName;
    private String avatarUrl;
    private String unionId;
    private Long userId;
}
