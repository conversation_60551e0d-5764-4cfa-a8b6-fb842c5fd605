/*
 * Copyright (c) 2014 杭州端点网络科技有限公司
 */

package moonstone.cart.service;

import io.terminus.common.model.BaseUser;
import io.terminus.common.model.Response;
import moonstone.item.model.Sku;

import java.util.List;
import java.util.Map;

/**
 * Copyright (c) 2015 杭州端点网络科技有限公司
 * Date: 3/10/16
 * Time: 4:50 PM
 * Author: 2015年 <a href="mailto:<EMAIL>">张成栋</a>
 */
public interface CartWriteService {
    /**
     * 改变永久购物车商品数量
     *
     * @param sku       商品信息
     * @param quantity  改变数量 正数为增负数为减
     * @param userId    用户id
     * @param buyerNote
     * @return 操作后购物车内该商品的数量
     */
    Response<Integer> changeCart(Sku sku, Integer quantity, Long userId, String buyerNote);

    /**
     * 批量改变购物车数量, 例如在订单预览页返回购物车修改
     * @param skuAndQuantity    商品与数量
     * @param userId            登录用户
     * @return 操作后最终购物车商品数量
     */
    Response<Map<Long, Integer>> changeCart(Map<Sku, Integer> skuAndQuantity, Long userId);

    /**
     * 根据id删除一条购物车商品
     *
     * @param id    购物商品记录的id
     * @return 操作成功
     */
    Response<Boolean> deleteById(Long id);

    /**
     * 清空购物车
     * @param userId 当前登录id
     * @return 是否删除成功
     */
    Response<Boolean> deleteCart(Long userId);

    /**
     * 批量删除永久购物车中的商品
     * @param skuIds 商品skuId组
     * @param userId 当前登录用户id
     * @return 是否删除成功
     */
    Response<Boolean> batchDelete(List<Long> skuIds, Long userId);

    /**
     * 提交商品到订单
     *
     * @param skuAndQuantity    商品与数量
     * @param buyer             买家
     * @return 需求清单编号
     */
    Response<Long> submitCart(Map<Long, Integer> skuAndQuantity, BaseUser buyer);
}
