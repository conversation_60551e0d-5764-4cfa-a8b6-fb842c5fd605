package moonstone.event;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 积分支付系统
 */
public record OrderIntegralEvent(Long orderId, Long userId,
                                 Long integralFee, String outId,
                                 List<Long> orderIds) implements Serializable, OrderEvent {

    @Serial
    private static final long serialVersionUID = 6068457279336231160L;

}
