package moonstone.order.enu;

public enum AgentPayOrderStatusEnum {
    NOT_PAID(0, "未支付"),
    PAID(1, "已支付"),
    ;

    private final Integer code;
    private final String description;

    AgentPayOrderStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static AgentPayOrderStatusEnum parse(Integer code) {
        for (var current : values()) {
            if (current.getCode().equals(code)) {
                return current;
            }
        }

        return null;
    }
}
