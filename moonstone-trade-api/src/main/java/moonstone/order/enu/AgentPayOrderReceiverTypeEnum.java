package moonstone.order.enu;

/**
 * 什么鸡脖大B, 小B, 小C， 都是支付公司（通商云）的会员类型
 */
public enum AgentPayOrderReceiverTypeEnum {
    ENTERPRISE(1, "企业(大B)"),
    ENTERPRISE_USER(2, "企业用户(小B)"),
    SUB_STORE(3, "门店(小c)"),
    ;

    private final Integer code;
    private final String description;

    AgentPayOrderReceiverTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
