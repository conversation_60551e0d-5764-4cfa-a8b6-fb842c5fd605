package moonstone.order.enu;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@AllArgsConstructor
@Getter
public enum WithdrawBy {
    /**
     * 提现方式 似乎会影响前端
     */
    INDEX("withdrawPattern")    //用于extra定位
    , DanDing("danDing")        //由我司完成提现
    , XinBaDa("xinBaDa")        //第三方
    , YunAccount("yunAccount")  //由第三方云账户完成
    , Other("other")            //其他
    ;
    String name;
}
