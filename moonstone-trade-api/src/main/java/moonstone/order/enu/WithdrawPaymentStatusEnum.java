package moonstone.order.enu;

public enum WithdrawPaymentStatusEnum {
    NOT_PAID(0, "未支付"),
    PAY_IN_PROGRESS(1, "支付中"), //如等待对方的支付结果回调
    PAID(2, "已支付"),
    FAILURE(3, "支付失败");

    private Integer code;
    private String description;

    WithdrawPaymentStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
