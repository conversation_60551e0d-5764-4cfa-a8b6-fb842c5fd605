package moonstone.order.model.result;

import lombok.Data;

@Data
public class ShopOrderProfitSummaryDO {

    /**
     * 预计收入
     */
    private Long foreseeProfit;

    /**
     * 可提现收入
     */
    private Long presentProfit;

    public static Long calculateTotalProfit(ShopOrderProfitSummaryDO summaryDO) {
        if (summaryDO == null) {
            return 0L;
        }

        return (summaryDO.getForeseeProfit() == null ? 0L : summaryDO.getForeseeProfit()) +
                (summaryDO.getPresentProfit() == null ? 0L : summaryDO.getPresentProfit());
    }
}
