package moonstone.order.model.result;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> <EMAIL>
 * @since 2022-08-13 16:11
 */
@Data
public class SimpleShopOrderView {

    /**
     * 主订单id
     */
    private Long id;

    /**
     * 申报单号
     */
    private String declaredId;

    /**
     * 订单创建时间
     */

    private Date createdAt;

    /**
     * 订单金额
     */
    private Long fee;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品图片url
     */
    private String skuImage;

    /**
     * 商品数量
     */
    private Integer quantity;
}
