package moonstone.order.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ShopOrderShareQr implements Serializable {

    private Long id;

    private Long shopId;

    private Long orderId;
    private String orderIds;

    private Long userId;

    private Long friendId;

    private String code;

    private Date endTime;

    private boolean status;

    private boolean deleted;

    private Date createdAt;

    private Date updatedAt;
}
