package moonstone.order.model.domain;

import moonstone.common.utils.Translate;
import moonstone.order.model.WithDrawProfitApply;

import java.util.Date;
import java.util.Map;
import java.util.Optional;

/**
 * 代理原对象操作, 以避免各种问题
 *
 * <AUTHOR>
 * @see WithDrawProfitApply 被代理操作的类, 操作会被透传入 影响到原来的类
 */
public abstract class AbstractWithdrawApplyDomain {
    WithDrawProfitApply delegate;

    /**
     * 生成一个代理 进行代理操作
     *
     * @param apply 提现申请
     * @return 代理操作类
     */
    public static AbstractWithdrawApplyDomain build(WithDrawProfitApply apply) {
        return new AbstractWithdrawApplyDomainImpl(apply);
    }

    /**
     * 生成更新类
     * 尽可能减少影响
     *
     * @return 更新类
     * @see WithDrawProfitApply#getExtra()  额外字段
     * @see WithDrawProfitApply#getStatus()     主要操作的状态
     * @see moonstone.common.model.EntityBase#setExtra(Map)  更新Extra 会更新extraStr
     */
    public WithDrawProfitApply buildUpdate() {
        WithDrawProfitApply update = new WithDrawProfitApply();
        update.setId(delegate.getId());
        update.setRequireQuery(delegate.getRequireQuery());
        update.setExtra(delegate.getExtra());
        update.setStatus(delegate.getStatus());
        return update;
    }

    public AbstractWithdrawApplyDomain requireQuery(boolean require) {
        delegate.setRequireQuery(require);
        return this;
    }

    public boolean requireQuery() {
        return Optional.of(delegate.getRequireQuery()).orElse(false);
    }

    public AbstractWithdrawApplyDomain error(String error) {
        delegate.getExtra().put(WithDrawProfitApply.ERROR, error);
        if (isError()) {
            throw new IllegalStateException(Translate.of("[%s]already at error status", delegate.getId()));
        }
        delegate.setStatus(delegate.getStatus() | WithDrawProfitApply.WithdrawExtraStatus.ERROR.getMaskBit());
        return this;
    }

    /**
     * 获取请求的UUID
     *
     * @return 请求UUID
     */
    public String requestUuid() {
        return delegate.requestUUID();
    }

    public AbstractWithdrawApplyDomain requestUuid(String requestUuid) {
        delegate.requestUUID(requestUuid);
        return this;
    }

    /**
     * 是否被修改过
     *
     * @return 是否被修改过
     */
    public boolean isBeenModified() {
        return delegate.isBeenModified();
    }

    public AbstractWithdrawApplyDomain changePaySerialNo(Long operatorId, String newPaySerialNo, String reason) {
        delegate.changePaySerialNo(operatorId, newPaySerialNo, reason);
        return this;
    }

    public AbstractWithdrawApplyDomain authModify(Long operatorId, boolean accept, String reason) {
        delegate.authModify(operatorId, accept, reason);
        return this;
    }

    public Boolean isModifyAuthed() {
        return delegate.isModifyAuthed();
    }

    public AbstractWithdrawApplyDomain setAuthAt(Date date) {
        delegate.setAuthAt(date);
        return this;
    }

    public Date getAuthAt() {
        return delegate.getAuthAt();
    }

    public boolean isWaiting() {
        return delegate.isWaiting();
    }

    public Boolean isPaid() {
        return delegate.isPaid();
    }

    public boolean isClosed() {
        return delegate.isClosed();
    }

    public boolean isError() {
        return delegate.isError();
    }

    public Boolean isOfflineWithdraw() {
        return delegate.isOfflineWithdraw();
    }

    public AbstractWithdrawApplyDomain setOfflineWithdraw() {
        delegate.setOfflineWithdraw();
        return this;
    }

    public Boolean isOnlineWithdraw() {
        return delegate.isOnlineWithdraw();
    }

    public AbstractWithdrawApplyDomain setOnlineWithdraw() {
        delegate.setOnlineWithdraw();
        return this;
    }

    public AbstractWithdrawApplyDomain bePaid(WithDrawProfitApply.WithdrawPaidType paidType, String paySerialNo) {
        delegate.bePaid(paidType, paySerialNo);
        return this;
    }

    public WithDrawProfitApply.WithdrawPaidType getPaidType() {
        return delegate.getPaidType();
    }

    public boolean isPending() {
        return delegate.isPending();
    }

    public boolean isReject() {
        return delegate.isReject();
    }

    public boolean isAuthed() {
        return delegate.isAuthed();
    }

    public AbstractWithdrawApplyDomain auth() {
        if (!delegate.auth()) {
            throw new IllegalStateException(Translate.of("[%s]incorrect status", delegate.getId()));
        }
        return this;
    }

    public AbstractWithdrawApplyDomain reject() {
        if (!delegate.reject()) {
            throw new IllegalStateException(Translate.of("[%s]incorrect status", delegate.getId()));
        }
        return this;
    }

    public AbstractWithdrawApplyDomain revokeAuth() {
        if (!delegate.revokeAuth()) {
            throw new IllegalStateException(Translate.of("[%s]incorrect status", delegate.getId()));
        }
        return this;
    }

    public AbstractWithdrawApplyDomain initAuth() {
        if (!delegate.initAuth()) {
            throw new IllegalStateException(Translate.of("[%s]incorrect status", delegate.getId()));
        }
        return this;
    }

    private static class AbstractWithdrawApplyDomainImpl extends AbstractWithdrawApplyDomain {
        AbstractWithdrawApplyDomainImpl(WithDrawProfitApply apply) {
            this.delegate = apply;
        }
    }
}
