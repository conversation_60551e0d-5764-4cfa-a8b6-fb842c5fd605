package moonstone.order.model;

import lombok.Data;

import java.util.List;

/**
 * 店铺会员信息
 *
 * @Author: wuxian-yjp
 * @Date: 2019/9/3 13:49
 */
@Data
public class ShopVipInformation {
    String _id;
    Long userId;//
    Long shopId;//
    String nickName;
    String vipName;//会员姓名
    Integer sex;//1 女生 0 男生
    Integer status;
    String birthDate;//
    String city;//
    String cityId;//
    String provinceId;//
    String province;//
    String countyId;//
    String county;//
    String referrerName;//推荐人
    String addDetail;//
    List<SonVipInformation> sonVipInformations;
    Long createAt;
    Long updateAt;
    String phone;
    String babyBirth;

    public String getBabyBirth() {
        if (babyBirth == null && sonVipInformations != null && !sonVipInformations.isEmpty())
            return sonVipInformations.get(0).getBabyDate();
        return this.babyBirth;
    }
}
