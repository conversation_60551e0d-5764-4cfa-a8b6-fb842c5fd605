package moonstone.order.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.EntityBase;
import moonstone.order.enu.WithdrawBy;

import java.util.Map;

/**
 * 提现使用的账户
 *
 * @apiNote extra字段被提现申请复制使用, 所以记住不要把extra搞太长
 * @see moonstone.order.api.BalanceDetailManager#withdraw(long, long, long, WithDrawProfitApply.WithdrawPaidType, WithdrawAccount)
 * @see WithDrawProfitApply#setExtra(Map) WithdrawApply使用了 WithdrawAccount的extra
 * @see WithDrawProfitApply.WithdrawPaidType
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WithdrawAccount extends EntityBase {
    Long userId;
    Long shopId;
    String name;
    String from;
    String account;
    Integer type = WithDrawProfitApply.WithdrawPaidType.ALIPAY.getType();
    public static String I_REAL_NAME = "realName";
    public static String I_ID_NO = "idNo";

    /**
     * 返回真实姓名
     *
     * @return
     */
    public String getRealName() {
        return getExtra().get(I_REAL_NAME);
    }

    /*
     * 设置真实姓名
     */
    public void setRealName(String realName) {
        getExtra().put(I_REAL_NAME, realName);
    }

    /**
     * 返回身份证
     *
     * @return
     */
    public String getIdNo() {
        return getExtra().get(I_ID_NO);
    }

    /**
     * 设置身份证
     *
     * @param idNo
     */
    public void setIdNo(String idNo) {
        getExtra().put(I_ID_NO, idNo);
    }

    /**
     * 获取提现渠道 默认为但丁
     *
     * @return 字符串
     * @see WithdrawBy
     */
    public String getWithdrawWay() {
        return getExtra().getOrDefault(WithdrawBy.INDEX.getName(), WithdrawBy.DanDing.getName());
    }

    /**
     * 设置提现渠道
     *
     * @param withdrawWay 提现渠道
     */
    public void setWithdrawWay(WithdrawBy withdrawWay) {
        getExtra().put(WithdrawBy.INDEX.getName(), withdrawWay.getName());
    }

    public boolean isDefault() {
        return getStatus() != null && (getStatus() & 2) == 2;
    }

    public void setDefault() {
        setDefault(true);
    }

    public void setDefault(boolean ifDefault) {
        if (getStatus() == null) {
            setStatus(1);
        }
        setStatus(ifDefault ? getStatus() | 2 : getStatus() & ~2);
    }
}
