/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 *(子)订单和退款单之间的关联, 这是n:m的关系, 一个(子)订单可以对应多个退款单, 一个退款单也可以对应多个(子)订单
 * 
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-16
 */
@EqualsAndHashCode(of={"refundId", "status"}, callSuper = true)
public class OrderRefund extends OrderRelation implements Serializable {
    private static final long serialVersionUID = 8796216368831017318L;

    /**
     * 主键id
     */
    @Getter
    @Setter
    private Long id;

    /**
     * 退款单id
     */
    @Getter
    @Setter
    private Long refundId;


    /**
     * 状态 0: 待退款, 1: 已退款, -1:已删除,  也可以不使用
     */
    @Getter
    @Setter
    private Integer status;


    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 更新时间
     */
    @Getter
    @Setter
    private Date updatedAt;


}
