/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.utils.JsonMapper;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import moonstone.common.constants.JacksonType;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

/**
 * 各种级别订单的发货单
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-20
 */
@EqualsAndHashCode(of = {"shipmentSerialNo", "shipmentCorpCode", "status"})
public class Shipment implements Serializable {

    private static final long serialVersionUID = -4238510569046303572L;

    private static final TypeReference<Map<String, String>> MAP_OF_LONG = new TypeReference<Map<String, String>>() {
    };

    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();

    /**
     * 主键id
     */
    @Getter
    @Setter
    private Long id;


    /**
     * 状态, 0: 待发货, 1:已发货, 2: 已收货, -1: 已删除, 状态最好同OrderStatus, 如果OrderStatus未定义的状态, 可以扩展
     */
    @Getter
    @Setter
    private Integer status;

    /**
     * 发货来源（1：代塔仓自有，2：京东云交易）
     */
    @Getter
    @Setter
    private Integer sourceType;

    /**
     * 发货单号
     */
    @Getter
    @Setter
    private String shipmentSerialNo;

    /**
     * 物流公司代码
     */
    @Getter
    @Setter
    private String shipmentCorpCode;

    /**
     * 未经编码转换的物流公司编号
     */
    @Getter
    @Setter
    private String originShipmentCorpCode;

    /**
     * 物流公司名称
     */
    @Getter
    @Setter
    private String shipmentCorpName;

    @Getter
    @Setter
    private Date shipmentAt;


    /**
     * 本次发货对应发货单的sku数量, 对于分多批发货的情况, 则本次发货的sku的数量和选定级别订单中sku的数量可能不一致
     * <p>
     * sku相关信息, 包含sku id及数量, 持久化到数据库
     */
    @Getter
    @JsonIgnore
    private String skuInfoJsons;


    /**
     * sku相关信息, 包含sku id及数量, 不持久化到数据库
     */
    @Getter
    private Map<Long, Integer> skuInfos;


    /**
     * 收货方信息, json表示
     */
    @Getter
    @Setter
    private String receiverInfos;

    /**
     * 发货单额外信息,持久化到数据库
     */
    @JsonIgnore
    @Getter
    private String extraJson;

    /**
     * 发货单额外信息,不持久化到数据库
     */
    @Getter
    private Map<String, String> extra;

    /**
     * 发货单tag信息,持久化到数据库
     */
    @JsonIgnore
    @Getter
    private String tagsJson;

    /**
     * 发货单tag信息,不持久化到数据库
     */
    @Getter
    private Map<String, String> tags;

    /**
     * 确认收货时间
     */
    @Getter
    @Setter
    private Date confirmAt;

    /**
     * 发货单创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 发货单更新时间
     */
    @Getter
    @Setter
    private Date updatedAt;

    public void setSkuInfoJsons(String skuInfoJsons) throws Exception {
        this.skuInfoJsons = skuInfoJsons;
        if (Strings.isNullOrEmpty(tagsJson)) {
            this.tags = Collections.emptyMap();
        } else {
            this.tags = objectMapper.readValue(tagsJson, MAP_OF_LONG);
        }
    }

    public void setSkuInfos(Map<Long, Integer> skuInfos) {
        this.skuInfos = skuInfos;
        if (skuInfos == null || skuInfos.isEmpty()) {
            this.skuInfos = null;
        } else {
            try {
                this.skuInfoJsons = objectMapper.writeValueAsString(skuInfos);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }


    public void setExtraJson(String extraJson) throws Exception {
        this.extraJson = extraJson;
        if (Strings.isNullOrEmpty(extraJson)) {
            this.extra = Collections.emptyMap();
        } else {
            this.extra = objectMapper.readValue(extraJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        if (extra == null || extra.isEmpty()) {
            this.extraJson = null;
        } else {
            try {
                this.extraJson = objectMapper.writeValueAsString(extra);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }

    public void setTagsJson(String tagsJson) throws Exception {
        this.tagsJson = tagsJson;
        if (Strings.isNullOrEmpty(tagsJson)) {
            this.tags = Collections.emptyMap();
        } else {
            this.tags = objectMapper.readValue(tagsJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
        if (tags == null || tags.isEmpty()) {
            this.tagsJson = null;
        } else {
            try {
                this.tagsJson = objectMapper.writeValueAsString(tags);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }
}
