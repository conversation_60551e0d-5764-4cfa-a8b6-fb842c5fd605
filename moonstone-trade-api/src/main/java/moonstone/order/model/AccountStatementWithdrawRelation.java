package moonstone.order.model;

import lombok.Data;
import moonstone.common.model.BaseEntity;

import java.util.Date;

/**
 * 佣金账单与提现申请表的关系表
 */
@Data
public class AccountStatementWithdrawRelation extends BaseEntity {

    /**
     * '主键'
     */
    private Long id;

    /**
     * '商家平台id'
     */
    private Long shopId;

    /**
     * 'parana_account_statement表主键, 账单id'
     */
    private Long accountStatementId;

    /**
     * 'parana_with_draw_profit_apply表主键, 提现申请单id'
     */
    private Long withdrawApplyId;

    /**
     * '提现单状态(1-待审核, 2-待付款, 3-审核驳回, 4-已支付, 5-支付失败)'
     *
     * @see moonstone.common.enums.AccountStatementWithdrawStatusEnum
     */
    private Integer withdrawApplyStatus;
}
