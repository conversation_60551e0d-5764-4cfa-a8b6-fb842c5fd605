package moonstone.order.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/9/3 14:56
 */
@Data
@EqualsAndHashCode(of = {"babyName", "babySex", "birthId", "birthImgUrl", "isAuthed"})
public class SonVipInformation {
    String babyName;//
    String babySex;//
    String babyDate;//
    String birthId; // 出生证明编号
    String birthImgUrl; // 出生证明的图片地址
    String reason;// 拒绝理由 (vo层属性,来源于其他数据源)
    Boolean isAuthed;// 是否审核通过(vo层属性,来源于其他数据源)
    Long id;// 数据源Id(vo层属性,来源于其他数据源)
    Long refererId;// 在哪个门店里添加的
}
