package moonstone.order.model;

import lombok.Data;
import moonstone.common.model.BaseEntity;

@Data
public class WithdrawPaymentReceiver extends BaseEntity {

    /**
     * '主键'
     */
    private Long id;

    /**
     * '商家平台id'
     */
    private Long shopId;

    /**
     * 'parana_withdraw_payment表主键，提现支付单id'
     */
    private Long withdrawPaymentId;

    /**
     * '收款方的用户id'
     */
    private Long userId;

    /**
     * '收款方账户类型'
     *
     * @see moonstone.order.model.WithDrawProfitApply.WithdrawPaidType
     */
    private Integer accountType;

    /**
     * '收款方账号'
     */
    private String accountNo;

    /**
     * '收款方户名'
     */
    private String accountUserName;

    /**
     * '收款方账户的发卡银行(供对公银行账户使用)'
     */
    private String issuingBank;
}
