/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.utils.JsonMapper;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import moonstone.common.constants.JacksonType;
import moonstone.common.enums.OrderOutFrom;

import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

/**
 * 订单基类
 */
@EqualsAndHashCode(of = {"shopId", "buyerId", "fee", "status"})
public abstract class OrderBase implements Serializable {
    protected static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();
    private static final long serialVersionUID = 2046708647265466777L;
    /**
     * 订单额外信息,持久化到数据库
     */
    @JsonIgnore
    @Getter
    protected String extraJson;
    /**
     * 订单额外信息,不持久化到数据库
     */
    @Getter
    protected Map<String, String> extra;
    /**
     * 订单tag信息,持久化到数据库
     */
    @JsonIgnore
    @Getter
    protected String tagsJson;
    /**
     * 订单tag信息,不持久化到数据库
     */
    @Getter
    protected Map<String, String> tags;
    Integer outFromType;
    /**
     * 主键id
     */
    @Getter
    @Setter
    private Long id;
    /**
     * 店铺id
     */
    @Getter
    @Setter
    private Long shopId;
    /**
     * 买家id
     */
    @Getter
    @Setter
    private Long buyerId;
    /**
     * 买家名称
     */
    @Getter
    private String buyerName_;
    @Getter
    private String buyerName;
    /**
     * 实付价
     */
    @Getter
    @Setter
    private Long fee;
    /**
     * 状态
     */
    @Getter
    @Setter
    private Integer status;
    /**
     * 使用的优惠活动id
     */
    @Getter
    @Setter
    private Long promotionId;
    /**
     * 外部订单来源
     */
    @Getter
    @Setter
    private String outFrom;
    /**
     * 订单创建时间
     */
    @Getter
    @Setter
    private Date createdAt;
    /**
     * 订单更新时间
     */
    @Getter
    @Setter
    private Date updatedAt;


    public void setExtraJson(String extraJson) throws Exception {
        this.extraJson = extraJson;
        if (Strings.isNullOrEmpty(extraJson)) {
            this.extra = Collections.emptyMap();
        } else {
            this.extra = objectMapper.readValue(extraJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        if (extra == null || extra.isEmpty()) {
            this.extraJson = null;
        } else {
            try {
                this.extraJson = objectMapper.writeValueAsString(extra);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }

    public void setTagsJson(String tagsJson) throws Exception {
        this.tagsJson = tagsJson;
        if (Strings.isNullOrEmpty(tagsJson)) {
            this.tags = Collections.emptyMap();
        } else {
            this.tags = objectMapper.readValue(tagsJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
        if (tags == null || tags.isEmpty()) {
            this.tagsJson = null;
        } else {
            try {
                this.tagsJson = objectMapper.writeValueAsString(tags);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }

    public abstract String getOutShopId();

    public void setBuyerName_(String buyerName_) {
        this.buyerName_ = buyerName_;
        if (buyerName_ != null) {
            try {
                buyerName = new String(Base64.getDecoder().decode(buyerName_.getBytes(StandardCharsets.UTF_8)));
            } catch (Exception e) {
                buyerName = buyerName_;
            }
        } else {
            buyerName = null;
        }
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
        if (buyerName != null) {
            buyerName_ = Base64.getEncoder().encodeToString(buyerName.getBytes(StandardCharsets.UTF_8));
        } else {
            buyerName_ = null;
        }
    }

    public Integer getOutFromType() {
        if (outFromType != null) {
            return outFromType;
        }
        if (outFrom != null) {
            for (OrderOutFrom value : OrderOutFrom.values()) {
                if (value.Code() == null) continue;
                if (value.Code().equals(outFrom)) {
                    this.outFromType = value.Id();
                    break;
                }
            }
            return outFromType;
        }
        return null;
    }

    public void setOutFromType(Integer outFromType) {
        if (outFromType == null) {
            return;
        }
        this.outFromType = outFromType;
        try {
            outFrom = OrderOutFrom.fromInt(outFromType).Code();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
