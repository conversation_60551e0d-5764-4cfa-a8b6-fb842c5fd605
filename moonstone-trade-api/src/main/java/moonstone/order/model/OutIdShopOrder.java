package moonstone.order.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.utils.JsonMapper;
import lombok.Data;
import moonstone.common.constants.JacksonType;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

@Data
public class OutIdShopOrder implements Serializable {
    private static final long serialVersionUID = -3108087639454673072L;

    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();
    /**
     * 主键
     */
    private Long id;
    /**
     * 商铺id
     */
    private Long shopId;
    /**
     * 店铺级订单Id
     */
    private Long shopOrderId;

    /**
     * 外部订单Id
     */
    private String outId;

    /**
     * 放扩展信息, 建议json字符串, 存数据库
     */
    @JsonIgnore
    private String extraJson;

    /**
     * 放扩展信息,不存数据库
     */
    private Map<String, String> extra;

    private Date createdAt;

    private Date updatedAt;

    public OutIdShopOrder() {
    }

    public OutIdShopOrder(Long shopId, Long shopOrderId, String outId, Map<String, String> extra) {
        this.shopId = shopId;
        this.shopOrderId = shopOrderId;
        this.outId = outId;
        this.extra = extra;
    }

    public void setExtraJson(String extraJson) throws Exception {
        this.extraJson = extraJson;
        if (Strings.isNullOrEmpty(extraJson)) {
            this.extra = Collections.emptyMap();
        } else {
            this.extra = objectMapper.readValue(extraJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        if (extra == null || extra.isEmpty()) {
            this.extraJson = null;
        } else {
            try {
                this.extraJson = objectMapper.writeValueAsString(extra);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }
}
