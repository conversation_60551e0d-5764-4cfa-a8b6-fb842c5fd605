/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 订单和其他实体的关联基类
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-24
 */
@EqualsAndHashCode(of={"orderId","orderType"})
public abstract class OrderRelation implements Serializable {
    private static final long serialVersionUID = 8983794903643663098L;

    /**
     * 订单id,  也可以是子订单级别的, 由orderType决定
     */
    @Getter
    @Setter
    protected Long orderId;

    /**
     * 本次支付针对的订单类型, 1: 店铺订单支付, 2: 子订单支付
     * 各应用自己选择支付单对应的级别, 但是一个应用最好只用一个级别
     */
    @Getter
    protected Integer orderType;

    /**
     * 不存数据库, 对应订单类型的枚举值
     */
    @JsonIgnore
    protected OrderLevel orderLevel;

    public OrderLevel getOrderLevel() {
        if (orderLevel == null) return OrderLevel.fromInt(orderType);
        return orderLevel;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
        this.orderLevel = OrderLevel.fromInt(orderType);
    }

    public void setOrderLevel(OrderLevel orderLevel) {
        this.orderLevel = orderLevel;
        this.orderType = orderLevel.getValue();
    }
}
