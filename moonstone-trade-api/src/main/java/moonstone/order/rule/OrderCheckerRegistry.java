/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.rule;

import moonstone.order.api.OrderChecker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-04-26
 */
@Component
public class OrderCheckerRegistry {
    private final SubmitOrderCheckerPipeline orderCheckerPipeline;

    private final PreOrderCheckerPipeline preOrderCheckPipeline;

    @Autowired
    public OrderCheckerRegistry(SubmitOrderCheckerPipeline orderCheckerPipeline,
                                PreOrderCheckerPipeline preOrderCheckPipeline) {
        this.orderCheckerPipeline = orderCheckerPipeline;
        this.preOrderCheckPipeline = preOrderCheckPipeline;
    }

    /**
     * 提交订单注册规则
     *
     * @param orderChecker 要注册的规则
     */
    public void submitOrderRegister(OrderChecker orderChecker) {
        orderCheckerPipeline.add(orderChecker);
    }

    /**
     * 下单预览注册规则
     * @param orderChecker 要注册的规则
     */
    public void preOrderRegister(OrderChecker orderChecker) {
        preOrderCheckPipeline.add(orderChecker);
    }
}
