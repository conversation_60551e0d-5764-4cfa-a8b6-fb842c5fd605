package moonstone.order.component;

import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.regex.Pattern.compile;

/**
 * <AUTHOR>
 */
public class PaymentAccountUtil {

    static private final Pattern SELLER_EMAIL = compile("seller_email=(.*?)&");
    static private final Pattern MCH_ID = compile("mch_id=(.*?)&");
    static private final Pattern SELLER_ID = compile("seller_id=(.*?)&");

    /**
     * 判断是否是自己的收款帐号
     *
     * @param payRequest 支付原请求
     * @return 是否
     */
    public static boolean isOwnedWeChatPayment(String payRequest) {
        return genRecpAccount(payRequest).filter(account -> Objects.equals(account, "**********")).isPresent();
    }

    public static Optional<String> genRecpAccount(String payRequest) {
        if (payRequest == null || payRequest.isEmpty()) {
            return Optional.empty();
        }
        Matcher matcher;
        if (payRequest.contains("seller_email")) {
            /// 支付宝支付的 帐号格式
            matcher = SELLER_EMAIL.matcher(payRequest);
        } else if (payRequest.contains("mch_id")) {
            /// 微信支付的 帐号格式
            matcher = MCH_ID.matcher(payRequest);
        } else if (payRequest.contains("seller_id")) {
            /// 支付宝的pid 帐号格式
            matcher = SELLER_ID.matcher(payRequest);
        } else {
            return Optional.empty();
        }
        if (matcher.find()) {
            return Optional.ofNullable(matcher.group(1));
        } else {
            return Optional.empty();
        }
    }
}
