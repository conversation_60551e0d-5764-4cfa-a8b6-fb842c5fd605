/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.component;

import moonstone.order.api.OrderCheckersConfigurer;
import moonstone.order.rule.*;

import javax.annotation.Resource;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-29
 */
public class DefaultOrderCheckersConfigurer extends OrderCheckersConfigurer {

    @Resource
    private SubStoreOnlyChecker subStoreOnlyChecker;

    @Resource
    private ReceiverInfoChecker receiverInfoChecker;

    /**
     * 配置规则
     *
     */
    @Override
    public void configureOrderCheckers() {
        //提交订单规则注册
        orderCheckerRegistry.submitOrderRegister(new StatusChecker());
        orderCheckerRegistry.submitOrderRegister(new StockChecker());
        orderCheckerRegistry.submitOrderRegister(receiverInfoChecker);
        orderCheckerRegistry.submitOrderRegister(subStoreOnlyChecker);

        orderCheckerRegistry.preOrderRegister(new StatusChecker());
        orderCheckerRegistry.preOrderRegister(new PreOrderStockChecker());
    }
}
