package moonstone.order.service;

import moonstone.common.model.Either;
import moonstone.order.dto.RichGatherOrder;
import moonstone.order.model.GatherOrder;

import java.util.List;

public interface GatherOrderWriteService {
    /**
     * 创建采购单
     *
     * @param gatherOrder 采购单
     * @return 采购单号
     */
    Either<Long> create(RichGatherOrder gatherOrder);

    /**
     * 创建采购单
     *
     * @param gatherOrderList 采购单
     * @return 采购单号
     */
    Either<List<Long>> create(List<RichGatherOrder> gatherOrderList);

    /**
     * 订单支付
     *
     * @param gatherOrder 采购单
     */
    Either<Boolean> paid(GatherOrder gatherOrder);

    /**
     * 修改采购单状态
     * 简单乐观锁
     *
     * @param gatherOrderId 采购单的Id
     * @param currentStatus 采购单目前状态
     * @param targetStatus  采购单目标状态
     * @return 是否成功修改
     */
    Either<Boolean> updateStatus(Long gatherOrderId, Integer currentStatus, Integer targetStatus);

    /**
     * 审核拒绝
     *
     * @param gatherOrderIdList 采购单Id列表
     * @return 采购单
     */
    Either<Boolean> reject(List<Long> gatherOrderIdList);

    /**
     * 审核通过
     *
     * @param gatherOrderIdList 采购单Id列表
     * @return 采购单
     */
    Either<Boolean> auth(List<Long> gatherOrderIdList);

    /**
     * 对某个采购单进行退款
     *
     * @param gatherOrder 采购单
     * @return 退款成功
     */
    Either<Boolean> refund(GatherOrder gatherOrder);
}
