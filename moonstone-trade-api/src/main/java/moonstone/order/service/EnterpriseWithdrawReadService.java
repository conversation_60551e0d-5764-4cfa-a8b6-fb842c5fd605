package moonstone.order.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.order.dto.EnterpriseWithdrawCriteria;
import moonstone.order.model.EnterpriseWithdraw;

public interface EnterpriseWithdrawReadService {

    Response<Paging<EnterpriseWithdraw>> paging(EnterpriseWithdrawCriteria criteria);

    Response<EnterpriseWithdraw> findById(Long enterpriseWithdrawId);
}
