/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.service;

import io.terminus.common.model.Response;
import moonstone.common.model.Either;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.Payment;

import java.util.Date;
import java.util.List;

/**
 * 支付单写服务
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-11
 */
public interface PaymentWriteService {

    /**
     * 创建支付单, 同时也会创建相应的订单和支付单的关联关系
     *
     * @param payment    支付单信息
     * @param orderIds   关联的(子)订单id列表
     * @param orderLevel 订单对应的级别
     * @return 支付单id
     */
    Response<Long> create(Payment payment, List<Long> orderIds, OrderLevel orderLevel);


    /**
     * 更新支付单的支付流水号和状态
     *
     * @param paymentId   支付单id
     * @param paySerialNo 支付流水号
     * @param paidAt      支付成功时间
     * @param status      支付成功的状态
     * @return 是否更新成功
     */
    Response<Boolean> payCallback(Long paymentId, String paySerialNo, Date paidAt, Integer status, String payResponse);


    /**
     * 更新支付单的状态, 同时也会更新和订单关联表记录的状态
     *
     * @param paymentId 支付单id
     * @param status    状态
     * @return 是否更新成功
     */
    Response<Boolean> updateStatus(Long paymentId, Integer status);

    /**
     * 更新支付单的推送状态
     *
     * @param paymentId  支付单id
     * @param pushStatus 推送状态
     * @return 是否更新成功
     */
    Response<Boolean> updatePushStatus(Long paymentId, PaymentPushStatus pushStatus);

    /**
     * 物理删除支付单
     *
     * @param paymentId 支付单id
     * @return 是否删除成功
     */
    Response<Boolean> delete(Long paymentId);

    Response<Boolean> update(Payment payment);

    /**
     * 通联支付（收银宝、云商通）：更新其使用的商户号
     *
     * @param outId
     * @param cusId
     * @return
     */
    Response<Boolean> updateAllInPayCusId(String outId, String cusId);

    Response<Boolean> pushedWaitCallback(Long paymentId);
}
