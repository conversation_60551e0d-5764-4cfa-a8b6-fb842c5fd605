package moonstone.order.service;

import moonstone.common.model.Either;
import moonstone.order.model.HonestFanSum;

import java.util.List;
import java.util.Optional;

public interface HonestFanDataReadService {
    /**
     * 由用户Id查找数据
     *
     * @param userId 忠粉数据的id
     * @param shopId 店铺的Id
     * @return
     */
    Either<Optional<HonestFanSum>> findByUserId(long userId, long shopId);

    /**
     * 检测用户是不是已经是忠粉了
     *
     * @param userId 用户Id
     * @param shopId 店铺Id
     * @return
     */
    Either<Boolean> isAFan(long userId, long shopId);

    /**
     * 由代理id查找所有的忠粉数据
     *
     * @param proxyId 代理人的id
     * @param shopId  店铺的id
     * @return
     */
    Either<List<HonestFanSum>> findByProxyId(long proxyId, long shopId);

    /**
     * 统计这个代理人的粉丝数量
     *
     * @param proxyId 代理人的id
     * @param shopId  店铺id
     * @return
     */
    Either<Long> countByProxyId(Long proxyId, Long shopId);

    Either<List<HonestFanSum>> listByShopId(long shopId);
}
