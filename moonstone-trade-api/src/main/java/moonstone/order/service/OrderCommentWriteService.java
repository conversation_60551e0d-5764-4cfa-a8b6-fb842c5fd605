package moonstone.order.service;


import io.terminus.common.model.Response;
import moonstone.order.model.OrderComment;

import java.util.List;

/**
 * Author:cp, yzf
 * Created on 4/22/16.
 */
public interface OrderCommentWriteService {

    /**
     * 添加订单评论
     *
     * @param orderComment 评论
     * @return 评论id
     */
    Response<Long> create(OrderComment orderComment);

    /**
     * 批量添加评论
     *
     * @param orderComments 评论
     * @return 评论id列表
     */
    Response<List<Long>> batchCreate(List<OrderComment> orderComments);

    /**
     * 评论逻辑删除
     *
     * @param id 评论id
     * @return 是否更新成功
     */
    Response<Boolean> updateStatus(Long id, Integer status);
}
