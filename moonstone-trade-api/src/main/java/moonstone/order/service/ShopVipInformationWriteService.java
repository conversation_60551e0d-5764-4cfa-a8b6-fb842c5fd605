package moonstone.order.service;

import moonstone.common.model.Either;
import moonstone.order.model.ShopVipInformation;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/9/3 14:07
 */
public interface ShopVipInformationWriteService {
    Either<Boolean> create(ShopVipInformation shopVipInformation);

    Either<Boolean> update(ShopVipInformation shopVipInformation);

    Either<Boolean> updateById(ShopVipInformation shopVipInformation);

    Either<Boolean> deleteById(String id);
}
