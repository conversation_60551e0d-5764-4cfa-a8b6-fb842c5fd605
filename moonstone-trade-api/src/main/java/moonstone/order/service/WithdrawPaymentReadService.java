package moonstone.order.service;

import io.terminus.common.model.Response;
import moonstone.order.model.WithdrawPayment;

import java.util.Date;
import java.util.List;

public interface WithdrawPaymentReadService {

    Response<WithdrawPayment> findById(Long id);

    Response<List<WithdrawPayment>> findByWithdrawApplyId(Long withdrawApplyId);

    Response<List<WithdrawPayment>> findPaying(Date createdAtEnd, Integer pageNo, Integer pageSize);
}
