package moonstone.order.api;

import java.util.Date;

/**
 * 交易批次号生成器
 * Created by cp on 6/11/17.
 */
public interface TradeBatchNoGenerator {

    int UMF_SUFFIX_LENGTH = 16;

    /**
     * 生成交易批次号
     *
     * @param happenedAt 交易发生时间
     * @param tradeId    支付单id或者退款单id
     * @return 批次号
     */
    String generateBatchNo(Date happenedAt, Long tradeId);

    String generateBatchNo(Date happenedAt, Long tradeId, int suffixLength);

}
