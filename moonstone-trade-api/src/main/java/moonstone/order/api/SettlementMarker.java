package moonstone.order.api;

import moonstone.order.model.Payment;
import moonstone.order.model.Refund;

/**
 * 判断订单的状态,比如是否需要进入订单结算,是否进入账务明细
 * Created by ya<PERSON><PERSON><PERSON> on 2016/11/11
 */
public interface SettlementMarker {

    /**
     * 判断支付单状态是否需要进入账务明细
     *
     * @param payment 支付单
     */
    boolean isPaymentNeedSettle(Payment payment);

    /**
     * 判断支付单状态是否需要进入订单结算
     *
     * @param payment 支付单
     */
    boolean isPaymentNeedOrderSettle(Payment payment);

    /**
     * 判断退款单是否需要进入账务明细
     *
     * @param refund 退款单
     */
    boolean isRefundNeedSettle(Refund refund);

    /**
     * 判断退款单是否需要进入订单结算
     *
     * @param refund 退款单
     */
    boolean isRefundNeedOrderSettle(Refund refund);
}
