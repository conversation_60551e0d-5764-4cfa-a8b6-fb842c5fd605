package moonstone.order.api.payoperation.fundstransfer;

import lombok.Data;
import moonstone.order.api.payoperation.BaseCallbackResult;

@Data
public class FundsTransferCallbackResult extends BaseCallbackResult {

    /**
     * parana_funds_transfer_payment 的 id
     */
    private Long fundsTransferPaymentId;

    public FundsTransferCallbackResult(String rawCallbackJson, boolean paySuccess, String tradeNo, Long fundsTransferPaymentId) {
        super(rawCallbackJson, paySuccess, tradeNo);
        this.fundsTransferPaymentId = fundsTransferPaymentId;
    }
}
