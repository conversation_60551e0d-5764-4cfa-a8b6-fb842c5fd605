/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.api;

import com.google.common.collect.ListMultimap;
import moonstone.order.dto.PersistedOrderInfosByOrderStruct;
import moonstone.order.dto.RichOrder;
import moonstone.order.model.OrderInvoice;
import moonstone.order.model.OrderReceiverInfo;

/**
 * <AUTHOR>
 * 订单组装器
 */
public abstract class AbstractPersistedOrderMaker {

    /**
     * 从richOrder构建出需要持久化的店铺订单, sku订单, 收货信息, 发票信息等实体来
     *
     * @param richOrder 输入的richOrder信息
     * @return 需要被持久化的对象列表
     */
    public PersistedOrderInfosByOrderStruct make(RichOrder richOrder) {
        /// 拆单逻辑打包
        PersistedOrderInfosByOrderStruct persistedOrderInfos = new PersistedOrderInfosByOrderStruct();
        packPersistedOrderInfosByOrderStruct(persistedOrderInfos, richOrder);
        return persistedOrderInfos;
    }


    /**
     * ## 从richOrder中打包出需要持久化的店铺订单结构
     *
     * @param persistedOrderInfosByOrderStruct 被打包的持久化结构
     * @param richOrder                        订单数据来源
     */
    protected abstract void packPersistedOrderInfosByOrderStruct(PersistedOrderInfosByOrderStruct persistedOrderInfosByOrderStruct, RichOrder richOrder);

    /**
     * 从richOrder构建出需要持久化的收货信息
     *
     * @param richOrder 输入的richOrder信息
     * @return 收货信息列表, 以店铺id进行归组, 如果为空或者empty map, 则表示没有收货信息(全为虚拟物品)
     * <p>
     * 如果有某个(子)订单没有收货地址, 则可能该订单为虚拟订单, 那么也会生成对应的OrderReceiverInfo, 只是对应的receiverInfo字段为空
     */
    protected abstract ListMultimap<Long, OrderReceiverInfo> retrieveReceiverInfos(RichOrder richOrder);

    /**
     * 从richOrder构建出需要持久化的发票信息
     *
     * @param richOrder 输入的richOrder信息 , 则表示没有发票信息
     * @return 发票列表, 以店铺id进行归组, 如果为空或者empty map, 则表示没有发票信息
     * 如果有某个(子)订单没有收货地址, 则可能该订单可能不需要发票, 那么也会生成对应的OrderInvoice, 只是对应的invoice字段为空
     */
    protected abstract ListMultimap<Long, OrderInvoice> retrieveOrderInvoices(RichOrder richOrder);
}
