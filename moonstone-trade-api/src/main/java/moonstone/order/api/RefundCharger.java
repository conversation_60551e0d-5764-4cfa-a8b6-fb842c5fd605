package moonstone.order.api;

import moonstone.order.model.Payment;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;

import java.util.List;

/**
 * Desc: 退款金额
 * Mail: <EMAIL>
 * Data: 16/7/11
 * Author: yangzefeng
 */
public interface RefundCharger {

    /**
     * 计算退款金额, 只能一个总订单退款,或者一个总订单下面的多个子订单一起退款 未计算营销   (不可当作退款金额使用)
     *
     * @param payment   支付单
     * @param shopOrder 总订单
     * @param skuOrders 子订单
     * @return 退款金额
     */
    List<Long> charge(Payment payment, ShopOrder shopOrder, List<SkuOrder> skuOrders);

    /**
     * 计算退款金额, 可以多个总订单退款,或者一个总订单下面的多个子订单一起退款(营销计算不准确) 因此不推荐作为退款金额使用
     * 两订单金额会叠加因此不推荐复合使用
     *
     * @param payment    支付单
     * @param shopOrders 主订单列表
     * @param skuOrders  子订单列表
     * @return 退款金额
     */
    List<Long> charge(Payment payment, List<ShopOrder> shopOrders, List<SkuOrder> skuOrders);
}
