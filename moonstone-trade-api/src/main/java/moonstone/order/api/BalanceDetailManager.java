package moonstone.order.api;

import io.terminus.common.model.Response;
import lombok.Data;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.Either;
import moonstone.order.dto.InComeDetail;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawAccount;
import moonstone.user.model.User;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;


public interface BalanceDetailManager {
    Response<List<InComeDetail>> getCash(Long userId, Long sourceId);

    /// withDraw 提现
    /// 创建申请并且修改明细
    /// *返回* 创建的提现申请Id
    /// *抛出* 运行态错误用于事务处理
    Either<Long> withdraw(long userId, long cash, long shopId, WithDrawProfitApply.WithdrawPaidType type, WithdrawAccount account);

    Either<Long> withdraw(long userId, long profitBelongUserId, Integer userRole, long cash, long shopId,
                          List<Long> accountStatementIdList, WithDrawProfitApply.WithdrawPaidType type, WithdrawAccount account);

    /**
     * 修改利润数据
     *
     * @param balanceDetail 利润详情
     * @return 修改成功
     */
    Either<Long> changeRealProfit(@NotNull BalanceDetail balanceDetail);

    //Response<Long> persistProfitChange(@NotNull BalanceDetail balanceDetail) throws RuntimeException;

    void payWithdraw(long supplierId, User applier, WithDrawProfitApply withDrawProfitApply);

    /// 拒绝提现
    boolean rejectWithDraw(Long operatorId, WithDrawProfitApply apply, String reason);

    /**
     * 争取待收益利润,会写入数据库有IO动作
     *
     * @param shopOrder    订单
     * @param orderOutFrom 订单来源
     * @return 返回赚取的利润
     */
    List<BalanceDetail> earnForeseeProfit(ShopOrder shopOrder, OrderOutFrom orderOutFrom);

    /**
     * 仅仅计算利润但不进行数据存储
     *
     * @param shopOrder 订单信息
     * @param outFrom   订单来源
     * @param present   利润是否可使用状态(如果是下单则为false,确认收货为true)
     */
    List<BalanceDetail> calculateProfit(ShopOrder shopOrder, OrderOutFrom outFrom, boolean present);

    /**
     * 转换该订单产生的待收益为可用收益
     * 持久化已有的利润
     *
     * @param order 主订单信息
     */
    List<BalanceDetail> persistForeseeProfit(ShopOrder order);

    /**
     * 筛选出未存在的利润
     *
     * @param profitDetail 利润
     * @return 是否未存在
     */
    boolean expectProfitNotExists(BalanceDetail profitDetail);

    /**
     * 初始化收益钱包
     *
     * @param userId   用户Id
     * @param sourceId 主体Id
     * @return 初始化后的数据Id
     */
    List<InComeDetail> initCash(long userId, long sourceId);

    /**
     * 为其打上可忽略标志
     * 维护性让其母上天
     *
     * @param inComeDetail 需要被打上可忽略的标记
     */
    void markWithIgnoreAble(InComeDetail inComeDetail);

    /**
     * 获取已赚取的
     *
     * @param userId 用户Id
     * @param shopId 平台Id
     * @return 已有的利润
     */
    Either<BalanceDetail> getEarned(Long userId, Long shopId);

    List<BalanceDetail> persistForeseeProfitWithLock(ShopOrder order);


    @Data
    class ProfitDataVO {
        private long count;
        private long paymentSum;
        private long settledProfit;
        private long unsettledProfit;
    }

    /**
     * 筛选用户的收益数据打包为收益驱动的汇总数据.
     * 其中订单数据仅与成功下单的有关,与退款无关
     * 依赖ShopOrderReadService.findByIds 与 ShopOrder.getFee
     *
     * @param userId         用户Id
     * @param sourceId       小程序(平台) 辨识实体主键,目前用shopId判定,当公众平台上线后全体使用projectId
     * @param betweenStartAt 从某天开始
     * @param betweenEndAt   到某天接受
     * @param exceptStatus   排除的收益`status`
     * @param includeStatus  必须包裹的收益`status`
     * @return 打包完毕的VO层数据
     */
    Optional<ProfitDataVO> queryProfitList(long userId, long sourceId, Date betweenStartAt, Date betweenEndAt, List<Integer> exceptStatus, List<Integer> includeStatus);

    Response<List<InComeDetail>> getCashByStoreProxy(Long userId, Long sourceId);

    /**
     * 生成记录统计数据变化的记录函数
     *
     * @param shopOrder 引发的订单
     * @return 记录函数
     */
    Consumer<BalanceDetail> generateRecordExecutor(ShopOrder shopOrder);

    /**
     * 发送提现失败信息
     *
     * @param apply  提现申请
     * @param reason 原因
     */
    void sendWithdrawFailEvent(WithDrawProfitApply apply, String reason);

    /**
     * 发送体现成功信息
     *
     * @param apply 提现申请
     */
    void sendWithdrawDoneEvent(WithDrawProfitApply apply);

    /**
     * 指定用户的佣金利润是否已完成结算
     * <br/>适用于门店和服务商
     *
     * @param shopId
     * @param userId
     * @return
     */
    boolean isProfitClean(Long shopId, Long userId);

    /**
     * 判断导购的佣金是否已完成结算 <br/>
     * （parana_shop_order -> parana_balance_detail -> profit_withdraw_record -> parana_with_draw_profit_apply）
     *
     * @param shopId
     * @param guiderUserId
     * @return
     */
    boolean isGuiderProfitClean(Long shopId, Long guiderUserId);
}
