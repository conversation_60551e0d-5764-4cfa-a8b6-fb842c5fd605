/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import moonstone.common.model.CommonUser;

import java.io.Serializable;

/**
 * 提交订单请求参数（社区运营）
 */
@Data
public class CommunitySubmittedOrder extends SubmittedOrder implements Serializable {

    /**
     * 订购人id
     */
    Long buyerId;

    @ApiModelProperty(value = "订单活动来源")
    protected Integer orderSource;

    @ApiModelProperty(value = "独立库存ID")
    private Long inventoryId;

    @ApiModelProperty(value = "团ID")
    private Long groupId;

    @ApiModelProperty(value = "活动ID")
    private Integer activityId;
}
