package moonstone.order.dto;

import lombok.Data;
import moonstone.common.model.PagingCriteria;

import java.io.Serial;
import java.util.Date;
import java.util.List;

@Data
public class PaymentForEnterpriseWithdrawCriteria extends PagingCriteria {
    @Serial
    private static final long serialVersionUID = -712136343487200750L;

    private Long shopId;

    /**
     * 支付渠道
     */
    private String payChannel;

    private List<Long> paymentIdList;

    /**
     * 主订单的订单金额区间(单位：分)
     */
    private Long shopOrderFeeFrom;
    private Long shopOrderFeeTo;

    /**
     * 订单下单时间区间
     */
    private Date shopOrderCreatedAtStart;
    private Date shopOrderCreatedAtEnd;

    /**
     * 订单发货时间区间
     */
    private Date shipmentAtStart;
    private Date shipmentAtEnd;

    /**
     * 订单确认收货时间区间
     */
    private Date orderConfirmAtStart;
    private Date orderConfirmAtEnd;
}
