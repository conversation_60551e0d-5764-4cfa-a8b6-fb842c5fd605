package moonstone.order.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PushOrderItem implements Serializable {
    private static final long serialVersionUID = -8002348899993364290L;
    /**
     * 商品名称 不必须
     */
    private String goodsName;

    /**
     * Sku编码（洋800 SKU编码）    必须
     */
    private String skuCode;

    /**
     * 外部商品编码（自己平台skuId）    必须
     */
    private String outCode;

    /**
     * 商品项数（大于0的整数） 不必须
     */
    private int num;

    /**
     * 所属仓库编码 （暂时不指定）   不必须
     */
    private String depotSn;

    /**
     * 实际金额
     */
    private BigDecimal price;

    /**
     * 税费金额
     */
    private BigDecimal tax;

    /**
     * 折扣/优惠
     */
    private BigDecimal discount;

    /**
     * sku级别运费 2018-08-10增加
     */
    private BigDecimal shipFee;
}
