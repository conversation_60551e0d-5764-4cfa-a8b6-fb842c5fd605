package moonstone.order.dto;

import lombok.Data;
import moonstone.order.dto.fsm.OrderOperation;
import moonstone.order.model.*;
import moonstone.order.vo.PayInfoVO;
import moonstone.promotion.model.Promotion;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Desc: 订单详情页展示信息
 * Mail: <EMAIL>
 * Data: 16/6/16
 * Author: yangzefeng
 */
@Data
public class OrderDetail implements Serializable {
    private static final long serialVersionUID = 1920296881601118563L;

    /**
     * 店铺订单
     */
    private ShopOrder shopOrder;

    /**
     * sku订单
     */
    private List<SkuOrder> skuOrders;

    /**
     * 订单总税费
     */
    private Long totalTax;

    /**
     * 订单支付信息
     */
    private Payment payment;

    /**
     * 平台级营销
     */
    private Promotion promotion;

    /**
     * sku订单总优惠
     */
    private Integer totalDiscountOfSkuOrders;

    /**
     * 总优惠:主订单(包括运费优惠)和子订单优惠之和,不包括平台优惠
     */
    private Integer discount;

    /**
     * 订单级别的操作
     */
    private Set<OrderOperation> shopOrderOperations;

    /**
     * 用户收货地址信息
     */
    private List<OrderReceiverInfo> orderReceiverInfos;

    /**
     * 发票信息
     */
    private List<Invoice> invoices;

    /**
     * 发货方式,0:尚未发货,1:主单发货,2:子单发货
     */
    private Integer shipType;

    /**
     * 发货时间
     */
    private Date shipAt;

    /**
     * 自动取消时间
     */
    private Date autoCancelTime;

    /**
     * 自动确认收货时间
     */
    private Date autoConfirmTime;

    /**
     * 收货时间
     */
    private Date confirmAt;

    /**
     * 评价时间
     */
    private Date commentAt;

    /**
     * 订单查询增加一小时内显示退款申请按钮
     * true显示 false 不显示
     */
    private Boolean refund = false;

    /**
     * Pay Info from ShopOrder Extra
     */
    private PayInfoVO payInfo;

    /**
     * 订单推送异常描述
     */
    private String orderPushErrorMessage;

    /**
     * 当前流转中的退款单号
     */
    private Long refundId;

    /**
     * 退货地址
     */
    private String sellerAddress;

    /**
     * 购买人的手机号
     */
    private String buyerMobile;

    /**
     * 订购人名称
     */
    private String buyerName;

    /**
     * 订购人身份证
     */
    private String buyerNo;

    /**
     * 订购人第三方id
     */
    private String buyerThirdId;

}
