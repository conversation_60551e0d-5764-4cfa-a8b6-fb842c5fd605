package moonstone.order.dto;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.model.Sku;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Data
@Slf4j
public class SkuSplitOrderList {
    List<SkuForOrderConstruct> skuForOrderConstructList = new ArrayList<>();
    /**
     * 当前包裹已使用容量的总和
     */
    Long packVolumeSumUp = 0L;
    /**
     * 当前包裹已使用价格的总和
     */
    Long priceSumUp = 0L;
    /**
     * 当前包裹最大容量数
     */
    Long maxVolume = 360L;
    /**
     * 当前包裹最大价格  单位到分，则500000=5000.00
      */

    Long maxPrice = 500000L;
    Long skuSize;

    public SkuSplitOrderList(Long maxPrice, Long maxVolume, Long skuSize) {
        this.maxPrice = maxPrice;
        this.maxVolume = maxVolume;
        this.skuSize = skuSize;
    }

    Integer getUnitQuantityBySku(SkuForOrderConstruct skuForOrderConstruct) {
        Sku sku = skuForOrderConstruct.getSku();
        if (sku == null) {
            return 1;
        }
        if (sku.getExtraMap() != null) {
            String unitQuantity = sku.getExtraMap().get(SkuExtraIndex.unitQuantity.getCode());
            if (unitQuantity != null && !unitQuantity.isEmpty()) {
                try {
                    return Integer.parseInt(unitQuantity);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    return 1;
                }
            }
        }
        return 1;
    }

    /**
     * 试图插入一个新的单品到自己队列中
     *
     * @param construct 想要被插入的
     * @return 单品剩余无法插入的剩余实体
     */
    public Optional<SkuForOrderConstruct> insert(@NotNull SkuForOrderConstruct construct) {
        if (construct == null || construct.getQuantity() == 0) {
            return Optional.empty();
        }
        if (construct.getItem().getIsBonded() == null
                || construct.getItem().getIsBonded() == 0) {
            return handleNonBondedInsertion(construct);
        }

        if (singleLimitExceed(construct)) {
            throw new RuntimeException("单个商品的规格已超出包裹限制");
        }

        if (full() || !containAble(construct)) {
            return Optional.of(construct);
        }

        return handleBondedInsertion(construct);
    }

    public boolean full() {
        return packVolumeSumUp >= maxVolume || priceSumUp >= maxPrice;
    }

    public boolean containAble(SkuForOrderConstruct skuForOrderConstruct) {
        // 剩余包裹的容量
        long lastRoomForMore = maxVolume - packVolumeSumUp;
        // 需要的空间 = 组合装数量（1） * 包裹长度
        long requireSpace = getUnitQuantityBySku(skuForOrderConstruct) * skuForOrderConstruct.getPackageSize();
        // 剩余的可使用的价格 = 5000 - 已存放的包裹总价
        long lastRoomForPrice = maxPrice - priceSumUp;
        // 当前sku的价格
        long price = skuForOrderConstruct.getSku().getPrice().longValue();
        return  lastRoomForPrice >= price && lastRoomForMore >= requireSpace;
    }


    /**
     * 完税拆单逻辑（仅按照拆单数量进行拆单）
     */
    private Optional<SkuForOrderConstruct> handleNonBondedInsertion(SkuForOrderConstruct construct) {
        long freeVolume = maxVolume - packVolumeSumUp;
        long volume = construct.getPackageSize();
        int volumeEndure = (int) (freeVolume / (getUnitQuantityBySku(construct) * volume));
        log.info("拆单 体积 {} skuId={}", volumeEndure, construct.getSku().getId());
        Integer quantity = Math.min(volumeEndure, construct.getQuantity());
        log.info("拆单 单位数量 {} skuId={}", quantity, construct.getSku().getId());
        Optional<SkuForOrderConstruct> result;
        if (construct.getQuantity() < quantity) {
            quantity = construct.getQuantity();
            construct.setQuantity(0);
            result = Optional.empty();
        } else {
            construct.setQuantity(construct.getQuantity() - quantity);
            result = Optional.of(construct);
        }
        if (quantity == 0) {
            return result;
        }
        packVolumeSumUp += getUnitQuantityBySku(construct) * quantity * volume;
        SkuForOrderConstruct beAdded = new SkuForOrderConstruct();
        BeanUtils.copyProperties(construct, beAdded);
        beAdded.setQuantity(quantity);
        skuForOrderConstructList.add(beAdded);
        return result;
    }

    /**
     * 保税拆单逻辑（按照价格不能超过5000+拆单数量来进行拆单）
     */
    private Optional<SkuForOrderConstruct> handleBondedInsertion(SkuForOrderConstruct construct) {
        long freeVolume = maxVolume - packVolumeSumUp;
        Long validPrice = maxPrice - priceSumUp;
        long volume = construct.getPackageSize();

        int volumeEndure = (int) (volume == 0L ? freeVolume : (freeVolume / (getUnitQuantityBySku(construct) * volume)));
        int priceEndure = (int) (validPrice / construct.getSku().getPrice());
        log.info("拆单 体积 {} 价格 {} skuId={}", volumeEndure, priceEndure, construct.getSku().getId());
        Integer quantity = Math.min(volumeEndure, priceEndure);
        log.info("拆单 单位数量 {} skuId={}", quantity, construct.getSku().getId());
        Optional<SkuForOrderConstruct> result;
        if (construct.getQuantity() < quantity) {
            quantity = construct.getQuantity();
            construct.setQuantity(0);
            result = Optional.empty();
        } else {
            construct.setQuantity(construct.getQuantity() - quantity);
            result = Optional.of(construct);
        }
        if (quantity == 0) {
            return result;
        }
        packVolumeSumUp += getUnitQuantityBySku(construct) * quantity * volume;
        priceSumUp += (long) quantity * construct.getSku().getPrice();
        SkuForOrderConstruct beAdded = new SkuForOrderConstruct();
        BeanUtils.copyProperties(construct, beAdded);
        beAdded.setQuantity(quantity);
        skuForOrderConstructList.add(beAdded);
        return result;
    }

    /**
     * 单品的规格是否就已经超出整个包裹的限制
     *
     * @param target
     * @return
     */
    public boolean singleLimitExceed(SkuForOrderConstruct target) {
        if (target == null) {
            return false;
        }

        return target.getPackageSize() > maxVolume || target.getSku().getPrice().longValue() > maxPrice;
    }
}
