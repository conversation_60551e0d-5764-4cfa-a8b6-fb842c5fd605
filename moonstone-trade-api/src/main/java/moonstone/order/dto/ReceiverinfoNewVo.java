package moonstone.order.dto;


import lombok.Data;

import java.util.List;

@Data
public class ReceiverinfoNewVo {

    /**
     * 收货地址主键ID
     */

    Long id;

    /**
     * 收货人姓名
     */
    String receiveUserName;

    /**
     * 手机号
     */
    String mobile;

    /**
     * 省
     */
    List<IdAndName> provinces;


    /**
     * 市
     */
    List<IdAndName> citys;

    /**
     * 区
     */
    List<IdAndName> regions;

    /**
     * 详细地址
     */
    String detail;

    /**
     * 默认地址, 1: 是, 0: 不是
     */
    Boolean isDefault;
}
