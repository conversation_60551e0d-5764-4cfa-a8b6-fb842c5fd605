package moonstone.order.dto;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SkuComboDTO {

	/**
	 * 主键id
	 */
	protected Long id;

	/**
	 * skuId
	 */
	private String skuId;

	/**
	 * 组合里的商品id
	 */
	private Long comboItemId;

	/**
	 * 组合里的商品名称
	 */
	private String comboItemName;


	/**
	 * 组合里的skuId
	 */
	private Long comboSkuId;

	/**
	 * 组合skuId数量
	 */
	private Integer comboSkuQuantity;

	/**
	 * 组合里的sku价格（详情使用）
	 */
	private Integer comboSkuPrice;

	/**
	 * 组合里的sku库存（详情使用）
	 */
	private Integer comboSkuStockQuantity;

	/**
	 * 组合里的sku规格（详情使用）
	 */
	private String comboSkuSpecification;


	/**
	 * 组合里的商品主图（详情使用）
	 */
	private String comboItemMainImage;
}
