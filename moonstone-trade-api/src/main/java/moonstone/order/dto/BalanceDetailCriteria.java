package moonstone.order.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.PagingCriteria;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class BalanceDetailCriteria extends PagingCriteria {
    List<Long> ids;
    Long userId;
    Long sourceId;
    Long relatedId;
    List<Long> relatedIds;
    Long fee;
    Integer status;
    Integer notStatus;
    List<Integer> notStatusBitMarks;
    List<Integer> statusList;
    List<Integer> statusBitMarks;
    Integer type;
    Date createdStartAt;
    Date createdEndAt;
}
