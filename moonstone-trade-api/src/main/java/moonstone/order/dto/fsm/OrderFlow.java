package moonstone.order.dto.fsm;

/**
 * <AUTHOR>
 * Pay Status Flow for Order
 */
class OrderFlow extends Flow {
    public OrderFlow(String name) {
        super(name);
    }

    /**
     * 配置流程
     */
    @Override
    protected void configure() {
        // 正常支付流转流程
        payNormalFlow();
        // 订单取消流转流程
        orderCancelFlow();
        // 退款单流转流程
        refundNormalFlow();
        // 拒绝与取消退款单流转流程
        rejectRefundFlow();
        // 退款单在发货的情况下流转流程
        shipRefundFlow();
        // 订单完成可操作流程
        orderCompleteFlow();
    }

    private void orderCompleteFlow() {
        addTransition(OrderStatus.CONFIRMED.getValue(),
                OrderEvent.COMMENT.toOrderOperation(),
                OrderStatus.CONFIRMED.getValue());
    }

    private void payNormalFlow() {
        // 待付款 -> 已付款
        addTransition(OrderStatus.NOT_PAID.getValue(),
                OrderEvent.PAY.toOrderOperation(),
                OrderStatus.PAID.getValue());
        // 已付款-> 待确认收货
        addTransition(OrderStatus.PAID.getValue(),
                OrderEvent.SHIP.toOrderOperation(),
                OrderStatus.SHIPPED.getValue());
        // 确认收货 -> 交易完成
        addTransition(OrderStatus.SHIPPED.getValue(),
                OrderEvent.CONFIRM.toOrderOperation(),
                OrderStatus.CONFIRMED.getValue());
    }

    private void orderCancelFlow() {

        // 在付款之前可以买卖双方均可取消订单
        // 待付款 -> 买家取消
        addTransition(OrderStatus.NOT_PAID.getValue(),
                OrderEvent.BUYER_CANCEL.toOrderOperation(),
                OrderStatus.BUYER_CANCEL.getValue());
        // 待付款 -> 商家取消
        addTransition(OrderStatus.NOT_PAID.getValue(),
                OrderEvent.SELLER_CANCEL.toOrderOperation(),
                OrderStatus.SELLER_CANCEL.getValue());

        // 在取消订单或超时关闭后，买家可删除订单
        // 买家取消 -> 删除
        addTransition(OrderStatus.BUYER_CANCEL.getValue(),
                OrderEvent.DELETE.toOrderOperation(),
                OrderStatus.DELETED.getValue());
        // 卖家取消 -> 删除
        addTransition(OrderStatus.SELLER_CANCEL.getValue(),
                OrderEvent.DELETE.toOrderOperation(),
                OrderStatus.DELETED.getValue());
        // 超时关闭 -> 删除
        addTransition(OrderStatus.TIMEOUT_CANCEL.getValue(),
                OrderEvent.DELETE.toOrderOperation(),
                OrderStatus.DELETED.getValue());
    }

    private void shipRefundFlow() {
        // 如果发货了 那就自然走发货流程
        // 发货 :: 商家同意退款 -> 发货
        addTransition(OrderStatus.REFUND_APPLY_AGREED.getValue(),
                OrderEvent.SHIP.toOrderOperation(),
                OrderStatus.SHIPPED.getValue());
        // 退款拒绝 + 发货
        // @deprecate
        // 商家拒绝退款 -> 待确认收货
        addTransition(OrderStatus.REFUND_APPLY_REJECTED.getValue(),
                OrderEvent.SHIP.toOrderOperation(),
                OrderStatus.SHIPPED.getValue());
        // 退款申请中 允许进入待收货状态, 让用户重新申请退款就好了
//        addTransition(OrderStatus.REFUND_APPLY.getValue(),
//                OrderEvent.SHIP.toOrderOperation(),
//                OrderStatus.SHIPPED.getValue());
    }

    private void rejectRefundFlow() {
        // todo: 待定, 需要修正原有的订单状态
        // 退款退货 -> 商家拒绝退款退货
        addTransition(OrderStatus.RETURN_APPLY.getValue(),
                OrderEvent.RETURN_APPLY_REJECT.toOrderOperation(),
                OrderStatus.PAID.getValue());
        // 申请退款退货 -> 已发货 (买家撤销退货申请)
        addTransition(OrderStatus.RETURN_APPLY.getValue(),
                OrderEvent.RETURN_APPLY_CANCEL.toOrderOperation(),
                OrderStatus.SHIPPED.getValue());
        // 拒绝退款退货 -> 已发货 (买家撤销退货申请)
        addTransition(OrderStatus.RETURN_APPLY_REJECTED.getValue(),
                OrderEvent.RETURN_APPLY_CANCEL.toOrderOperation(),
                OrderStatus.SHIPPED.getValue());
        // 买家已退货 -> 商家拒绝退货
        addTransition(OrderStatus.RETURN.getValue(),
                OrderEvent.RETURN_REJECT.toOrderOperation(),
                OrderStatus.RETURN_REJECTED.getValue());


        // 取消退款
        // 拒绝退款 :: 买家申请退款 -> 已付款
        addTransition(OrderStatus.REFUND_APPLY.getValue(),
                OrderEvent.REFUND_APPLY_REJECT.toOrderOperation(),
                OrderStatus.PAID.getValue());
        // 买家申请退款 -> 已付款 (买家撤销退款)
        addTransition(OrderStatus.REFUND_APPLY.getValue(),
                OrderEvent.REFUND_APPLY_CANCEL.toOrderOperation(),
                OrderStatus.PAID.getValue());
        // 商家拒绝退款 -> 已付款
        addTransition(OrderStatus.REFUND_APPLY_REJECTED.getValue(),
                OrderEvent.REFUND_APPLY_CANCEL.toOrderOperation(),
                OrderStatus.PAID.getValue());

    }

    private void refundNormalFlow() {
        // 在付款后发货前, 买家可申请退款, 商家也可主动退款
        // 已付款 -> 买家申请退款
        addTransition(OrderStatus.PAID.getValue(),
                OrderEvent.REFUND_APPLY.toOrderOperation(),
                OrderStatus.REFUND_APPLY.getValue());
        // 买家申请退款 -> 商家同意退款
        addTransition(OrderStatus.REFUND_APPLY.getValue(),
                OrderEvent.REFUND_APPLY_AGREE.toOrderOperation(),
                OrderStatus.REFUND_APPLY_AGREED.getValue());

        // 买家申请退款 -> 商家强制同意退款 
        addTransition(OrderStatus.REFUND_APPLY.getValue(),
                OrderEvent.FORCE_REFUND_AGREE.toOrderOperation(),
                OrderStatus.REFUND_APPLY_AGREED.getValue());

        // 商家拒绝退款 -> 再次申请退款
        addTransition(OrderStatus.REFUND_APPLY_REJECTED.getValue(),
                OrderEvent.REFUND_APPLY.toOrderOperation(),
                OrderStatus.REFUND_APPLY.getValue());

        // 确认收货，也可以申请仅退款
        addTransition(OrderStatus.CONFIRMED.getValue(),
                OrderEvent.REFUND_APPLY.toOrderOperation(),
                OrderStatus.REFUND_APPLY.getValue());
        // 在发货后, 买家可申请退款退货
        addTransition(OrderStatus.CONFIRMED.getValue(),
                OrderEvent.RETURN_APPLY.toOrderOperation(),
                OrderStatus.RETURN_APPLY.getValue());
        // 已发货 -> 仅仅申请退款
        addTransition(OrderStatus.SHIPPED.getValue(),
                OrderEvent.REFUND_APPLY.toOrderOperation(),
                OrderStatus.REFUND_APPLY.getValue());
//        // 已发货 -> 申请退款退货
        addTransition(OrderStatus.SHIPPED.getValue(),
                OrderEvent.RETURN_APPLY.toOrderOperation(),
                OrderStatus.RETURN_APPLY.getValue());
        // 退款退货-> 商家同意退款退货
        addTransition(OrderStatus.RETURN_APPLY.getValue(),
                OrderEvent.RETURN_APPLY_AGREE.toOrderOperation(),
                OrderStatus.RETURN_APPLY_AGREED.getValue());

        // 退款退货-> 商家强制同意退款 （货由商家自己线下处理）
        addTransition(OrderStatus.RETURN_APPLY.getValue(),
                OrderEvent.FORCE_REFUND_AGREE.toOrderOperation(),
                OrderStatus.REFUND_APPLY_AGREED.getValue());

        // 退货流程
        // 商家同意退款退货 -> 买家已退货
        addTransition(OrderStatus.RETURN_APPLY_AGREED.getValue(),
                OrderEvent.RETURN.toOrderOperation(),
                OrderStatus.RETURN.getValue());
        // 买家已退货 -> 商家已确认收退货
        addTransition(OrderStatus.RETURN.getValue(),
                OrderEvent.RETURN_CONFIRM.toOrderOperation(),
                OrderStatus.RETURN_CONFIRMED.getValue());

        // 退款
        // 商家同意退款 -> 退款处理中
        addTransition(OrderStatus.REFUND_APPLY_AGREED.getValue(),
                OrderEvent.REFUND.toOrderOperation(),
                OrderStatus.REFUND_PROCESSING.getValue());

        // 退款
        // 买家已退货  -> 退款处理中
        addTransition(OrderStatus.RETURN.getValue(),
                OrderEvent.REFUND.toOrderOperation(),
                OrderStatus.REFUND_PROCESSING.getValue());

        // 退款
        // 商家确认已退货  -> 退款处理中
//        addTransition(OrderStatus.RETURN_CONFIRMED.getValue(),
//                OrderEvent.REFUND.toOrderOperation(),
//                OrderStatus.REFUND_PROCESSING.getValue());

        // 退款回调
        // 退款处理中 -> 商家已退款
        addTransition(OrderStatus.REFUND_PROCESSING.getValue(),
                OrderEvent.REFUND_SUCCESS.toOrderOperation(),
                OrderStatus.REFUND.getValue());

    }
}
