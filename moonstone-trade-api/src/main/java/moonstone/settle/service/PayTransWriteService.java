package moonstone.settle.service;

import io.terminus.common.model.Response;
import moonstone.settle.model.AlipayTrans;
import moonstone.settle.model.KjtpayTrans;
import moonstone.settle.model.UnionpayTrans;
import moonstone.settle.model.WechatpayTrans;

/**
 * DATE: 16/7/25 上午10:40 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
public interface PayTransWriteService {

    /**
     * 创建支付宝账务信息(已判断去重)
     * @param trans 账务信息
     * @return 是否成功, 如果新增则会返回Long, 如果已经存在则不返回
     */
    Response<Long> createAlipayTrans(AlipayTrans trans);

    /**
     * 创建微信账务信息(已判断去重)
     * @param trans 账务信息
     * @return 是否成功
     */
    Response<Boolean> createWechatPayTrans(WechatpayTrans trans);

    /**
     * 创建银联账务信息(已判断去重)
     * @param trans 账务信息
     * @return 是否成功
     */
    Response<Boolean> createUnionpayTrans(UnionpayTrans trans);

    /**
     * 创建快捷通账务信息(已判断去重)
     * @param trans 账务信息
     * @return 是否成功
     */
    Response<Boolean> createKjtpayTrans(KjtpayTrans trans);

}
