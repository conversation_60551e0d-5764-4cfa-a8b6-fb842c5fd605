package moonstone.settle.service;

import io.terminus.common.model.Response;
import moonstone.settle.model.SettleOrderDetail;

import java.util.List;

/**
 * Code generated by terminus code gen
 * Desc: 写服务
 * Date: 2016-07-24
 */

public interface SettleOrderDetailWriteService {

    /**
     * 创建SettleOrderDetail
     * @param settleOrderDetail
     * @return 主键id
     */
    Response<Long> createSettleOrderDetail(SettleOrderDetail settleOrderDetail);

    /**
     * 创建多个SettleOrderDetail
     * @param settleOrderDetails
     * @return 主键id
     */
    Response<Boolean> createSettleOrderDetails(List<SettleOrderDetail> settleOrderDetails);


    /**
     * 更新SettleOrderDetail
     * @param settleOrderDetail
     * @return 是否成功
     */
    Response<Boolean> updateSettleOrderDetail(SettleOrderDetail settleOrderDetail);

    /**
     * 根据主键id删除SettleOrderDetail
     * @param settleOrderDetailId
     * @return 是否成功
     */
    Response<Boolean> deleteSettleOrderDetailById(Long settleOrderDetailId);
}