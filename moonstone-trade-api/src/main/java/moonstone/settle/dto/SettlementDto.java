package moonstone.settle.dto;

import lombok.Data;
import moonstone.order.model.Payment;
import moonstone.order.model.Refund;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.settle.model.Settlement;

import java.io.Serializable;
import java.util.List;

/**
 * 账务明细的详情
 *
 * DATE: 16/7/21 上午11:56 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Data
public class SettlementDto implements Serializable {

    private Settlement settlement;

    private Payment payment;

    private Refund refund;

    private List<ShopOrder> shopOrderList;

    private List<SkuOrder> skuOrderList;
}
