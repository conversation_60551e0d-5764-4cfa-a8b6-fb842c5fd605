package moonstone.settle.dto.paging;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import moonstone.common.model.PagingCriteria;
import org.joda.time.DateTime;

import java.io.Serializable;
import java.util.Date;

/**
 * DATE: 16/8/2 上午9:22 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PayTransCriteria extends PagingCriteria implements Serializable {

    private static final long serialVersionUID = -4118002783835337285L;
    private Date tradeStartAt;

    private Date tradeEndAt;

    /**
     * 如果Start的时间和End的时间一致, 则End+1day
     */
    @Override
    public void formatDate(){
        if(tradeStartAt != null && tradeEndAt != null){
            if(tradeStartAt.equals(tradeEndAt)){
                tradeEndAt=new DateTime(tradeEndAt.getTime()).plusDays(1).minusSeconds(1).toDate();
            }
        }
    }
}
