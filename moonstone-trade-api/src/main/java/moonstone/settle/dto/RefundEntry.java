package moonstone.settle.dto;

import lombok.Data;
import moonstone.order.model.Payment;
import moonstone.order.model.Refund;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;

import java.io.Serializable;
import java.util.List;

/**
 * DATE: 16/11/10 下午6:20 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Data
public class RefundEntry implements Serializable {
    private static final long serialVersionUID = 3768572074272545614L;

    private Integer refundType;
    private Refund refund;
    private Integer stage;
    private Payment payment;

    /**
     *  整个订单退款(包含退首款、退尾款、退全款, 不拆分);
     *  其他情况时,作为冗余数据,也不为空
     */
    private ShopOrder shopOrder;

    //单个子订单退款(包含退首款、退尾款、退全款, 不拆分)
    private SkuOrder skuOrder;

    //多个子订单退款
    private List<SkuOrder> skuOrderList;

}
