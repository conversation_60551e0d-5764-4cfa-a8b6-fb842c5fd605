package moonstone.settle.model;

import lombok.Data;
import moonstone.settle.enums.CommissionBusinessType;

import java.io.Serializable;
import java.util.Date;

/**
 * 平台佣金规则
 * Created with IntelliJ IDEA
 * Author: songrenfei
 * Date: 3/23/16
 * Time: 11:40 AM
 */
@Data
public class CommissionRule implements Serializable {


    private static final long serialVersionUID = -8321141665769199101L;


    private Long id;


    /**
     * 业务类型（由解决方案决定）
     *  @see CommissionBusinessType
     */
    private Integer businessType;

    /**
     * 业务id
     */
    private Long businessId;

    /**
     * 业务名称
     */
    private String businessName;

    /**
     * 佣金扣点（万分之一）
     */
    private Integer  rate;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
}
