package moonstone.settle.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 快捷通账务记录
 *
 * DATE: 16/7/21 下午2:53 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Data
public class KjtpayTrans implements Serializable {
    private static final long serialVersionUID = 3430771011217588392L;

    private static final BigDecimal ratio = new BigDecimal("100");  // 元转分时的倍率


    /** 主键id */
    private Long id;
    /** 商户订单号 */
    private String outerNo;
    /**原商户订单号(退款的原交易原始凭 证号) */
    private String origOuterNo;
    /** 交易订单号*/
    private String innerNo;
    /** 交易类型*/
    private String type;
    /** 交易下单时间*/
    private Date orderAt;
    /** 支付时间*/
    private Date paidAt;
    /** 交易金额*/
    private String amount;
    /** 费率 */
    private String rate;
    /** 手续费 */
    private String rateFee;
    /** 交易状态*/
    private String status;

    private Date createdAt;

    private Date updatedAt;


    /**
     * 将快捷通的金额转换成内部的单位计算（单位：分）
     */
    public Long amountToFen() {
        if (amount == null) return 0L;
        BigDecimal money = new BigDecimal(amount);
        return money.multiply(ratio).longValue();
    }

}
