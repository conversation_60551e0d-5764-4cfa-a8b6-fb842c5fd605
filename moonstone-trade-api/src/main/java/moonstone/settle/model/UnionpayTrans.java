/*
 * Copyright (c) 2014 杭州端点网络科技有限公司
 */

package moonstone.settle.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 银联账务记录
 *
 * DATE: 16/7/21 下午2:53 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Data
public class UnionpayTrans implements Serializable {

    private static final long serialVersionUID = -2660985544430867518L;


    /**
     * ID
     */
    private Long id;

    /**
     * 交易码
     */
    private String transactionCode;
    /**
     * 代理机构标识码
     */
    private String acqInsCode;
    /**
     * 发送机构标识码
     */
    private String sendCode;
    /**
     * 系统跟踪号
     */
    private String traceNo;

    /**
     * 交易传输时间
     */
    private Date txnTime;

    /**
     * 帐号
     */
    private String payCardNo;
    /**
     * 交易金额
     */
    private String txnAmt;

    /**
     * 商户类别
     */
    private String merCatCode;

    /**
     * 终端类型
     */
    private String termType;

    /**
     * 查询流水号
     */
    private String queryId;

    /**
     * 支付方式（旧）
     */
    private String type;


    /**
     * 商户订单号
     */
    private String orderId;

    /**
     * 支付卡类型
     */
    private String payCardType;
    /**
     * 原始交易的系统跟踪号
     */
    private String originalTraceNo;

    /**
     * 原始交易日期时间
     */
    private String originalTime;

    /**
     * 商户手续费
     */
    private String thirdPartyFee;

    /**
     * 结算金额
     */
    private String settleAmount;

    /**
     * 支付方式
     */
    private String payType;

    /**
     * 集团商户代码
     */
    private String companyCode;

    /**
     * 交易类型
     */
    private String txnType;

    /**
     * 交易子类
     */
    private String txnSubType;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 帐号类型
     */
    private String accType;

    /**
     * 账单类型
     */
    private String billType;

    /**
     * 账单号码
     */
    private String billNo;

    /**
     * 交互方式
     */
    private String interactMode;

    /**
     * 原交易查询流水号
     */
    private String origQryId;

    /**
     * 商户代码
     */
    private String merId;


    /**
     * 分账入账方式
     */
    private String divideType;

    /**
     * 二级商户代码
     */
    private String subMerId;

    /**
     * 二级商户简称
     */
    private String subMerAbbr;

    /**
     * 二级商户分账入账金额
     */
    private String divideAmount;

    /**
     * 清算净额
     */
    private String clearing;

    /**
     * 终端号
     */
    private String termId;

    /**
     * 商户自定义域
     */
    private String merReserved;

    /**
     * 优惠金额
     */
    private String discount;

    /**
     * 发票金额
     */
    private String invoice;

    /**
     * 分期付款附加手续费
     */
    private String additionThirdPartyFee;

    /**
     * 分期付款期数
     */
    private String stage;

    /**
     * 交易介质
     */
    private String transactionMedia;

    /**
     * 原始交易订单号
     */
    private String originalOrderId;

    /**
     * 创建时间
     */
    private Date createdAt;                 // 创建时间
    /**
     * 更新时间
     */
    private Date updatedAt;                 // 更新时间


    /**
     * （单位：分）
     */
    public Long getTxnAmt() {
        if (txnAmt == null) return 0L;
        BigDecimal money = new BigDecimal(txnAmt);
        return money.longValue();
    }


    /**
     * （单位：分）
     */
    public Long getThirdPartyFee() {
        if (thirdPartyFee == null||thirdPartyFee.equals("")) return 0L;
        BigDecimal money = new BigDecimal(thirdPartyFee);
        return money.longValue();
    }


}
