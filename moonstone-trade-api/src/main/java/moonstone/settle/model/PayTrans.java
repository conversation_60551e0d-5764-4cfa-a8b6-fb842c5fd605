package moonstone.settle.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PayTrans implements Serializable {
    private static final long serialVersionUID = -8665706011897347097L;

    private Long id;
    /**
     * 网关交易流水号, 用于唯一标识
     */
    private String gatewayLogId;

    /**
     * 交易类型{@link moonstone.settle.enums.TradeType}
     */
    private Integer tradeType;

    /**
     * 支付渠道
     */
    private String channel;

    /**
     * 相应支付渠道的账户
     */
    private String accountNo;

    /**
     * 支付流水号
     */
    private String tradeNo;

    /**
     * 退款流水号
     */
    private String refundNo;

    /**
     * 交易时间
     */
    private Date tradeAt;

    /**
     * 支付金额
     */
    private Long fee;

    /**
     * 支付平台佣金 100倍
     */
    private Long commission;

    /**
     * 支付平台佣金费率 10000倍
     */
    private Long rate;

    /**
     * 账务明细信息, json格式, 由具体渠道实现负责存储
     */
    private String details;

    /**
     * 拉取时间
     */
    private Date loadAt;

    private Date createdAt;

    private Date updatedAt;
}
