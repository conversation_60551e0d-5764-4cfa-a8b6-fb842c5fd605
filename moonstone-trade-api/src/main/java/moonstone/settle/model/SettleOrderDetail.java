package moonstone.settle.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 结算 订单明细
 * Created with IntelliJ IDEA
 * Author: songrenfei
 * Date: 7/21/16
 * Time: 2:53 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SettleOrderDetail extends EntityExtra{

    private static final long serialVersionUID = -4465337110750295395L;

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单类型: 指结算系统对订单的分类, 如普通订单、二阶段支付的订单等
     */
    private Integer orderType;

    /**
     * 商家ID
     */
    private Long sellerId;

    /**
     * 商家名称
     */
    private String sellerName;

    /**
     * 应收货款
     */
    private Long originFee;

    /**
     * 商家优惠
     */
    private Long sellerDiscount;

    /**
     * 电商平台优惠
     */
    private Long platformDiscount;

    /**
     * 运费
     */
    private Long shipFee;

    /**
     * 运费优惠
     */
    private Long shipFeeDiscount;

    /**
     * 税费
     */
    private Long tax;

    /**
     * 实收货款
     */
    private Long actualPayFee;

    /**
     * 差价: 用于改价场景中, diffFee = (公式计算出actualFee - 改价后的actualFee)
     */
    private Long diffFee;

    /**
     * 支付平台佣金
     */
    private Long gatewayCommission;

    /**
     * 由商家承担的支付平台佣金
     */
    private Long sellerGatewayCommission;
    /**
     * 电商平台佣金
     */
    private Long platformCommission;

    /**
     * 其他佣金计算
     */
    private Long commission1;

    private Long commission2;

    private Long commission3;

    private Long commission4;

    private Long commission5;

    /**
     * 商家应收
     */
    private Long sellerReceivableFee;

    /**
     * 电商平台交易流水号
     */
    private String tradeNo;

    /**
     * 支付平台交易流水号
     */
    private String gatewayTradeNo;

    /**
     * 支付渠道
     */
    private String channel;

    /**
     * 支付渠道账号
     */
    private String channelAccount;

    /**
     * 订单创建时间
     */
    private Date orderCreatedAt;

    /**
     * 订单完成时间
     */
    private Date orderFinishedAt;

    /**
     * 支付时间
     */
    private Date paidAt;

    /**
     * 对账状态 {@link moonstone.settle.enums.CheckStatus}
     */
    private Integer checkStatus;

    /**
     * 对账时间
     */
    private Date checkAt;

    /**
     * 汇总依据的时间: 为具体时间,如订单完成时间
     */
    private Date sumAt;

    private Date createdAt;

    private Date updatedAt;
}
