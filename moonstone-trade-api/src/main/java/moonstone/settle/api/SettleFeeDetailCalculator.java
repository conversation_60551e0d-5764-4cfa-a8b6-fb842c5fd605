package moonstone.settle.api;

import moonstone.settle.dto.SettleFeeDetailTree;
import moonstone.settle.dto.SettleTrade;

import java.math.BigDecimal;

/**
 * DATE: 16/11/9 下午3:06 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
public interface SettleFeeDetailCalculator {

    SettleFeeDetailTree calculate(SettleTrade settleTrade);

    static Long split(Long toSplit,Long total,Long part){
        if(toSplit==null){
            toSplit=0L;
        }
        if(total==null){
            return null;
        }
        if(part == null){
            part=0L;
        }
        return new BigDecimal(toSplit).multiply(
                new BigDecimal(part).divide(new BigDecimal(total), 10, BigDecimal.ROUND_HALF_DOWN))
                .setScale(0, BigDecimal.ROUND_HALF_DOWN)
                .longValue();//取0位小数 向上取整
    }

    static Long split(Long toSplit, Long rate){
        return new BigDecimal(toSplit)
                .multiply(new BigDecimal(rate)
                        .divide(new BigDecimal(10000), 10, BigDecimal.ROUND_HALF_DOWN))
                .setScale(0, BigDecimal.ROUND_HALF_DOWN)
                .longValue();//取0位小数 向上取整
    }

    static Long split(Long toSplit, BigDecimal rate){
        return new BigDecimal(toSplit)
                .multiply(rate)
                .setScale(0, BigDecimal.ROUND_HALF_DOWN)
                .longValue();//取0位小数 向上取整
    }

    static BigDecimal getRate(Long total, Long part){
        return new BigDecimal(part).divide(new BigDecimal(total), 10, BigDecimal.ROUND_HALF_DOWN);
    }
}
