package moonstone.stock.impl.service;

import lombok.Data;
import lombok.NoArgsConstructor;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;

import java.util.Map;

/**
 * <AUTHOR>
 * 仓库关区代码查询器
 */
public interface DepotCustomManager {

    /**
     * 查询订单的关区代码
     *
     * @param shopOrder 订单
     * @return 订单关区代码
     */
    String getCustomCodeByOrder(ShopOrder shopOrder);

    /**
     * 注册海关信息
     *
     * @param code 海关代码
     * @param name 海关名称
     */
    void registerCustom(String code, String name);

    /**
     * 注册仓库所在海关信息
     *
     * @param depotCode  仓库代码
     * @param customCode 海关代码
     */
    void registerDepotCustomInfo(String depotCode, String customCode);

    /**
     * 获取所有的关区配置信息
     *
     * @return 关区配置信息
     */
    Map<String, CustomCode> getCustomCodeMapByName();

    void deleteDepotCustomInfo(String depotCode);

    String getCustomCodeByOrder(SkuOrder skuOrder);

    String getCustomCodeByShopOrder(ShopOrder shopOrder);

    enum CustomBySkuExtra {
        /**
         * 用于绑定和获取这个单品的海关信息
         */
        custom
    }

    enum VirtualDepot {
        /**
         * 虚拟仓信息
         */
        GongXiao("GXPTWARE", "供销虚拟仓");

        String code;
        String desc;

        VirtualDepot(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }
    }

    /**
     * 缺少外部配置功能, 仅仅用于启动时默认加载
     */
    enum originCustomConfig {
        /**
         * 默认设置关区代号
         */
        ZONGSHU("ZONGSHU", "总署", "ZS"),
        HANGZHOU("HANGZHOU", "杭州", "HZ"),
        TIANJIN("TIANJIN", "天津", "TJ"),
        GUANGZHOU("GUANGZHOU", "广州", "GZ"),
        SHANGHAI("SHANGHAI", "上海", "SH"),
        HAIKOU("HAIKOU", "海口", "HK"),
        DOMESTIC("DOMESTIC", "大贸", ""),
        JINYI("HANGZHOU", "金义", "JY"),
        YIWU("HANGZHOU", "义乌", "YW"),
        CHONGQING("CHONGQING", "重庆", "CQ"),
        SHAOXING("SHAOXING", "绍兴", "SX"),
        ;

        String name, code, shortName;

        originCustomConfig(String code, String name, String shortName) {
            this.code = code;
            this.name = name;
            this.shortName = shortName;
        }

        public String getShortName() {
            return shortName;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }
    }

    String CUSTOM_NOT_FOUND = "NOT_FOUND";

    @Data
    @NoArgsConstructor
    class CustomCode {
        /**
         * 原本打算使用final 但是Java的很多规范都被曲解使用, JSON不支持构造器注入那也就和final绝缘了
         */
        public String code;
        public String name;

        public CustomCode(String code, String name) {
            this.code = code;
            this.name = name;
        }
    }
}
