package moonstone.stock.impl.service;

import io.terminus.common.model.Response;
import moonstone.order.model.DepotCustomInfo;

import java.util.List;

public interface DepotCustomInfoReadService {
    Response<DepotCustomInfo> findByDepotCode(String DepotCode);
    Response<DepotCustomInfo> findById(Long id);
    Response<List<DepotCustomInfo>> findByIds(List<Long> ids);
    Response<List<DepotCustomInfo>> findAll();
}
