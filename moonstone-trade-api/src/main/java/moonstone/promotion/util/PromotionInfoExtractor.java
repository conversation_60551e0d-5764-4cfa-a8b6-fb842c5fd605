package moonstone.promotion.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import io.terminus.common.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import moonstone.promotion.dto.SkuWithDiscountInfo;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 营销活动信息析取
 * Author:cp
 * Created on 7/4/16.
 */
@Slf4j
public abstract class PromotionInfoExtractor {

    private static final ObjectMapper objectMapper = JsonMapper.nonDefaultMapper().getMapper();

    private static final TypeReference<List<SkuWithDiscountInfo>> LIST_OF_SKU_WITH_DISCOUNT_INFO = new TypeReference<List<SkuWithDiscountInfo>>() {
    };

    /**
     * 从商品折扣活动的行为参数中析取sku营销信息
     *
     * @param skuId
     * @param behaviorParams 商品折扣活动的行为参数
     * @return 每个skuId对应的营销信息
     */
    public static SkuWithDiscountInfo extractFromDiscountSkuOrderBehavior(Long skuId, Map<String, String> behaviorParams) {
        String skusWithDiscount = behaviorParams.get("skusWithDiscount");
        if (!StringUtils.hasText(skusWithDiscount)) {
            throw new IllegalArgumentException("skus.with.discount.miss");
        }

        List<SkuWithDiscountInfo> skuWithDiscountInfos;
        try {
            skuWithDiscountInfos = objectMapper.readValue(skusWithDiscount, LIST_OF_SKU_WITH_DISCOUNT_INFO);
        } catch (IOException e) {
            //ignore this
            return null;
        }

        Map<Long, SkuWithDiscountInfo> skuWithDiscountInfoMap = Maps.newHashMapWithExpectedSize(skuWithDiscountInfos.size());
        for (SkuWithDiscountInfo skuWithDiscountInfo : skuWithDiscountInfos) {
            skuWithDiscountInfoMap.put(skuWithDiscountInfo.getSkuId(), skuWithDiscountInfo);
        }
        SkuWithDiscountInfo skuWithDiscountInfo = skuWithDiscountInfoMap.get(skuId);
        if (skuWithDiscountInfo == null) {
            log.error("sku with discount info not found where sku id={}", skuId);
            throw new IllegalStateException("sku.with.discount.not.found");
        }
        return skuWithDiscountInfo;
    }

}
