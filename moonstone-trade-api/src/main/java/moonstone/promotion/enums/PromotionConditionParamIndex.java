package moonstone.promotion.enums;

public enum PromotionConditionParamIndex {
    limitedItemMinBoughtNum("limitedItemMinBoughtNum", "使用门店限定卷时，限定商品的最小购买数量"),
    activityConditionItemNum("activityConditionItemNum", "门店限定卷, 活动商品的指定数量"),
    ;

    private String code;
    private String description;

    PromotionConditionParamIndex(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
