package moonstone.promotion.enums;

import com.google.common.base.Objects;

/**
 * 典型营销类型
 * todo: 优惠类型细分为优惠卷和优惠活动
 * Author:cp
 * Created on 6/25/16.
 */
public enum PromotionType {

    /**
     * 平台优惠券
     */
    GLOBAL_COUPON(0),

    /**
     * 商品优惠券
     */
    ITEM_COUPON(1),

    /**
     * 店铺优惠券
     */
    SHOP_COUPON(2),

    /**
     * 商品折扣
     */
    SKU_DISCOUNT(3),

    /**
     * 门店限定卷
     */
    SUB_STORE_ONLY(4),

    /**
     * 运费优惠券
     */
    SHIPMENT_COUPON(20),

    /**
     * 运费优惠
     */
    SHIPMENT_DISCOUNT(21),

    /**
     * 店铺满减
     */
    SHOP_CONDITION_REDUCTION(22),

    /**
     * 其他
     */
    OTHER(9999);

    private final int value;

    PromotionType(int value) {
        this.value = value;
    }

    public static PromotionType from(int type) {
        for (PromotionType promotionType : values()) {
            if (Objects.equal(promotionType.value, type)) {
                return promotionType;
            }
        }
        return PromotionType.OTHER;
    }

    public int getValue() {
        return value;
    }

    /**
     * 判断该营销是否是用户营销(如优惠券、积分之类)
     * 如果是用户营销,当参与该营销时一般需要操作user_promotion表(如减积分)
     *
     * @param type 营销类型
     * @return 是返回true, 否则返回false
     */
    public static boolean isUserPromotion(int type) {
        return isCoupon(type);
    }

    /**
     * 判断是否是优惠券
     *
     * @param type 营销类型
     * @return 是优惠券返回true, 否则返回false
     */
    public static boolean isCoupon(int type) {
        return switch (from(type)) {
            case GLOBAL_COUPON, ITEM_COUPON, SHOP_COUPON, SHIPMENT_COUPON, SUB_STORE_ONLY -> true;
            default -> false;
        };
    }
}
