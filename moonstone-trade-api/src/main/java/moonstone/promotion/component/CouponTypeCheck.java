package moonstone.promotion.component;

import io.terminus.common.exception.JsonResponseException;
import moonstone.cache.PromotionCacher;
import moonstone.cache.PromotionToolCacher;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.RichSku;
import moonstone.order.dto.RichSkusByShop;
import moonstone.promotion.api.Behavior;
import moonstone.promotion.api.CouponCheck;
import moonstone.promotion.api.PromotionTool;
import moonstone.promotion.constants.PromotionConditionKey;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * author: wulianlei
 *
 * @see PromotionQualificationValidator 这个是最终选择优惠是否可用
 */
@Component
public class CouponTypeCheck implements CouponCheck {

    private final Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private PromotionCacher promotionCacher;
    @Autowired
    private PromotionToolCacher promotionToolCacher;

    @Override
    public void check(RichOrder richOrder) {
        boolean allIsIntegralItem = true;
        for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
            Set<Long> usedItemCouponIds = new HashSet<>();
            boolean singleOrderAllIsIntegralItem = true;
            for (RichSku richSku : richSkusByShop.getRichSkus()) {
                if (singleOrderAllIsIntegralItem && richSku.getItem().getType() >= 3)
                    singleOrderAllIsIntegralItem = false;
                final Long promotionId = richSku.getPromotionId();
                if (promotionId == null) {
                    continue;
                }

                Promotion promotion = promotionCacher.findByPromotionId(promotionId);
                if (PromotionType.from(promotion.getType()) != PromotionType.ITEM_COUPON) {
                    continue;
                }
                //  积分优惠仅可以用于积分商品
                if (richSku.getItem().getType() >= 3) {
                    if (!promotion.getConditionParams().getOrDefault(PromotionConditionKey.REQUIRE_INTEGRAL, "false").equals("true")) {
                        richSku.setPromotionId(null);
                        continue;
                    }
                } else {
                    if (promotion.getConditionParams().getOrDefault(PromotionConditionKey.REQUIRE_INTEGRAL, "false").equals("true")) {
                        richSku.setPromotionId(null);
                        continue;
                    }
                }
                PromotionTool<? extends Behavior> promotionTool = promotionToolCacher.findByPromotionDefId(promotion.getPromotionDefId());
                boolean inSkuScope = false;
                boolean inUserScope = false;
                try {
                    inSkuScope = promotionTool.skuScope().apply(richSku, richOrder.getBuyer(), promotion.getSkuScopeParams());
                    inUserScope = promotionTool.userScope().apply(richOrder.getBuyer(), richSkusByShop.getShop(), promotion.getUserScopeParams());
                } catch (Exception ex) {
                    log.error("{} promotion[{}] buyer[{}] shop[{}] richSku[{}] fail to check the scope, inSkuScope[{}] inUserScope[{}]", LogUtil.getClassMethodName()
                            , richOrder.getBuyer().getId(), richSkusByShop.getShop().getId(), richSku.getSku().getId(), inSkuScope, inUserScope, ex);
                    richSku.setPromotionId(null);
                }

                if (usedItemCouponIds.contains(promotionId)) {
                    log.error("the item coupon(promotionId={}) has used too many in a shop",
                            promotionId);
                    throw new JsonResponseException(Translate.of("同一种商品优惠价每订单只能使用一次"));
                } else {
                    usedItemCouponIds.add(promotionId);
                }
            }
            if (richSkusByShop.getPromotionId() != null) {
                Promotion promotion = promotionCacher.findByPromotionId(richSkusByShop.getPromotionId());
                //  积分优惠仅可以用于积分商品
                if (singleOrderAllIsIntegralItem) {
                    if (!promotion.getConditionParams().getOrDefault(PromotionConditionKey.REQUIRE_INTEGRAL, "false").equals("true"))
                        richSkusByShop.setPromotionId(null);
                } else {
                    if (promotion.getConditionParams().getOrDefault(PromotionConditionKey.REQUIRE_INTEGRAL, "false").equals("true"))
                        richSkusByShop.setPromotionId(null);
                }
            }
            if (!singleOrderAllIsIntegralItem && allIsIntegralItem) allIsIntegralItem = false;
        }
        if (richOrder.getPromotionId() != null) {
            Promotion promotion = promotionCacher.findByPromotionId(richOrder.getPromotionId());
            //  积分优惠仅可以用于积分商品
            if (allIsIntegralItem) {
                if (!promotion.getConditionParams().getOrDefault(PromotionConditionKey.REQUIRE_INTEGRAL, "false").equals("true"))
                    richOrder.setPromotionId(null);
            } else {
                if (promotion.getConditionParams().getOrDefault(PromotionConditionKey.REQUIRE_INTEGRAL, "false").equals("true"))
                    richOrder.setPromotionId(null);
            }
        }
    }
}
