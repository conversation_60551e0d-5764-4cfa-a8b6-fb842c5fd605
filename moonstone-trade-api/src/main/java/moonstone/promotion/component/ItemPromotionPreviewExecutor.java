package moonstone.promotion.component;

import com.alibaba.fastjson.JSON;
import moonstone.cache.*;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.item.api.IndexedItemProcessor;
import moonstone.item.model.Sku;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.RichSku;
import moonstone.order.dto.RichSkusByShop;
import moonstone.promotion.api.*;
import moonstone.promotion.constants.PromotionConditionKey;
import moonstone.promotion.dto.PromotionContext;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.search.dto.IndexedItem;
import moonstone.shop.model.Shop;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Predicate;

@Component
public class ItemPromotionPreviewExecutor implements IndexedItemProcessor {
    Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PromotionToolCacher promotionToolCacher;

    @Autowired
    private ShopCacheHolder shopCacheHolder;

    @Autowired
    private ItemCacheHolder itemCacheHolder;

    @Autowired
    private SkuCacheHolder skuCacheHolder;

    @Autowired
    private PromotionCacher promotionCacher;

    @Override
    public IndexedItem process(IndexedItem indexedItem) {
        if (indexedItem == null) return null;
        List<Promotion> promotionList = promotionCacher.findOngoingPromotionOf(indexedItem.getShopId());
        IndexedItem executedItem = new IndexedItem();
        IndexedItem lowestPrice = new IndexedItem();
        BeanUtils.copyProperties(indexedItem, lowestPrice);
        for (Promotion promotion : promotionList) {
            if (!Objects.equals(promotion.getType(), PromotionType.SKU_DISCOUNT.getValue()))
                continue;
            if (!(promotion.getEndAt().after(new Date()) && promotion.getStartAt().before(new Date())))
                continue;

            // 暂时修复补丁,复制conditionParam的值
            if (Optional.ofNullable(promotion.getConditionParams()).orElseGet(HashMap::new).getOrDefault(PromotionConditionKey.REQUIRE_INTEGRAL, "false").equals("true")) {
                Map<String, String> skuScopeParam = Optional.ofNullable(promotion.getSkuScopeParams()).orElseGet(HashMap::new);
                skuScopeParam.put(SkuScope.REQUIRE_INTEGRAL, "true");
                promotion.setSkuScopeParams(skuScopeParam);
            }
            if (promotion.getStatus() <= 0) continue;
            BeanUtils.copyProperties(indexedItem, executedItem);
            executedItem = preExecute(executedItem, promotion);
            if (lowestPrice.getPreviewPrice() == null || executedItem.getPreviewPrice() < lowestPrice.getPreviewPrice())
                BeanUtils.copyProperties(executedItem, lowestPrice);
        }
        if (Optional.ofNullable(lowestPrice.getPreviewPrice()).filter(Predicate.isEqual(lowestPrice.getPrice())).isPresent())
            lowestPrice.setPromotionName(null);
        //  积分商品4舍弃5入
        if (lowestPrice.getType() > 2 && lowestPrice.getPreviewPrice() != null) {
            lowestPrice.setPreviewPrice((lowestPrice.getPreviewPrice() / 100) * 100 + (lowestPrice.getPreviewPrice() % 100 >= 50 ? 100 : 0));
            if (lowestPrice.getPreviewPrice() == 0) lowestPrice.setPreviewPrice(100);
        }
        return lowestPrice;
    }

    /**
     * 预先执行营销活动
     *
     * @param indexedItem 搜索引擎内的商品
     * @param promotion   营销工具
     */
    public IndexedItem preExecute(IndexedItem indexedItem, Promotion promotion) {
        if (promotion == null) return indexedItem;
        PromotionTool<? extends Behavior> promotionTool = promotionToolCacher.findByPromotionDefId(promotion.getPromotionDefId());
        if (!promotionTool.behavior().affectPay())
            return indexedItem;
        Shop shop = shopCacheHolder.findShopById(indexedItem.getShopId());
        PromotionContext promotionContext = new PromotionContext(null, shop, null, promotion);
        log.debug("{} promotion [{}] preExecute for item[{}]", LogUtil.getClassMethodName(), JSON.toJSONString(promotion), indexedItem.getId());
        try {
            RichOrder richOrder = new RichOrder();
            RichSkusByShop richSkusByShop = new RichSkusByShop();
            RichSku richSku = new RichSku();
            richSkusByShop.setRichSkus(Collections.singletonList(richSku));
            richOrder.setRichSkusByShops(Collections.singletonList(richSkusByShop));
            richSku.setQuantity(1);
            Integer lowestPrice = indexedItem.getPrice();
            richSku.setItem(itemCacheHolder.findItemById(indexedItem.getId()));
            for (Sku sku : skuCacheHolder.findSkusByItemId(indexedItem.getId())) {
                richSku.setSku(sku);
                richSku.setFee(sku.getPrice().longValue());
                richSku.setOriginFee(richSku.getFee());
                richSkusByShop.setFee(richSku.getFee());
                richSkusByShop.setOriginFee(richSku.getFee());
                richOrder.setFee(richSkusByShop.getFee());
                richOrder.setOriginFee(richSkusByShop.getFee());
                Behavior behavior = promotionTool.behavior();
                try {
                    if (!promotionTool.skuScope().apply(richSku, null, promotion.getSkuScopeParams())) {
                        log.warn("{} sku[{}] not suitable for promotion[{}}", LogUtil.getClassMethodName(), sku.getId(), promotion.getId());
                        continue;
                    }
                    if (behavior instanceof SkuOrderBehavior) {
                        richSku.setPromotionId(promotion.getId());
                        if (!((SkuOrderBehavior) behavior).condition(richSku, promotionContext, promotion.getConditionParams())) {
                            log.warn("{} sku[{}] not in condition[{}] of promotion[{}]", LogUtil.getClassMethodName(), sku.getId(), promotion.getConditionParams(), promotion.getId());
                            continue;
                        }
                        ((SkuOrderBehavior) behavior).execute(richSku, promotionContext, promotion.getBehaviorParams());
                        lowestPrice = Math.min(richSku.getFee().intValue(), lowestPrice);
                        indexedItem.setPromotionName(Optional.ofNullable(promotion.getExtra()).orElse(new HashMap<>()).getOrDefault("promotionName", promotion.getName()));
                        indexedItem.setPromotionId(promotion.getId());
                        indexedItem.setPromotionStartAt(promotion.getStartAt());
                        indexedItem.setPromotionEndAt(promotion.getEndAt());
                        continue;
                    }
                    if (behavior instanceof ShopOrderBehavior) {
                        richSkusByShop.setPromotionId(promotion.getId());
                        if (!((ShopOrderBehavior) behavior).condition(richSkusByShop, promotionContext, promotion.getConditionParams())) {
                            log.warn("{} sku[{}] not in condition[{}] of promotion[{}]", LogUtil.getClassMethodName(), sku.getId(), promotion.getConditionParams(), promotion.getId());
                            continue;
                        }
                        ((ShopOrderBehavior) behavior).execute(richSkusByShop, new PromotionContext(null, shop, null, promotion), promotion.getBehaviorParams());
                        lowestPrice = Math.min(richSkusByShop.getFee().intValue(), lowestPrice);
                        indexedItem.setPromotionName(Optional.ofNullable(promotion.getExtra()).orElse(new HashMap<>()).getOrDefault("promotionName", promotion.getName()));
                        indexedItem.setPromotionId(promotion.getId());
                        indexedItem.setPromotionStartAt(promotion.getStartAt());
                        indexedItem.setPromotionEndAt(promotion.getEndAt());
                        continue;
                    }
                    if (behavior instanceof RichOrderBehavior) {
                        richOrder.setPromotionId(promotion.getId());
                        if (!((RichOrderBehavior) behavior).condition(richOrder, promotionContext, promotion.getConditionParams())) {
                            log.warn("{} sku[{}] not in condition[{}] of promotion[{}]", LogUtil.getClassMethodName(), sku.getId(), promotion.getConditionParams(), promotion.getId());
                            continue;
                        }
                        ((RichOrderBehavior) behavior).execute(richOrder, new PromotionContext(null, shop, null, promotion), promotion.getBehaviorParams());
                        lowestPrice = Math.min(richSkusByShop.getFee().intValue(), lowestPrice);
                        indexedItem.setPromotionName(Optional.ofNullable(promotion.getExtra()).orElse(new HashMap<>()).getOrDefault("promotionName", promotion.getName()));
                        indexedItem.setPromotionId(promotion.getId());
                        indexedItem.setPromotionStartAt(promotion.getStartAt());
                        indexedItem.setPromotionEndAt(promotion.getEndAt());
                        continue;
                    }
                } catch (Exception ex) {
                    log.error("{} promotion preview failed with item[{}] promotion[{}] error:", LogUtil.getClassMethodName(), indexedItem.getId(), promotion.getId(), ex);
                    continue;
                }
                throw new RuntimeException(new Translate("未知营销工具[%s] 商品[%s] [%s] 无法预计算", promotion.getId(), indexedItem.getName(), indexedItem.getPreviewPrice()).toString());
            }
            indexedItem.setPreviewPrice(lowestPrice);
            return indexedItem;
        } catch (Exception ex) {
            log.error("{} fail to preExecute promotion[{}] for item[{}]", LogUtil.getClassMethodName(), promotion.getId(), indexedItem.getId(), ex);
            return indexedItem;
        }
    }
}
