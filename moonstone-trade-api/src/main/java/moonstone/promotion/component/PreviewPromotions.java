/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.component;

import com.google.common.base.MoreObjects;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.common.model.BaseUser;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.PromotionCacher;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.GenerateSelector;
import moonstone.common.utils.LogUtil;
import moonstone.component.item.component.TaxChecker;
import moonstone.component.membership.component.MembershipPriceChecker;
import moonstone.item.model.Sku;
import moonstone.order.api.DeliveryFeeCharger;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.RichSku;
import moonstone.order.dto.RichSkusByShop;
import moonstone.promotion.api.CoupCount;
import moonstone.promotion.api.PromotionPicker;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.model.UserPromotion;
import moonstone.promotion.model.view.PromotionView;
import moonstone.promotion.service.UserPromotionReadService;
import moonstone.shop.model.Shop;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopSku;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 订单预览页面营销活动处理
 */
@Component
@Slf4j
public record PreviewPromotions(QualifiedPromotions qualifiedPromotions, PromotionPicker promotionPicker,
                                PromotionExecutor promotionExecutor, TaxChecker taxChecker,
                                PromotionCacher promotionCacher,
                                DeliveryFeeCharger deliveryFeeCharger,
                                UserPromotionReadService userPromotionReadService,
                                MembershipPriceChecker membershipPriceChecker) {

    /**
     * 根据richOrder来确定优惠信息, 给订单预览页面使用
     * <p>
     * 各种级别的营销活动信息会记录在richOrder对应的地方, 最后还会返回默认的或者确定使用的营销活动概览
     *
     * @param richOrder 订单相关的信息
     */
    public void composePromotions(RichOrder richOrder) {
        OrderPromotionStatus orderPromotionStatus = new OrderPromotionStatus();
        orderPromotionStatus.buyer = richOrder.getBuyer();
        try {
            // limit user promotions
            limitTheCoup(richOrder);
            // 计算整个店铺级订单的价格
            calculateShopOrderPromotion(orderPromotionStatus, richOrder.getRichSkusByShops(), richOrder);
            // 设置全订单计算订单优惠后的价格
            richOrder.setOriginFee(orderPromotionStatus.totalFee);

            List<Promotion> promotions = PromotionView.from(qualifiedPromotions.findGlobalPromotions(richOrder));

            if (Optional.ofNullable(richOrder.getTags()).map(tags -> tags.get("giftOrder")).orElse("").equals("true")) {
                promotions = new ArrayList<>();
                richOrder.setPromotionId(null);
            }
            richOrder.setPromotions(promotions);
            if (richOrder.getPromotionId() == null) {
                Long defaultPromotionId = promotionPicker.defaultGlobalPromotion(promotions);
                if (Suggest.One.SUGGEST_PROMOTION.get()) {
                    richOrder.setPromotionId(defaultPromotionId);
                }
            }
            trimSkuCoupPromotions(richOrder.getRichSkusByShops(), richOrder.getBuyer().getId());
            // 开始计算平台优惠后的订单价格
            promotionExecutor.processRichOrder(richOrder);
            //  积分商品四舍五入 不允许0积分
            if (orderPromotionStatus.allIsIntegral) {
                richOrder.setFee((richOrder.getFee() / 100) * 100 + (richOrder.getFee() % 100 >= 50 ? 100 : 0));
                if (richOrder.getFee() == 0L)
                    richOrder.setFee(100L);
            }
        } finally {
            CoupCount.clearStorage();
        }
    }

    /**
     * 去除不足的优惠卷
     *
     * @param richSkusByShops r
     */
    private void trimSkuCoupPromotions(List<RichSkusByShop> richSkusByShops, Long buyerId) {
        try {
            List<Promotion> shouldDelete = new ArrayList<>();
            Map<Long, Integer> usedCount = new TreeMap<>();
            for (RichSkusByShop richSkusByShop : richSkusByShops) {
                for (RichSku richSkus : richSkusByShop.getRichSkus()) {

                    if (richSkus.getPromotionId() != null) {
                        Promotion promotion = promotionCacher.findByPromotionId(richSkus.getPromotionId());
                        if (!PromotionType.isCoupon(promotion.getType())) {
                            continue;
                        }

                        usedCount.putIfAbsent(richSkus.getPromotionId(), 0);
                        usedCount.computeIfPresent(richSkus.getPromotionId(), (pid, num) -> num + 1);
                    }
                    for (Promotion skuPromotion : richSkus.getSkuPromotions()) {
                        if (!PromotionType.isCoupon(skuPromotion.getType())) {
                            continue;
                        }
                        for (UserPromotion userPromotion : userPromotionReadService.findByUserIdAndPromotionId(buyerId, skuPromotion.getId())
                                .getResult()) {
                            if (userPromotion.getStatus() == 2) {
                                if (userPromotion.getAvailableQuantity() <= usedCount.getOrDefault(skuPromotion.getId(), 0)) {
                                    shouldDelete.add(skuPromotion);
                                }
                                break;
                            }
                        }
                    }

                }
            }
            for (RichSkusByShop richSkusByShop : richSkusByShops) {
                for (RichSku richSkus : richSkusByShop.getRichSkus()) {
                    for (Promotion promotion : shouldDelete) {
                        if (!Objects.equals(richSkus.getPromotionId(), promotion.getId())) {
                            richSkus.getSkuPromotions()
                                    .removeIf(p-> Objects.equals(p.getId(), promotion.getId()));
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Fail to Delete Promotion", e);
        }
    }

    private void limitTheCoup(RichOrder richOrder) {
        try {
            Map<Long, Integer> count = new HashMap<>();
            for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                for (RichSku richSkus : richSkusByShop.getRichSkus()) {
                    Long promotionId = richSkus.getPromotionId();
                    if (promotionId == null) {
                        continue;
                    }
                    // 优惠卷数量不足则取消优惠卷
                    if (count.containsKey(promotionId)) {
                        if (count.get(promotionId) < 1) {
                            richSkus.setPromotionId(null);
                            continue;
                        }
                        count.computeIfPresent(promotionId, (key, num) -> num - 1);
                        continue;
                    }
                    // 判断是否为优惠卷, 不是优惠卷则不检测, 如果是则填充并扣减数量
                    for (UserPromotion userPromotion : userPromotionReadService.findByUserIdAndPromotionId(richOrder.getBuyer().getId(), promotionId).getResult()) {
                        if (!PromotionType.isCoupon(userPromotion.getType())) {
                            break;
                        }
                        if (userPromotion.getStatus() == 2) {
                            if (userPromotion.getAvailableQuantity() >= 1) {
                                count.put(promotionId, userPromotion.getAvailableQuantity() - 1);
                            } else {
                                richSkus.setPromotionId(null);
                            }
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("Fail to check the promotion", e);
        }
    }

    /**
     * 计算全部店铺级订单的优惠后价格
     *
     * @param orderPromotionStatus 整个订单利润的状态
     * @param richSkusByShops      全部店铺级的订单
     * @param richOrder            主订单
     */
    private void calculateShopOrderPromotion(OrderPromotionStatus orderPromotionStatus, List<RichSkusByShop> richSkusByShops, RichOrder richOrder) {
        for (RichSkusByShop richSkusByShop : richSkusByShops) {
            CommonUser buyer = orderPromotionStatus.buyer;
            LinkedList<Boolean> emptyForIntegralFlag = new LinkedList<>();
            Long shopOrderOriginFee =
                    calculateSkuOrderPromotion(orderPromotionStatus, richSkusByShop, emptyForIntegralFlag);
            // 将计算完单品优惠后的价格作为订单优惠前的原价
            richSkusByShop.setOriginFee(shopOrderOriginFee);

            List<Promotion> promotions = PromotionView.from(qualifiedPromotions.findShopPromotions(richSkusByShop, buyer));
            // 去除已经被使用完毕的优惠卷
            if (promotions != null) {
                promotions.removeIf(promotion -> promotion == null || orderPromotionStatus.usedShopPromotionCoupIdSet.contains(promotion.getId()));
            }

            // 选择和启用优惠卷
            if (richSkusByShop.getPromotionId() == null) {
                Long defaultPromotionId = promotionPicker.defaultShopPromotion(promotions);
                if (Suggest.One.SUGGEST_PROMOTION.get()) {
                    richSkusByShop.setPromotionId(defaultPromotionId);
                }
            }
            // 积分订单没有优惠卷
            if (Optional.ofNullable(richOrder.getTags()).map(tags -> tags.get("giftOrder")).orElse("").equals("true")) {
                promotions = new ArrayList<>();
                richSkusByShop.setPromotionId(null);
            }
            richSkusByShop.setShopPromotions(promotions);

            CoupCount.Coup.setLimit();
            // 计算利润
            promotionExecutor.processShopOrder(richOrder.getBuyer(), richSkusByShop);
            CoupCount.Coup.closeLimit();

            // 如果真的使用了店铺优惠卷, 就需要判断 店铺优惠卷是否还有剩余
            if (richSkusByShop.getPromotionId() != null && CoupCount.Coup.getCount(richSkusByShop.getPromotionId()) != null) {
                if (Optional.ofNullable(CoupCount.Coup.getCount(richSkusByShop.getPromotionId())).orElse(1) <= 0) {
                    orderPromotionStatus.usedShopPromotionCoupIdSet.add(richSkusByShop.getPromotionId());
                }
            }

            //  积分商品四舍五入 不允许0积分
            if (emptyForIntegralFlag.isEmpty()) {
                richSkusByShop.setFee((richSkusByShop.getFee() / 100) * 100 + (richSkusByShop.getFee() % 100 >= 50 ? 100 : 0));
                if (richSkusByShop.getFee() == 0L) richSkusByShop.setFee(100L);
            } else if (orderPromotionStatus.allIsIntegral) orderPromotionStatus.allIsIntegral = false;
            //计算运费
            deliveryFeeCharger.charge(Lists.newArrayList(richSkusByShop), richOrder.getReceiverInfo());

            List<Promotion> shipmentPromotions = qualifiedPromotions.findShipmentPromotions(richSkusByShop, buyer);
            richSkusByShop.setShipmentPromotions(shipmentPromotions);
            if (richSkusByShop.getShipmentPromotionId() == null) {
                Long defaultShipmentPromotionId = promotionPicker.defaultShipmentPromotion(shipmentPromotions);
                richSkusByShop.setShipmentPromotionId(defaultShipmentPromotionId);
            }

            promotionExecutor.processShipment(richOrder.getBuyer(), richOrder.getReceiverInfo(), richSkusByShop);
            richSkusByShop.setFee(richSkusByShop.getFee() + MoreObjects.firstNonNull(richSkusByShop.getShipFee(), 0));
            richSkusByShop.setOriginFee(richSkusByShop.getOriginFee() + MoreObjects.firstNonNull(richSkusByShop.getShipFee(), 0));

            orderPromotionStatus.totalFee += richSkusByShop.getFee();
        }
    }

    /**
     * 计算单品级订单的优惠卷利润
     *
     * @param orderPromotionStatus 全局订单状态
     * @param richSkusByShop       整个店铺级订单
     * @param emptyForIntegral     如果为空则是积分订单
     * @return 这个店铺级订单的价格
     */
    private Long calculateSkuOrderPromotion(OrderPromotionStatus orderPromotionStatus, RichSkusByShop richSkusByShop, LinkedList<Boolean> emptyForIntegral) {
        final Shop shop = richSkusByShop.getShop();
        Long shopOrderOriginFee = 0L;
        //已选择的商品券
        Set<Long> hasSelectedItemCouponIds = new HashSet<>();
        CommonUser buyer = orderPromotionStatus.buyer;
        for (RichSku richSku : richSkusByShop.getRichSkus()) {
            refillWeShopPrice(buyer, richSku, richSkusByShop.getWeShop());
            refillMemberPrice(buyer, richSku);
            richSku.setTax(taxChecker.getTax(richSku.getSku(), richSku.getQuantity()));
            // after tax calculate
            recalculateTax(richSku);
            List<Promotion> promotions = PromotionView.from(qualifiedPromotions.findSkuPromotions(richSku, buyer, shop));
            // 限定保证一个商品券只能在一个店铺使用一张
            Iterables.removeIf(promotions, promotion -> Objects.isNull(promotion) || hasSelectedItemCouponIds.contains(promotion.getId()));

            Long selectedPromotionId = richSku.getPromotionId();
            Map<Long, Promotion> skuPromotionsByIdIndex = indexPromotionsById(promotions);

            if (selectedPromotionId == null || !skuPromotionsByIdIndex.containsKey(selectedPromotionId)) {
                Long defaultPromotionId = promotionPicker.defaultSkuPromoiton(promotions);
                // 如果是第一次进入这个页面则需要推荐优惠卷 不然则选择使用
                if (Suggest.One.SUGGEST_PROMOTION.get()) {
                    richSku.setPromotionId(defaultPromotionId);
                }
            }
            // preview the discount
            promotions = previewDiscount(buyer, richSkusByShop.getShop(), richSku, promotions);
            richSku.setSkuPromotions(PromotionView.from(promotions));
            CoupCount.Coup.setLimit();
            promotionExecutor.processSkuOrder(buyer, richSkusByShop.getShop(), richSku);
            CoupCount.Coup.closeLimit();
            // set the promotion discount fee look like the real one
            //  积分商品四舍五入 不允许0积分
            if ((richSku.getItem().getType() > 2)) {
                richSku.setFee((richSku.getFee() / 100) * 100 + ((richSku.getFee() % 100 >= 50) ? 100 : 0));
                if (richSku.getFee() == 0L)
                    richSku.setFee(100L);
            } else {
                if (emptyForIntegral.isEmpty())
                    emptyForIntegral.add(false);
            }

            shopOrderOriginFee += richSku.getFee();
            // 重新读回优惠是否可用
            selectedPromotionId = richSku.getPromotionId();

            if (selectedPromotionId != null) {
                Promotion selectedPromotion = skuPromotionsByIdIndex.get(selectedPromotionId);
                if (isSkuOrderLevelCoupon(selectedPromotion)) {
                    hasSelectedItemCouponIds.add(selectedPromotionId);
                }
            }

        }
        return shopOrderOriginFee;
    }

    private List<Promotion> previewDiscount(CommonUser buyer, Shop shop, RichSku richSku, List<Promotion> promotions) {
        List<Promotion> viewPromotion = new LinkedList<>();
        RichSku sku = new RichSku();
        BeanUtils.copyProperties(richSku, sku);
        for (Promotion origin : promotions) {
            Promotion p = new Promotion();
            BeanUtils.copyProperties(origin, p);
            // calculate the sku real reduce fee
            sku.setPromotionId(p.getId());
            promotionExecutor.processSkuOrder(buyer, shop, sku);
            String reduceFee = sku.getOriginFee() - sku.getFee() + "";
            p.getBehaviorParams().put("reduceFee", reduceFee);
            viewPromotion.add(p);
        }
        return viewPromotion;
    }

    /**
     * 查找订单的最优优惠方案
     *
     * @param richOrder 订单内容
     */
    public void tryGetMaxDiscountOrder(RichOrder richOrder) {
        Map<Long, Shop> shopMapByShopId = new HashMap<>();
        richOrder.getRichSkusByShops().forEach(richSkusByShop -> shopMapByShopId.put(richSkusByShop.getShop().getId(), richSkusByShop.getShop()));
        Consumer<RichOrder> calAllOrderDiscount = promotionExecutor::processRichOrder;
        Consumer<RichSkusByShop> calShopOrderDiscount = order -> promotionExecutor.processShopOrder(richOrder.getBuyer(), order);
        Consumer<RichSku> calSkuOrderDiscount = order -> promotionExecutor.processSkuOrder(richOrder.getBuyer(), shopMapByShopId.get(order.getSku().getShopId()), order);
        java.util.function.Function<Long, Integer> getPromotionCount = promotionId ->
                Optional.ofNullable(userPromotionReadService.findByUserIdAndPromotionId(richOrder.getBuyer().getId(), promotionId).getResult())
                        .orElse(new ArrayList<>()).stream().filter(userPromotion -> userPromotion.getStatus() > 0 && userPromotion.getAvailableQuantity() > 0)
                        .map(UserPromotion::getAvailableQuantity).reduce(Integer::sum).orElse(0);
        getMaxDiscountOrder(calAllOrderDiscount, calShopOrderDiscount, calSkuOrderDiscount, getPromotionCount, richOrder);
    }

    /**
     * 试图获取
     *
     * @param calAllOrderDiscount  计算总订单优惠数据
     * @param calShopOrderDiscount 计算店铺订单优惠数据
     * @param calSkuOrderDiscount  计算单品订单优惠数据
     * @param richOrder            订单
     */
    private void getMaxDiscountOrder(Consumer<RichOrder> calAllOrderDiscount
            , Consumer<RichSkusByShop> calShopOrderDiscount
            , Consumer<RichSku> calSkuOrderDiscount
            , Function<Long, Integer> getPromotionCount
            , RichOrder richOrder) {
        Map<Long, Integer> promotionCount = new HashMap<>();
        List<Consumer<Long>> promotionAccepts = new ArrayList<>();
        java.util.function.Function<List<Promotion>, List<Long>> getPromotionIds = (list) -> {
            if (list == null || list.isEmpty()) return new ArrayList<>();
            return list.stream().map(Promotion::getId).collect(Collectors.toList());
        };
        java.util.function.Function<Object, Consumer<Long>> promotionAcceptMaker = (obj) -> (promotionId) -> {
            if (obj instanceof RichSku) ((RichSku) obj).setPromotionId(promotionId);
            if (obj instanceof RichSkusByShop) ((RichSkusByShop) obj).setPromotionId(promotionId);
            if (obj instanceof RichOrder) ((RichOrder) obj).setPromotionId(promotionId);
        };
        List<List<Long>> promotionIds = new ArrayList<>();
        List<Long> cacheList;
        cacheList = new ArrayList<>(getPromotionIds.apply(richOrder.getPromotions()));
        if (!cacheList.isEmpty()) {
            promotionAccepts.add(promotionAcceptMaker.apply(richOrder));
            promotionIds.add(cacheList);
        }
        for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
            cacheList = getPromotionIds.apply(richSkusByShop.getShopPromotions());
            if (!cacheList.isEmpty()) {
                promotionAccepts.add(promotionAcceptMaker.apply(richSkusByShop));
                promotionIds.add(cacheList);
            }
            for (RichSku richSku : richSkusByShop.getRichSkus()) {
                cacheList = (getPromotionIds.apply(richSku.getSkuPromotions()));
                if (!cacheList.isEmpty()) {
                    promotionAccepts.add(promotionAcceptMaker.apply(richSku));
                    promotionIds.add(cacheList);
                }
            }
        }
        for (Long promotionId : promotionIds.stream().reduce(new ArrayList<>(), (left, right) -> {
            left.addAll(right);
            return left;
        })) {
            promotionCount.put(promotionId, getPromotionCount.apply(promotionId));
        }
        Random random = new Random();
        Supplier<List<List<Integer>>> genIgnite = () ->
                Stream.generate(() -> random.ints(promotionAccepts.size()).boxed().collect(Collectors.toList())).limit(20).collect(Collectors.toList());
        java.util.function.Function<List<Integer>, Integer> discountCalculator = (selections) -> {
            Map<Long, Integer> countMap = new HashMap<>();
            for (int i = 0; i < selections.size(); i++) {
                Integer size = promotionIds.get(i).size();
                Long promotionId = promotionIds.get(i).get(selections.get(i) % size);
                countMap.put(promotionId, countMap.getOrDefault(promotionId, 0) + 1);
                if (countMap.get(promotionId) > promotionCount.get(promotionId)) {
                    return -9999;
                }
                promotionAccepts.get(i).accept(promotionId);
            }
            long originFee = 0L;
            for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
                for (RichSku richSkus : richSkusByShop.getRichSkus()) {
                    originFee += (long) richSkus.getSku().getPrice() * richSkus.getQuantity();
                    calSkuOrderDiscount.accept(richSkus);
                }
                //richSkusByShop.getRichSkus().forEach(richSku -> promotionExecutor.processSkuOrder(richOrder.getBuyer(), richSkusByShop.getShop(), richSku));
                calShopOrderDiscount.accept(richSkusByShop);
                //promotionExecutor.processShopOrder(richOrder.getBuyer(), richSkusByShop);
            }
            calAllOrderDiscount.accept(richOrder);
            // promotionExecutor.processRichOrder(richOrder)
            // return richOrder.getOriginFee().intValue() - richOrder.getFee().intValue()
            return (int) originFee - richOrder.getFee().intValue();
        };
        GenerateSelector<Integer> promotionSelector = new GenerateSelector<>();
        promotionSelector.setGenInit(genIgnite);
        boolean found = promotionSelector.run(discountCalculator, 20).stream()
                .map(GenerateSelector.Result::getSource)
                .findFirst()
                .map(discountCalculator)
                .isPresent();
        if (!found) {
            log.warn("{} failed to find the best promotion for this order", LogUtil.getClassMethodName());
        }
    }

    /**
     * 根据微分销richOrder来确定优惠信息, 给微分销订单预览页面使用
     * <p>
     * 各种级别的营销活动信息会记录在richOrder对应的地方, 最后还会返回默认的或者确定使用的营销活动概览
     *
     * @param richOrder 订单相关的信息
     */
    public void composeDistributionPromotions(RichOrder richOrder) {
        composePromotions(richOrder);
    }

    private boolean isSkuOrderLevelCoupon(Promotion promotion) {
        return PromotionType.from(promotion.getType()) == PromotionType.ITEM_COUPON;
    }

    private Map<Long, Promotion> indexPromotionsById(List<Promotion> promotions) {
        if (CollectionUtils.isEmpty(promotions)) {
            return new HashMap<>();
        }
        return Maps.uniqueIndex(promotions, Promotion::getId);
    }

    /**
     * calculate for tax after insert operation
     *
     * @param richSku sku order
     */
    private void recalculateTax(RichSku richSku) {
        if (Objects.isNull(richSku.getWeShopItem())) {
            return;
        }
        Optional<WeShopSku> weShopSku = Optional.ofNullable(richSku.getWeShopSku());
        if (weShopSku.map(WeShopSku::getTaxSellerBear).orElse(false)) {
            richSku.setTax(0L);
        }
    }

    /**
     * 设置会员价格
     *
     * @param buyer   买家
     * @param richSku 包含用于计算Sku价格的单品
     */
    private void refillMemberPrice(BaseUser buyer, RichSku richSku) {
        Integer originPrice = richSku.getSku().getPrice();
        membershipPriceChecker.check(richSku.getSku(), Optional.ofNullable(richSku.getPid()).orElse(buyer.getId()));
        richSku.setDiffFee(Optional.ofNullable(richSku.getDiffFee()).orElse(0)
                + originPrice - richSku.getSku().getPrice()
        );
    }

    /**
     * 设置微店价格
     *
     * @param buyer   买家
     * @param richSku sku
     * @param weShop  微店
     */
    private void refillWeShopPrice(BaseUser buyer, RichSku richSku, WeShop weShop) {
        if (Objects.isNull(weShop)) {
            return;
        }
        Optional<WeShopSku> weShopSku = Optional.ofNullable(richSku.getWeShopSku());
        if (weShopSku.isEmpty()) {
            return;
        }
        Sku weShopSkuImpact = new Sku();
        BeanUtils.copyProperties(richSku.getSku(), weShopSkuImpact);
        Long finalPrice = weShopSku.map(WeShopSku::getPrice).orElseGet(() -> weShopSku.get().getDiffPrice() + weShopSkuImpact.getPrice());
        weShopSkuImpact.setPrice(finalPrice.intValue());

        weShopSkuImpact.setPrice(finalPrice.intValue());
        log.debug("{} buyer[{}] buy sku[{}] at weShop[{}] from shop[{}] at final price[{}]", LogUtil.getClassMethodName(),
                buyer.getId(),
                weShopSkuImpact.getId(),
                weShop.getId(),
                weShopSkuImpact.getShopId(),
                finalPrice);
        richSku.setSku(weShopSkuImpact);
    }

    public enum Suggest {
        One;
        public final ThreadLocal<Boolean> SUGGEST_PROMOTION = ThreadLocal.withInitial(() -> true);
    }

    static class OrderPromotionStatus {
        CommonUser buyer;
        boolean allIsIntegral = true;
        Long totalFee = 0L;
        // 已经使用光的订单级别优惠卷
        Set<Long> usedShopPromotionCoupIdSet = new HashSet<>();
    }
}
