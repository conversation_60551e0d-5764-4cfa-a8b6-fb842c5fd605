package moonstone.promotion.component;

import com.google.common.base.MoreObjects;
import moonstone.common.model.CommonUser;
import moonstone.order.dto.RichOrder;
import moonstone.order.model.GatherOrder;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.promotion.dto.PromotionResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 支付单计算器
 * Author:cp
 * Created on 8/3/16.
 */
@Component
public class PaymentCharger {

    private final PromotionExecutor promotionExecutor;

    @Autowired
    public PaymentCharger(PromotionExecutor promotionExecutor) {
        this.promotionExecutor = promotionExecutor;
    }

    /**
     * 支付单计算
     *
     * @param shopOrders  主订单列表
     * @param buyer       买家
     * @param promotionId 平台级别的营销id
     * @return 结算结果
     */
    public PromotionResult chargeForShopOrders(List<ShopOrder> shopOrders, CommonUser buyer, Long promotionId) {
        long originFee = 0;
        int shipFee = 0;
        for (ShopOrder shopOrder : shopOrders) {
            originFee += shopOrder.getFee();
            shipFee += MoreObjects.firstNonNull(shopOrder.getShipFee(), 0);
        }

        PromotionResult promotionResult = charge(buyer, originFee - shipFee, promotionId);
        promotionResult.setFee(promotionResult.getFee() + shipFee);
        return promotionResult;
    }

    /**
     * 支付单计算
     *
     * @param skuOrders   子订单列表
     * @param buyer       买家
     * @param promotionId 平台级别的营销id
     * @return 结算结果
     */
    public PromotionResult chargeForSkuOrders(List<SkuOrder> skuOrders, CommonUser buyer, Long promotionId) {
        long originFee = 0;
        long shipFee = 0;
        for (SkuOrder skuOrder : skuOrders) {
            originFee += skuOrder.getFee();
            shipFee += MoreObjects.firstNonNull(skuOrder.getShipFee(), 0L);
        }

        PromotionResult promotionResult = charge(buyer, originFee - shipFee, promotionId);
        promotionResult.setFee(promotionResult.getFee() + shipFee);
        return promotionResult;
    }

    private PromotionResult charge(CommonUser buyer, long originFee, Long promotionId) {
        RichOrder richOrder = new RichOrder();
        richOrder.setOriginFee(originFee);
        richOrder.setBuyer(buyer);
        richOrder.setPromotionId(promotionId);

        promotionExecutor.processRichOrder(richOrder);

        PromotionResult promotionResult = new PromotionResult();
        promotionResult.setOriginFee(originFee);
        promotionResult.setFee(MoreObjects.firstNonNull(richOrder.getFee(), originFee));
        promotionResult.setDiscount(richOrder.getDiscount());
        return promotionResult;
    }


    public PromotionResult chargeForGatherOrders(List<GatherOrder> gatherOrderList, CommonUser buyer, Long promotionId) {
        PromotionResult promotionResult = new PromotionResult();
        // todo: 计算promotion价格
        long originFee = 0L;
        long fee = 0L;
        for (GatherOrder gatherOrder : gatherOrderList) {
            originFee += gatherOrder.getOriginFee();
            fee += gatherOrder.getFee();
        }
        promotionResult.setOriginFee(originFee);
        promotionResult.setFee(fee);
        promotionResult.setDiscount(0);
        return promotionResult;
    }
}
