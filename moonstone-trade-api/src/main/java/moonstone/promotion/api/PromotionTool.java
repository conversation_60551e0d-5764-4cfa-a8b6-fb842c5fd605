/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.api;

import moonstone.promotion.dto.PromotionLevel;

import java.io.Serializable;

/**
 * 营销工具,  从PromotionDef中组装出来
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-31
 */
public abstract class PromotionTool<T extends Behavior> implements Serializable {

    private static final long serialVersionUID = 3085238769384704823L;

    /**
     * 营销工具的唯一标识符, 同PromotionDefId
     *
     * @return 营销工具的唯一标识符
     */
    public abstract Long id();

    /**
     * 营销活动的级别
     *
     * @return 营销活动的级别
     */
    public PromotionLevel level() {
        T behaviorInstance = behavior();
        return PromotionLevel.fromBehavior(behaviorInstance);
    }


    /**
     * 营销针对的用户范围
     *
     * @return 用户范围
     */
    public abstract UserScope userScope();

    /**
     * 营销针对的sku范围
     *
     * @return sku范围
     */
    public abstract SkuScope skuScope();

    /**
     * 营销方式, 是SkuOrderBehavior, ShopOrderBehavior, RichOrderBehavior, ShipmentFeeBehavior中的一种
     *
     * @return 营销方式
     */
    public abstract T behavior();
}
