/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.api;

import moonstone.order.dto.RichSku;
import moonstone.promotion.dto.PromotionContext;

import java.util.Map;

/**
 * sku级别的营销的结果, 如打折, 送券等
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-31
 */
public interface SkuOrderBehavior extends Behavior {

    /**
     * 从参数中析取sku订单的营销信息,主要用于在商品详情页显示sku的营销信息
     *
     * @param richSku          sku订单
     * @param promotionContext 营销执行上下文
     * @param conditionParams  营销方式涉及的条件参数
     * @param behaviorParams   营销方式涉及的参数
     * @return sku订单的营销信息
     */
    Map<String, Object> extractSkuOrderPromotionInfo(RichSku richSku, PromotionContext promotionContext,
                                                     Map<String, String> conditionParams, Map<String, String> behaviorParams);

    /**
     * 判断sku级别的营销是否满足执行条件
     *
     * @param richSku          sku订单
     * @param promotionContext 营销执行上下文
     * @param conditionParams  营销方式涉及的条件参数
     * @return 是否满足
     */
    boolean condition(RichSku richSku, PromotionContext promotionContext, Map<String, String> conditionParams);

    /**
     * sku级别营销的结果, 如果影响支付金额, 会设置richSku的fee字段,以及可能的discount, integral, balance等字段,
     * <p>
     * 注意, 不要有任何持久化操作!!
     *
     * @param richSku          sku订单
     * @param promotionContext 营销执行上下文
     * @param behaviorParams   营销方式涉及的参数
     */
    void execute(RichSku richSku, PromotionContext promotionContext, Map<String, String> behaviorParams);
}
