/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.api;

import moonstone.common.model.CommonUser;
import moonstone.shop.model.Shop;

import java.util.Map;

/**
 * 营销面向的客户群, 如店铺高级会员
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-31
 */
public interface UserScope {

    /**
     * 判断当前买家用户是否适用本营销活动
     *
     * @param commonUser      当前买家用户
     * @param shop            当前店铺
     * @param userScopeParams 参数
     * @return 是否适用
     */
    boolean apply(CommonUser commonUser, Shop shop, Map<String, String> userScopeParams);

    /**
     * 检查优惠卷格式是否正确
     *
     * @param userScopeParams 优惠卷范围
     * @return 正确或者错误
     */
    default boolean valid(Map<String, String> userScopeParams) {
        return true;
    }
}
