package moonstone.promotion.bricks;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.order.dto.RichSku;
import moonstone.promotion.annotation.PromotionBrick;
import moonstone.promotion.api.BrickType;
import moonstone.promotion.api.CoupCount;
import moonstone.promotion.api.SkuOrderBehavior;
import moonstone.promotion.dto.PromotionContext;
import moonstone.promotion.enums.PromotionConditionParamIndex;
import moonstone.promotion.service.UserPromotionReadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Slf4j
@PromotionBrick(key = "sku-sub-store-only", type = BrickType.BEHAVIOR)
public class SubStoreOnlySkuOrderBehavior implements SkuOrderBehavior {

    @Resource
    private UserPromotionReadService userPromotionReadService;

    @Override
    public boolean affectPay() {
        return false;
    }

    @Override
    public boolean userOwn(CommonUser buyer, Long promotionId) {
        var response = userPromotionReadService.findByUserIdAndPromotionId(buyer.getId(), promotionId);
        if (!response.isSuccess()) {
            log.error("fail to find user promotion by userId={}, promotionId={}, cause:{}",
                    buyer.getId(), promotionId, response.getError());
            throw new RuntimeException(response.getError());
        }
        if (CollectionUtils.isEmpty(response.getResult())) {
            return false;
        }

        for (var userPromotion : response.getResult()) {
            if (userPromotion.inProcess() && userPromotion.getAvailableQuantity() > 0) {
                CoupCount.Coup.putCountIfAbsent(userPromotion.getPromotionId(), userPromotion.getAvailableQuantity());
                return true;
            }
        }

        return false;
    }

    @Override
    public Map<String, Object> extractSkuOrderPromotionInfo(RichSku richSku, PromotionContext promotionContext,
                                                            Map<String, String> conditionParams, Map<String, String> behaviorParams) {
        return null;
    }

    @Override
    public boolean condition(RichSku richSku, PromotionContext promotionContext, Map<String, String> conditionParams) {
        Integer minBoughtNum = getMinBoughtNum(conditionParams);
        return Objects.equals(richSku.getQuantity(), minBoughtNum);
    }

    @Override
    public void execute(RichSku richSku, PromotionContext promotionContext, Map<String, String> behaviorParams) {
        // 不影响支付金额，而是以佣金的形式返现给用户
    }

    private Integer getMinBoughtNum(Map<String, String> conditionParams) {
        try {
            if (CollectionUtils.isEmpty(conditionParams)) {
                throw new RuntimeException("营销条件参数为空");
            }

            String minBoughtNum = conditionParams.get(PromotionConditionParamIndex.limitedItemMinBoughtNum.getCode());
            if (StringUtils.isBlank(minBoughtNum)) {
                throw new RuntimeException("最小购买数量配置为空");
            }

            return Integer.valueOf(minBoughtNum);
        } catch (Exception ex) {
            log.error("SubStoreOnlySkuOrderBehavior.getMinBoughtNum error ", ex);
            throw new RuntimeException("获取限定商品最小购买数量失败");
        }
    }
}
