/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-06-16
 */
@Data
public class PromotionResult implements Serializable {
    private static final long serialVersionUID = 1443346777811572817L;

    /**
     * sku营销价格,用于单品营销
     */
    private Long skuPromotionPrice;

    /**
     * 优惠
     */
    private Integer discount;

    /**
     * 不含税费的原价
     */
    private Long originFee;

    /**
     * 含税费的实际金额
     */
    private Long fee;

    /**
     * 当表示sku级别优惠时用，表示不含税费的金额
     */
    private Long feeWithoutTax;
}
