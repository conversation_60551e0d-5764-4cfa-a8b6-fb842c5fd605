package moonstone.common.enums;

import com.google.common.base.Objects;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/11/5 10:48
 */
public enum RiskManagementTime {
    /**
     * 小时：hour
     * 分钟：minute
     * 秒：second
     * 天：day
     * 月：month
     * 年：year
     */


    /**
     * second
     */
    SECOND("second", "秒"),

    /**
     * minute
     */
    MINUTE("minute", "分"),

    /**
     * hour
     */
    HOUR("hour", "小时"),
    /**
     * day
     */
    DAY("day", "天"),
    /**
     * month
     */
    MONTH("month", "月"),
    /**
     * year
     */
    YEAR("year", "年"),

    /**
     * unknow
     */
    UNKNOW("unknow", "其他");

    private final String code;

    private final String name;

    RiskManagementTime(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String Code() {
        return this.code;
    }

    public String Name() {
        return this.name;
    }

    public static RiskManagementTime fromInt(String code) {
        for (RiskManagementTime orderFrom : RiskManagementTime.values()) {
            if (Objects.equal(orderFrom.code, code)) {
                return orderFrom;
            }
        }
        throw new IllegalArgumentException("unknown status: " + code);
    }

}
