package moonstone.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * author：书生
 */
@Getter
@AllArgsConstructor
public enum ShipmentStatusEnum {


    /**
     * 未发货
     */
    UN_SHIPPED(0, "未发货"),


    /**
     * 已发货
     */
    SHIPPED(1, "已发货"),

    /**
     * 发货完成
     */
    COMPLETED_SHIPMENTS(2, "已发货"),
    ;

    private final int code;

    private final String desc;


    public static String getDesc(int code) {
        for (ShipmentStatusEnum shipmentStatusEnum : ShipmentStatusEnum.values()) {
            if (shipmentStatusEnum.getCode() == code) {
                return shipmentStatusEnum.getDesc();
            }
        }
        return "";
    }
}
