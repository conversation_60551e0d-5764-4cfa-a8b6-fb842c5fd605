package moonstone.common.enums;

/**
 * 业务操作类型
 * 
 */
public enum OpBusinessType
{
    /**
     * 其它
     */
    OTHER(1,"其它"),

    /**
     * 新增
     */
    INSERT(2,"新增"),

    /**
     * 修改
     */
    UPDATE(3,"修改"),

    /**
     * 删除
     */
    DELETE(4,"删除"),

    /**
     * 授权
     */
    GRANT(5,"授权"),

    /**
     * 导出
     */
    EXPORT(6,"导出"),

    /**
     * 导入
     */
    IMPORT(7,"导入"),

    /**
     * 强退
     */
    FORCE(8,"强退"),

    /**
     * 清空
     */
    CLEAN(9,"清空"),
    /**
     * 登录
     */
    LOGIN(10,"登录");

    private final Integer code;

    private final String desc;

    OpBusinessType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public OpBusinessType getEnum(Integer code) {
        for (OpBusinessType i: OpBusinessType.values()) {
            if (i.code == code)
                return i;
        }
        return OTHER;
    }

    public static OpBusinessType getEnumByCode(Integer code) {
        for (OpBusinessType i: OpBusinessType.values()) {
            if (i.code == code)
                return i;
        }
        return OTHER;
    }

}
