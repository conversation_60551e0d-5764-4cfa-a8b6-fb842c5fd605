package moonstone.common.constants;

/**
 * <AUTHOR>
 */
public interface RoleContextKeys {

    /**
     * (商家或商家子账户的) 店铺ID
     */
    String SHOP_ID = "shopId";

    /**
     * (微分销店主) 微分销店铺ID
     */
    String WE_SHOP_ID = "weShopId";

    /**
     * 子账户用, 此子账户代表的主账户ID
     */
    String PRESENT_USER_ID = "presentUserId";

    /**
     * platform id, may contain as list json
     */
    String SOURCE_ID = "sourceId";
}
