package moonstone.common.constants;

/**
 * author：书生
 */
public class RedisKeyConstant {


    /**
     * 小程序审核通知前缀
     */
    public static final String SHOP_MINI_VERSION_AUDIT_MESSAGE_PREFIX = "shop:mini:version:audit:message:";


    /**
     * 生成订单利润异常数
     */
    public static final String GENERATE_ORDER_FORESEE_PROFIT_EXCEPTION_COUNT = "generate:order:foresee:profit:exception:count:";

    /**
     * 订单退款申请
     */
    public static final String ORDER_REFUND_APPLY_PREFIX = "order:refund:apply:";

    /**
     * 同意退款申请
     */
    public static final String REFUND_APPLY_AGREE_PREFIX = "refund:agree:apply:";

    /**
     * 驳回退款申请
     */
    public static final String REFUND_APPLY_REJECT_PREFIX = "refund:apply:reject:";

    /**
     * 上传退款快递单号
     */
    public static final String REFUND_UPLOAD_EXPRESS_INFO_PREFIX = "refund:upload:express:info:";


    /**
     * 退款处理
     */
    public static final String REFUND_HANDLE_PREFIX = "refund:handle:";

}
