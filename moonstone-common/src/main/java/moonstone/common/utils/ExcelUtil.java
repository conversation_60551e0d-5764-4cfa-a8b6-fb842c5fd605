package moonstone.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.util.ObjectUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;

/**
 * 导出excel的工具类
 * 全部采用string类型，如果需要加入其它类型需要修改工具方法
 *
 * @param <T>
 * <AUTHOR>
 * 2017年3月20日 下午5:01:22
 */
public class ExcelUtil<T> {
    Class<T> clazz;

    public ExcelUtil(Class<T> clazz) {
        this.clazz = clazz;
    }

    /**
     * 批量导入
     * 这里的批量导入指的是一个excel文件中有多个相同类型的sheet页
     * 注：记得关闭流
     *
     * @param input
     * @return
     * @throws Exception
     */
    public List<T> importBatch(InputStream input) throws Exception {
        List<T> newList = new ArrayList<T>();
        HSSFWorkbook workbook = new HSSFWorkbook(input);
        int sheets = workbook.getNumberOfSheets();
        if (sheets > 0) {
            HSSFSheet sheet = workbook.getSheetAt(0);
            if (null != sheet) {
                List<T> importProcessor = importProcessor(sheet);
                newList.addAll(importProcessor);
            }
        }
        return newList;
    }

    /**
     * 指定sheet页导入，如果不指定默认会选第一个sheet
     * 注：记得关闭流
     */
    public List<T> importExcel(String sheetName, InputStream input) throws Exception {
        HSSFWorkbook workbook = new HSSFWorkbook(input);
        HSSFSheet sheet = workbook.getSheet(sheetName);
        if (!sheetName.trim().equals("")) {
            sheet = workbook.getSheet(sheetName);// 如果指定sheet名,则取指定sheet中的内容.  
        }
        if (sheet == null) {
            sheet = workbook.getSheetAt(0); // 如果传入的sheet名不存在则默认指向第1个sheet.  
        }
        return importProcessor(sheet);
    }

    /**
     * 具体处理导入
     *
     * @param sheet
     * @return
     * @throws Exception
     */
    private List<T> importProcessor(HSSFSheet sheet) throws Exception {
        int maxCol = 0;
        List<T> list = new ArrayList<T>();
        int rows = sheet.getPhysicalNumberOfRows();
        if (rows > 0) {// 有数据时才处理
            List<Field> allFields = getMappedFiled(clazz, null);
            Map<Integer, Field> fieldsMap = new HashMap<>();// 定义一个map用于存放列的序号和field.
            Map<String, Field> fieldByNameMap = new HashMap<>();
            List<String> nameList = new ArrayList<>(16);
            for (Field field : allFields) {
                // 将有注解的field存放到map中.
                if (field.isAnnotationPresent(ExcelField.class)) {
                    ExcelField attr = field
                            .getAnnotation(ExcelField.class);
                    if (!attr.column().equals("") && maxCol >= 0) {
                        int col = getExcelCol(attr.column());// 获得列号
                        maxCol = Math.max(col, maxCol);
                        field.setAccessible(true);// 设置类的私有字段属性可访问.
                        fieldsMap.put(col, field);
                    } else {
                        fieldByNameMap.put(attr.name(), field);
                        field.setAccessible(true);// 设置类的私有字段属性可访问.
                        maxCol = -1;
                    }
                }
            }
            for (int i = 0; i < rows; i++) {// 从第2行开始取数据,默认第一行是表头.
                HSSFRow row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }
                // init the column
                if (i == 0) {
                    // add the name
                    for (int j = row.getFirstCellNum(); j <= row.getLastCellNum(); j++) {
                        if (row.getCell(j) != null) {
                            nameList.add(row.getCell(j).getStringCellValue());
                        }
                    }
                    continue;
                }
                int cellNum = maxCol >= 0 ? maxCol : row.getLastCellNum();
                T entity = null;
                for (int j = 0; j < Math.max(cellNum + 1, nameList.size()); j++) {
                    if (!nameList.isEmpty() && j >= nameList.size()){
                        break;
                    }
                    HSSFCell cell = row.getCell(j);
                    if (cell == null || cell.toString().isEmpty()) {
                        if (fieldByNameMap.get(nameList.get(j)) != null && fieldByNameMap.get(nameList.get(j)).getType() == BufferedImage.class) {
                            for (HSSFShape child : sheet.getDrawingPatriarch().getChildren()) {
                                Field field = fieldByNameMap.get(nameList.get(j));
                                if (entity == null) {
                                    entity = clazz.newInstance();
                                }
                                if (child.getAnchor() instanceof HSSFClientAnchor) {
                                    HSSFClientAnchor anchor = (HSSFClientAnchor) child.getAnchor();
                                    if (anchor.getCol1() == j && anchor.getRow1() == i) {
                                        field.set(entity, ImageIO.read(new ByteArrayInputStream(((HSSFPicture) child).getPictureData().getData())));
                                        break;
                                    }
                                }
                            }

                        }
                        continue;
                    }
                    CellType cellType = cell.getCellType();
                    String c = "";
                    if (cellType == CellType.NUMERIC) {
                        try {
                            BigDecimal s = new BigDecimal(cell.toString());
                            BigDecimal[] decimals = s.multiply(new BigDecimal("100")).divideAndRemainder(new BigDecimal("100"));
                            if (decimals.length == 2 && decimals[1].compareTo(BigDecimal.ZERO) == 0) {
                                DecimalFormat df = new DecimalFormat("0");
                                String cellValue = df.format(cell.getNumericCellValue());
                                c = String.valueOf(cellValue);
                            } else {
                                DecimalFormat df = new DecimalFormat("0.00");
                                String cellValue = df.format(cell.getNumericCellValue());
                                c = String.valueOf(cellValue);
                            }
                        } catch (Exception e) {
                            c = cell.toString();
                        }
                    } else if (cellType == CellType.BOOLEAN) {
                        c = String.valueOf(cell.getBooleanCellValue());
                    } else if (cellType == CellType.FORMULA) {
                        try {
                            c = String.valueOf(cell.getNumericCellValue());
                        } catch (Exception e) {
                            c = String.valueOf(cell.getStringCellValue());
                        }
                    } else {
                        c = !ObjectUtils.isEmpty(cell.getStringCellValue()) ? cell.getStringCellValue().trim() : "";
                    }
                    if (c == null || c.equals("")) {
                        continue;
                    }
                    entity = (entity == null ? clazz.newInstance() : entity);// 如果不存在实例则新建.
                    Field field;
                    if (maxCol == -1) {
                        field = fieldByNameMap.get(nameList.get(j));
                    } else {
                        field = fieldsMap.get(j);// 从map中得到对应列的field.
                    }
                    if (field == null) {
                        continue;
                    }
                    // 取得类型,并根据对象类型设置值.
                    Class<?> fieldType = field.getType();
                    if (String.class == fieldType) {
                        field.set(entity, StringUtils.trim(c));
                    } else if ((Integer.TYPE == fieldType)
                            || (Integer.class == fieldType)) {
                        field.set(entity, Integer.parseInt(c));
                    } else if ((Long.TYPE == fieldType)
                            || (Long.class == fieldType)) {
                        field.set(entity, Long.valueOf(c));
                    } else if ((Float.TYPE == fieldType)
                            || (Float.class == fieldType)) {
                        field.set(entity, Float.valueOf(c));
                    } else if ((Short.TYPE == fieldType)
                            || (Short.class == fieldType)) {
                        field.set(entity, Short.valueOf(c));
                    } else if ((Double.TYPE == fieldType)
                            || (Double.class == fieldType)) {
                        field.set(entity, Double.valueOf(c));
                    } else if (Character.TYPE == fieldType) {
                        if ((c != null) && (c.length() > 0)) {
                            field.set(entity, Character
                                    .valueOf(c.charAt(0)));
                        }
                    } else if (BufferedImage.class == fieldType) {
                        for (HSSFShape child : sheet.getDrawingPatriarch().getChildren()) {
                            if (child.getAnchor() instanceof HSSFClientAnchor) {
                                HSSFClientAnchor anchor = (HSSFClientAnchor) child.getAnchor();
                                if (anchor.getCol1() + 1 == j && anchor.getRow1() == i) {
                                    field.set(entity, ImageIO.read(new ByteArrayInputStream(((HSSFPicture) child).getPictureData().getData())));
                                    break;
                                }
                            }
                        }
                    }

                }
                if (entity != null) {
                    list.add(entity);
                }
            }
        }
        return list;
    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param sheetNames 工作表的名称
     * @param output     java输出流
     */
    public boolean exportExcel(List<T>[] lists, String[] sheetNames, OutputStream output) {
        if (lists.length != sheetNames.length) {
            return false;
        }
        HSSFWorkbook workbook = new HSSFWorkbook();// 产生工作薄对象
        for (int ii = 0; ii < lists.length; ii++) {
            List<T> list = lists[ii];
            String sheetName = sheetNames[ii];
            List<Field> fields = getMappedFiled(clazz, null);
            HSSFSheet sheet = workbook.createSheet();// 产生工作表对象
            workbook.setSheetName(ii, sheetName);
            HSSFRow row;
            HSSFCell cell;// 产生单元格
            HSSFCellStyle style = workbook.createCellStyle();
            style.setFillForegroundColor((short) 40);
            style.setFillBackgroundColor((short) 55);
            row = sheet.createRow(0);// 产生一行  
            for (Field field : fields) {
                ExcelField attr = field.getAnnotation(ExcelField.class);
                int col = getExcelCol(attr.column());// 获得列号  
                cell = row.createCell(col);// 创建列  
                cell.setCellType(CellType.STRING);// 设置列中写入内容为String类型
                cell.setCellValue(attr.name());// 写入列名  
                if (!attr.prompt().trim().equals("")) {
                    setHSSFPrompt(sheet, "", attr.prompt(), 1, 100, col, col);// 这里默认设了2-101列提示.
                }
                if (attr.combo().length > 0) {
                    setHSSFValidation(sheet, attr.combo(), 1, 100, col, col);// 这里默认设了2-101列只能选择不能输入.
                }
                cell.setCellStyle(style);
            }
            int startNo = 0;
            int endNo = list.size();
            for (int i = startNo; i < endNo; i++) {
                row = sheet.createRow(i + 1 - startNo);
                T vo = list.get(i);
                for (int j = 0; j < fields.size(); j++) {
                    Field field = fields.get(j);
                    field.setAccessible(true);
                    ExcelField attr = field.getAnnotation(ExcelField.class);
                    try {
                        if (attr.isExport()) {
                            cell = row.createCell(getExcelCol(attr.column()));
                            cell.setCellType(CellType.STRING);
                            cell.setCellValue(field.get(vo) == null ? ""
                                    : String.valueOf(field.get(vo)));
                        }
                    } catch (IllegalArgumentException e) {
                        e.printStackTrace();
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        try {
            output.flush();
            workbook.write(output);
            output.close();
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }

    }

    /**
     * 对list数据源将其里面的数据导入到excel表单
     *
     * @param sheetName 工作表的名称
     *                  每个sheet中数据的行数,此数值必须小于65536
     * @param output    java输出流
     */
    @SuppressWarnings("unchecked")
    public boolean exportExcel(List<T> list, String sheetName,
                               OutputStream output) {
        //此处 对类型进行转换
        List<T> ilist = new ArrayList<>();
        for (T t : list) {
            ilist.add(t);
        }
        List<T>[] lists = new ArrayList[1];
        lists[0] = ilist;

        String[] sheetNames = new String[1];
        sheetNames[0] = sheetName;

        return exportExcel(lists, sheetNames, output);
    }

    /**
     * 将EXCEL中A,B,C,D,E列映射成0,1,2,3,4
     *
     * @param col
     */
    public static int getExcelCol(String col) {
        col = col.toUpperCase();
        // 从-1开始计算,字母重1开始运算。这种总数下来算数正好相同。  
        int count = -1;
        char[] cs = col.toCharArray();
        for (int i = 0; i < cs.length; i++) {
            count += (cs[i] - 64) * Math.pow(26, cs.length - 1 - i);
        }
        return count;
    }

    /**
     * 设置单元格上提示
     *
     * @param sheet         要设置的sheet.
     * @param promptTitle   标题
     * @param promptContent 内容
     * @param firstRow      开始行
     * @param endRow        结束行
     * @param firstCol      开始列
     * @param endCol        结束列
     * @return 设置好的sheet.
     */
    public static HSSFSheet setHSSFPrompt(HSSFSheet sheet, String promptTitle,
                                          String promptContent, int firstRow, int endRow, int firstCol,
                                          int endCol) {
        // 构造constraint对象  
        DVConstraint constraint = DVConstraint
                .createCustomFormulaConstraint("DD1");
        // 四个参数分别是：起始行、终止行、起始列、终止列  
        CellRangeAddressList regions = new CellRangeAddressList(firstRow,
                endRow, firstCol, endCol);
        // 数据有效性对象  
        HSSFDataValidation data_validation_view = new HSSFDataValidation(
                regions, constraint);
        data_validation_view.createPromptBox(promptTitle, promptContent);
        sheet.addValidationData(data_validation_view);
        return sheet;
    }

    /**
     * 设置某些列的值只能输入预制的数据,显示下拉框.
     *
     * @param sheet    要设置的sheet.
     * @param textlist 下拉框显示的内容
     * @param firstRow 开始行
     * @param endRow   结束行
     * @param firstCol 开始列
     * @param endCol   结束列
     * @return 设置好的sheet.
     */
    public static HSSFSheet setHSSFValidation(HSSFSheet sheet,
                                              String[] textlist, int firstRow, int endRow, int firstCol,
                                              int endCol) {
        // 加载下拉列表内容  
        DVConstraint constraint = DVConstraint
                .createExplicitListConstraint(textlist);
        // 设置数据有效性加载在哪个单元格上,四个参数分别是：起始行、终止行、起始列、终止列  
        CellRangeAddressList regions = new CellRangeAddressList(firstRow,
                endRow, firstCol, endCol);
        // 数据有效性对象  
        HSSFDataValidation data_validation_list = new HSSFDataValidation(
                regions, constraint);
        sheet.addValidationData(data_validation_list);
        return sheet;
    }

    /**
     * 得到实体类所有通过注解映射了数据表的字段
     *
     * @return
     */
    @SuppressWarnings("rawtypes")
    private List<Field> getMappedFiled(Class clazz, List<Field> fields) {
        if (fields == null) {
            fields = new ArrayList<Field>();
        }

        Field[] allFields = clazz.getDeclaredFields();// 得到所有定义字段
        // 得到所有field并存放到一个list中.  
        for (Field field : allFields) {
            if (field.isAnnotationPresent(ExcelField.class)) {
                fields.add(field);
            }
        }
        if (clazz.getSuperclass() != null
                && !clazz.getSuperclass().equals(Object.class)) {
            getMappedFiled(clazz.getSuperclass(), fields);
        }

        return fields;
    }

    public static Sheet mergeSheet(Sheet sheet) {
        //递归 递归 必须重置递归环境
        //FP:STACK INTO LOOP - Row scan
        int stackScanR;
        Row scanRow = null;
        for (stackScanR = sheet.getFirstRowNum() + 1; stackScanR <= sheet.getLastRowNum(); stackScanR++) {
            scanRow = sheet.getRow(stackScanR);
            if (Objects.nonNull(scanRow))
                break;
        }
        // found nothing
        if (Objects.isNull(scanRow)) {
            return sheet;
        }
        //FP:STACK INTO LOOP - Cell scan
        boolean lastMerged = true;
        ArrayList<List<Row>> mergedRow = new ArrayList<>();
        ArrayList<List<Row>> newMergedRow = new ArrayList<>();
        ArrayList<Row> pack = new ArrayList<>(sheet.getLastRowNum() - sheet.getFirstRowNum() + 1);
        for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
            pack.add(sheet.getRow(i));
        }
        newMergedRow.add(new ArrayList<>(pack));
        pack.clear();
        for (int stackScanC = scanRow.getFirstCellNum(); stackScanC <= scanRow.getLastCellNum(); stackScanC++) {
            int mergeStartRow = -1;
            int mergeEndRow = -1;
            mergedRow.clear();
            mergedRow.addAll(newMergedRow);
            newMergedRow.clear();
            int cellScanR;
            if (mergedRow.isEmpty() || !lastMerged) {
                break;
            }
            lastMerged = false;
            for (List<Row> rowPack : mergedRow) {
                for (Row aimRow : rowPack) {
                    cellScanR = aimRow.getRowNum();
                    Cell aimCell = aimRow.getCell(stackScanC);
                    if (aimCell == null) {
                        break;
                    }
                    //rowPack首部跳过 因为是合并包内的第一个无论如何不会引发合并检测事件
                    //以此跳过复杂的检测与bug
                    if (rowPack.indexOf(aimRow) == 0) {
                        continue;
                    }
                    Row lastRow = sheet.getRow(cellScanR - 1);
                    if (lastRow != null) {
                        Cell lastRowCell = lastRow.getCell(stackScanC);
                        if (lastRowCell != null) {
                            String lastString = sheet.getRow(cellScanR - 1).getCell(stackScanC).getStringCellValue();
                            if (lastString == null || lastString.equals(aimCell.getStringCellValue())) {
                                if (mergeStartRow == -1) {
                                    mergeStartRow = cellScanR - 1;
                                }
                                if (rowPack.indexOf(aimRow) == rowPack.size() - 1) {
                                    //pack 的最后一个 必须合并
                                    //pack 尾部因为合并动作是由不相同数据触发, 所以为了避免污染下一个pack这里必须合并
                                    mergeEndRow = aimRow.getRowNum();
                                    //合到当前row
                                    if (mergeEndRow <= mergeStartRow) {//拒绝单行合并
                                        mergeStartRow = -1;
                                        mergeEndRow = -1;
                                        continue;
                                    }
                                    sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, mergeEndRow, stackScanC, stackScanC));
                                    for (int i = mergeStartRow; i <= mergeEndRow; i++)//合并后产生新的pack
                                        pack.add(sheet.getRow(i));
                                    newMergedRow.add(new ArrayList<>(pack));
                                    pack.clear();
                                    lastMerged = true;
                                    mergeStartRow = -1;
                                    mergeEndRow = -1;
                                }
                                continue;
                            }
                            if (mergeStartRow == -1) {
                                continue;
                            } else {
                                int lastRowIndex = rowPack.indexOf(aimRow) - 1;
                                mergeEndRow = rowPack.get(lastRowIndex).getRowNum();
                                //mergeEndRow = cellScanR - 1;
                                if (mergeEndRow <= mergeStartRow) {
                                    mergeStartRow = -1;
                                    mergeEndRow = -1;
                                    continue;
                                }
                                sheet.addMergedRegion(new CellRangeAddress(mergeStartRow, mergeEndRow, stackScanC, stackScanC));
                                for (int i = mergeStartRow; i <= mergeEndRow; i++)
                                    pack.add(sheet.getRow(i));
                                newMergedRow.add(new ArrayList<>(pack));
                                pack.clear();
                                lastMerged = true;
                                mergeStartRow = -1;
                                mergeEndRow = -1;
                            }
                        }
                    }

                }// inside for aimRow
            }//outside for rowPack
        }
        return sheet;
    }
}