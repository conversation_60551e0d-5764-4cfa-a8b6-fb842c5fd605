package moonstone.common.utils;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.model.CommonPaging;
import moonstone.common.model.Either;

import java.util.List;
import java.util.Objects;


@Slf4j
public abstract class APIRespWrapper {
    /**
     * 将Response转换为AppResponse
     *
     * @param res 应用内response
     * @param <T> 类型
     * @return AppResponse
     */
    public static <T> APIResp<T> wrap(Response<T> res) {
        return res.isSuccess() ? APIResp.ok(res.getResult()) : error(res.getError());
    }

    /**
     * 将Response转换为AppResponse
     *
     * @param res 应用内response
     * @param <T> 类型
     * @return AppResponse
     */
    public static <T> APIResp<T> wrap(Either<T> res) {
        log.error("{} wrap error out", LogUtil.getClassMethodName(), res.getError());
        return res.isSuccess() ? APIResp.ok(res.take()) : error(res.getErrorMsg());
    }

    public static <T> APIResp<T> error(String error) {
        return Objects.equals(error, "user.not.login") ? APIResp.notLogin() : APIResp.error(error);
    }

    /**
     * 将Response[Paging]转换为AppPaging
     *
     * @param pagingRes 切页类型
     * @param page      第几页
     * @param size      大小
     * @param <T>       类型
     * @return 切页完毕的
     */
    public static <T> APIResp<List<T>> wrapPaging(Response<Paging<T>> pagingRes, int page, int size) {
        if (pagingRes.isSuccess()) {
            int totalCount = pagingRes.getResult().getTotal().intValue();
            return CommonPaging.sliceOf(totalCount / size + ((totalCount / size) * size < totalCount ? 1 : 0), pagingRes.getResult().getTotal().intValue(), page, size, pagingRes.getResult().getData());
        }
        return error(pagingRes.getError());
    }

    public static <T> APIResp<List<T>> wrapPaging(Either<Paging<T>> pagingRes, int page, int size) {
        if (pagingRes.isSuccess()) {
            int totalCount = pagingRes.getResult().getTotal().intValue();
            return CommonPaging.sliceOf(totalCount / size + ((totalCount / size) * size < totalCount ? 1 : 0), pagingRes.getResult().getTotal().intValue(), page, size, pagingRes.getResult().getData());
        }
        log.error("{} wrap error out", LogUtil.getClassMethodName(), pagingRes.getError());
        return error(pagingRes.getErrorMsg());
    }

    /**
     * 描述一下解包Either的结果
     *
     * @param either  带有错误信息的Either
     * @param reason  原因
     * @param objects 参数
     * @param <T>     类
     * @return 结果
     */
    public static <T> APIResp<T> describe(Either<T> either, String reason, Object... objects) {
        return either.map(APIResp::ok)
                .logException(e -> log.error((reason == null ? LogUtil.getClassMethodName(3) : LogUtil.getClassMethodName(3) + reason), concat(objects, e)))
                .elseMap(e -> e instanceof NullPointerException ? error(Translate.of("空指针异常")) : error(e.getMessage()));
    }

    private static Object[] concat(Object[] a, Object b) {
        Object[] c = new Object[a.length + 1];
        System.arraycopy(a, 0, c, 0, a.length);
        c[c.length - 1] = b;
        return c;
    }
}
