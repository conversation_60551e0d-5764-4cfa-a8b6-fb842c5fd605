package moonstone.common.utils;

import moonstone.common.api.SqlInitDatabaseGenerator;

import java.lang.annotation.*;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.function.Function;

public interface MybatisGenerator {

    /**
     * copy and update the xml, replace all the element that use the same id
     * @param exists exists xml
     * @param xml the new generation mybatis-xml
     * @return updated(exists) xml
     */
    static XML mergeMybatisXML(XML exists, XML xml) {
        Map<String, Map<String, XML>> elementMap = new HashMap<>();
        for (Map.Entry<String, List<XML>> e : xml.elements().entrySet()) {
            var m = elementMap.computeIfAbsent(e.getKey(), name -> new HashMap<>());
            for (XML one : e.getValue()) {
                if (one.getAttribute("id") != null) {
                    var id = one.getAttribute("id");
                    m.put(id, one);
                }
            }
        }
        Set<XML> added = new HashSet<>();
        // copy and update
        Map<String, List<XML>> nodes = new LinkedHashMap<>(elementMap.size());
        for (Map.Entry<String, List<XML>> e : exists.elements().entrySet()) {
            if (elementMap.containsKey(e.getKey())) {
                var k = e.getKey();
                var c = elementMap.get(k);
                List<XML> iNodes = new ArrayList<>();
                for (XML one : e.getValue()) {
                    var id = one.getAttribute("id");
                    if (id == null) {
                        iNodes.add(one);
                        continue;
                    }
                    var i = c.getOrDefault(id, one);
                    // if the name exists
                    added.add(i);
                    iNodes.add(i);
                }
                nodes.put(e.getKey(), iNodes);
            } else {
                nodes.put(e.getKey(), e.getValue());
            }
        }
        var updateXML = XML.xml(exists.name());
        List<XML> notAddedElement = new ArrayList<>();
        elementMap.values().stream().map(Map::values).forEach(notAddedElement::addAll);
        updateXML.elements().putAll(nodes);
        notAddedElement.stream().filter(i -> !added.contains(i))
                .forEach(updateXML::node);
        for (String i : exists.getAllAttribute()) {
            updateXML.attr(i, exists.getAttribute(i));
        }
        return updateXML;
    }

    static String generateRepository(Class<?> type, Function<String, String> nameChange) {
        var repName = nameChange.apply(type.getName());
        var b = new StringBuilder();
        b.append("package ").append(repName, 0, repName.lastIndexOf(".")).append(";");
        b.append("\n\n" +
                        "import org.apache.ibatis.annotations.Mapper;\n\n")
                .append("@Mapper\n")
                .append("public interface ");

        b.append(repName.substring(repName.lastIndexOf('.') + 1)).append(" {\n}");
        return b.toString();
    }

    static String generateInsert(Function<String, String> tableNameConvert, Class<?> type) {
        // get the field first
        Set<String> name = new HashSet<>();
        for (Field declaredField : type.getDeclaredFields()) {
            name.add(declaredField.getName());
        }
        var builder = new StringBuilder("insert into ");
        builder.append(tableNameConvert.apply(type.getSimpleName())).append("(");
        List<String> fieldName = new ArrayList<>();
        var head = true;
        for (Method method : type.getMethods()) {
            var propertyName = method.getName();
            if (propertyName.length() > 3 && propertyName.startsWith("get")) {
                propertyName = propertyName.substring(3, 4).toLowerCase(Locale.ROOT)
                        + propertyName.substring(4);
            }
            if (!name.contains(propertyName)) {
                continue;
            }
            if (propertyName.equals("id")) {
                continue;
            }
            fieldName.add(propertyName);
            if (!head) builder.append(", ");
            builder.append(camelNameConvert(propertyName));
            head = false;
        }
        builder.append(") values (");
        head = true;
        for (String field : fieldName) {
            if (!head) builder.append(", ");
            if (field.equals("createdAt") || field.equals("updatedAt")) {
                builder.append("now()");
            } else {
                builder.append("#{").append(field).append("}");
            }
            head = false;
        }
        builder.append(");");
        return builder.toString();
    }

    static String camelNameConvert(String i) {

        var b = new StringBuilder();
        var h = true;
        for (char c : i.toCharArray()) {
            if (Character.isUpperCase(c)) {
                if (!h) {
                    b.append("_");
                }
                b.append(Character.toLowerCase(c));
            } else {
                b.append(c);
            }
            h = false;
        }
        return b.toString();

    }

    static XML generate(Function<String, String> nameConvert, String className) throws Exception {
        return generate(nameConvert, i -> i, Class.forName(className));
    }

    static XML update(Class<?> type) throws Exception {
        var nameConvert = (Function<String, String>) name -> {
            var builder = new StringBuilder();
            boolean head = true;
            for (char c : name.toCharArray()) {
                if (Character.isUpperCase(c) && !head) {
                    builder.append("_");
                }
                if (head) head = false;
                builder.append(Character.toLowerCase(c));
            }
            return builder.toString();
        };
        var getter = methods(type);
        var update = XML.xml("sql")
                .attr("id", "update-sql");
        getter.forEach((name, _m) -> {
            if (name.equals("id")) {
                return;
            }
            update.node(XML.xml("if")
                    .attr("test", name + " != null")
                    .node(XML.content(" " + nameConvert.apply(name) + " = #{" + name + "}, ")));
        });
        return update;
    }

    static Map<String, Method> methods(Class<?> type) {
        var fieldMap = new HashMap<String, Field>();
        for (Field declaredField : type.getDeclaredFields()) {
            fieldMap.put(declaredField.getName(), declaredField);
        }
        var getter = new HashMap<String, Method>();
        for (Method declaredMethod : type.getDeclaredMethods()) {
            if (declaredMethod.getParameterCount() == 0) {
                // getter must
                var name = declaredMethod.getName();
                if (name.startsWith("get") && name.length() > 3) {
                    name = name.substring(3, 4).toLowerCase(Locale.ROOT) + name.substring(4);
                    getter.put(name,
                            declaredMethod);
                } else if (fieldMap.containsKey(name)) {
                    getter.put(name, declaredMethod);
                }
            }
        }
        return getter;
    }

    static XML generate(Function<String, String> tableNameConvert,
                        Function<String, String> namespaceConvert,
                        Class<?> type) throws Exception {
        var getter = methods(type);
        // here we got the all getter and setter
        // use the max constructor
        Constructor<?> init = null;
        if (type.getConstructors().length <= 1 || type.isRecord()) {
            init = type.getConstructors()[0];
        } else {
            for (Constructor<?> constructor : type.getConstructors()) {
                if (init == null) {
                    init = constructor;
                } else if (init.getParameterCount() < constructor.getParameterCount()) {
                    init = constructor;
                }
            }
        }
        assert init != null;

        // is all construct we need follow the name to do it
        XML.SimpleXML result = XML.xml("resultMap")
                .attr("id", type.getSimpleName() + "Map")
                .attr("type", type.getName());
        if (getter.containsKey("id")) {
            result.node(XML.xml("id")
                    .attr("column", "id")
                    .attr("property", "id"));
        }
        var nameConvert = (Function<String, String>) name -> {
            var builder = new StringBuilder();
            boolean head = true;
            for (char c : name.toCharArray()) {
                if (Character.isUpperCase(c) && !head) {
                    builder.append("_");
                }
                if (head) head = false;
                builder.append(Character.toLowerCase(c));
            }
            return builder.toString();
        };
        getter.forEach((name, _m) -> {
            if (!name.equals("id")) {
                var c = _m.getAnnotation(SqlInitDatabaseGenerator.Column.class);
                if (c == null) {
                    try {
                        var f = type.getDeclaredField(name);
                        c = f.getAnnotation(SqlInitDatabaseGenerator.Column.class);
                    } catch (Exception ignore) {

                    }
                }
                if (c!=null) {
                    result.node(XML.xml("result")
                            .attr("column", c.name())
                            .attr("property", name));
                }else {
                    result.node(XML.xml("result")
                            .attr("column", nameConvert.apply(name))
                            .attr("property", name));
                }
            }
        });
        TableName tableName = type.getAnnotation(TableName.class);
        var columnName = tableName == null ? nameConvert.apply(type.getSimpleName())
                : tableName.name();
        if (tableNameConvert != null) {
            columnName = tableNameConvert.apply(columnName);
        }
        var sqlTableName = XML.xml("sql")
                .attr("id", "tb")
                .node(XML.content(columnName));

        var update = XML.xml("sql")
                .attr("id", "update-sql");
        var head = true;
        for (Map.Entry<String, Method> e : getter.entrySet()) {
            String key = e.getKey();
            if (key.equals("id")) {
                continue;
            }
            update.node(XML.xml("if")
                    .attr("test", key + " != null")
                    .node(XML.content((head ? " " : ", ") + nameConvert.apply(key) + " = #{" + key + "} ")));
            head = false;
        }
        var builder = new StringBuilder();
        if (init.getParameterCount() == 0) {
            head = true;
            for (Map.Entry<String, Method> entry : getter.entrySet()) {
                String name = entry.getKey();
                if (!head) builder.append(", ");
                head = false;
                builder.append(nameConvert.apply(name));
            }
        } else {
            if (!Arrays.stream(init.getParameters()).allMatch(p -> getter.containsKey(p.getName()))) {
                System.err.println(type.getName() + " CONTAIN NO FULL PARAMETER CONSTRUCTION!!!");
            }
            for (Parameter parameter : init.getParameters()) {
                builder.append(nameConvert.apply(parameter.getName())).append(", ");
            }
            builder.deleteCharAt(builder.length() - 2);
        }
        var allColumn = XML.xml("sql")
                .attr("id", "cols")
                .node(XML.content(builder.toString()));
        builder.delete(0, builder.length());
        var values = XML.xml("sql")
                .attr("id", "vals");
        if (init.getParameterCount() == 0) {
            head = true;
            for (Map.Entry<String, Method> entry : getter.entrySet()) {
                if (!head) builder.append(", ");
                String name = entry.getKey();
                builder.append("#{").append(name).append("}");
                head = false;
            }
        } else {
            if (!Arrays.stream(init.getParameters()).allMatch(p -> getter.containsKey(p.getName()))) {
                System.err.println(type.getName() + " CONTAIN NO FULL PARAMETER CONSTRUCTION!!!");
            }
            for (Parameter parameter : init.getParameters()) {
                builder.append("#{").append(parameter.getName()).append("}, ");
            }
            builder.deleteCharAt(builder.length() - 2);
        }
        var insert = XML.xml("insert")
                .attr("id", "create")
                .attr("parameterType", type.getName());
        if (getter.containsKey("id")) {
            insert.attr("useGeneratedKeys", "true");
        }
        insert.node(XML.content(generateInsert(tableNameConvert, type)));
        var root = XML.xml("mapper")
                .attr("namespace", namespaceConvert.apply(type.getName()))
                .node(result)
                .node(sqlTableName)
                .node(allColumn)
                .node(values)
                .node(update)
                .node(insert);
        return root;
    }

    @Target({ElementType.METHOD})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    @interface TableName {
        String name();
    }
}