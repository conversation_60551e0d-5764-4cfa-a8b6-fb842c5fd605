package moonstone.common.utils;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Data
public abstract class EmojiBreaker {
    String str;
    String encode;

    public EmojiBreaker breakEmoji(String str) {
        return new EmojiBringer(str);
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class EmojiBringer extends EmojiBreaker {

        public EmojiBringer(String outStr) {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            int skip = 0;
            for (byte c : outStr.getBytes(StandardCharsets.UTF_8)) {
                if (skip > 0) {
                    skip--;
                    continue;
                }
                if (normalChar(c)) byteArrayOutputStream.write(c);
                else {
                    skip = EmojiBreaker.emoji(c);
                    if (skip == 0)
                        byteArrayOutputStream.write(c);
                }
            }
            str = byteArrayOutputStream.toString();
            encode = Base64.getEncoder().encodeToString(outStr.getBytes());
        }
    }

    private static boolean normalChar(byte c) {
        return Byte.toUnsignedInt(c) >> 7 == 0;
    }

    private static int emoji(byte c) {
        int raw = Byte.toUnsignedInt(c);
        if (raw >> 4 == 15) return 3;
        if (raw >> 3 == 31) return 4;
        if (raw >> 2 == 63) return 5;
        return 0;
    }
}
