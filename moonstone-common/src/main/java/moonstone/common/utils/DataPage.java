package moonstone.common.utils;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/19 13:17
 */
@Data
public class DataPage<T> {

	Pages page;

	List<T> dataList;

	public static <T> DataPage<T> build(long total, long size, long current, long pages, List<T> dataList) {
		DataPage<T> dataPage = new DataPage<>();
		Pages page = new Pages();
		page.setTotalCount(total);
		page.setPageSize(size);
		page.setCurrentPage(current);
		page.setTotalPage(pages);
		dataPage.setPage(page);
		dataPage.setDataList(dataList);
		return dataPage;
	}

	/**
	 * llyj
	 *
	 * @param totalCalculation
	 * @param dataList
	 * @param <T>
	 * @return
	 */
	public static <T> DataPage<T> build(TotalCalculation totalCalculation, List<T> dataList) {
		DataPage<T> dataPage = new DataPage<>();
		Pages page = new Pages();
		page.setTotalCount(totalCalculation.getTotalCount());
		page.setPageSize(totalCalculation.getPageSize());
		page.setCurrentPage(totalCalculation.getCurrentPage());
		page.setTotalPage(totalCalculation.getTotalPage());
		dataPage.setPage(page);
		dataPage.setDataList(dataList);
		return dataPage;
	}

	/**
	 * llyj
	 *
	 * @param dataClass
	 * @param <T>
	 * @return
	 */
	public static <T> DataPage<T> empty(T dataClass) {
		DataPage<T> dataPage = new DataPage<>();
		Pages page = new Pages();
		page.setTotalCount(0);
		page.setPageSize(0);
		page.setCurrentPage(0);
		page.setTotalPage(0);
		dataPage.setPage(page);
		dataPage.setDataList(new ArrayList<T>());
		return dataPage;
	}
}
