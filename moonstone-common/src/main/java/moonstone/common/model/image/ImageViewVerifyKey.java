package moonstone.common.model.image;

import org.springframework.util.DigestUtils;

import java.util.Base64;

/**
 * <AUTHOR>
 */
interface ImageViewVerifyKey {
    long STEP = 1000 * 24 * 60 * 60;

    /**
     * 生成密钥
     *
     * @param id 原数据
     * @return 填充到8位以上的密钥(Long.BYTES)
     */
    static byte[] generateKey(long id) {
        StringBuilder hex = new StringBuilder(Long.toHexString(id));
        while (hex.toString().getBytes().length < Long.BYTES) {
            hex.append(hex);
        }
        return hex.toString().getBytes();
    }


    /**
     * 校验密钥是否有效
     *
     * @param time   时间(mills second)
     * @param verify 校验码
     * @param key    密钥
     * @return 是否有效
     */
    static boolean validate(long time, String verify, String key) {
        long nowStep = time / STEP;
        long lastStep = time / STEP;
        if (!verify.equals(DigestUtils.md5DigestAsHex((Long.toHexString(nowStep) + key).getBytes()))) {
            return verify.equals(DigestUtils.md5DigestAsHex((Long.toHexString(lastStep) + key).getBytes()));
        }
        return true;
    }

    /**
     * 校验密钥是否有效
     *
     * @param time 时间(mills second)
     * @param key  密钥
     * @return verify 校验码
     */
    static String generateVerify(long time, String key) {
        long nowStep = time / STEP;
        return DigestUtils.md5DigestAsHex((Long.toHexString(nowStep) + key).getBytes());
    }


    /**
     * 解码密钥
     *
     * @param key     带有用户Id(clientId)的密钥
     * @param ownerId 拥有人
     * @return 用户Id (clientId)
     */
    static long decodeKey(String key, long ownerId) {
        byte[] keyMatrix = ImageViewVerifyKey.generateKey(ownerId);
        byte[] dataMatrix = Base64.getUrlDecoder().decode(key);
        for (int i = 0; i < dataMatrix.length; i++) {
            dataMatrix[i] = (byte) (dataMatrix[i] ^ keyMatrix[i % keyMatrix.length]);
        }
        try {
            String[] part = new String(dataMatrix).split("@");
            if (part[1].equals(Long.toString(ownerId))) {
                return Long.parseLong(part[0]);
            }
            return ownerId;
        } catch (Exception ignore) {
            return ownerId;
        }
    }
}
