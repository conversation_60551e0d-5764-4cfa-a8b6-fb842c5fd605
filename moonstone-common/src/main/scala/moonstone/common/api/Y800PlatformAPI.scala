package moonstone.common.api

import com.google.gson.JsonObject
import moonstone.common.model.Either
import moonstone.common.model.rpcAPI.y800dto._

trait Y800PlatformAPI extends Y800API {

  /**
   * 计算税费
   *
   * @param taxRequestModel 商品信息
   * @return
   */
  def goodsTaxCal(taxRequestModel: Y800TaxRequestModelDTO): Either[ListDTO[Y800TaxResultDTO]]

  /**
   * 批量查询商品详情
   *
   * @param goodsSearchQueryModel 商品详情数据
   * @return
   */
  def goodsSearch(goodsSearchQueryModel: Y800GoodsSearchQueryModelDTO): Either[JsonObject]

  /**
   * 商品详细查询
   *
   * @param list 洋800单品外部SkuId列表
   * @return
   */
  def goodsQuery(list: ListDTO[Y800SkuCodeDTO]): Either[java.util.List[Y800SkuQueryResponse]]
}
