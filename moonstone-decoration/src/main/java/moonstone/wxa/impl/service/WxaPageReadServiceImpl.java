package moonstone.wxa.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.wxa.impl.dao.WxaPageDao;
import moonstone.wxa.model.WxaPage;
import moonstone.wxa.service.WxaPageReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by CaiZhy on 2018/11/12.
 */
@Slf4j
@Service
@RpcProvider
public class WxaPageReadServiceImpl implements WxaPageReadService {
    @Autowired
    private WxaPageDao wxaPageDao;

    @Override
    public Response<WxaPage> findById(Long id){
        try {
            WxaPage wxaPage = wxaPageDao.findById(id);
            return Response.ok(wxaPage);
        } catch (Exception e){
            log.error("fail to find wxaPage by id={}, cause: {}", id, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("wxaPage.find.fail");
        }
    }

    @Override
    public Response<List<WxaPage>> findByTemplateId(Long templateId){
        try {
            List<WxaPage> wxaPages = wxaPageDao.findByTemplateId(templateId);
            return Response.ok(wxaPages);
        } catch (Exception e){
            log.error("fail to find wxaPages by templateId={}, cause: {}", templateId, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("wxaPage.find.fail");
        }
    }

    @Override
    public Response<WxaPage> findByTemplateIdAndPath(Long templateId, String path){
        try {
            WxaPage wxaPage = wxaPageDao.findByTemplateIdAndPath(templateId, path);
            return Response.ok(wxaPage);
        } catch (Exception e){
            log.error("fail to find wxaPage by templateId={}, path={}, cause: {}", templateId, path, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("wxaPage.find.fail");
        }
    }
}
