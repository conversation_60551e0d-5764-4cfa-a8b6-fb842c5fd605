package moonstone.wxToken.impl.dao;

import moonstone.wxToken.model.WxToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * Created by CaiZhy on 2018/9/17.
 */
@Repository
public class WxTokenDao {
    @Autowired
    private MongoTemplate mongoTemplate;

    public void create(WxToken wxToken){
        mongoTemplate.save(wxToken);
    }

    public WxToken findByName(String name){
        Query query = new Query(Criteria.where("name").is(name));
        WxToken wxToken = mongoTemplate.findOne(query, WxToken.class);
        return wxToken;
    }

    public Boolean update(WxToken wxToken){
        Query query = new Query(Criteria.where("_id").is(wxToken.getId()));
        Update update = new Update();
        if (wxToken.getName() != null){
            update.set("name", wxToken.getName());
        }
        if (wxToken.getCode() != null){
            update.set("code", wxToken.getCode());
        }
        if (wxToken.getExpiredAt() != null){
            update.set("expired_at", wxToken.getExpiredAt());
        }
        mongoTemplate.updateFirst(query, update, WxToken.class);
        return Boolean.TRUE;
    }

    public Boolean updateByName(WxToken wxToken){
        Query query = new Query(Criteria.where("name").is(wxToken.getName()));
        Update update = new Update();
        if (wxToken.getCode() != null){
            update.set("code", wxToken.getCode());
        }
        if (wxToken.getExpiredAt() != null){
            update.set("expired_at", wxToken.getExpiredAt());
        }
        mongoTemplate.updateFirst(query, update, WxToken.class);
        return Boolean.TRUE;
    }

    public Boolean delete(String name) {
        Query query = new Query(Criteria.where("name").is(name));
        mongoTemplate.remove(query, WxToken.class);
        return Boolean.TRUE;
    }
}
