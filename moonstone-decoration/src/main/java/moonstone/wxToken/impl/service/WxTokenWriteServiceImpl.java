package moonstone.wxToken.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.wxToken.impl.dao.WxTokenDao;
import moonstone.wxToken.model.WxToken;
import moonstone.wxToken.service.WxTokenWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by CaiZhy on 2018/9/17.
 */
@Slf4j
@Service
@RpcProvider
public class WxTokenWriteServiceImpl implements WxTokenWriteService{
    @Autowired
    WxTokenDao wxTokenDao;

    @Override
    public Response<Boolean> create(WxToken wxToken){
        try {
            wxTokenDao.create(wxToken);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e){
            log.error("fail to {}, cause: {}", wxToken, Throwables.getStackTraceAsString(e));
            return Response.fail("wxToken.create.fail");
        }
    }

    @Override
    public Response<Boolean> update(WxToken wxToken){
        try {
            return Response.ok(wxTokenDao.update(wxToken));
        } catch (Exception e){
            log.error("fail to update {}, cause: {}", wxToken, Throwables.getStackTraceAsString(e));
            return Response.fail("wxToken.update.fail");
        }
    }

    @Override
    public Response<Boolean> delete(String name){
        try {
            return Response.ok(wxTokenDao.delete(name));
        } catch (Exception e){
            log.error("fail to delete wxToken by name={}, cause: {}", name, Throwables.getStackTraceAsString(e));
            return Response.fail("wxToken.delete.fail");
        }
    }
}
