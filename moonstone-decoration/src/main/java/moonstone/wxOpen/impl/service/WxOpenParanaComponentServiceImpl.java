package moonstone.wxOpen.impl.service;

import com.alibaba.fastjson.JSON;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Splitter;
import com.google.gson.JsonObject;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.JsonResponseException;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxError;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.util.crypto.SHA1;
import me.chanjar.weixin.common.util.crypto.WxCryptUtil;
import me.chanjar.weixin.common.util.http.URIUtil;
import me.chanjar.weixin.common.util.json.WxGsonBuilder;
import me.chanjar.weixin.open.bean.WxOpenAuthorizerAccessToken;
import me.chanjar.weixin.open.bean.WxOpenComponentAccessToken;
import me.chanjar.weixin.open.bean.auth.WxOpenAuthorizationInfo;
import me.chanjar.weixin.open.bean.message.WxOpenXmlMessage;
import me.chanjar.weixin.open.bean.result.WxOpenAuthorizerInfoResult;
import me.chanjar.weixin.open.bean.result.WxOpenQueryAuthResult;
import me.chanjar.weixin.open.util.json.WxOpenGsonBuilder;
import moonstone.common.utils.LogUtil;
import moonstone.shopWxa.enums.AuthorizeStatus;
import moonstone.shopWxa.impl.dao.ShopWxaDao;
import moonstone.wxOpen.impl.storage.WxOpenTokenStorage;
import moonstone.wxOpen.service.WxOpenParanaComponentService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/10/30.
 */
@Slf4j
@Service
@RpcProvider
public class WxOpenParanaComponentServiceImpl implements WxOpenParanaComponentService {
    @Autowired
    private WxOpenTokenStorage wxOpenTokenStorage;

    @Autowired
    private ShopWxaDao shopWxaDao;

    @Value("${parana.wxopen.requestdomain}")
    private String requestDomain;

    @Value("${parana.wxopen.wsrequestdomain}")
    private String wsrequestDomain;

    @Value("${parana.wxopen.uploaddomain}")
    private String uploadDomain;

    @Value("${parana.wxopen.downloaddomain}")
    private String downloadDomain;

    @Override
    public boolean checkSignature(String timestamp, String nonce, String signature) {
        try {
            return SHA1.gen(wxOpenTokenStorage.getComponentToken(), timestamp, nonce).equals(signature);
        } catch (Exception var5) {
            WxOpenParanaComponentServiceImpl.log.error("Checking signature failed, and the reason is :" + var5.getMessage());
            return false;
        }
    }

    @Override
    public String decryptToXml(String encryptedXml, String timestamp, String nonce, String msgSignature) {
        WxCryptUtil cryptUtil = new WxCryptUtil(wxOpenTokenStorage.getComponentToken(),
                wxOpenTokenStorage.getComponentAesKey(), wxOpenTokenStorage.getComponentAppId());
        return cryptUtil.decrypt(msgSignature, timestamp, nonce, encryptedXml);
    }

    @Override
    public String encryptFromXml(String plainXml) {
        WxCryptUtil pc = new WxCryptUtil(wxOpenTokenStorage.getComponentToken(),
                wxOpenTokenStorage.getComponentAesKey(), wxOpenTokenStorage.getComponentAppId());
        return pc.encrypt(plainXml);
    }

    @Override
    public String route(WxOpenXmlMessage wxMessage) throws WxErrorException {
        if (wxMessage == null) {
            throw new NullPointerException("message is empty");
        } else if (StringUtils.equalsIgnoreCase(wxMessage.getInfoType(), "component_verify_ticket")) {
            wxOpenTokenStorage.updateComponentVerifyTicket(wxMessage.getComponentVerifyTicket());
            return "success";
        } else if (StringUtils.equalsIgnoreCase(wxMessage.getInfoType(), "authorized") || StringUtils.equalsIgnoreCase(wxMessage.getInfoType(), "updateauthorized")) {
            WxOpenQueryAuthResult queryAuth = this.getQueryAuth(wxMessage.getAuthorizationCode());
            if (queryAuth != null && queryAuth.getAuthorizationInfo() != null && queryAuth.getAuthorizationInfo().getAuthorizerAppid() != null) {
                return "success";
            } else {
                throw new NullPointerException("failed to getQueryAuth");
            }
        } else if (StringUtils.equalsIgnoreCase(wxMessage.getInfoType(), "unauthorized")) {
            shopWxaDao.updateIsAuthorizedByAppId(wxMessage.getAuthorizerAppid(), AuthorizeStatus.UNAUTHORIZED.getValue());
            return "success";
        } else {
            return "";
        }
    }

    @Override
    public String getComponentAccessToken(boolean forceRefresh) throws WxErrorException {
        if (wxOpenTokenStorage.isComponentAccessTokenExpired() || forceRefresh) {
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("component_appid", wxOpenTokenStorage.getComponentAppId());
            jsonObject.addProperty("component_appsecret", wxOpenTokenStorage.getComponentAppSecret());
            jsonObject.addProperty("component_verify_ticket", wxOpenTokenStorage.getComponentVerifyTicket());
            String responseContent = this.realPost("https://api.weixin.qq.com/cgi-bin/component/api_component_token", jsonObject.toString());
            WxOpenComponentAccessToken componentAccessToken = WxOpenComponentAccessToken.fromJson(responseContent);
            wxOpenTokenStorage.updateComponentAccessToken(componentAccessToken);
        }

        return wxOpenTokenStorage.getComponentAccessToken();
    }

    @Override
    public String getAuthorizerAccessToken(String appId, boolean forceRefresh) throws WxErrorException {
        if (wxOpenTokenStorage.isAuthorizerAccessTokenExpired(appId) || forceRefresh) {
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("component_appid", wxOpenTokenStorage.getComponentAppId());
            jsonObject.addProperty("authorizer_appid", appId);
            jsonObject.addProperty("authorizer_refresh_token", wxOpenTokenStorage.getAuthorizerRefreshToken(appId));
            String responseContent = this.post("https://api.weixin.qq.com/cgi-bin/component/api_authorizer_token", jsonObject.toString());
            WxOpenAuthorizerAccessToken wxOpenAuthorizerAccessToken = WxOpenAuthorizerAccessToken.fromJson(responseContent);
            wxOpenTokenStorage.updateAuthorizerAccessToken(appId, wxOpenAuthorizerAccessToken);
        }

        return wxOpenTokenStorage.getAuthorizerAccessToken(appId);
    }

    private String realPost(String uri, String postData) throws WxErrorException {
        log.debug("[(POST){}] postData: {}", uri, postData);
        HttpRequest request = HttpRequest.post(uri).contentType("application/json", "utf-8").send(postData);
        if (request.ok()) {
            String result = request.body();
            log.debug("{} [(POST){}] result: {}", LogUtil.getClassMethodName(), uri, result);
            Map<String, Object> map = JSON.parseObject(result);
            if (map.get("errcode") != null && (int) map.get("errcode") != 0) {
                // todo remove the hack
                if (map.get("errmsg").toString().contains("no domain to modify after filtered")) {
                    return result;
                }
                throw new WxErrorException(WxError.builder().errorCode((int) map.get("errcode")).errorMsg(map.get("errmsg").toString()).build());
            }
            return result;
        } else {
            log.error("[(POST){}] HttpRequest failed, postData( {})", uri, postData);
            throw new JsonResponseException(500, "http.request.fail");
        }
    }

    private String post(String uri, String postData) throws WxErrorException {
        return this.post(uri, postData, "component_access_token");
    }

    private String post(String uri, String postData, String accessTokenKey) throws WxErrorException {
        String componentAccessToken = this.getComponentAccessToken(false);
        String uriWithComponentAccessToken = uri + (uri.contains("?") ? "&" : "?") + accessTokenKey + "=" + componentAccessToken;

        try {
            return this.realPost(uriWithComponentAccessToken, postData);
        } catch (WxErrorException var8) {
            WxError error = var8.getError();
            /*
             * 发生以下情况时尝试刷新access_token
             * 40001 获取access_token时AppSecret错误，或者access_token无效
             * 42001 access_token超时
             * 40014 不合法的access_token，请开发者认真比对access_token的有效性（如是否过期），或查看是否正在为恰当的公众号调用接口
             */
            if (error.getErrorCode() == 42001 || error.getErrorCode() == 40001 || error.getErrorCode() == 40014) {
                // 强制设置wxMpConfigStorage它的access token过期了，这样在下一次请求里就会刷新access token
                wxOpenTokenStorage.expireComponentAccessToken();
                if (wxOpenTokenStorage.autoRefreshToken()) {
                    return this.post(uri, postData, accessTokenKey);
                }
            }

            if (error.getErrorCode() != 0) {
                throw new WxErrorException(error, var8);
            } else {
                return null;
            }
        }
    }

    @Override
    public String getPreAuthUrl(String redirectUri) throws WxErrorException {
        return this.getPreAuthUrl(redirectUri, null, null);
    }

    @Override
    public String getPreAuthUrl(String redirectUri, String authType, String bizAppid) throws WxErrorException {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("component_appid", wxOpenTokenStorage.getComponentAppId());
        String responseContent = this.post("https://api.weixin.qq.com/cgi-bin/component/api_create_preauthcode", jsonObject.toString());
        jsonObject = WxGsonBuilder.create().fromJson(responseContent, JsonObject.class);
        StringBuilder preAuthUrl = new StringBuilder(String.format("https://mp.weixin.qq.com/cgi-bin/componentloginpage?component_appid=%s&pre_auth_code=%s&redirect_uri=%s",
                wxOpenTokenStorage.getComponentAppId(), jsonObject.get("pre_auth_code").getAsString(), URIUtil.encodeURIComponent(redirectUri)));
        if (StringUtils.isNotEmpty(authType)) {
            preAuthUrl.append("&auth_type=").append(authType);
        }

        if (StringUtils.isNotEmpty(bizAppid)) {
            preAuthUrl.append("&biz_appid=").append(bizAppid);
        }
        log.debug("[op:getPreAuthUrl] preAuthUrl={}", preAuthUrl);
        return preAuthUrl.toString();
    }

    @Override
    public WxOpenQueryAuthResult getQueryAuth(String authorizationCode) throws WxErrorException {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("component_appid", wxOpenTokenStorage.getComponentAppId());
        jsonObject.addProperty("authorization_code", authorizationCode);
        String responseContent = this.post("https://api.weixin.qq.com/cgi-bin/component/api_query_auth", jsonObject.toString());
        WxOpenQueryAuthResult queryAuth = WxOpenGsonBuilder.create().fromJson(responseContent, WxOpenQueryAuthResult.class);
        if (queryAuth != null && queryAuth.getAuthorizationInfo() != null) {
            WxOpenAuthorizationInfo authorizationInfo = queryAuth.getAuthorizationInfo();
            if (authorizationInfo.getAuthorizerAccessToken() != null) {
                wxOpenTokenStorage.updateAuthorizerAccessToken(authorizationInfo.getAuthorizerAppid(), authorizationInfo.getAuthorizerAccessToken(), authorizationInfo.getExpiresIn());
            }

            if (authorizationInfo.getAuthorizerRefreshToken() != null) {
                wxOpenTokenStorage.setAuthorizerRefreshToken(authorizationInfo.getAuthorizerAppid(), authorizationInfo.getAuthorizerRefreshToken());
            }
            //授权成功
            shopWxaDao.updateIsAuthorizedByAppId(authorizationInfo.getAuthorizerAppid(), AuthorizeStatus.AUTHORIZED.getValue());

        }
        return queryAuth;
    }

    @Override
    public WxOpenAuthorizerInfoResult getAuthorizerInfo(String authorizerAppid) throws WxErrorException {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("component_appid", wxOpenTokenStorage.getComponentAppId());
        jsonObject.addProperty("authorizer_appid", authorizerAppid);
        String responseContent = this.post("https://api.weixin.qq.com/cgi-bin/component/api_get_authorizer_info", jsonObject.toString());
        return WxOpenGsonBuilder.create().fromJson(responseContent, WxOpenAuthorizerInfoResult.class);
    }

    @Override
    public String modifyDomain(String authorizerAppid) throws WxErrorException {
        String authorizerAccessToken = this.getAuthorizerAccessToken(authorizerAppid, false);
        String uri = "https://api.weixin.qq.com/wxa/modify_domain";
        String uriWithAuthorizerAccessToken = uri + "?" + "access_token=" + authorizerAccessToken;
        Map<String, Object> params = new HashMap<>(5);
        params.put("action", "add");
        List<String> requestDomainList = Splitter.on(";").splitToList(this.requestDomain);
        List<String> wsrequestDomainList = Splitter.on(";").splitToList(this.wsrequestDomain);
        List<String> uploadDomainList = Splitter.on(";").splitToList(this.uploadDomain);
        List<String> downloadDomainList = Splitter.on(";").splitToList(this.downloadDomain);
        params.put("requestdomain", requestDomainList);
        params.put("wsrequestdomain", wsrequestDomainList);
        params.put("uploaddomain", uploadDomainList);
        params.put("downloaddomain", downloadDomainList);
        try {
            return this.realPost(uriWithAuthorizerAccessToken, JSON.toJSONString(params));
        } catch (Exception e) {
            if (e.getMessage().contains("no domain to modify after filtered")) {
                return "";
            }
            throw e;
        }
    }
}
