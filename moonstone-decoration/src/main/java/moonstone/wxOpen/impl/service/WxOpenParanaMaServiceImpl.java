package moonstone.wxOpen.impl.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.gson.JsonObject;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxError;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.open.bean.message.WxOpenMaSubmitAuditMessage;
import me.chanjar.weixin.open.bean.result.WxOpenMaCategoryListResult;
import me.chanjar.weixin.open.bean.result.WxOpenMaPageListResult;
import me.chanjar.weixin.open.bean.result.WxOpenMaSubmitAuditResult;
import moonstone.wxOpen.impl.storage.WxOpenTokenStorage;
import moonstone.wxOpen.service.WxOpenParanaComponentService;
import moonstone.wxOpen.service.WxOpenParanaMaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * Created by CaiZhy on 2018/11/21.
 */
@Slf4j
@Service
@RpcProvider
public class WxOpenParanaMaServiceImpl implements WxOpenParanaMaService {
    @Autowired
    private WxOpenTokenStorage wxOpenTokenStorage;

    @RpcConsumer
    private WxOpenParanaComponentService wxOpenParanaComponentService;

    @Override
    public String codeCommit(Long templateId, String userVersion, String userDesc, String extInfo, String appId) throws WxErrorException {
        JsonObject params = new JsonObject();
        params.addProperty("template_id", templateId);
        params.addProperty("user_version", userVersion);
        params.addProperty("user_desc", userDesc);
        params.addProperty("ext_json", extInfo);
        String response = this.post("https://api.weixin.qq.com/wxa/commit", params.toString(), appId);
        return response;
    }

    @Override
    public WxOpenMaCategoryListResult getCategoryList(String appId) throws WxErrorException {
        String response = this.get("https://api.weixin.qq.com/wxa/get_category", null, appId);
        return JSONObject.parseObject(response, WxOpenMaCategoryListResult.class);
    }

    @Override
    public WxOpenMaPageListResult getPageList(String appId) throws WxErrorException {
        String response = this.get("https://api.weixin.qq.com/wxa/get_page", null, appId);
        return JSONObject.parseObject(response, WxOpenMaPageListResult.class);
    }

    @Override
    public WxOpenMaSubmitAuditResult submitAudit(WxOpenMaSubmitAuditMessage submitAuditMessage, String appId) throws WxErrorException {
        String response = this.post("https://api.weixin.qq.com/wxa/submit_audit", JSON.toJSONString(submitAuditMessage), appId);
        return JSONObject.parseObject(response, WxOpenMaSubmitAuditResult.class);
    }

    @Override
    public String getLatestAuditStatus(String appId) throws WxErrorException {
        return this.get("https://api.weixin.qq.com/wxa/get_latest_auditstatus", null, appId);
    }

    @Override
    public String releaseAuthed(String appId) throws WxErrorException {
        JsonObject params = new JsonObject();
        String response = this.post("https://api.weixin.qq.com/wxa/release", params.toString(), appId);
        return response;
    }

    private String realGet(String uri, Map<String, String> queryParam) throws WxErrorException {
        log.debug("[(GET){}] queryParam: {}", uri, queryParam);
        HttpRequest request = HttpRequest.get(uri, queryParam, true);
        if (request.ok()) {
            String result = request.body();
            log.debug("[(GET){}] result: {}", uri, result);
            Map<String, Object> map = JsonMapper.nonDefaultMapper().fromJson(result, Map.class);
            if (map.get("errcode") != null && (int) map.get("errcode") != 0) {
                throw new WxErrorException(WxError.builder().errorCode((int) map.get("errcode")).errorMsg(map.get("errmsg").toString()).build());
            }
            return result;
        } else {
            log.error("[(GET){}] HttpRequest failed, queryParam( {})", uri, queryParam);
            throw new JsonResponseException(500, "http.request.fail");
        }
    }

    private String get(String uri, Map<String, String> queryParam, String appId) throws WxErrorException {
        return this.get(uri, queryParam, "access_token", appId);
    }

    private String get(String uri, Map<String, String> queryParam, String accessTokenKey, String appId) throws WxErrorException {
        try {

            String accessToken = wxOpenParanaComponentService.getAuthorizerAccessToken(appId, false);
            String uriWithComponentAccessToken = uri + (uri.contains("?") ? "&" : "?") + accessTokenKey + "=" + accessToken;

            try {
                return this.realGet(uriWithComponentAccessToken, queryParam);
            } catch (WxErrorException var8) {
                WxError error = var8.getError();
                /*
                 * 发生以下情况时尝试刷新access_token
                 * 40001 获取access_token时AppSecret错误，或者access_token无效
                 * 42001 access_token超时
                 * 40014 不合法的access_token，请开发者认真比对access_token的有效性（如是否过期），或查看是否正在为恰当的公众号调用接口
                 */
                if (error.getErrorCode() == 42001 || error.getErrorCode() == 40001 || error.getErrorCode() == 40014) {
                    // 强制设置wxMpConfigStorage它的access token过期了，这样在下一次请求里就会刷新access token
                    wxOpenTokenStorage.expireComponentAccessToken();
                    if (wxOpenTokenStorage.autoRefreshToken()) {
                        return this.get(uri, queryParam, accessTokenKey, appId);
                    }
                }

                if (error.getErrorCode() != 0) {
                    throw new WxErrorException(error, var8);
                } else {
                    return null;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String realPost(String uri, String postData) throws WxErrorException {
        log.debug("[(POST){}] postData: {}", uri, postData);
        HttpRequest request = HttpRequest.post(uri).contentType("application/json", "utf-8").send(postData);
        if (request.ok()) {
            String result = request.body();
            log.debug("[(POST){}] result: {}", uri, result);
            Map<String, Object> map = JsonMapper.nonDefaultMapper().fromJson(result, Map.class);
            if (map.get("errcode") != null && (int) map.get("errcode") != 0) {
                throw new WxErrorException(WxError.builder().errorCode((int) map.get("errcode")).errorMsg(map.get("errmsg").toString()).build());
            }
            return result;
        } else {
            log.error("[(POST){}] HttpRequest failed, postData( {})", uri, postData);
            throw new JsonResponseException(500, "http.request.fail");
        }
    }

    private String post(String uri, String postData, String appId) throws WxErrorException {
        return this.post(uri, postData, "component_access_token", appId);
    }

    private String post(String uri, String postData, String accessTokenKey, String appId) throws WxErrorException {
        try {
            String accessToken = wxOpenParanaComponentService.getAuthorizerAccessToken(appId, false);
            String uriWithComponentAccessToken = uri + (uri.contains("?") ? "&" : "?") + accessTokenKey + "=" + accessToken;

            try {
                return this.realPost(uriWithComponentAccessToken, postData);
            } catch (WxErrorException var8) {
                WxError error = var8.getError();
                if (error.getErrorCode() == 42001 || error.getErrorCode() == 40001 || error.getErrorCode() == 40014) {
                    wxOpenTokenStorage.expireComponentAccessToken();
                    if (wxOpenTokenStorage.autoRefreshToken()) {
                        return this.post(uri, postData, accessTokenKey, appId);
                    }
                }

                if (error.getErrorCode() != 0) {
                    throw new WxErrorException(error, var8);
                } else {
                    return null;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
