package moonstone.file.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.file.model.ShopFile;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *<AUTHOR>
 */
@Repository
public class ShopFileDao extends MyBatisDao<ShopFile> {

    public List<ShopFile> getShopFileByShopId(Long shopId, Long groupId, String fileType){
        return getSqlSession().selectList(sqlId("getShopFilebyShopId"), ImmutableMap.of("shopId", shopId, "groupId", groupId, "fileType", fileType));
    }

    public Integer moveShopFiles(Long shopId,Long groupId,List<Long>fileIds){
        return getSqlSession().update(sqlId("moveShopFiles"), ImmutableMap.of("shopId", shopId,  "groupId", groupId,  "ids",fileIds));
    }

    public Integer softDeleteFiles(Long shopId,List<Long>fileIds){
        return getSqlSession().update(sqlId("deletes"),ImmutableMap.of("shopId", shopId,"ids",fileIds));
    }


    public List<ShopFile> getShopFileByShopIdAll(Long shopId) {
        return getSqlSession().selectList(sqlId("getShopFileByShopIdAll"), ImmutableMap.of("shopId", shopId));

    }

    public void deleteShopFilesByFileUrl(Long shopId, String fileUrl) {
        getSqlSession().update(sqlId("deleteShopFilesByFileUrl"), ImmutableMap.of( "shopId",shopId,"fileUrl",fileUrl));
    }
}
