package moonstone.shopMini.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.shopMini.impl.dao.ShopMiniBindQrCodeDao;
import moonstone.shopMini.model.ShopMiniBindQrCode;
import moonstone.shopMini.service.ShopMiniBindQrCodeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * author：书生
 */
@Service
@Slf4j
public class ShopMiniBindQrCodeServiceImpl implements ShopMiniBindQrCodeService {

    @Resource
    private ShopMiniBindQrCodeDao shopMiniBindQrCodeDao;

    @Override
    public List<ShopMiniBindQrCode> list(Map<String, Object> query) {
        return shopMiniBindQrCodeDao.selectList(query);
    }

    @Override
    public void save(ShopMiniBindQrCode shopMiniBindQrCode) {
         shopMiniBindQrCodeDao.create(shopMiniBindQrCode);
    }
}
