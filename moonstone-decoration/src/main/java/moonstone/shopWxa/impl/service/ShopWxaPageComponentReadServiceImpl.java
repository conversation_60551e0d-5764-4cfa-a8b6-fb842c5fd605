package moonstone.shopWxa.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.shopWxa.impl.dao.ShopWxaPageComponentDao;
import moonstone.shopWxa.model.ShopWxaPageComponent;
import moonstone.shopWxa.service.ShopWxaPageComponentReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by CaiZhy on 2018/11/12.
 */
@Slf4j
@Service
@RpcProvider
public class ShopWxaPageComponentReadServiceImpl implements ShopWxaPageComponentReadService {
    @Autowired
    private ShopWxaPageComponentDao shopWxaPageComponentDao;

    @Override
    public Response<ShopWxaPageComponent> findById(Long id){
        try {
            ShopWxaPageComponent shopWxaPageComponent = shopWxaPageComponentDao.findById(id);
            return Response.ok(shopWxaPageComponent);
        } catch (Exception e){
            log.error("fail to find shopWxaProject by id={}, cause: {}", id, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("shopWxaPageComponent.find.fail");
        }
    }

    @Override
    public Response<List<ShopWxaPageComponent>> findByWxaProjectId(Long wxaProjectId){
        try {
            List<ShopWxaPageComponent> shopWxaPageComponents = shopWxaPageComponentDao.findByWxaProjectId(wxaProjectId);
            return Response.ok(shopWxaPageComponents);
        } catch (Exception e){
            log.error("fail to find shopWxaProjects by wxaProjectId={}, cause: {}",
                    wxaProjectId, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("shopWxaPageComponent.find.fail");
        }
    }

    @Override
    public Response<List<ShopWxaPageComponent>> findByWxaProjectIdAndPageId(Long wxaProjectId, Long pageId){
        try {
            List<ShopWxaPageComponent> shopWxaPageComponents = shopWxaPageComponentDao.findByWxaProjectIdAndPageId(wxaProjectId, pageId);
            return Response.ok(shopWxaPageComponents);
        } catch (Exception e){
            log.error("fail to find shopWxaProjects by wxaProjectId={}, pageId={}, cause: {}",
                    wxaProjectId, pageId, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("shopWxaPageComponent.find.fail");
        }
    }
}
