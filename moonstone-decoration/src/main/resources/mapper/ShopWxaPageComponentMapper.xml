<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ShopWxaPageComponent">

    <resultMap id="ShopWxaPageComponentMap" type="ShopWxaPageComponent">
        <id property="id" column="id"/>
        <result column="wxa_project_id" property="wxaProjectId"/>
        <result column="page_id" property="pageId"/>
        <result column="component_id" property="componentId"/>
        <result column="sort" property="sort"/>
        <result column="param_json" property="paramJson"/>
        <result column="extra_json" property="extraJson"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_shop_wxa_page_components
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `wxa_project_id`, `page_id`, `component_id`, `sort`, `param_json`, `extra_json`,
        `status`, `created_at`, `updated_at`
    </sql>

    <sql id="vals">
        #{wxaProjectId}, #{pageId}, #{componentId}, #{sort}, #{paramJson}, #{extraJson},
        #{status}, now(), now()
    </sql>

    <sql id="criteria">
        <if test="ids != null">AND `id` IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="wxaProjectId!=null">AND `wxa_project_id` = #{wxaProjectId}</if>
        <if test="pageId!=null">AND `page_id` = #{pageId}</if>
        <if test="componentId!=null">AND `component_id` = #{componentId}</if>
        <if test="statuses == null and status == null">
            AND `status` != -1
        </if>
        <if test="status != null">AND `status` = #{status}</if>
        <if test="statuses != null">
            and `status` in
            <foreach collection="statuses" open="(" close=")" separator="," item="s">
                #{s}
            </foreach>
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'wxaProjectId'">ORDER BY `wxa_project_id`
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'pageId'">ORDER BY page_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'componentId'">ORDER BY component_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'status'">ORDER BY status
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <insert id="create" parameterType="ShopWxaPageComponent" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.wxaProjectId}, #{i.pageId}, #{i.componentId}, #{i.sort}, #{i.paramJson}, #{i.extraJson},
            #{i.status}, now(), now()
            )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="ShopWxaPageComponentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="ShopWxaPageComponentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByWxaProjectId" parameterType="long" resultMap="ShopWxaPageComponentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE wxa_project_id = #{wxaProjectId} AND status != -1
        ORDER BY page_id ASC, sort ASC
    </select>

    <select id="findByWxaProjectIdAndPageId" parameterType="map" resultMap="ShopWxaPageComponentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE wxa_project_id = #{wxaProjectId} AND page_id = #{pageId} AND status != -1
        ORDER BY sort ASC
    </select>

    <update id="update" parameterType="ShopWxaPageComponent">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="wxaProjectId != null">`wxa_project_id` = #{wxaProjectId},</if>
            <if test="pageId != null">`page_id` = #{pageId},</if>
            <if test="componentId != null">`component_id` = #{componentId},</if>
            <if test="sort != null">`sort` = #{sort},</if>
            <if test="paramJson != null">`param_json` = #{paramJson},</if>
            <if test="extraJson != null">`extra_json` = #{extraJson},</if>
            <if test="status != null">`status` = #{status},</if>
            `updated_at` = now()
        </set>
        WHERE id = #{id} and status != -1
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}
        WHERE id = #{id}
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <update id="deletes" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET status=-1
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="ShopWxaPageComponentMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>
</mapper>
