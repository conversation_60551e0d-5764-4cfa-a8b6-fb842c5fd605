<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ShopFileGroup">

    <resultMap id="ShopFileGroupMap" type="ShopFileGroup">
        <id property="id" column="id"/>
        <result column="group_name" property="groupName"/>
        <result column="shop_id" property="shopId"/>
        <result column="group_type" property="groupType"/>
        <result column="sort" property="sort"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_upload_group
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `group_name`,`shop_id`,`group_type`,`sort`,`status`,`created_at`,`updated_at`
    </sql>

    <sql id="vals">
        #{groupName},${shopId},#{groupType},${sort},${status},now(),now()
    </sql>

    <sql id="criteria">
        <if test="ids != null">AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="groupName !=null">and group_name=${groupName}</if>
        <if test="shopId != null">AND shop_id = #{shopId}</if>
        <if test="groupType != null">and group_type =${groupType}</if>
        <if test="sort!= null">AND sort= #{sort}</if>
        <if test="status != null">AND status = #{status}</if>
        <if test="statuses != null">
            and status in
            <foreach collection="statuses" open="(" close=")" separator="," item="s">
                #{s}
            </foreach>
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'shopId'">ORDER BY shop_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'name'">ORDER BY `name`
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'appId'">ORDER BY app_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'wxaUserName'">ORDER BY wxa_user_name
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'wxaNickName'">ORDER BY wxa_nick_name
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <insert id="create" parameterType="ShopFileGroup" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            ${i.groupName},${i.shopId},${i.groupType},${i.sort},${i.status},now(),now()
            )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="ShopFileGroupMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="ShopFileGroupMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getShopShopFileGroupByShopId" parameterType="map" resultMap="ShopFileGroupMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id=${shopId} and group_type='${groupType}'
    </select>

    <select id="findByShopId" parameterType="long" resultMap="ShopFileGroupMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId} AND status != -1
    </select>

    <select id="findByShopIdAndStatus" parameterType="map" resultMap="ShopFileGroupMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId} AND status = #{status} LIMIT 1
    </select>
    <update id="update" parameterType="ShopFileGroup">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="groupName !=null">group_name=#{groupName},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="groupType != null">group_type =#{groupType},</if>
            <if test="sort!= null">sort= #{sort},</if>
            <if test="status != null">status = #{status},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
        and status != -1
    </update>
      <update id="updateStatus" parameterType="map">
          UPDATE
          <include refid="tb"/>
          SET status = #{status}, updated_at = now()
          WHERE id = #{id}
      </update>

      <delete id="delete" parameterType="long">
          DELETE FROM
          <include refid="tb"/>
          WHERE id = #{id}
      </delete>

      <update id="deletes" parameterType="list">
          UPDATE
          <include refid="tb"/>
          SET status=-1
          WHERE id IN
          <foreach item="id" collection="list" open="(" separator="," close=")">
              #{id}
          </foreach>
      </update>

      <select id="count" parameterType="map" resultType="long">
          SELECT COUNT(1)
          FROM
          <include refid="tb"/>
          <where>
              <include refid="criteria"/>
          </where>
      </select>

      <select id="paging" parameterType="map" resultMap="ShopFileGroupMap">
          SELECT
          <include refid="cols_all"/>
          FROM
          <include refid="tb"/>
          <where>
              <include refid="criteria"/>
          </where>
          <include refid="custom_sort"/>
          LIMIT #{offset}, #{limit}
      </select>
  </mapper>
