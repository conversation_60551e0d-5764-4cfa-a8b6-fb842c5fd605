package moonstone.showcase.mq.config;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import moonstone.showcase.mq.event.MQEvent;
import moonstone.showcase.mq.event.MQOrderlyEvent;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;

import javax.annotation.Resource;

@Slf4j
@Setter
public class RocketMQEventSender {

    /**
     * 事件依赖组件
     */
    @Resource
    private RocketMQTemplate rocketMQTemplate;

    public void publish(MQEvent event) {
        send(event);
    }

    private void send(MQEvent event) {
        SendResult sendResult = rocketMQTemplate.syncSend(event.topic(), event.toMQEvent());
        log.info("发送结果 ： {}", sendResult.getMsgId());
    }

    public void syncSendOrderly(MQOrderlyEvent event) {
        SendResult sendResult = rocketMQTemplate.syncSendOrderly(event.topic(), event.toMQEvent(), event.hashKey());
        log.info("顺序发送结果 ： {}", sendResult.getMsgId());
    }

}
