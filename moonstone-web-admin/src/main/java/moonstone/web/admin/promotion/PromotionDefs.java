package moonstone.web.admin.promotion;

import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.promotion.api.Behavior;
import moonstone.promotion.component.PromotionBricks;
import moonstone.promotion.dto.PromotionDefState;
import moonstone.promotion.dto.PromotionLevel;
import moonstone.promotion.model.PromotionDef;
import moonstone.promotion.service.PromotionDefReadService;
import moonstone.promotion.service.PromotionDefWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Author:cp
 * Created on 6/23/16.
 */
@RestController
@RequestMapping("/api/promotion-def")
@Slf4j
public class PromotionDefs {

    @Resource
    private PromotionDefWriteService promotionDefWriteService;

    @Resource
    private  PromotionDefReadService promotionDefReadService;

    @Autowired
    private PromotionBricks promotionBricks;

    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long create(@RequestBody PromotionDef promotionDef) {
        Response<Long> createResp = promotionDefWriteService.create(promotionDef);
        if (!createResp.isSuccess()) {
            log.error("fail to create promotionDef:{},cause:{}",
                    promotionDef, createResp.getError());
            throw new JsonResponseException(createResp.getError());
        }
        return createResp.getResult();
    }

    @RequestMapping(value = "/{id}/name", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean updateName(@PathVariable("id") Long id,
                              @RequestParam("name") String name) {
        PromotionDef toUpdated = new PromotionDef();
        toUpdated.setId(id);
        toUpdated.setName(name);

        Response<Boolean> updateResp = promotionDefWriteService.update(toUpdated);
        if (!updateResp.isSuccess()) {
            log.error("fail to update promotionDef(id={}) name to {},cause:{}",
                    id, name, updateResp.getError());
            throw new JsonResponseException(updateResp.getError());
        }
        return updateResp.getResult();
    }

    @RequestMapping(value = "/{id}/publish", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean publish(@PathVariable("id") Long id) {
        checkStatus(id, 0);

        Response<Boolean> resp = promotionDefWriteService.updateStatus(id, 1);
        if (!resp.isSuccess()) {
            log.error("fail to publish promotionDef(id={}),cause:{}",
                    id, resp.getError());
            throw new JsonResponseException(resp.getError());
        }
        return resp.getResult();
    }

    @RequestMapping(value = "/{id}/stop", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean stop(@PathVariable("id") Long id) {
        checkStatus(id, 1);

        Response<Boolean> resp = promotionDefWriteService.updateStatus(id, -1);
        if (!resp.isSuccess()) {
            log.error("fail to stop promotionDef(id={}),cause:{}",
                    id, resp.getError());
            throw new JsonResponseException(resp.getError());
        }
        return resp.getResult();
    }

    @RequestMapping(value = "/{id}/resume", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean resume(@PathVariable("id") Long id) {
        checkStatus(id, -1);

        Response<Boolean> resp = promotionDefWriteService.updateStatus(id, 1);
        if (!resp.isSuccess()) {
            log.error("fail to resume promotionDef(id={}),cause:{}",
                    id, resp.getError());
            throw new JsonResponseException(resp.getError());
        }
        return resp.getResult();
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean delete(@PathVariable("id") Long id) {
        Response<Boolean> resp = promotionDefWriteService.updateStatus(id, -2);
        if (!resp.isSuccess()) {
            log.error("fail to delete promotionDef(id={}),cause:{}",
                    id, resp.getError());
            throw new JsonResponseException(resp.getError());
        }
        return resp.getResult();
    }

    @RequestMapping(value = "/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Paging<PromotionDef> paging(@RequestParam(value = "status", required = false) Integer status,
                                       @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                       @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        Response<Paging<PromotionDef>> pagingResp = promotionDefReadService.paging(status, pageNo, pageSize);
        if (!pagingResp.isSuccess()) {
            log.error("fail to paging promotionDef with params:status={},pageNo={},pageSize={},cause:{}",
                    status, pageNo, pageSize, pagingResp.getError());
            throw new JsonResponseException(pagingResp.getError());
        }
        return pagingResp.getResult();
    }

    @RequestMapping(value = "/valid-of-global", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<PromotionDefState> findValidOfGlobal() {
        Response<List<PromotionDef>> findPromotionDefsResp = promotionDefReadService.findAllValid();
        if (!findPromotionDefsResp.isSuccess()) {
            log.error("failed to find all valid promotionDefs, cause:{}", findPromotionDefsResp.getError());
            throw new JsonResponseException(findPromotionDefsResp.getError());
        }
        List<PromotionDef> promotionDefs = findPromotionDefsResp.getResult();

        List<Long> promotionIds = Lists.newArrayListWithCapacity(promotionDefs.size());
        for (PromotionDef promotionDef : promotionDefs) {
            promotionIds.add(promotionDef.getId());
        }
        Response<Map<Long, Long>> countResp = promotionDefReadService.countInProcess(0L, promotionIds);
        if (!countResp.isSuccess()) {
            log.error("fail to count promotionDefs in process,promotionDefIds={},cause:{}",
                    promotionIds, countResp.getError());
            throw new JsonResponseException(countResp.getError());
        }
        Map<Long, Long> countByPromotionDefId = countResp.getResult();

        List<PromotionLevel> expectedPromotionLevels = Arrays.asList(PromotionLevel.GLOBAL);
        List<PromotionDefState> promotionDefStates = Lists.newArrayList();
        for (PromotionDef promotionDef : promotionDefs) {
            Behavior behavior = promotionBricks.getBehavior(promotionDef.getBehaviorKey());
            if (behavior == null) {
                log.warn("behavior key({}) not registered ", promotionDef.getBehaviorKey());
                continue;
            }
            PromotionLevel promotionLevel = PromotionLevel.fromBehavior(behavior);
            if (expectedPromotionLevels.contains(promotionLevel)) {
                long inProcessCount = countByPromotionDefId.get(promotionDef.getId());
                PromotionDefState promotionDefState = new PromotionDefState();
                promotionDefState.setPromotionDef(promotionDef);
                promotionDefState.setLevel(promotionLevel.getValue());
                promotionDefState.setInProcessCount(inProcessCount);
                promotionDefStates.add(promotionDefState);
            }
        }
        return promotionDefStates;
    }


    private void checkStatus(Long id, Integer expectedStatus) {
        Response<PromotionDef> findResp = promotionDefReadService.findById(id);
        if (!findResp.isSuccess()) {
            log.error("fail to find promotionDef by id={},cause:{}",
                    id, findResp.getError());
            throw new JsonResponseException(findResp.getError());
        }
        PromotionDef promotionDef = findResp.getResult();

        if (!Objects.equal(promotionDef.getStatus(), expectedStatus)) {
            log.error("promotionDef(id={}) expect status is:{},but actual is:{}",
                    id, expectedStatus, promotionDef.getStatus());
            throw new JsonResponseException("promotion.def.status.invalid");
        }
    }

}
