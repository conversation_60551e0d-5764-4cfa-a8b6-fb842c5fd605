package moonstone.web.admin.settle;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.RespUtil;
import moonstone.settle.dto.paging.SettleAbnormalTrackCriteria;
import moonstone.settle.model.SettleAbnormalTrack;
import moonstone.settle.service.SettleAbnormalTrackReadService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结算异常管理
 *
 * DATE: 16/11/13 下午6:00 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Slf4j
@RestController
@ConditionalOnProperty(name = "settle.controller.enable",  havingValue = "true",  matchIfMissing = true)
@RequestMapping("/api/settle")
public class AdminAbnormalTracks {

    @RpcConsumer
    private SettleAbnormalTrackReadService settleAbnormalTrackReadService;

    /**
     * 获取结算异常分页
     *
     * @param criteria 查询条件
     * @return 结算异常分页
     */
    @RequestMapping(value = "/abnormal-track-paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Paging<SettleAbnormalTrack>> pagingAbnormalTracks(SettleAbnormalTrackCriteria criteria){
        return RespUtil.log(
                settleAbnormalTrackReadService.pagingSettleAbnormalTracks(criteria),
                "pagingAbnormalTracks", criteria
        );
    }

}
