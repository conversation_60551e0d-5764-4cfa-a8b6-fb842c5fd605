package moonstone.web.admin.decoration;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.InvalidException;
import moonstone.wxa.model.WxaComponent;
import moonstone.wxa.service.WxaComponentReadService;
import moonstone.wxa.service.WxaComponentWriteService;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;


/**
 * Created by CaiZhy on 2018/11/20.
 */
@Slf4j
@RestController
@RequestMapping("/api/decoration/wxaComponent")
public class AdminWxaComponents {
    @RpcConsumer
    private WxaComponentReadService wxaComponentReadService;

    @RpcConsumer
    private WxaComponentWriteService wxaComponentWriteService;

    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long create(@RequestBody WxaComponent wxaComponent){
        try{
            Response<WxaComponent> exist = wxaComponentReadService.findByTemplateIdAndCode(wxaComponent.getTemplateId(), wxaComponent.getCode());
            if (!exist.isSuccess()){
                log.error("failed to find wxaComponent by templateId={}, code={}, error code: {}", wxaComponent.getTemplateId(), wxaComponent.getCode(), exist.getError());
                throw new JsonResponseException(exist.getError());
            }
            if (exist.getResult() != null){
                throw new JsonResponseException("wxaComponent.templateId.code.exist");
            }

            wxaComponent.setStatus(1);
            Response<Long> response = wxaComponentWriteService.create(wxaComponent);
            if (!response.isSuccess()) {
                log.error("failed to create wxaComponent({}), error code: {}", wxaComponent, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e){
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to create wxaComponent({}), cause: {}", wxaComponent, e.getMessage());
            throw new JsonResponseException(e.getMessage());
        }
    }

    @RequestMapping(method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean update(@RequestBody WxaComponent wxaComponent){
        try{
            //检查当前模板是否已存在该code
            if (StringUtils.hasText(wxaComponent.getCode())) {
                Response<WxaComponent> existWxaComponent = wxaComponentReadService.findById(wxaComponent.getId());
                if (!existWxaComponent.isSuccess()){
                    log.error("failed to find wxaComponent by id={}, error code: {}", wxaComponent.getId(), existWxaComponent.getError());
                    throw new JsonResponseException(existWxaComponent.getError());
                }
                Response<WxaComponent> exist = wxaComponentReadService.findByTemplateIdAndCode(existWxaComponent.getResult().getTemplateId(), wxaComponent.getCode());
                if (!exist.isSuccess()) {
                    log.error("failed to find wxaComponent by templateId={}, code={}, error code: {}", wxaComponent.getTemplateId(), wxaComponent.getCode(), exist.getError());
                    throw new JsonResponseException(exist.getError());
                }
                if (exist.getResult() != null) {
                    throw new JsonResponseException("wxaComponent.templateId.code.exist");
                }
            }
            //防止虚假数据
            wxaComponent.setTemplateId(null);

            Response<Boolean> response = wxaComponentWriteService.update(wxaComponent);
            if (!response.isSuccess()) {
                log.error("failed to update wxaComponent({}), error code: {}", wxaComponent, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e){
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to update wxaComponent({}), cause: {}", wxaComponent, e.getMessage());
            throw new JsonResponseException(e.getMessage());
        }
    }

    @RequestMapping(value = "/{id}",method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean delete(@PathVariable Long id){
        try{
            Response<Boolean> response = wxaComponentWriteService.updateStatus(id, -1);
            if (!response.isSuccess()) {
                log.error("failed to delete wxaComponent by id={}, error code: {}", id, response.getError());
                throw new JsonResponseException(response.getError());
            }
            return response.getResult();
        } catch (Exception e){
            Throwables.propagateIfInstanceOf(e, InvalidException.class);
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            log.error("fail to delete wxaComponent by id={}, cause: {}", id, e.getMessage());
            throw new JsonResponseException(e.getMessage());
        }
    }
}
