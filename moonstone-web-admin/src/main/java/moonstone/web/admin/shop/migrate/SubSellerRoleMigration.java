package moonstone.web.admin.shop.migrate;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.shop.service.ShopReadService;
import moonstone.user.enums.SubSellerRoleStatusEnum;
import moonstone.user.service.SellerRoleReadService;
import moonstone.user.service.SellerRoleWriteService;
import moonstone.web.admin.shop.migrate.request.MigrateRequest;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
public class SubSellerRoleMigration {

    @Resource
    private ShopReadService shopReadService;

    @Resource
    private SellerRoleReadService sellerRoleReadService;

    @Resource
    private SellerRoleWriteService sellerRoleWriteService;

    /**
     * 迁移内容：（量小，随便搞）
     * parana_sub_seller_roles
     *
     * @param request
     * @return
     */
    @PostMapping("/api/shop/subSellerRole/migrate")
    public APIResp<Boolean> migrate(@RequestBody MigrateRequest request) {
        if (request == null || request.getSourceShopId() == null || request.getTargetShopId() == null) {
            return APIResp.error("入参缺失");
        }

        var sourceShop = shopReadService.findById(request.getSourceShopId()).getResult();
        var targetShop = shopReadService.findById(request.getTargetShopId()).getResult();

        // 只迁 shop 的 userId 为主账号的东西
        var list = sellerRoleReadService.findByMasterUserIdAndStatus(sourceShop.getUserId(),
                SubSellerRoleStatusEnum.VALID.getCode()).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return APIResp.ok(true, "源商家的 parana_sub_seller_roles 数据集为空");
        }

        list.forEach(entity -> {
            entity.setMasterUserId(targetShop.getUserId());
            entity.setId(null);
        });
        sellerRoleWriteService.creates(list);

        return APIResp.ok(true);
    }
}
