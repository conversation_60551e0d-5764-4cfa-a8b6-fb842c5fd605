package moonstone.web.admin.shop;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.model.vo.DropDownBoxVo;
import moonstone.shop.service.ShopReadService;
import moonstone.shop.service.ShopWriteService;
import moonstone.shop.vo.ShopComboBoxVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 店铺相关接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/admin/shops")
@Slf4j
public class AdminShopsController {

    @Resource
    private ShopReadService shopReadService;

    @Resource
    private ShopWriteService shopWriteService;


    /**
     * 查询商家列表（组合成下拉框）
     * @param status 商家状态  （1：正常  -2：冻结  0：未激活）
     * @return 商家列表
     */
    @GetMapping("/combo/box/list")
    public Result<List<ShopComboBoxVo>> comboBoxList(@RequestParam(value = "status",required = false) Integer status) {
        log.info("小二端查询商家列表 （组合成下拉框）GET 请求参数 {}", status);
        return Result.data(shopReadService.comboBoxList(status));
    }

    /**
     * 查询商家列表（组合成下拉框）
     * @param status 商家状态  （1：正常  -2：冻结  0：未激活）
     * @return 商家列表
     */
    @PostMapping("/combo/box/list")
    public Result<List<DropDownBoxVo>> shopComboBoxList(@RequestParam(value = "status",required = false) Integer status) {
        log.info("小二端查询商家列表 （组合成下拉框）POST 请求参数 {}", status);
        return Result.data(shopReadService.shopComboBoxList(status));
    }
}
