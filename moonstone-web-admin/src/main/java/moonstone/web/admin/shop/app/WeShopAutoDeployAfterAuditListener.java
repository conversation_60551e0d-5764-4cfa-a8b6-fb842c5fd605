package moonstone.web.admin.shop.app;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.decoration.app.WeiXinMPVersionApp;
import moonstone.web.core.shop.events.WechatAuditFailEvent;
import moonstone.web.core.shop.model.WechatAuditRecord;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
public class WeShopAutoDeployAfterAuditListener {
    MongoTemplate mongoTemplate;
    WeiXinMPVersionApp weiXinMPVersionApp;
    ShopWxaCacheHolder shopWxaCacheHolder;

//    @Scheduled(cron = "0 0/5 * * * ?")
    public void autoQuery() {
        for (WechatAuditRecord auditRecord : mongoTemplate.find(Query.query(Criteria.where("status").is(0)), WechatAuditRecord.class)) {
            log.debug("{} auto query audit status for App[AppId => {}, ShopWxaId => {}, ProjectId => {}, TemplateId => {}]", LogUtil.getClassMethodName(),
                    shopWxaCacheHolder.findShopWxaByProjectId(auditRecord.getProjectId()).getId(), auditRecord.getShopWxaId(), auditRecord.getProjectId(), auditRecord.getTemplateId());
            if (weiXinMPVersionApp.getLastAuthResult(auditRecord.getProjectId())
                    .logErrorStr(reason -> EventSender.sendApplicationEvent(new WechatAuditFailEvent(auditRecord.getShopWxaId(), reason)))
                    .orElse(false)) {
                weiXinMPVersionApp.deploy(auditRecord.getProjectId())
                        .ifSuccess(success -> mongoTemplate.updateFirst(Query.query(Criteria.where("id").is(auditRecord.getId())), Update.update("status", 2), WechatAuditRecord.class))
                        .logException(e -> log.error("{} fail to deploy the app[ProjectId => {}, ShopWxaId => {}]", LogUtil.getClassMethodName("WechatAppAutoDeploy"),
                                auditRecord.getProjectId(), auditRecord.getShopWxaId(), e));
            }
        }
    }

//    @Scheduled(cron = "0 0/1 * * * ?")
    public void findAndDeploy() {
        for (WechatAuditRecord auditRecord : mongoTemplate.find(Query.query(Criteria.where("status").is(1)), WechatAuditRecord.class)) {
            log.debug("{} auto deploy App[ProjectId => {}, ShopWxaId => {}]", LogUtil.getClassMethodName(), auditRecord.getProjectId(), auditRecord.getShopWxaId());
            weiXinMPVersionApp.deploy(auditRecord.getProjectId())
                    .ifSuccess(success -> mongoTemplate.updateFirst(Query.query(Criteria.where("id").is(auditRecord.getId())), Update.update("status", 2), WechatAuditRecord.class))
                    .logException(e -> log.error("{} fail to deploy the app[ProjectId => {}, ShopWxaId => {}]", LogUtil.getClassMethodName("WechatAppAutoDeploy"),
                            auditRecord.getProjectId(), auditRecord.getShopWxaId(), e));
        }
    }
}
