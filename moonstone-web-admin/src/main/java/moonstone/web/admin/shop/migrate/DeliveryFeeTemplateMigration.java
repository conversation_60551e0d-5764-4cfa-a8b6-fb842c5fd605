package moonstone.web.admin.shop.migrate;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.delivery.model.DeliveryFeeBase;
import moonstone.delivery.model.DeliveryFeeTemplate;
import moonstone.delivery.model.SpecialDeliveryFee;
import moonstone.delivery.service.DeliveryFeeReadService;
import moonstone.delivery.service.DeliveryFeeWriteService;
import moonstone.delivery.service.SpecialDeliveryFeeReadService;
import moonstone.delivery.service.SpecialDeliveryFeeWriteService;
import moonstone.web.admin.shop.migrate.request.MigrateRequest;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class DeliveryFeeTemplateMigration {

    @Resource
    private DeliveryFeeWriteService deliveryFeeWriteService;

    @Resource
    private DeliveryFeeReadService deliveryFeeReadService;

    @Resource
    private SpecialDeliveryFeeReadService specialDeliveryFeeReadService;

    @Resource
    private SpecialDeliveryFeeWriteService specialDeliveryFeeWriteService;

    /**
     * 迁移内容：
     * parana_delivery_fee_templates
     *
     * @param request
     * @return
     */
    @PostMapping("/api/shop/deliveryFeeTemplate/migrate")
    public APIResp<Boolean> migrate(@RequestBody MigrateRequest request) {
        if (request == null || request.getSourceShopId() == null || request.getTargetShopId() == null) {
            return APIResp.error("入参缺失");
        }

        var list = deliveryFeeReadService.findDeliveryFeeTemplateByShopId(
                request.getSourceShopId()).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return APIResp.ok(true, "源数据集为空");
        }

        var map = findMap(list);

        list.forEach(entity -> {
            Long sourceId = entity.getId();

            entity.setId(null);
            entity.setShopId(request.getTargetShopId());
            var result = deliveryFeeWriteService.create(entity).getResult();
            if (result == null) {
                throw new RuntimeException("运费模板插入失败");
            }

            var subList = map.get(sourceId);
            if (!CollectionUtils.isEmpty(subList)) {
                createSub(result, request.getTargetShopId(), subList);
            }

        });

        return APIResp.ok(true);
    }

    private void createSub(Long templateId, Long targetShopId, List<SpecialDeliveryFee> subList) {
        subList.forEach(entity -> {
            entity.setId(null);
            entity.setShopId(targetShopId);
            entity.setDeliveryFeeTemplateId(templateId);
        });

        var response = specialDeliveryFeeWriteService.creates(subList).getResult();
        if (!Boolean.TRUE.equals(response)) {
            throw new RuntimeException(String.format("templateId=%s,运费模板明细插入失败", templateId));
        }
    }

    private Map<Long, List<SpecialDeliveryFee>> findMap(List<DeliveryFeeTemplate> list) {
        var source = specialDeliveryFeeReadService.findByDeliveryFeeTemplateIds(
                list.stream().map(DeliveryFeeBase::getId).toList()).getResult();
        if (CollectionUtils.isEmpty(source)) {
            return Collections.emptyMap();
        }

        return source.stream().collect(Collectors.groupingBy(SpecialDeliveryFee::getDeliveryFeeTemplateId));
    }
}
