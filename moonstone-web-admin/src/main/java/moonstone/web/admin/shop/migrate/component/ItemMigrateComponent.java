package moonstone.web.admin.shop.migrate.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.item.emu.ItemExtraIndex;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.model.Item;
import moonstone.item.model.RestrictedSalesAreaTemplate;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.*;
import moonstone.shop.model.Shop;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ItemMigrateComponent {

    @Resource
    private ItemWriteService itemWriteService;

    @Resource
    private SkuReadService skuReadService;

    @Resource
    private SkuWriteService skuWriteService;

    @Resource
    private SkuCustomReadService skuCustomReadService;

    @Resource
    private SkuCustomWriteService skuCustomWriteService;

    @Resource
    private ItemDetailReadService itemDetailReadService;

    @Resource
    private ItemDetailWriteService itemDetailWriteService;

    @Resource
    private ItemAttributeReadService itemAttributeReadService;

    @Resource
    private ItemAttributeWriteService itemAttributeWriteService;

    @Resource
    private ItemDeliveryFeeReadService itemDeliveryFeeReadService;

    @Resource
    private ItemDeliveryFeeWriteService itemDeliveryFeeWriteService;

    @Transactional(rollbackFor = Exception.class)
    public void migrate(Item item, Shop targetShop, Long deliveryFeeTemplateId, RestrictedSalesAreaTemplate restrictedSalesAreaTemplate) {
        Long sourceItemId = item.getId();

        // parana_items
        Long targetItemId = migrateItem(item, targetShop, restrictedSalesAreaTemplate);
        if (targetItemId == null || targetItemId.equals(sourceItemId)) {
            throw new RuntimeException(String.format("新商品id为空(sourceItemId=%s)", sourceItemId));
        }

        // parana_item_details
        migrateItemDetail(sourceItemId, targetItemId);

        // parana_item_attributes
        migrateItemAttribute(sourceItemId, targetItemId);

        // parana_item_delivery_fees
        migrateItemDeliveryFee(sourceItemId, targetItemId, deliveryFeeTemplateId);

        // parana_skus, parana_sku_customs
        migrateSkuInfo(sourceItemId, targetItemId, targetShop.getId());
    }

    private void migrateSkuInfo(Long sourceItemId, Long targetItemId, Long targetShopId) {
        var sourceSkus = skuReadService.findSkusByItemId(sourceItemId).getResult();
        if (CollectionUtils.isEmpty(sourceSkus)) {
            log.info("sourceItemId={}, 对应的 sku 为空", sourceItemId);
            return;
        }

        // key = skuId
        var skuCustomMap = findSkuCustomMap(sourceSkus.stream().map(Sku::getId).collect(Collectors.toList()));

        sourceSkus.forEach(sourceSku -> {
            Long sourceSkuId = sourceSku.getId();

            // parana_skus
            Long targetSkuId = migrateSku(sourceSku, targetItemId, targetShopId);

            // parana_sku_customs
            var sourceSkuCustom = skuCustomMap.get(sourceSkuId);
            if (sourceSkuCustom != null) {
                migrateSkuCustom(sourceSkuCustom, targetSkuId);
            }
        });
    }

    private void migrateSkuCustom(SkuCustom sourceSkuCustom, Long targetSkuId) {
        sourceSkuCustom.setId(null);
        sourceSkuCustom.setSkuId(targetSkuId);

        skuCustomWriteService.create(sourceSkuCustom);
    }

    private Long migrateSku(Sku sourceSku, Long targetItemId, Long targetShopId) {
        Long sourceSkuId = sourceSku.getId();

        sourceSku.setId(null);
        sourceSku.setShopId(targetShopId);
        sourceSku.setItemId(targetItemId);

        // 保存一下源 skuId
        var extra = sourceSku.getExtraMap();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        extra.put(SkuExtraIndex.migrateSourceSkuId.getCode(), sourceSkuId.toString());
        sourceSku.setExtraMap(extra);

        skuWriteService.create(sourceSku);
        return sourceSku.getId();
    }

    /**
     * @param skuIds
     * @return key = skuId
     */
    private Map<Long, SkuCustom> findSkuCustomMap(List<Long> skuIds) {
        var list = skuCustomReadService.findBySkuIds(skuIds).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(SkuCustom::getSkuId, o -> o, (k1, k2) -> k1));
    }

    private void migrateItemDeliveryFee(Long sourceItemId, Long targetItemId, Long deliveryFeeTemplateId) {
        var itemDeliveryFee = itemDeliveryFeeReadService.findByItemId(sourceItemId).getResult();
        if (itemDeliveryFee == null) {
            log.info("sourceItemId={}, 对应的 itemDeliveryFee 为空", sourceItemId);
            return;
        }

        itemDeliveryFee.setId(null);
        itemDeliveryFee.setItemId(targetItemId);
        itemDeliveryFee.setDeliveryFeeTemplateId(deliveryFeeTemplateId);
        itemDeliveryFeeWriteService.create(itemDeliveryFee);
    }

    private void migrateItemAttribute(Long sourceItemId, Long targetItemId) {
        var itemAttribute = itemAttributeReadService.findByItemId(sourceItemId).getResult();
        if (itemAttribute == null) {
            log.info("sourceItemId={}, 对应的 itemAttribute 为空", sourceItemId);
            return;
        }

        itemAttribute.setItemId(targetItemId);
        itemAttributeWriteService.create(itemAttribute);
    }

    private void migrateItemDetail(Long sourceItemId, Long targetItemId) {
        var itemDetail = itemDetailReadService.findByItemId(sourceItemId).getResult();
        if (itemDetail == null) {
            log.info("sourceItemId={}, 对应的 itemDetail 为空", sourceItemId);
            return;
        }

        itemDetail.setItemId(targetItemId);
        itemDetailWriteService.create(itemDetail);
    }

    private Long migrateItem(Item item, Shop targetShop, RestrictedSalesAreaTemplate restrictedSalesAreaTemplate) {
        Long sourceItemId = item.getId();

        item.setId(null);
        item.setShopId(targetShop.getId());
        item.setShopName(targetShop.getName());

        if (restrictedSalesAreaTemplate != null && restrictedSalesAreaTemplate.getId() != null) {
            item.setRestrictedSalesAreaTemplateId(restrictedSalesAreaTemplate.getId());
        }

        // 记录一下源头 item_id
        var extra = item.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            extra = new HashMap<>();
        }
        extra.put(ItemExtraIndex.migrateSourceItemId.name(), sourceItemId.toString());
        item.setExtra(extra);

        itemWriteService.create(item);
        return item.getId();
    }
}
