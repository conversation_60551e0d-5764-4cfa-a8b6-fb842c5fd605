package moonstone.web.admin.profit.application;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.view.WithdrawApplyView;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.service.WithDrawProfitApplyReadService;
import moonstone.order.service.WithDrawProfitApplyWriteService;
import moonstone.web.core.component.pay.app.Json;
import moonstone.web.core.component.pay.xinbada.domain.dto.OrderQueryDTO;
import moonstone.web.core.component.pay.xinbada.domain.slice.XinBaDaPayAccountHelperSlice;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.locks.Lock;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
@RestController
public class WithdrawErrorQueryRejectJob {
    private final WithDrawProfitApplyReadService withDrawProfitApplyReadService;
    private final WithDrawProfitApplyWriteService withDrawProfitApplyWriteService;
    private final XinBaDaPayAccountHelperSlice xinBaDaPayAccountHelperSlice;
    private final RedissonClient redissonClient;

    @GetMapping("/api/manual/check")
//    @Scheduled(cron = "0 0/5 * * * ?")
    public void scanAndQuery() {
        List<WithDrawProfitApply> needQueryList = withDrawProfitApplyReadService.getRequireQuery(null, Integer.MAX_VALUE).take();
        for (WithDrawProfitApply apply : needQueryList) {
            if (Objects.isNull(apply.getPaySerialNo())) {
                if (!apply.isReject() && !apply.isClosed() && !apply.isPaid()) {
                    log.warn("{} [{}] stay at unknown status", LogUtil.getClassMethodName(), Json.toJson(WithdrawApplyView.from(apply)));
                }
                continue;
            }
            Lock lock = redissonClient.getLock(String.format("[%s](%s)", WithDrawProfitApply.class.getSimpleName(), apply.getId()));
            try {
                if (!lock.tryLock()) {
                    continue;
                }
                xinBaDaPayAccountHelperSlice.getPayToken(apply.getSourceId())
                        .ifSuccess(token -> {
                            OrderQueryDTO orderQueryDTO = xinBaDaPayAccountHelperSlice.queryPaymentOrder(token, apply.getPaySerialNo()).take();
                            if (orderQueryDTO.isCancelled() || orderQueryDTO.isRefund()) {
                                if (!apply.isReject() && !apply.isPaid()) {
                                    withDrawProfitApplyWriteService.reject(apply.getPaySerialNo(), LocalDateTime.now(), "支付已经取消").take();
                                } else {
                                    log.warn("{} apply[{}] is rejected already ", LogUtil.getClassMethodName(), apply.getId());
                                }
                            }
                            if (orderQueryDTO.isSuccess()) {
                                if (!apply.isPaid()) {
                                    withDrawProfitApplyWriteService.paidAt(apply.getPaySerialNo(), LocalDateTime.now()).take();
                                }
                            }
                        });
            } catch (Exception e) {
                log.error("{} query error of withdraw [{}]", LogUtil.getClassMethodName(), Json.toJson(apply), e);
            } finally {
                lock.unlock();
            }
        }
    }
}
