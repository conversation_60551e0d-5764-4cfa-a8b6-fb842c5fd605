package moonstone.web.admin.inner;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.user.model.RoleMenu;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 内部接口
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("api/inner/role/menu")
public class RoleMenuInnerController {


	@Resource
	private MongoTemplate mongoTemplate;

	/**
	 * 手动保存角色菜单
	 * @param roleMenu 角色菜单
	 * @return 保存后的角色菜单
	 */
	@PostMapping
	public Result<RoleMenu> addRoleMenu(@RequestBody RoleMenu roleMenu) {
		log.info("手动保存角色菜单 {}", JSONUtil.toJsonStr(roleMenu));
		roleMenu.setCreatedAt(new Date());
		mongoTemplate.save(roleMenu);
		log.info("保存后的菜单 {}", JSONUtil.toJsonStr(roleMenu));
		return Result.data(roleMenu);
	}

	/**
	 * 手动根据路由名称查询路由信息
	 * @param name 路由名称
	 * @return 路由名称表
	 */
	@GetMapping("find/by/name")
	public Result<List<RoleMenu>> findByName(@RequestParam("name") String name) {
		log.info("手动根据路由名称查询路由信息 {}", name);
		return Result.data(mongoTemplate.find(Query.query(Criteria.where("name").is(name)), RoleMenu.class));
	}


}
