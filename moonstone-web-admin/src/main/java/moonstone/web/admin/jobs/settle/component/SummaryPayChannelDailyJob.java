package moonstone.web.admin.jobs.settle.component;

import com.google.common.base.Stopwatch;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.settle.service.PayChannelDailySummaryWriteService;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * DATE: 16/8/11 下午3:58 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Slf4j
@Component
public class SummaryPayChannelDailyJob {

    @Resource
    private PayChannelDailySummaryWriteService payChannelDailySummaryWriteService;

    @Resource
    private RedissonClient redissonClient;

    private static final DateTimeFormatter DFT = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    public void payChannelDailySummary(Date sumAt) {

        Lock iLock = redissonClient.getLock(getClass().getName() + "#payChannelDailySummary");
        if (!iLock.tryLock()) {
            return;
        }

        log.info("[CRON-JOB] generatePayChannelDailySummary begin {}", DFT.print(DateTime.now()));

        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            Response<Boolean> response = payChannelDailySummaryWriteService.generatePayChannelDailySummary(sumAt);
            if (!response.isSuccess()) {
                log.error("handle payChannel daily summary fail,error:{}", response.getError());
            }
            stopwatch.stop();
            log.info("[CRON-JOB] generatePayChannelDailySummary done at {} cost {} ms", DFT.print(DateTime.now()), stopwatch.elapsed(TimeUnit.MILLISECONDS));
        } finally {
            iLock.unlock();
        }
    }

}
