package moonstone.web.admin.jobs;

import com.google.common.base.Stopwatch;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import lombok.extern.slf4j.Slf4j;
import moonstone.weShop.service.WeShopProfitSettleService;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * Created by CaiZhy on 2018/12/18.
 */
@Slf4j
@RestController
@ConditionalOnProperty(value = "enable.weShopProfit.settle", havingValue = "true", matchIfMissing = true)
public class SettleWeShopProfitJob {
    @RpcConsumer
    public WeShopProfitSettleService weShopProfitSettleService;

    private static final DateTimeFormatter DFT = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

//    @Scheduled(cron="0 0/5 * * * ?")
    @RequestMapping("/api/weShopProfit/settle")
    public void settleProfitJob(){  //从0分钟开始，每隔5分钟执行一次
        log.info("[CRON-JOB] settle weShop profit begin {}", DFT.print(DateTime.now()));
        Stopwatch stopwatch = Stopwatch.createStarted();

        weShopProfitSettleService.settleProfitJob();

        stopwatch.stop();
        log.info("[CRON-JOB] settle weShop profit done at {} cost {} ms", DFT.print(DateTime.now()), stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }
}
