package moonstone.web.admin.jobs.payment;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.XxlJobDesc;
import moonstone.message.PaymentPaidMessage;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.model.Payment;
import moonstone.order.service.PaymentReadService;
import moonstone.showcase.mq.Topic;
import moonstone.web.admin.config.JobSwitch;
import moonstone.web.core.order.service.OrderDeclareService;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
@RestController
@Slf4j
public class PaymentInfoJob {
    private static final DateTimeFormatter DFT = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
    @Autowired
    private PaymentReadService paymentReadService;
    @Autowired
    private OrderDeclareService orderDeclareService;
    @Autowired
    private JobSwitch jobSwitch;
    @Resource
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 额外线程进行完成推送任务
     */
    @PostMapping("/api/payment/push/circle")
    public void scanPaymentInThreadPool() {
        scanPayment("");
    }

    // 旧的推送系统
    @PostMapping("/api/payment/push/normal")
    public void oldNormalPush() {
        pushWaitPayment("");
    }

    // 已经推送失败的订单，降低其再次推送的时间间隔
    @PostMapping("/api/payment/push/fail")
    public void pushFailPaymentAtTP() {
        pushFail("");
    }

    @XxlJob("PayInfoPushJob#scanPayment")
    @XxlJobDesc(desc = "支付单扫描", core = "0 0/5 * * * ?")
    public ReturnT<String> scanPayment(String param) {
        log.info("支付单推送 scanPayment");
        List<Payment> paymentList = paymentReadService.listByPushStatus(PaymentPushStatus.WAIT_PUSH).getResult();
        log.info("支付单推送 scanPayment 待推送 {}", paymentList.size());

        paymentList.addAll(paymentReadService.listByPushStatus(PaymentPushStatus.DECLARED_FAIL_WAIT_PUSH_RETRY).getResult());
        log.info("支付单推送 scanPayment 所有数量 {}", paymentList.size());

        for (Payment payment : paymentList) {
            sendPaymentPaidMessage(payment.getId());
        }
        paymentList = paymentReadService.listByPushStatus(PaymentPushStatus.DECLARE_SUCCESS).getResult();
        log.info("支付单推送 scanPayment 支付单申报成功 {}", paymentList.size());

        for (Payment payment : paymentList) {
            sendPaymentPaidMessage(payment.getId());
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("PayInfoPushJob#pushWaitPayment")
    @XxlJobDesc(desc = "支付单推送", core = "0 0/3 * * * ?")
    public ReturnT<String> pushWaitPayment(String param) {  //
        if (jobSwitch.isOpenPayInfoPush()) {
            DateTime startAt = DateTime.now();
            Stopwatch stopwatch = Stopwatch.createStarted();
            List<Payment> paymentList = paymentReadService.listByPushStatus(PaymentPushStatus.WAIT_PUSH).getResult();
            log.info("待推送的支付单单号id {}", JSONUtil.toJsonStr(paymentList.stream().map(Payment::getId).toList()));
            final int waitPaymentSize = paymentList.size();
            for (Payment payment : paymentList) {
                try {
                    orderDeclareService.paymentDeclare(payment);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    log.error("{} paymentId:{}", LogUtil.getClassMethodName("DECLARED"), payment.getId());
                }
            }
            paymentList = paymentReadService.listByPushStatus(PaymentPushStatus.DECLARE_SUCCESS).getResult();
            log.info("支付单推送 pushWaitPayment 支付单申报成功 {}", paymentList.size());
            final int declaredPaymentSize = paymentList.size();
            for (Payment payment : paymentList) {
                try {
                    orderDeclareService.payInfoPushV1(payment);
                    log.debug("{} paymentId:{}", LogUtil.getClassMethodName("ready-push-declare"), payment == null ? "null" : payment.getId());
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("{} paymentId:{}", LogUtil.getClassMethodName("PUSH-PAY-INFO"), Optional.ofNullable(payment).map(Payment::getId).orElse(null));
                }
            }
            stopwatch.stop();
            log.info("[CRON-JOB] push payInfo(WAIT_PUSH) begin at {} done at {} tried-count (wait:[{}]) (declared:[{}]) cost {} ms", startAt, DFT.print(DateTime.now()), waitPaymentSize, declaredPaymentSize, stopwatch.elapsed(TimeUnit.MILLISECONDS));
        } else {
            log.info("payInfo push switch is close");
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("PayInfoPushJob#pushFail")
    @XxlJobDesc(desc = "支付单失败重推", core = "0 0/15 * * * ?")
    public ReturnT<String> pushFail(String param) {  //每隔30分钟执行一次
        if (jobSwitch.isOpenPayInfoPush()) {
            log.info("[CRON-JOB] push payInfo(PUSH_FAIL) begin {}", DFT.print(DateTime.now()));
            Stopwatch stopwatch = Stopwatch.createStarted();

            List<Payment> paymentList = paymentReadService.listByPushStatus(PaymentPushStatus.DECLARE_FAIL).getResult();
            for (Payment payment : paymentList) {
                sendPaymentPaidMessage(payment.getId());
            }
            paymentList = paymentReadService.listByPushStatus(PaymentPushStatus.DECLARE_SUCCESS).getResult();

            log.info("支付单推送 pushFail 支付单申报成功 {}", paymentList.size());
            paymentList.addAll(Optional.ofNullable(paymentReadService.listByPushStatus(PaymentPushStatus.PUSH_FAIL).getResult()).orElse(new ArrayList<>()));

            log.info("支付单推送 pushFail 支付单申报成功加推送失败 {}", paymentList.size());
            for (Payment payment : paymentList) {
                sendPaymentPaidMessage(payment.getId());
            }
            stopwatch.stop();
            log.info("[CRON-JOB] push payInfo(PUSH_FAIL) done at {} cost {} ms", DFT.print(DateTime.now()), stopwatch.elapsed(TimeUnit.MILLISECONDS));
        } else {
            log.warn("payInfo push switch is close");
        }
        return ReturnT.SUCCESS;
    }


    private void sendPaymentPaidMessage(Long paymentId) {
        PaymentPaidMessage message = new PaymentPaidMessage();
        message.setPaymentId(paymentId);
        SendResult sendResult = rocketMQTemplate.syncSend(Topic.PAYMENT_PAID_MESSAGE_TOPIC, message);
        if (SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
            log.info("支付成功消息发送成功 {} {}", paymentId, sendResult.getMsgId());
        } else {
            log.error("支付成功消息发送失败 {}", JSONObject.toJSONString(message));
        }
    }

}
