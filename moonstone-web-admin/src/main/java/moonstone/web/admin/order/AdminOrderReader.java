package moonstone.web.admin.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.constants.ParanaConstants;
import moonstone.common.utils.DataPage;
import moonstone.common.utils.Json;
import moonstone.common.utils.TotalCalculation;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.OrderDetail;
import moonstone.order.dto.OrderGroup;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.OrderReceiverInfo;
import moonstone.order.model.ReceiverInfo;
import moonstone.order.vo.PayInfoVO;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.web.admin.order.dto.OrderGroupVo;
import moonstone.web.core.order.OrderReadLogic;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Mail: <EMAIL>
 * Data: 16/6/28
 * Author: yangzefeng
 */
@Controller
@Slf4j
public class AdminOrderReader {

    @Autowired
    private OrderReadLogic orderReadLogic;

    @RpcConsumer
    private ShopReadService shopReadService;

//    https://admin-mall.yang800.com/trade/order?
//    id=1&  主表
//    orderId=2&
//    declaredId=3& 主表
//    buyerId=4& 主表
//    mobile=5&
//    pushStatus=1&
//    shopName=7&
//    startAt=2025-01-17&
//    endAt=2025-01-18&
//    start=2025-01-19&
//    end=2025-01-20&
//    statusStr=-1
    @RequestMapping(value = "/api/order/paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<Paging<OrderGroupVo>> findBy(@RequestParam Map<String, String> orderCriteria) {
        orderCriteria.put("shipmentStartAt", orderCriteria.get("start"));
        orderCriteria.put("shipmentEndAt", orderCriteria.get("end"));
        orderCriteria.remove("start");
        orderCriteria.remove("end");
        log.info("criteria:{}", JSON.toJSONString(orderCriteria));

        // 对查询条件进行处理然后在查询数据
        var param = Json.parseObject(Json.toJson(orderCriteria), OrderCriteria.class);
        assert param != null;
        Response<Paging<OrderGroup>> orderGroupResponse = orderReadLogic.pagingOrder(param);
        /// 实体化json数据
        if (orderGroupResponse.isSuccess()) {
            List<OrderGroup> orderGroups = orderGroupResponse.getResult().getData();
            List<OrderGroupVo> orderGroupVos = new LinkedList<>();
            for (OrderGroup orderGroup : orderGroups) {
                List<OrderGroupVo.SkuOrderAndOperation> orderAndOperations = new LinkedList<>();
                OrderGroupVo orderGroupVo = new OrderGroupVo();
                BeanUtils.copyProperties(orderGroup, orderGroupVo);
                for (var skuOrderAndOperation : orderGroup.getSkuOrderAndOperations()) {
                    OrderGroupVo.SkuOrderAndOperation skuOrderAndOperationVo = new OrderGroupVo.SkuOrderAndOperation();
                    String json = skuOrderAndOperation.order().getExtra() == null ? "[]" : skuOrderAndOperation.order().getExtra().getOrDefault("pushSystemList", "[]");
                    JSONArray pushSystemListContents = JSON.parseArray(json);
                    BeanUtils.copyProperties(skuOrderAndOperation, skuOrderAndOperationVo);
                    skuOrderAndOperationVo.setPushSystemListContents(pushSystemListContents);
                    orderAndOperations.add(skuOrderAndOperationVo);
                }
                orderGroupVo.setSkuOrderAndOperations(orderAndOperations);
                orderGroupVos.add(orderGroupVo);
            }
            Paging<OrderGroupVo> paging = new Paging<>();
            paging.setTotal(orderGroupResponse.getResult().getTotal());
            paging.setData(orderGroupVos);          /// 描述化审核状态
            Map<Long, Shop> shopIdTowerShop = new HashMap<>();
            for (OrderGroupVo orderGroupVo : paging.getData()) {
                //init Shop map
                if (shopIdTowerShop.get(orderGroupVo.getShopOrder().getShopId()) == null) {
                    Response<Shop> rShop = shopReadService.findById(orderGroupVo.getShopOrder().getShopId());
                    if (!rShop.isSuccess()) {
                        log.error("shop order find fail shop (id:{})", orderGroupVo.getShopOrder().getShopId());
                        return Response.fail(rShop.getError());
                    }
                    shopIdTowerShop.put(rShop.getResult().getId(), rShop.getResult());
                }
                /// 1. 判断shop是否开启审核
                Shop shop = shopIdTowerShop.get(orderGroupVo.getShopOrder().getShopId());
                Map<String, String> shopExtra = shapeMap(shop.getExtra());
                /// 2. 如果开启了审核则 设置订单显示状态
                if (shopExtra.getOrDefault(ParanaConstants.SHOP_OPEN_ORDER_AUTH, "false").equals("true")) {
                    if (orderGroupVo.getShopOrder().getStatus() == OrderStatus.PAID.getValue()) {
                        orderGroupVo.setWaitingSellerAuthDesc(orderGroupVo.getWaitingSellerAuth() != null && orderGroupVo.getWaitingSellerAuth() ? "待审核" : "审核通过");
                    }
                }
                OrderStatus status = OrderStatus.fromInt(orderGroupVo.getShopOrder().getStatus());
                /// 3. 此时如果订单状态为null 则再设置其本来的订单状态
                if (orderGroupVo.getWaitingSellerAuthDesc() == null) {
                    orderGroupVo.setWaitingSellerAuthDesc(status.intoString());
                }
            }
            return Response.ok(paging);
        }
        return Response.fail(orderGroupResponse.getError());

    }

    /**
     * llyj
     */
    @RequestMapping(value = "/api/order/paging/new", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Result<?> findByNew(@RequestParam Map<String, String> orderCriteria) {
        orderCriteria.put("shipmentStartAt", orderCriteria.get("start"));
        orderCriteria.put("shipmentEndAt", orderCriteria.get("end"));
        orderCriteria.remove("start");
        orderCriteria.remove("end");
        log.info("criteria:{}", JSON.toJSONString(orderCriteria));

        var param = Json.parseObject(Json.toJson(orderCriteria), OrderCriteria.class);
        assert param != null;
        /// 实体化json数据
        Response<Paging<OrderGroup>> orderGroupResponse = orderReadLogic.pagingOrder(param);
        if (orderGroupResponse.isSuccess()) {
            List<OrderGroup> orderGroups = orderGroupResponse.getResult().getData();
            List<OrderGroupVo> orderGroupVos = new LinkedList<>();
            for (OrderGroup orderGroup : orderGroups) {
                List<OrderGroupVo.SkuOrderAndOperation> orderAndOperations = new LinkedList<>();
                OrderGroupVo orderGroupVo = new OrderGroupVo();
                BeanUtils.copyProperties(orderGroup, orderGroupVo);
                for (var skuOrderAndOperation : orderGroup.getSkuOrderAndOperations()) {
                    OrderGroupVo.SkuOrderAndOperation skuOrderAndOperationVo = new OrderGroupVo.SkuOrderAndOperation();
                    String json = skuOrderAndOperation.order().getExtra() == null ? "[]" : skuOrderAndOperation.order().getExtra().getOrDefault("pushSystemList", "[]");
                    JSONArray pushSystemListContents = JSON.parseArray(json);
                    BeanUtils.copyProperties(skuOrderAndOperation, skuOrderAndOperationVo);
                    skuOrderAndOperationVo.setPushSystemListContents(pushSystemListContents);
                    orderAndOperations.add(skuOrderAndOperationVo);
                }
                orderGroupVo.setSkuOrderAndOperations(orderAndOperations);
                orderGroupVos.add(orderGroupVo);
            }
            Paging<OrderGroupVo> paging = new Paging<>();
            paging.setTotal(orderGroupResponse.getResult().getTotal());
            paging.setData(orderGroupVos);          /// 描述化审核状态
            //获取分页信息
            long pageSize;
            long currentPage;
            //获取当页条数
            if (Objects.nonNull(orderCriteria.get("size"))) {
                pageSize = Long.parseLong(orderCriteria.get("size"));
            } else {
                pageSize = 20L;
            }
            //获取当前页数
            if (Objects.nonNull(orderCriteria.get("pageNo"))) {
                currentPage = Long.parseLong(orderCriteria.get("pageNo"));
            } else {
                currentPage = 1L;
            }
            //构建分页信息类
            TotalCalculation totalCalculation = new TotalCalculation(pageSize, currentPage, paging.getTotal(), 0L);
            totalCalculation.setTotalPage(totalCalculation.getPageSize(), totalCalculation.getTotalCount());
            Map<Long, Shop> shopIdTowerShop = new HashMap<>();
            for (OrderGroupVo orderGroupVo : paging.getData()) {
                //init Shop map
                if (shopIdTowerShop.get(orderGroupVo.getShopOrder().getShopId()) == null) {
                    Response<Shop> rShop = shopReadService.findById(orderGroupVo.getShopOrder().getShopId());
                    if (!rShop.isSuccess()) {
                        log.error("shop order find fail shop (id:{})", orderGroupVo.getShopOrder().getShopId());
                        return Result.fail(rShop.getError());
                    }
                    shopIdTowerShop.put(rShop.getResult().getId(), rShop.getResult());
                }
                /// 1. 判断shop是否开启审核
                Shop shop = shopIdTowerShop.get(orderGroupVo.getShopOrder().getShopId());
                Map<String, String> shopExtra = shapeMap(shop.getExtra());
                /// 2. 如果开启了审核则 设置订单显示状态
                if (shopExtra.getOrDefault(ParanaConstants.SHOP_OPEN_ORDER_AUTH, "false").equals("true")) {
                    if (orderGroupVo.getShopOrder().getStatus() == OrderStatus.PAID.getValue()) {
                        orderGroupVo.setWaitingSellerAuthDesc(orderGroupVo.getWaitingSellerAuth() != null && orderGroupVo.getWaitingSellerAuth() ? "待审核" : "审核通过");
                    }
                }
                OrderStatus status = OrderStatus.fromInt(orderGroupVo.getShopOrder().getStatus());
                /// 3. 此时如果订单状态为null 则再设置其本来的订单状态
                if (orderGroupVo.getWaitingSellerAuthDesc() == null) {
                    orderGroupVo.setWaitingSellerAuthDesc(status.intoString());
                }
            }
            return Result.data(DataPage.build(totalCalculation, paging.getData()));
        }
        return Result.fail(orderGroupResponse.getError());

    }

    private Map<String, String> shapeMap(Map<String, String> map) {
        return map == null ? new TreeMap<>() : map;
    }


    @RequestMapping(value = "/api/order/{id}/showPayerInfo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<OrderDetail> showPayerInfo(@PathVariable("id") Long id) {
        return orderReadLogic.showPayerInfo(id);
    }


    @RequestMapping(value = "/api/order/{id}/detail", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Response<OrderDetail> detail(@PathVariable("id") Long id) {
        Response<OrderDetail> detailResponse = orderReadLogic.orderDetail(id);
        OrderDetail orderDetail = detailResponse.getResult();
        if (Objects.nonNull(orderDetail)) {
            PayInfoVO payInfo = orderDetail.getPayInfo();
            if (Objects.nonNull(payInfo)) {
                payInfo.desensitization();
            }
            List<OrderReceiverInfo> orderReceiverInfos = orderDetail.getOrderReceiverInfos();
            if (!CollectionUtils.isEmpty(orderReceiverInfos)) {
                orderReceiverInfos.forEach(item -> {
                    // 脱敏
                    ReceiverInfo receiverInfo = item.getReceiverInfo();
                    receiverInfo.desensitization();
                });
            }
        }
        return detailResponse;
    }
}