package moonstone.web.admin.order.app;

import moonstone.common.utils.LogUtil;
import moonstone.web.core.component.order.MarkIfFirstOrderApp;
import org.dozer.inject.Inject;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;

@RestController
public class AdminFirstOrderMarkController {
  @Resource
  MarkIfFirstOrderApp markIfFirstOrderApp;

  @RequestMapping("/api/order/first-order-mark")
  @GetMapping
  public String mark(String date, Long shopId) {
    try {
      new Thread(() -> markIfFirstOrderApp.scanAndMarkFirstOrder(LocalDate.parse(date), shopId)).start();
      return "SUCCESS";
    } catch (Exception e) {
      return LogUtil.express(e);
    }
  }
}
