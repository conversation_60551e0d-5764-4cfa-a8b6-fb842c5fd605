package moonstone.web.admin.order;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.danding.encrypt.constant.RegexConstant;
import com.google.common.base.Objects;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.api.Result;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UUID;
import moonstone.common.utils.*;
import moonstone.order.api.OrderAndOperation;
import moonstone.order.api.OrderExportReader;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.OrderGroup;
import moonstone.order.dto.PushOneOrderResult;
import moonstone.order.dto.RefundCriteria;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.dto.fsm.PaymentPushStatus;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.model.PayerInfo;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.exports.common.Exporter;
import moonstone.web.core.fileNew.dto.AdminShopOrderDto;
import moonstone.web.core.fileNew.logic.ShopOrderLogic;
import moonstone.web.core.files.service.OSSClientService;
import moonstone.web.core.order.OrderReadLogic;
import moonstone.web.core.order.OrderWriteLogic;
import moonstone.web.core.order.dto.OrderExportView;
import moonstone.web.core.order.service.OrderPushService;
import org.apache.ibatis.session.SqlSession;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Predicate;

/**
 * Mail: <EMAIL>
 * Data: 16/7/19PromotionExecutor
 * Author: yangzefeng
 */
@SuppressWarnings("deprecation")
@Slf4j
@RestController
public class AdminOrders {

    private final ExecutorService EXECUTOR = Executors.newFixedThreadPool(8, new ParanaDefaultThreadFactory("adminOrders"));
    private final ConcurrentMap<String, String> UPLOAD_TASK_STATUS = new ConcurrentHashMap<>();
    @Autowired
    Exporter exporter;
    @Autowired
    SqlSession sqlSession;
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    ReceiverInfoReadService receiverInfoReadService;
    @Autowired
    private OrderWriteLogic orderWriteLogic;
    @Autowired
    private OrderReadLogic orderReadLogic;
    @Autowired
    private OrderExportReader orderExportReader;
    @Autowired
    private SkuOrderReadService skuOrderReadService;
    @Autowired
    private UserTypeBean userTypeBean;
    @Autowired
    private OrderPushService orderPushService;
    @Autowired
    private SkuOrderWriteService skuOrderWriteService;
    @Autowired
    private ShopOrderReadService shopOrderReadService;
    @Autowired
    private OrderWriteService orderWriteService;
    @Autowired
    private PaymentReadService paymentReadService;
    @Autowired
    private PaymentWriteService paymentWriteService;
    @Autowired
    private OSSClientService ossClientService;
    @Autowired
    private EnvironmentConfig environmentConfig;

    @Autowired
    private ShopOrderLogic shopOrderLogic;

    @RequestMapping(value = "/api/admin/order/cancel", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean adminCancelOrder(@RequestParam("orderId") Long orderId,
                                    @RequestParam(value = "orderType", defaultValue = "1") Integer orderType) {
        OrderLevel orderLevel = OrderLevel.fromInt(orderType);
        OrderBase orderBase = orderReadLogic.findOrder(orderId, orderLevel);
        return orderWriteLogic.updateOrderStatusByOrderEvent(orderBase, orderLevel, OrderEvent.SELLER_CANCEL);
    }

    @RequestMapping("/api/admin/order/refundOrderExcelExport")
    public void refundOrderExcelExport(RefundCriteria criteria, HttpServletResponse response, @RequestParam(defaultValue = "false") boolean noAuth) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.addHeader("Content-Disposition", "attachment; filename=RefundOrderExcelExport.xlsx");
            if (!noAuth && UserUtil.getCurrentUser() == null) {
                return;
            }
            orderExportReader.refundExportExcel(criteria, response.getOutputStream());
        } catch (Exception ex) {
            log.error("orderExcelExport failed:" + ex.getMessage());
            response.setStatus(502);
        }
    }

    /**
     * 订单导出
     * @param req 请求参数
     * @return 导出任务ID
     */
    @PostMapping("/api/admin/order/orderExcelExport/v2")
    public Result<Long> orderExcelExport(@RequestBody AdminShopOrderDto req) {
        log.info("订单导出 请求参数 {}", JSONUtil.toJsonStr(req));
        return shopOrderLogic.adminOrderExcelExport(req);
    }

    @RequestMapping("/api/admin/order/orderExcelExport")
    public void orderExcelExport(OrderCriteria criteria, @RequestParam(name = "start", required = false) Date start, @RequestParam(name = "end", required = false) Date end, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.addHeader("Content-Disposition", "attachment; filename=orderExcelExport.xlsx");
            List<OrderExportView> orderGroups = new LinkedList<>();
            if(criteria.getStartAt() != null && criteria.getEndAt() != null) {
                criteria.setStartAt(DateUtil.withTimeAtStartOfDay(criteria.getStartAt()));
                criteria.setEndAt(DateUtil.withTimeAtEndOfDay(criteria.getEndAt()));
            }
            if (start != null && end != null) {
                criteria.setShipmentStartAt(DateUtil.withTimeAtStartOfDay(start));
                criteria.setShipmentEndAt(DateUtil.withTimeAtEndOfDay(end));
            }
            for (int i = 1; i < 1000; i++) {
                criteria.setPageNo(i);
                criteria.setSize(50);
                var orders = orderReadLogic.queryOrder(criteria).getData();
                if (orders.isEmpty()) break;
                orderGroups.addAll(packOrderExportView(orders));
            }
            exporter.export(orderGroups, OrderExportView.class).write(response.getOutputStream());
        } catch (Exception ex) {
            log.error("orderExcelExport failed:" + ex.getMessage());
            response.setStatus(502);
        }
    }

    private Collection<? extends OrderExportView> packOrderExportView(List<OrderGroup> orders) {
        List<OrderExportView> views = new LinkedList<>();
        for (OrderGroup order : orders) {
            // 全部进行脱敏
            String payerName = null;
            String payerNo = null;
            var orderId = order.getShopOrder().getId();
            String mobile = "";
            String address = "";
            String userName = "";
            for (ReceiverInfo receiverInfo : receiverInfoReadService.findByOrderId(orderId, OrderLevel.SHOP).getResult()) {
                receiverInfo.desensitization();
                mobile = receiverInfo.getMobile();
                address = new StringBuilder().append(receiverInfo.getProvince())
                        .append("-").append(receiverInfo.getCity())
                        .append("-").append(receiverInfo.getRegion())
                        .append("-").append(receiverInfo.getStreet())
                        .append("-").append(receiverInfo.getDetail()).toString();
                userName = receiverInfo.getReceiveUserName();
            }
            var info = mongoTemplate.findOne(Query.query(Criteria.where("orderId").is(order.getShopOrder().getId())), PayerInfo.class);
            if (info != null) {
                try {
                    var data = PayerInfo.Helper.decode(EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey),
                            info);
                    payerName = data.getName().replaceAll(RegexConstant.NAME,RegexConstant.NAME_REPLACE);
                    payerNo = data.getNo().replaceAll(RegexConstant.IDCARD,RegexConstant.IDCARD_REPLACE);
                } catch (Exception e) {
                    log.error("FAIL TO DECODE THE ERROR", e);
                }
            }
            for (OrderAndOperation skuOrderAndOperation : order.getSkuOrderAndOperations()) {
                var skuOrder = (SkuOrder) skuOrderAndOperation.order();
                var skuOrderView = new OrderExportView();
                BeanUtils.copyProperties(skuOrder, skuOrderView);
                skuOrderView.setTax(showFee(skuOrder.getTax()));
                skuOrderView.setShipFee(showFee(skuOrder.getShipFee()));
                skuOrderView.setDiscount(showFee(skuOrder.getDiscount()));
                skuOrderView.setOriginFee(showFee(skuOrder.getOriginFee()));
                // set the shipment corp
                Shipment ship = sqlSession.selectOne("Shipment.findShipInfo", Map.of("orderId", skuOrder.getOrderId(), "type", 1));
                if (ship == null) {
                    ship = sqlSession.selectOne("Shipment.findShipInfo", Map.of("orderId", skuOrder.getId(), "type", 2));
                }
                if (ship != null) {
                    skuOrderView.setShipmentName(ship.getShipmentCorpName());
                    skuOrderView.setShipmentTime(DateUtil.toString(ship.getCreatedAt()));
                    skuOrderView.setConfirmAt(DateUtil.toString(ship.getConfirmAt()));
                }
                skuOrderView.setCreateAt(DateUtil.toString(skuOrder.getCreatedAt()));
                switch (skuOrder.getPushStatus()) {
                    case 2 -> skuOrderView.setPushStatus("推送完成");
                    case 1 -> skuOrderView.setPushStatus("待推送");
                    case 4 -> skuOrderView.setPushStatus("等待支付单推送");
                    case 0 -> skuOrderView.setPushStatus("无需推送");
                    case -1 -> skuOrderView.setPushStatus(skuOrder.getPushErrorMsg());
                    default -> skuOrderView.setPushStatus("未知推送状态");
                }

                skuOrderView.setFee(showFee(order.getShopOrder().getFee()));
                skuOrderView.setStatus(OrderStatus.fromInt(skuOrder.getStatus()).intoString());

                Payment payment = sqlSession.selectOne("Payment.findPaidAtWithSerial", Map.of("orderId", orderId
                        ,"orderType", 1));
                if (payment!=null) {
                    skuOrderView.setPaySerialNo(payment.getPaySerialNo());
                    skuOrderView.setPaidAt(DateUtil.toString(payment.getPaidAt()));
                }
                skuOrderView.setPayerNo(payerNo);
                skuOrderView.setPayerName(payerName);

                // set the receive-info
                skuOrderView.setMobile(mobile);
                skuOrderView.setAddress(address);
                skuOrderView.setReceiveUserName(userName);

                views.add(skuOrderView);
            }
        }
        return views;
    }

    private String showFee(Number tax) {
        if (tax == null) return "";
        return BigDecimal.valueOf(tax.longValue()).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN).toString();
    }

    @GetMapping("/api/admin/upload-download")
    public String queryStatus(String uuid) {
        return UPLOAD_TASK_STATUS.get(uuid);
    }

    @PostMapping("/api/admin/order/export-by-job")
    public String orderExportJob(OrderCriteria criteria) throws IOException {
        File tmp = File.createTempFile("job", "order-export-at-" + LocalDateTime.now());
        String jobId = UUID.randomUUID().toString();
        EXECUTOR.submit(() -> {
            try {
                OutputStream outputStream = new FileOutputStream(tmp);
                UPLOAD_TASK_STATUS.put(jobId, "开始导出");
                orderExportReader.exportOrderExcel(criteria, outputStream);
                outputStream.flush();
                outputStream.close();
                UPLOAD_TASK_STATUS.put(jobId, "导出完成");
                String url = ossClientService.upload(environmentConfig.getEnv(), "订单导出-" + jobId + ".xlsx", tmp);
                UPLOAD_TASK_STATUS.put(jobId, "上传完成, 请下载[" + url + "]");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return jobId;
    }


    @RequestMapping(value = "/api/admin/push", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public PushOneOrderResult push(@RequestParam("ids[]") Long[] ids) {
        StringBuilder pushErrorMsg = null;
        for (long id : ids) {
            CommonUser commonUser = UserUtil.getCurrentUser();

            Response<List<SkuOrder>> rSkuOrders = skuOrderReadService.findByShopOrderId(id);
            if (!rSkuOrders.isSuccess()) {
                log.error("failed to find sku orders by shop order id={}, error code:{}", id, rSkuOrders.getError());
                throw new JsonResponseException(rSkuOrders.getError());
            }
            List<SkuOrder> skuOrders = rSkuOrders.getResult();
            //判断用户是否有推单权限
            for (SkuOrder skuOrder : skuOrders) {
                if (!userTypeBean.isAdmin(commonUser) && !userTypeBean.isOperator(commonUser) && !Objects.equal(skuOrder.getShopId(), commonUser.getShopId())) {
                    log.error("user(id={}) is not allowed to push sku order(id={})", commonUser.getId(), id);
                    throw new JsonResponseException("sku.order.push.not.allowed");
                }

                //判断订单是否允许推单
                if (skuOrder.getStatus() != OrderStatus.PAID.getValue()) {
                    log.error("sku order(id={}) status is not PAID", id);
                    throw new JsonResponseException("sku.order.not.paid");
                }
                if (SkuOrderPushStatus.fromInt(skuOrder.getPushStatus()) == SkuOrderPushStatus.WAITING_SELLER_AUTH) {
                    log.error("sku order(id={}) need seller auth before push.", skuOrder.getId());
                    throw new JsonResponseException("sku.order.need.auth.before.push");
               /* case ERROR: {
                    log.error("sku order(id={}) has been pushed fail, need repush first.");
                    throw new JsonResponseException("sku.order.push.has.fail");
                }
                case FINISHED: {
                    log.error("sku order(id={}) has been pushed successfully, it do not need push.");
                    throw new JsonResponseException("sku.order.push.has.success");
                }*/
                }
                boolean f = skuOrderWriteService.updatePushStatus(skuOrder.getId(), SkuOrderPushStatus.WAITING.value(), skuOrder.getStatus());
                log.debug("f---" + JSON.toJSONString(f) + skuOrder.getId());
            }

            Boolean result = orderPushService.pushOrder(id);
            if (!result) {
                log.error("failed to push order(id={})", id);

                Response<List<SkuOrder>> rAfterPushSkuOrders = skuOrderReadService.findByShopOrderId(id);
                if (!rAfterPushSkuOrders.isSuccess()) {
                    log.error("failed to find sku orders by order id={}, error code: {}", id, rAfterPushSkuOrders.getError());
                    throw new JsonResponseException("sku.order.push.error.msg.find.fail");
                }

                for (SkuOrder skuOrder : rAfterPushSkuOrders.getResult()) {
                    if (skuOrder.getPushStatus() == SkuOrderPushStatus.ERROR.value() && skuOrder.getPushErrorMsg() != null) {
                        if (pushErrorMsg != null) {
                            pushErrorMsg.append(skuOrder.getPushErrorMsg());
                        } else {
                            pushErrorMsg = new StringBuilder(skuOrder.getPushErrorMsg());
                        }

                        break;
                    }
                }
            }
        }
        if (pushErrorMsg != null) {
            return PushOneOrderResult.fail(pushErrorMsg.toString());
        }
        return PushOneOrderResult.ok();
    }

    @RequestMapping(value = "/api/admin/order/auth", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean sellerAuthOrder(@RequestParam Long shopOrderId, @RequestParam String sellerNote, @RequestParam(defaultValue = "false") Boolean skip) {
       /* ParanaUser paranaUser = UserUtil.getCurrentUser();
        //检查商家是否开启商家审核
        Response<Shop> rShop = shopReadService.findById(paranaUser.getShopId());
        if (!rShop.isSuccess()) {
            log.error("failed to find shop by id={}, error code: {}", paranaUser.getShopId(), rShop.getError());
            throw new JsonResponseException(rShop.getError());
        }
        Shop shop = rShop.getResult();
        Map<String, String> extra = shop.getExtra();
        if (extra == null || !Objects.equal("true", extra.get(ParanaConstants.SHOP_OPEN_ORDER_AUTH))) {
            log.error("shop(id={}) want to auth order without opening orderAuth", shop.getId());
            throw new JsonResponseException("shop.not.open.order.auth");
        }
        //检查操作权限
        OrderBase orderBase = orderReadLogic.findOrder(shopOrderId, OrderLevel.SHOP);
        if (!Objects.equal(orderBase.getShopId(), paranaUser.getShopId())) {
            log.error("the shop order(id={}) not belong to seller(shop id={})", shopOrderId, paranaUser.getShopId());
            throw new JsonResponseException("order.not.belong.to.seller");
        }*/
        val rShopOrder = shopOrderReadService.findById(shopOrderId);
        if (!rShopOrder.isSuccess() || rShopOrder.getResult() == null) {
            throw new JsonResponseException("order.find.fail");
        }
        Response<List<SkuOrder>> rSkuOrders = skuOrderReadService.findByShopOrderId(shopOrderId);
        if (!rSkuOrders.isSuccess() || CollectionUtils.isEmpty(rSkuOrders.getResult())) {
            log.error("find skuOrders from shopOrder fail (order id:{})", shopOrderId);
            throw new JsonResponseException("order.find.fail");
        }
        //审核
        Boolean result = orderWriteLogic.sellerAuthOrder(shopOrderId, rSkuOrders.getResult(), sellerNote);
        if (!result) {
            return false;
        }

        ShopOrder shopOrder = rShopOrder.getResult();
        Map<String, String> orderExtra = shapeMap(shopOrder.getExtra());
        skip = !skip;
        orderExtra.put("skipAoXinPush", "" + skip);
        shopOrder.setExtra(orderExtra);
        orderWriteService.updateOrderExtra(shopOrder.getId(), OrderLevel.SHOP, orderExtra);
        return true;
    }


    @RequestMapping("/api/admin/order/{shopOrderId}/identity/change")
    public Boolean changeIdentity(@PathVariable Long shopOrderId, String identityName, String identityCode) {
        if (!identityCode.matches("(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{4}\\*{9}\\d{4}$)")) {
            throw new JsonResponseException(400, "receiver.info.paperNo.invalid");
        }
        val rShopOrder = shopOrderReadService.findById(shopOrderId);
        if (!rShopOrder.isSuccess()) {
            throw new JsonResponseException(rShopOrder.getError());
        }
        ShopOrder shopOrder = rShopOrder.getResult();
        /*//是否属于
        if (!Objects.equal(shopOrder.getShopId(), ((ParanaUser) UserUtil.getCurrentUser()).getShopId()) && !Objects.equal(shopOrder.getBuyerId(), UserUtil.getUserId())) {
            throw new JsonResponseException("item.not.belong.to.user");
        }*/
        //是否推送失败
        if (!identityFailCheck(shapeMap(shopOrder.getExtra()))) {
            return false;
        }
        Map<String, String> extra = shapeMap(shopOrder.getExtra());
        extra.put("payerName", identityName);
        extra.put("payerNo", identityCode);
        extra.put("identityError", "wait");
        shopOrder.setExtra(extra);
        val success = orderWriteService.updateOrderExtra(shopOrderId, OrderLevel.SHOP, extra);
        val rPay = paymentReadService.findByOrderIdAndOrderLevel(shopOrderId, OrderLevel.SHOP);
        val rPayUpdate = rPay.getResult().stream()
                .peek(payment -> payment.setPushStatus(PaymentPushStatus.DECLARED_FAIL_WAIT_PUSH_RETRY.getValue()))
                .map(paymentWriteService::update)
                .map(Response::isSuccess)
                .allMatch(Predicate.isEqual(true));
        if (!success.isSuccess()) {
            throw new JsonResponseException(success.getError());
        }
        if (!rPayUpdate) {
            throw new JsonResponseException();
        }
        return true;
    }

    private Map<String, String> shapeMap(Map<String, String> extra) {
        return extra == null ? new TreeMap<>() : extra;
    }

    boolean identityFailCheck(Map<String, String> extra) {
        return extra.getOrDefault("identityError", "false").equals("true");
    }
}
