package moonstone.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.Splitter;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.item.service.ItemReadService;
import moonstone.weShop.dto.WeShopViewedItem;
import moonstone.weShop.model.WeShopItem;
import moonstone.weShop.service.WeShopItemReadService;
import moonstone.weShop.service.WeShopReadService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by CaiZhy on 2018/12/19.
 */
@Slf4j
@Component
public class WeShopItemCacher {

    private LoadingCache<Long, WeShopItem> weShopItemCache;

    private LoadingCache<String, Long> weShopItemByWeShopIdAndItemIdCache;

    private LoadingCache<Long, WeShopViewedItem> weShopViewedItemCache;

    private LoadingCache<Long, List<Long>> weShopItemsByWeShopIdCache;

    private LoadingCache<String, List<Long>> weShopItemsByWeShopIdAndShopIdCache;

    @RpcConsumer
    private WeShopItemReadService weShopItemReadService;

    @RpcConsumer
    private ItemReadService itemReadService;

    @RpcConsumer
    private WeShopReadService weShopReadService;

    @Value("${cache.duration.in.minutes: 3}")
    private Integer duration;

    @PostConstruct
    public void init() {

        weShopItemCache = Caffeine.newBuilder()
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build(weShopItemId -> {
                    final Response<WeShopItem> rWeShopItem = weShopItemReadService.findById(weShopItemId);
                    if (!rWeShopItem.isSuccess()) {
                        log.error("failed to find weShop item(id={}), error code:{}", weShopItemId, rWeShopItem.getError());
                        throw new ServiceException(rWeShopItem.getError());
                    }
                    return rWeShopItem.getResult();
                });

        weShopItemByWeShopIdAndItemIdCache = Caffeine.newBuilder()
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build(weShopIdAndItemId -> {
                    List<String> weShopIdAndItemIdList = Splitter.on("_").splitToList(weShopIdAndItemId);
                    log.debug("{} weShopIdAndItemId[{}]", LogUtil.getClassMethodName(), weShopIdAndItemId);
                    final Response<WeShopItem> rWeShopItem = weShopItemReadService.findByWeShopIdAndItemId(
                            Long.valueOf(weShopIdAndItemIdList.get(0)), Long.valueOf(weShopIdAndItemIdList.get(1)));
                    if (!rWeShopItem.isSuccess()) {
                        log.error("failed to find weShop item by weShopId={}, itemId={}, error code:{}",
                                weShopIdAndItemIdList.get(0), weShopIdAndItemIdList.get(1), rWeShopItem.getError());
                        throw new ServiceException(rWeShopItem.getError());
                    }
                    return rWeShopItem.getResult().getId();
                });

        weShopViewedItemCache = Caffeine.newBuilder()
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build(weShopItemId -> {
                    Response<WeShopViewedItem> rWeShopViewedItem = weShopItemReadService.findForView(weShopItemId);
                    if (!rWeShopViewedItem.isSuccess()) {
                        log.error("failed to find weShopItem viewed info by id={}, error code: {}", weShopItemId, rWeShopViewedItem.getError());
                        throw new ServiceException(rWeShopViewedItem.getError());
                    }
                    return rWeShopViewedItem.getResult();
                });

        weShopItemsByWeShopIdCache = Caffeine.newBuilder()
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build(weShopId -> {
                    Response<List<WeShopItem>> response = weShopItemReadService.findByWeShopId(weShopId);
                    if (!response.isSuccess()) {
                        log.error("failed to find weShopItems by weShopId={}, error code: {}", weShopId, response.getError());
                        throw new ServiceException(response.getError());
                    }
                    return response.getResult().stream().map(WeShopItem::getId).collect(Collectors.toList());
                });

        weShopItemsByWeShopIdAndShopIdCache = Caffeine.newBuilder()
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build(weShopIdAndShopId -> {
                    List<String> weShopIdAndShopIdList = Splitter.on("_").splitToList(weShopIdAndShopId);
                    Long weShopId = Long.valueOf(weShopIdAndShopIdList.get(0));
                    Long shopId = Long.valueOf(weShopIdAndShopIdList.get(1));
                    Response<List<WeShopItem>> response = weShopItemReadService.findByWeShopIdAndShopId(weShopId, shopId);
                    if (!response.isSuccess()) {
                        log.error("failed to find weShopItems by weShopId={}, shopId={}, error code: {}", weShopId, shopId, response.getError());
                        throw new ServiceException(response.getError());
                    }
                    return response.getResult().stream().map(WeShopItem::getId).collect(Collectors.toList());
                });
    }

    /**
     * 根据商品id查找对应的微分销店铺商品
     *
     * @param weShopItemId  微分销店铺商品id
     * @return  对应的微分销店铺商品信息
     */
    public WeShopItem findWeShopItemById(Long weShopItemId){
        return weShopItemCache.get(weShopItemId);
    }

    /**
     * 根据微分销店铺和商品id查找对应的微分销店铺商品
     *
     * @param weShopId  微分销店铺id
     * @param itemId 商品id
     * @return  对应的微分销店铺商品信息
     */
    public WeShopItem findWeShopItemByWeShopIdAndItemId(Long weShopId, Long itemId) {
        return Optional.ofNullable(weShopItemByWeShopIdAndItemIdCache.get(weShopId + "_" + itemId))
                .map(weShopItemCache::get).orElse(null);
    }

    /**
     * 根据微分销商品id查找用于显示的的微分销商品详情
     *
     * @param weShopItemId  微分销商品id
     * @return  显示的的微分销商品详情
     */
    public WeShopViewedItem findForView(Long weShopItemId){
        return weShopViewedItemCache.get(weShopItemId);
    }

    /**
     * 根据微分销店铺id获取关联的所有微分销商品
     *
     * @param weShopId 微分销店铺id
     * @return 微分销商品列表
     */
    public List<WeShopItem> listWeShopItemsByWeShopId(Long weShopId) {
        return Optional.ofNullable(weShopItemsByWeShopIdCache.get(weShopId))
                .orElseGet(ArrayList::new)
                .stream().map(weShopItemCache::get).collect(Collectors.toList());
    }

    /**
     * 清除对应微分销店铺的微分销商品列表
     */
    public void invalidateWeShopItemById(Long weShopItemId) {
        weShopItemCache.invalidate(weShopItemId);
        weShopViewedItemCache.invalidate(weShopItemId);
        weShopItemByWeShopIdAndItemIdCache.invalidateAll();
        weShopItemsByWeShopIdAndShopIdCache.invalidateAll();
        weShopItemsByWeShopIdCache.invalidateAll();
    }

    /**
     * 清除对应微分销店铺的微分销商品列表
     *
     * @param weShopId 微分销店铺id
     */
    public void invalidateWeShopItemsByWeShopId(Long weShopId) {
        weShopItemsByWeShopIdCache.invalidate(weShopId);
        List<WeShopItem> weShopItemList = weShopItemReadService.findByWeShopId(weShopId).getResult();
        weShopItemList.stream().map(WeShopItem::getId)
                .forEach(weShopViewedItemCache::invalidate);
        weShopItemList.stream().map(WeShopItem::getId)
                .forEach(weShopItemCache::invalidate);
    }

    /**
     * 根据微分销店铺id和商家id获取关联的所有微分销商品
     * @param weShopId 微分销店铺id
     * @param shopId 商家id
     * @return 微分销商品列表
     */
    public List<WeShopItem> listWeShopItemsByWeShopIdAndShopId(Long weShopId, Long shopId) {
        return Optional.ofNullable(weShopItemsByWeShopIdAndShopIdCache.get(weShopId + "_" + shopId))
                .orElseGet(ArrayList::new).stream().map(weShopItemCache::get).collect(Collectors.toList());
    }

    /**
     *
     * 清除对应微分销店铺对应商家的微分销商品列表
     * @param weShopId 微分销店铺id
     * @param shopId 商家id
     */
    public void invalidateWeShopItemsByWeShopIdAndShopId(Long weShopId, Long shopId){
        weShopItemsByWeShopIdAndShopIdCache.invalidate(weShopId + "_" + shopId);
    }
}
