package moonstone.weShop.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Function;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.terminus.common.utils.JsonMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import moonstone.common.constants.JacksonType;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Author:  CaiZhy
 * Date:    2018/12/28
 */
@ToString
public class WeShopAccountDetail implements Serializable {
    private static final long serialVersionUID = 7873495055938334378L;

    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();

    @Getter
    @Setter
    private Long id;

    /**
     * 收益类型
     * @see moonstone.weShop.enums.WeShopAccountDetailType
     */
    @Getter
    @Setter
    private Integer type;

    /**
     * 变动金额，负数表示扣减
     */
    @Getter
    @Setter
    private Long amounts;

    /**
     * 账户余额
     */
    @Getter
    @Setter
    private Long balance;

    /**
     * 微分销店铺id
     */
    @Getter
    @Setter
    private Long weShopId;

    /**
     * 账户所关联的商家id
     */
    @Getter
    @Setter
    private Long shopId;

    /**
     * 来源id列表，为相应的收益或提现id，存数据库，json形式
     */
    @Getter
    @JsonIgnore
    private String sourceIdsJson;

    /**
     * 来源id列表，为相应的收益或提现id，不存数据库
     */
    @Getter
    private List<Long> sourceIds;

    /**
     * 备注
     */
    @Getter
    @Setter
    private String remark;

    /**
     * 额外信息json字符串，存数据库
     */
    @Getter
    @JsonIgnore
    private String extraJson;

    /**
     * 额外信息map，不存数据库
     */
    @Getter
    private Map<String, String> extra;

    /**
     * 标签的json表示形式，存数据库
     */
    @Getter
    @JsonIgnore
    private String tagsJson;

    /**
     * 标签map, 不存数据库
     */
    @Getter
    private Map<String, String> tags;

    /**
     * 状态 1:正常, -1:删除
     */
    @Getter
    @Setter
    private Integer status;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 修改时间
     */
    @Getter
    @Setter
    private Date updatedAt;

    public void setSourceIdsJson(String sourceIdsJson) throws Exception{
        this.sourceIdsJson = sourceIdsJson;
        if(Strings.isNullOrEmpty(sourceIdsJson)){
            this.sourceIds = Collections.emptyList();
        } else{
            List<String> result = objectMapper.readValue(sourceIdsJson, JacksonType.LIST_OF_STRING);
            this.sourceIds = Lists.transform(result, new Function<String, Long>() {
                @Override
                public Long apply(String s) {
                    return Long.valueOf(s);
                }
            });
        }
    }

    public void setSourceIds(List<Long> sourceIds) {
        this.sourceIds = sourceIds;
        if(sourceIds == null || sourceIds.isEmpty()){
            this.sourceIdsJson = null;
        }else{
            try {
                this.sourceIdsJson = objectMapper.writeValueAsString(sourceIds);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }

    public void setExtraJson(String extraJson) throws Exception{
        this.extraJson = extraJson;
        if(Strings.isNullOrEmpty(extraJson)){
            this.extra= Collections.emptyMap();
        } else{
            this.extra = objectMapper.readValue(extraJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        if(extra == null || extra.isEmpty()){
            this.extraJson = null;
        }else{
            try {
                this.extraJson = objectMapper.writeValueAsString(extra);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }

    public void setTagsJson(String tagsJson) throws Exception{
        this.tagsJson = tagsJson;
        if(Strings.isNullOrEmpty(tagsJson)){
            this.tags= Collections.emptyMap();
        } else{
            this.tags = objectMapper.readValue(tagsJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
        if(tags == null || tags.isEmpty()){
            this.tagsJson = null;
        }else{
            try {
                this.tagsJson = objectMapper.writeValueAsString(tags);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }
}
