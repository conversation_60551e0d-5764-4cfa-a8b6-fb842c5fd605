package moonstone.weShop.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import moonstone.item.model.Sku;
import moonstone.weShop.model.WeShopSku;

import java.util.Optional;

public interface WeShopSkuView {
    Integer getPrice();

    Long getTax();

    static WeShopSkuView build(WeShopSku weShopSku, Sku sku) {
        return new WeShopSkuViewImpl(Optional.ofNullable(weShopSku.getPrice()).orElseGet(() -> weShopSku.getDiffPrice() + sku.getPrice()).intValue(), weShopSku.getTax());
    }

    @Data
    @AllArgsConstructor
    class WeShopSkuViewImpl implements WeShopSkuView {
        Integer price;
        Long tax;
    }
}
