package moonstone.weShop.service;

import io.terminus.common.model.Response;
import moonstone.weShop.model.WeShopShopAccount;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/12/3.
 */
public interface WeShopShopAccountWriteService {
    Response<Long> create(WeShopShopAccount weShopShopAccount);

    Response<Boolean> update(WeShopShopAccount weShopShopAccount);

    Response<Boolean> updateStatus(Long weShopShopAccountId, Integer status);

    Response<Boolean> settleProfit(Long weShopShopAccountId, Long profit);

    Response<Boolean> addBalance(Long weShopShopAccountId, Long amount);

    Response<Boolean> addWithdraw(Long weShopShopAccountId, Long amount);

    Response<Boolean> addTotalProfitAmount(Long weShopShopAccountId, Long profit);

    Response<Boolean> delete(Long weShopShopAccountId);

    Response<Boolean> frozen(Long weShopShopAccountId);

    Response<Boolean> unfrozen(Long weShopShopAccountId);
}
