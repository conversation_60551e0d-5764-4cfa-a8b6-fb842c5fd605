package moonstone.search.weShopItem;

import io.terminus.common.model.Response;

/**
 * Created by <PERSON>aiZhy on 2018/12/24.
 */
public interface WeShopItemSearchWriteService {

    /**
     * 索引微分销商品
     *
     * @param weShopItemId 微分销商品id
     * @return  是否调用成功
     */
    Response<Boolean> index(Long weShopItemId);

    /**
     * 删除微分销商品
     *
     * @param weShopItemId 微分销商品id
     * @return  是否调用成功
     */
    Response<Boolean> delete(Long weShopItemId);

    /**
     * 通过商品id删除微分销商品
     *
     * @param itemId 商品id
     * @return 是否调用成功
     */
    Response<Boolean> deleteByItem(Long itemId);

    /**
     * 索引或者删除微分销商品
     *
     * @param weShopItemId  微分销商品id
     * @return 是否调用成功
     */
    Response<Boolean> update(Long weShopItemId);

    /**
     * 根据商品id索引或者删除微分销商品
     *
     * @param itemId 商品id
     * @return 是否调用成功
     */
    Response<Boolean> updateByItem(Long itemId);

    /**
     * 索引或者删除店铺的全部商品,这个是给店铺状态改变时调用的
     *
     * @param weShopId 微分销店铺id
     * @return 是否成功
     */
    Response<Boolean> updateByWeShopId(Long weShopId);
}
