package moonstone.weDistributionApplication.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.utils.JsonMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import moonstone.common.constants.JacksonType;
import moonstone.weDistributionApplication.enums.RecommendType;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/12/3.
 */
@ToString
public class WeDistributionApplication implements Serializable {
    private static final long serialVersionUID = -1440908854272972418L;

    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();

    @Getter
    @Setter
    private Long id;

    /**
     * 微分销店铺id
     */
    @Getter
    @Setter
    private Long weShopId;

    /**
     * 创建人用户id（冗余）
     */
    @Getter
    @Setter
    private Long userId;

    /**
     * 申请分销的商家id
     */
    @Getter
    @Setter
    private Long shopId;

    /**
     * 推荐来源：1：商家邀请，2：微分销店主邀请
     * @see RecommendType
     */
    @Getter
    @Setter
    private Integer recommendType;

    /**
     * 推荐商家id
     */
    @Getter
    @Setter
    private Long recommendShopId;

    /**
     * 推荐用户id
     */
    @Getter
    @Setter
    private Long recommendUserId;

    /**
     * 申请备注
     */
    @Getter
    @Setter
    private String applyRemark;

    /**
     * 审核备注
     */
    @Getter
    @Setter
    private String auditingRemark;

    /**
     * 额外信息json字符串，存数据库
     */
    @Getter
    @JsonIgnore
    private String extraJson;

    /**
     * 额外信息map，不存数据库
     */
    @Getter
    private Map<String, String> extra;

    /**
     * 标签的json表示形式，存数据库
     */
    @Getter
    @JsonIgnore
    private String tagsJson;

    /**
     * 标签map, 不存数据库
     */
    @Getter
    private Map<String, String> tags;

    /**
     * 状态 1:审核中, 2:审核通过, -1:取消审核，-2:商家拒绝，-99:删除
     */
    @Getter
    @Setter
    private Integer status;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 修改时间
     */
    @Getter
    @Setter
    private Date updatedAt;

    public void setExtraJson(String extraJson) throws Exception{
        this.extraJson = extraJson;
        if(Strings.isNullOrEmpty(extraJson)){
            this.extra= Collections.emptyMap();
        } else{
            this.extra = objectMapper.readValue(extraJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        if(extra == null || extra.isEmpty()){
            this.extraJson = null;
        }else{
            try {
                this.extraJson = objectMapper.writeValueAsString(extra);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }

    public void setTagsJson(String tagsJson) throws Exception{
        this.tagsJson = tagsJson;
        if(Strings.isNullOrEmpty(tagsJson)){
            this.tags= Collections.emptyMap();
        } else{
            this.tags = objectMapper.readValue(tagsJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setTags(Map<String, String> tags) {
        this.tags = tags;
        if(tags == null || tags.isEmpty()){
            this.tagsJson = null;
        }else{
            try {
                this.tagsJson = objectMapper.writeValueAsString(tags);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }
}
