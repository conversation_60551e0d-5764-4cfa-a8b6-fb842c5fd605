/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.admin.spu;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import configuration.web.FrontWebConfiguration;
import moonstone.attribute.dto.GroupedOtherAttribute;
import moonstone.attribute.dto.GroupedSkuAttribute;
import moonstone.attribute.dto.OtherAttribute;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.item.dto.ImageInfo;
import moonstone.spu.dto.FullSpu;
import moonstone.spu.model.SkuTemplate;
import moonstone.spu.model.Spu;
import moonstone.spu.model.SpuDetail;
import moonstone.web.core.BaseWebTest;
import org.junit.After;
import org.junit.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ContextConfiguration;

import java.util.List;
import java.util.Random;

import static org.hamcrest.CoreMatchers.not;
import static org.hamcrest.core.Is.is;
import static org.hamcrest.core.IsNull.notNullValue;
import static org.junit.Assert.assertThat;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-03-04
 */
@ContextConfiguration(classes = {FrontWebConfiguration.class})
public class SpusTest extends BaseWebTest {

    @Test
    public void testValidSpu() throws Exception {
        FullSpu fullSpu = makeFullSpu();

        Long id = postForObject("/api/spu", fullSpu, Long.class);
        assertThat(id, notNullValue());
    }

    private FullSpu makeFullSpu() throws Exception {
        FullSpu fullSpu = new FullSpu();

        Spu spu = new Spu();
        spu.setCategoryId(1L);
        spu.setName("testSpu"+new Random(System.currentTimeMillis()).nextInt());
        spu.setBrandId(1L);
        spu.setBrandName("nike");
        spu.setExtra(ImmutableMap.of("unit","双"));
        spu.setMainImage("http://taobao.com");

        fullSpu.setSpu(spu);

        SpuDetail spuDetail = new SpuDetail();
        spuDetail.setDetail("i am detail");
        spuDetail.setImages(ImmutableList.of(new ImageInfo("xx", "http://xx0oo.com")));
        spuDetail.setPacking(ImmutableMap.of("包邮","true"));

        fullSpu.setSpuDetail(spuDetail);

        SkuAttribute color_red = new SkuAttribute();
        color_red.setAttrKey("颜色");
        color_red.setAttrVal("红色");

        SkuAttribute color_black = new SkuAttribute();
        color_black.setAttrKey("颜色");
        color_black.setAttrVal("黑色");

        SkuAttribute size_110 = new SkuAttribute();
        size_110.setAttrKey("尺码");
        size_110.setAttrVal("110");
        size_110.setUnit("cm");

        SkuAttribute size_120 = new SkuAttribute();
        size_120.setAttrKey("尺码");
        size_120.setAttrVal("120");
        size_120.setUnit("cm");


        GroupedSkuAttribute colorGroup = new GroupedSkuAttribute("颜色",ImmutableList.of(color_black,color_red));
        GroupedSkuAttribute sizeGroup = new GroupedSkuAttribute("尺码",ImmutableList.of(size_110,size_120));

        fullSpu.setGroupedSkuAttributes(ImmutableList.of(colorGroup,sizeGroup));

        OtherAttribute address = new OtherAttribute();
        address.setAttrKey("产地");
        address.setAttrVal("杭州");
        GroupedOtherAttribute groupedOtherAttribute = new GroupedOtherAttribute("其他参数",ImmutableList.of(address));

        fullSpu.setGroupedOtherAttributes(ImmutableList.of(groupedOtherAttribute));

        fullSpu.setSkuTemplates(ImmutableList.of(makeSkuTemplate(1)));
        return fullSpu;
    }

    @Test
    public void testOnlyOneSkuAttribute() throws Exception {  //允许在两个sku候选中只选择一个作为sku属性

        FullSpu onlyOneSkuAttributeSpu = makeFullSpu();

        //删掉一个sku属性
        SkuAttribute size_120 = new SkuAttribute();
        size_120.setAttrKey("尺码");
        size_120.setAttrVal("120");
        size_120.setUnit("cm");


        SkuAttribute color_red = new SkuAttribute();
        color_red.setAttrKey("颜色");
        color_red.setAttrVal("红色");

        OtherAttribute other_size_120 = new OtherAttribute();
        other_size_120.setAttrKey("尺码");
        other_size_120.setAttrVal("120");
        other_size_120.setUnit("cm");

        OtherAttribute address = new OtherAttribute();
        address.setAttrKey("产地");
        address.setAttrVal("杭州");


        GroupedOtherAttribute groupedOtherAttribute = new GroupedOtherAttribute("其他参数", Lists.newArrayList(other_size_120,address));

        onlyOneSkuAttributeSpu.setGroupedOtherAttributes(ImmutableList.of(groupedOtherAttribute));

        final List<GroupedSkuAttribute> groupedSkuAttributes = ImmutableList.of(new GroupedSkuAttribute("颜色", ImmutableList.of(color_red)));
        onlyOneSkuAttributeSpu.setGroupedSkuAttributes(groupedSkuAttributes);

        SkuTemplate skuTemplate = onlyOneSkuAttributeSpu.getSkuTemplates().get(0);
        skuTemplate.setAttrs(ImmutableList.of(color_red));

        ResponseEntity<Long> result = postForEntity("/api/spu", onlyOneSkuAttributeSpu, Long.class);
        assertThat(result.getStatusCode(), is(HttpStatus.OK));

    }

    @Test
    public void testBadSkuAttribute() throws Exception {   //sku属性不合法
        FullSpu badSkuAttributeSpu = makeFullSpu();

        //一个不合法的sku属性
        SkuAttribute size_120 = new SkuAttribute();
        size_120.setAttrKey("foo");
        size_120.setAttrVal("120");
        size_120.setUnit("cm");


        final List<GroupedSkuAttribute> groupedSkuAttributes = ImmutableList.of(new GroupedSkuAttribute("foo", ImmutableList.of(size_120)));
        badSkuAttributeSpu.setGroupedSkuAttributes(groupedSkuAttributes);

        ResponseEntity<String> result = postForEntity("/api/spu", badSkuAttributeSpu, String.class);
        assertThat(result.getStatusCode(), not(is(HttpStatus.OK)));
    }

    @Test
    public void testMissRequiredAttribute() throws Exception {   //没有选择必须的其他属性
        FullSpu missRequiredAttributeSpu = makeFullSpu();

        OtherAttribute address = new OtherAttribute();
        address.setAttrKey("等级");
        address.setAttrVal("bar");

        final List<GroupedOtherAttribute> groupedOtherAttributes = ImmutableList.of(new GroupedOtherAttribute("其他参数", ImmutableList.of(address)));
        missRequiredAttributeSpu.setGroupedOtherAttributes(groupedOtherAttributes);

        ResponseEntity<String> result = postForEntity("/api/spu", missRequiredAttributeSpu, String.class);
        assertThat(result.getStatusCode(), not(is(HttpStatus.OK)));
    }

    @Test
    public void testBadOtherAttribute() throws Exception {   //其他属性值不合法
        FullSpu badOtherAttributeSpu = makeFullSpu();

        OtherAttribute grade = new OtherAttribute();
        grade.setAttrKey("等级");
        grade.setAttrVal("bar");

        final GroupedOtherAttribute groupedGrade = new GroupedOtherAttribute("其他参数", ImmutableList.of(grade));

        OtherAttribute address = new OtherAttribute();
        address.setAttrKey("产地");
        address.setAttrVal("杭州");
        GroupedOtherAttribute groupedOtherAttribute = new GroupedOtherAttribute("其他参数",ImmutableList.of(address));

        final List<GroupedOtherAttribute> groupedOtherAttributes = ImmutableList.of(groupedOtherAttribute,groupedGrade);
        badOtherAttributeSpu.setGroupedOtherAttributes(groupedOtherAttributes);

        ResponseEntity<String> result = postForEntity("/api/spu", badOtherAttributeSpu, String.class);
        assertThat(result.getStatusCode(), not(is(HttpStatus.OK)));
    }

    @Test
    public void testBadValueTypeSpu() throws Exception {
        FullSpu badValueTypeSpu = makeFullSpu();

        OtherAttribute weight = new OtherAttribute();
        weight.setAttrKey("重量");
        weight.setAttrVal("foobar");
        GroupedOtherAttribute groupedWeight = new GroupedOtherAttribute("其他参数",ImmutableList.of(weight));

        OtherAttribute address = new OtherAttribute();
        address.setAttrKey("产地");
        address.setAttrVal("杭州");
        GroupedOtherAttribute groupedOtherAttribute = new GroupedOtherAttribute("其他参数",ImmutableList.of(address));

        final List<GroupedOtherAttribute> groupedOtherAttributes = ImmutableList.of(groupedOtherAttribute,groupedWeight);
        badValueTypeSpu.setGroupedOtherAttributes(groupedOtherAttributes);

        ResponseEntity<String> result = postForEntity("/api/spu", badValueTypeSpu, String.class);
        assertThat(result.getStatusCode(), not(is(HttpStatus.OK)));
    }


    @Test
    public void testNoCategoryAttributeButSpuHasSkuAttributesTest() throws Exception {
        FullSpu fullSpu = makeFullSpu();

        //category(id=3) 没有任何属性(包括销售属性), 那么该类目下的商品也不允许定义销售属性,(但允许有sku)
        fullSpu.getSpu().setCategoryId(3L);

//        ResponseEntity<String> result =  postForEntity("/api/spu", fullSpu, String.class);
        ResponseEntity<String> result =  restTemplate.postForEntity("http://localhost:{port}/api/spu", fullSpu,
                                                                    String.class, ImmutableMap.of("port", port));
        assertThat(result.getStatusCode(), not(is(HttpStatus.OK)));

    }

    @Test
    public void testNoCategoryAttributeTest() throws Exception {
        FullSpu fullSpu = makeFullSpu();

        //category(id=3) 没有任何属性(包括销售属性), 那么该类目下的商品也不允许定义销售属性,(但允许有sku)
        fullSpu.getSpu().setCategoryId(3L);

        fullSpu.setGroupedSkuAttributes(null);

        for (SkuTemplate sku : fullSpu.getSkuTemplates()) {
            sku.setAttrs(null);
        }

        Long id = postForObject("/api/spu", fullSpu, Long.class);
        assertThat(id, notNullValue());

    }

    @After
    public void tearDown() throws Exception {
    }

    private SkuTemplate makeSkuTemplate(long i) throws Exception{
        SkuTemplate skuTemplate = new SkuTemplate();
        Random r = new Random();
        skuTemplate.setSkuCode("skuCode"+i);
        skuTemplate.setSpecification("ZUC-CW000-RED");
        skuTemplate.setStatus(1);
        skuTemplate.setImage("image"+i);
        skuTemplate.setThumbnail("thumbnail"+i);
        skuTemplate.setName("sku"+i);
        skuTemplate.setPrice(6000);
        SkuAttribute color_red = new SkuAttribute();
        color_red.setAttrKey("颜色");
        color_red.setAttrVal("红色");

        SkuAttribute size_120 = new SkuAttribute();
        size_120.setAttrKey("尺码");
        size_120.setAttrVal("120");
        size_120.setUnit("cm");

        skuTemplate.setAttrs(ImmutableList.of(color_red,size_120));
        skuTemplate.setStockQuantity(Math.abs(r.nextInt()));
        skuTemplate.setStockType(0);
        skuTemplate.setExtraPrice(ImmutableMap.of("originPrice",3, "platformPrice", 4));
        return skuTemplate;
    }
}
