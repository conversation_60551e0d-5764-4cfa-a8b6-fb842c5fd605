package moonstone.web.core.front.open;

import io.terminus.pampas.openplatform.entity.OPResponse;
import moonstone.category.model.BackCategory;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

/**
 * Author:cp
 * Created on 8/12/16.
 */
public class CategoryApiTest extends OpenBase {

    @Test
    public void testFindChildrenByPid() {
        putKey("back.category.children.find.by.pid");
        addParam("pid", 0);

        OPResponse<List<BackCategory>> opResponse = getForObject(OPResponse.class);
        Assert.assertTrue(opResponse.isSuccess());
        Assert.assertTrue(!CollectionUtils.isEmpty(opResponse.getResult()));
    }

}
