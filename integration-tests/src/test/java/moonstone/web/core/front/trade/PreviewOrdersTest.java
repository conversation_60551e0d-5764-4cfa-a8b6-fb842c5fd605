/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.front.trade;

import com.google.common.collect.Lists;
import configuration.web.FrontWebConfiguration;
import io.terminus.common.utils.JsonMapper;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.SubmittedSku;
import moonstone.web.core.BaseWebTest;
import org.junit.Test;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.core.Is.is;
import static org.hamcrest.core.IsNull.notNullValue;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-13
 */
@ContextConfiguration(classes = {FrontWebConfiguration.class})
public class PreviewOrdersTest extends BaseWebTest {

    @Test
    public void preview() throws Exception {
        List<SubmittedSku> submittedSkus = Lists.newArrayList();
        final SubmittedSku ss1 = new SubmittedSku();
        ss1.setSkuId(1L);
        ss1.setQuantity(2);
        submittedSkus.add(ss1);

        final SubmittedSku ss2 = new SubmittedSku();
        ss2.setSkuId(2L);
        ss2.setQuantity(3);
        submittedSkus.add(ss2);

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl("http://localhost:" + port+"/api/order/preview" )
                .queryParam("data", JsonMapper.JSON_NON_EMPTY_MAPPER.toJson(submittedSkus)) ;
        RichOrder richOrder = restTemplate.getForObject(builder.build().encode().toUri(),RichOrder.class);
        assertThat(richOrder, notNullValue());
        assertThat(richOrder.getRichSkusByShops().size(), is(1));
        assertThat(richOrder.getRichSkusByShops().get(0).getRichSkus().size(),is(2));
    }

}