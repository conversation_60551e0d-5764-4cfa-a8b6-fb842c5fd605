package moonstone.showcase.controller;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/scan/rocketmq")
public class RocketMQConsumerController {

    @Autowired
    private ApplicationContext applicationContext;

    @GetMapping("/consumers")
    public List<ConsumerInfo> getRocketMQConsumers() {
        List<ConsumerInfo> consumerInfos = new ArrayList<>();
        Map<String, RocketMQListener> listeners = applicationContext.getBeansOfType(RocketMQListener.class);

        listeners.forEach((beanName, listener) -> {
            Class<?> targetClass = AopUtils.getTargetClass(listener);
            RocketMQMessageListener annotation = targetClass.getAnnotation(RocketMQMessageListener.class);

            if (annotation != null) {
                ConsumerInfo info = new ConsumerInfo();
                info.setBeanName(beanName);
                info.setClassName(targetClass.getSimpleName());
                info.setTopic(annotation.topic());
                info.setConsumerGroup(annotation.consumerGroup());
                info.setSelectorType(annotation.selectorType().name());
                info.setSelectorExpression(annotation.selectorExpression());
                info.setConsumeMode(annotation.consumeMode().name());
                info.setMessageModel(annotation.messageModel().name());

                consumerInfos.add(info);
            }
        });

        return consumerInfos;
    }

    public static class ConsumerInfo {
        private String beanName;
        private String className;
        private String topic;
        private String consumerGroup;
        private String selectorType;
        private String selectorExpression;
        private String consumeMode;
        private String messageModel;

        // getters and setters
        public String getBeanName() {
            return beanName;
        }

        public void setBeanName(String beanName) {
            this.beanName = beanName;
        }

        public String getClassName() {
            return className;
        }

        public void setClassName(String className) {
            this.className = className;
        }

        public String getTopic() {
            return topic;
        }

        public void setTopic(String topic) {
            this.topic = topic;
        }

        public String getConsumerGroup() {
            return consumerGroup;
        }

        public void setConsumerGroup(String consumerGroup) {
            this.consumerGroup = consumerGroup;
        }

        public String getSelectorType() {
            return selectorType;
        }

        public void setSelectorType(String selectorType) {
            this.selectorType = selectorType;
        }

        public String getSelectorExpression() {
            return selectorExpression;
        }

        public void setSelectorExpression(String selectorExpression) {
            this.selectorExpression = selectorExpression;
        }

        public String getConsumeMode() {
            return consumeMode;
        }

        public void setConsumeMode(String consumeMode) {
            this.consumeMode = consumeMode;
        }

        public String getMessageModel() {
            return messageModel;
        }

        public void setMessageModel(String messageModel) {
            this.messageModel = messageModel;
        }
    }
}