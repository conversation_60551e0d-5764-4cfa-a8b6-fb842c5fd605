package moonstone.showcase.mq.paid;

import lombok.extern.slf4j.Slf4j;
import moonstone.message.PaymentRefluxMessage;
import moonstone.showcase.mq.Topic;
import moonstone.web.admin.jobs.payment.PaymentInfoPushMessageHandler;
import moonstone.web.op.allinpay.lock.RedissonDistributedLock;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
@RocketMQMessageListener(
        topic = Topic.PAYMENT_REFLUX_MESSAGE_TOPIC,
        consumerGroup = "parana_payment_reflux_group",
        enableMsgTrace = false,
        messageModel = MessageModel.CLUSTERING
)
public class PaymentRefluxEventConsumer implements RocketMQListener<PaymentRefluxMessage> {

    @Resource
    private PaymentInfoPushMessageHandler paymentInfoPushMessageHandler;

    @Resource
    private RedissonDistributedLock distributedLock;

    public void onMessage(PaymentRefluxMessage event) {
        Long paymentId = event.getPaymentId();
        log.info("PaymentRefluxEventConsumer 收到消息 : {}", paymentId);

        String key = "parana:payment_paid_event_topic:" + paymentId;
        distributedLock.lock(key, ()-> {
            paymentInfoPushMessageHandler.reflux(paymentId);
            return null;
        });
    }

}
