package moonstone.showcase.mq.event;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.events.shop.ShopPayInfoDeleteEvent;
import moonstone.web.core.events.shop.listener.ShopPayInfoDeleteListener;
import moonstone.web.op.allinpay.lock.RedissonDistributedLock;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 广播消费
 */
@Slf4j
@Component
@RocketMQMessageListener(
        topic = ShopPayInfoDeleteEvent.TOPIC,
        consumerGroup = ShopPayInfoDeleteEvent.TOPIC,
        enableMsgTrace = false,
        messageModel = MessageModel.BROADCASTING
)
public class ShopPayInfoDeleteEventConsumer implements RocketMQListener<ShopPayInfoDeleteEvent> {

    @Resource
    private ShopPayInfoDeleteListener shopPayInfoDeleteListener;

    @Resource
    private RedissonDistributedLock distributedLock;


    public void onMessage(ShopPayInfoDeleteEvent message) {
        log.info("ShopPayInfoDeleteEventConsumer 收到消息 : {}", JSON.toJSONString(message));

        Long shopId = message.getShopId();

        String key = "parana:shop_pay_info_delete_event_topic" + shopId;
        distributedLock.lock(key, ()-> {
            shopPayInfoDeleteListener.shopPayInfoDelete(message);
            return null;
        });
    }
}
