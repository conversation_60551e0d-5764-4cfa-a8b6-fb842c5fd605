package moonstone.showcase.mq.event;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.shop.model.ShopPayInfo;
import moonstone.web.core.events.shop.ShopPayInfoChangeEvent;
import moonstone.web.core.events.shop.listener.ShopPayInfoChangeListener;
import moonstone.web.op.allinpay.lock.RedissonDistributedLock;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 广播消费
 */
@Slf4j
@Component
@RocketMQMessageListener(
        topic = ShopPayInfoChangeEvent.TOPIC,
        consumerGroup = ShopPayInfoChangeEvent.TOPIC,
        enableMsgTrace = false,
        messageModel = MessageModel.BROADCASTING
)
public class ShopPayInfoChangeEventConsumer implements RocketMQListener<ShopPayInfoChangeEvent> {

    @Resource
    private ShopPayInfoChangeListener shopPayInfoChangeListener;

    @Resource
    private RedissonDistributedLock distributedLock;


    public void onMessage(ShopPayInfoChangeEvent message) {
        log.info("ShopPayInfoChangeEventConsumer 收到消息 : {}", JSON.toJSONString(message));

        ShopPayInfo shopPayInfo = message.getShopPayInfo();
        if(shopPayInfo == null){
            log.error("无支付信息 忽略");
            return;
        }
        Long id = shopPayInfo.getShopId();

        String key = "parana:shop_pay_info_change_event_topic" + id;
        distributedLock.lock(key, ()-> {
            shopPayInfoChangeListener.shopPayInfoChange(message);
            return null;
        });
    }
}
